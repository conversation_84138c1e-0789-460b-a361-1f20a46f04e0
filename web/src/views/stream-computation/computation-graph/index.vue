<template>
  <PageLayout
    title="计算图"
    description="管理和配置计算图，定义数据计算逻辑和执行流程"
  >
    <!-- 操作栏 -->
    <template #toolbar>
      <div
        class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"
      >
        <div class="flex-1 max-w-md">
          <el-input
            v-model="searchQuery"
            placeholder="搜索计算图名称..."
            clearable
            :prefix-icon="Search"
            @input="handleSearch"
          />
        </div>
        <div class="flex items-center space-x-3">
          <span class="text-sm text-gray-600 dark:text-gray-400">
            共 {{ total }} 个计算图
          </span>
          <el-button type="primary" @click="showCreateDialog">
            <el-icon size="16" class="mr-2"><Plus /></el-icon>
            新建计算图
          </el-button>
        </div>
      </div>
    </template>

    <!-- 表格 -->
    <el-table
      :data="tableData"
      v-loading="loading"
      stripe
      style="width: 100%"
      class="rounded-lg"
      @sort-change="handleSortChange"
    >
      <el-table-column prop="id" label="图ID" width="100" sortable />
      <el-table-column prop="name" label="图名称" min-width="150" sortable>
        <template #default="{ row }">
          <div class="flex items-center">
            <div
              class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center mr-3 flex-shrink-0"
            >
              <el-icon size="14" class="text-white"><Share /></el-icon>
            </div>
            <span class="font-medium">{{ row.name }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
      <el-table-column prop="algorithm" label="算法类型" width="120" sortable />
      <el-table-column prop="complexity" label="复杂度" width="100" sortable>
        <template #default="{ row }">
          <el-tag
            :type="
              row.complexity === '简单'
                ? 'success'
                : row.complexity === '中等'
                ? 'warning'
                : 'danger'
            "
            effect="light"
          >
            {{ row.complexity }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100" sortable>
        <template #default="{ row }">
          <el-tag
            :type="row.status === 'active' ? 'success' : 'info'"
            effect="light"
          >
            <div class="flex items-center">
              <div
                :class="[
                  'w-2 h-2 rounded-full mr-2',
                  row.status === 'active'
                    ? 'bg-green-500 animate-pulse'
                    : 'bg-gray-400'
                ]"
              />
              {{ row.status === "active" ? "活跃" : "未激活" }}
            </div>
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="executionTime" label="执行时间" width="120" sortable />
      <el-table-column prop="instanceCount" label="实例数量" width="120" sortable />
      <el-table-column prop="createTime" label="创建时间" width="160" sortable />
      <el-table-column label="操作" width="220" fixed="right">
        <template #default="{ row }">
          <el-button
            size="small"
            type="primary"
            text
            @click="handleViewInstances(row)"
          >
            详情
          </el-button>
          <el-button
            size="small"
            type="success"
            text
            @click="handleViewConfig(row)"
          >
            查看配置
          </el-button>
          <el-dropdown
            trigger="click"
            @command="(command: string) => handleCommand(command, row)"
          >
            <el-button size="small" type="primary" text class="ml-2">
              更多
              <el-icon size="12" class="ml-1"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="edit">
                  <el-icon size="14" class="mr-2 text-blue-500">
                    <Edit />
                  </el-icon>
                  编辑
                </el-dropdown-item>
                <el-dropdown-item command="execute">
                  <el-icon size="14" class="mr-2 text-green-500">
                    <CaretRight />
                  </el-icon>
                  执行
                </el-dropdown-item>
                <el-dropdown-item command="optimize">
                  <el-icon size="14" class="mr-2 text-purple-500">
                    <Setting />
                  </el-icon>
                  优化
                </el-dropdown-item>
                <el-dropdown-item command="copy">
                  <el-icon size="14" class="mr-2 text-orange-500">
                    <CopyDocument />
                  </el-icon>
                  复制
                </el-dropdown-item>
                <el-dropdown-item command="delete" class="text-red-500">
                  <el-icon size="14" class="mr-2"><Delete /></el-icon>
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="flex justify-center mt-6">
      <el-pagination
        v-model:currentPage="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </PageLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import {
  Plus,
  Search,
  Share,
  ArrowDown,
  Edit,
  CaretRight,
  CopyDocument,
  Delete,
  Setting
} from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { PageLayout } from "@/components/PageLayout";

// 路由
const router = useRouter();

// 状态定义
const loading = ref(false);
const searchQuery = ref("");
const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);
const sortField = ref("");
const sortOrder = ref("");

// 模拟数据 - 生成更多数据用于测试
const generateMockData = () => {
  const algorithms = ["协同过滤", "决策树", "神经网络", "随机森林", "支持向量机", "逻辑回归"];
  const complexities = ["简单", "中等", "复杂"];
  const statuses = ["active", "inactive"];
  
  const data = [];
  for (let i = 1; i <= 150; i++) {
    data.push({
      id: `G${i.toString().padStart(3, '0')}`,
      name: `计算图${i}`,
      description: `第${i}个计算图的描述信息`,
      algorithm: algorithms[Math.floor(Math.random() * algorithms.length)],
      complexity: complexities[Math.floor(Math.random() * complexities.length)],
      status: statuses[Math.floor(Math.random() * statuses.length)],
      executionTime: `${(Math.random() * 5 + 0.5).toFixed(1)}s`,
      instanceCount: Math.floor(Math.random() * 20) + 1,
      createTime: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().substring(0, 16).replace('T', ' ')
    });
  }
  return data;
};

const allGraphs = ref(generateMockData());

// 过滤和分页后的数据
const filteredGraphs = computed(() => {
  let data = allGraphs.value;
  
  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    data = data.filter(
      graph =>
        graph.name.toLowerCase().includes(query) ||
        graph.description.toLowerCase().includes(query) ||
        graph.id.toLowerCase().includes(query)
    );
  }
  
  // 排序
  if (sortField.value && sortOrder.value) {
    data = [...data].sort((a, b) => {
      const aVal = a[sortField.value];
      const bVal = b[sortField.value];
      
      if (sortOrder.value === 'ascending') {
        return aVal > bVal ? 1 : -1;
      } else {
        return aVal < bVal ? 1 : -1;
      }
    });
  }
  
  total.value = data.length;
  return data;
});

// 表格显示的数据
const tableData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredGraphs.value.slice(start, end);
});

// 生命周期钩子
onMounted(() => {
  fetchData();
});

// 数据获取方法
const fetchData = async () => {
  loading.value = true;
  try {
    // TODO: 调用API获取计算图数据
    console.log("获取计算图数据");
    await new Promise(resolve => setTimeout(resolve, 1000));
  } catch (error) {
    ElMessage.error("获取数据失败");
    console.error(error);
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1;
};

// 排序处理
const handleSortChange = ({ prop, order }) => {
  sortField.value = prop;
  sortOrder.value = order;
};

// 分页处理
const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize;
  currentPage.value = 1;
};

const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage;
};

// 显示创建对话框
const showCreateDialog = () => {
  ElMessage.info("新建计算图功能开发中...");
};

// 查看图实例列表
const handleViewInstances = (graph: any) => {
  router.push({
    name: "ComputationGraphInstances",
    params: { graphId: graph.id }
  });
};

// 查看配置
const handleViewConfig = (graph: any) => {
  router.push({
    path: "/stream-graph-config",
    query: {
      graphId: graph.id,
      graphName: graph.name
    }
  });
};

// 处理操作命令
const handleCommand = (command: string, graph: any) => {
  switch (command) {
    case "edit":
      ElMessage.info(`编辑计算图 ${graph.name}`);
      break;
    case "execute":
      ElMessage.success(`执行计算图 ${graph.name}`);
      break;
    case "optimize":
      ElMessage.info(`优化计算图 ${graph.name}`);
      break;
    case "copy":
      ElMessage.success(`复制计算图 ${graph.name}`);
      break;
    case "delete":
      ElMessage.success(`删除计算图 ${graph.name}`);
      break;
  }
};
</script>
