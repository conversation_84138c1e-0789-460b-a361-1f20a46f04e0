<template>
  <div
    class="stream-plugin-management-container min-h-screen bg-gray-50 dark:bg-gray-900 p-6"
  >
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex items-center mb-4">
        <div class="w-1 h-8 bg-primary rounded-full mr-4" />
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            图元库管理
          </h1>
          <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
            管理图元库的安装、配置、卸载等操作
          </p>
        </div>
      </div>

      <!-- 工具栏 -->
      <div
        class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
      >
        <div
          class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"
        >
          <div class="flex flex-1 items-center space-x-3 max-w-2xl">
            <el-input
              v-model="searchQuery"
              placeholder="搜索图元库名称或描述..."
              clearable
              :prefix-icon="Search"
              style="max-width: 400px; flex: 1"
            />
            <el-select
              v-model="statusFilter"
              placeholder="选择状态"
              style="width: 120px"
            >
              <el-option label="全部" value="all" />
              <el-option label="已启用" value="enabled" />
              <el-option label="已禁用" value="disabled" />
            </el-select>
          </div>
          <div class="flex items-center space-x-3">
            <span class="text-sm text-gray-600 dark:text-gray-400">
              共 {{ filteredLibraries.length }} 个图元库
            </span>
            <el-button
              type="primary"
              @click="showUploadDialog"
              :loading="uploading"
            >
              <el-icon size="16" class="mr-2"><Plus /></el-icon>
              添加图元库
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="flex justify-center items-center h-96">
      <div class="text-center">
        <el-icon size="32" class="text-blue-500 animate-spin mb-4">
          <Loading />
        </el-icon>
        <p class="text-gray-500 dark:text-gray-400">正在加载图元库列表...</p>
      </div>
    </div>

    <!-- 空状态 -->
    <div
      v-else-if="filteredLibraries.length === 0"
      class="flex justify-center items-center h-96"
    >
      <div class="text-center">
        <div
          class="w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4"
        >
          <el-icon size="24" class="text-gray-400"><Box /></el-icon>
        </div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
          {{ searchQuery ? "未找到相关图元库" : "暂无图元库" }}
        </h3>
        <p class="text-gray-500 dark:text-gray-400 mb-6">
          {{
            searchQuery ? "请尝试其他搜索关键词" : "开始添加您的第一个图元库"
          }}
        </p>
        <el-button v-if="!searchQuery" type="primary" @click="showUploadDialog">
          <el-icon size="16" class="mr-2"><Plus /></el-icon>
          添加图元库
        </el-button>
      </div>
    </div>

    <!-- 图元库列表 -->
    <div
      v-else
      class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-6"
    >
      <div
        v-for="library in filteredLibraries"
        :key="library.libraryId"
        :class="[
          'group bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-lg border border-gray-200 dark:border-gray-700 transition-all duration-300 hover:-translate-y-1 overflow-hidden',
          {
            'opacity-60': !library.enable
          }
        ]"
      >
        <!-- 卡片头部 -->
        <div class="p-6">
          <div class="flex items-start justify-between mb-4">
            <div class="flex items-center flex-1 min-w-0">
              <div
                class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center mr-3 flex-shrink-0"
              >
                <el-icon size="20" class="text-white"><Box /></el-icon>
              </div>
              <div class="min-w-0 flex-1">
                <h3
                  class="text-lg font-semibold text-gray-900 dark:text-white truncate"
                >
                  {{ library.libraryName }}
                </h3>
                <p class="text-sm text-gray-500 dark:text-gray-400 truncate">
                  {{ library.libraryProvider || library.libraryId }}
                </p>
              </div>
            </div>
          </div>

          <!-- 图元库信息 -->
          <div class="space-y-3 text-sm">
            <div class="flex justify-between items-center">
              <span class="text-gray-500 dark:text-gray-400">状态</span>
              <div class="flex items-center">
                <el-icon
                  :size="14"
                  :class="
                    library.enable
                      ? 'text-green-500 mr-1'
                      : 'text-gray-400 mr-1'
                  "
                >
                  <CircleCheck v-if="library.enable" />
                  <CircleClose v-else />
                </el-icon>
                <span
                  :class="
                    library.enable
                      ? 'text-green-600 dark:text-green-400 font-medium'
                      : 'text-gray-500 dark:text-gray-400'
                  "
                >
                  {{ library.enable ? "已启用" : "已禁用" }}
                </span>
              </div>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-500 dark:text-gray-400">版本</span>
              <span class="text-gray-900 dark:text-white font-medium">
                {{ library.libraryVersion }}
              </span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-500 dark:text-gray-400">节点数</span>
              <span class="text-gray-900 dark:text-white">
                {{ library.shapes?.length || 0 }} 个
              </span>
            </div>
            <div v-if="library.libraryProvider" class="flex justify-between">
              <span class="text-gray-500 dark:text-gray-400">提供者</span>
              <span class="text-gray-900 dark:text-white">
                {{ library.libraryProvider }}
              </span>
            </div>
            <div v-if="library.buildTime" class="flex justify-between">
              <span class="text-gray-500 dark:text-gray-400">构建时间</span>
              <span class="text-gray-900 dark:text-white text-xs">
                {{ formatDate(library.buildTime) }}
              </span>
            </div>
          </div>
        </div>

        <!-- 卡片底部操作区 -->
        <div
          class="px-6 py-4 bg-gray-50 dark:bg-gray-900/50 border-t border-gray-100 dark:border-gray-700"
        >
          <div class="flex justify-between items-center">
            <el-button
              size="small"
              type="primary"
              text
              class="text-blue-500 hover:text-blue-600"
              @click="handleViewDetail(library)"
            >
              查看详情
            </el-button>
            <div class="flex items-center space-x-2">
              <!-- 启用/禁用切换 -->
              <el-button
                size="small"
                :type="library.enable ? 'warning' : 'success'"
                plain
                :loading="library.libraryId === switchingId"
                @click="handleToggleStatus(library)"
              >
                <el-icon size="14" class="mr-1"><Switch /></el-icon>
                {{ library.enable ? "禁用" : "启用" }}
              </el-button>
              <!-- 卸载按钮 -->
              <el-button
                size="small"
                type="danger"
                plain
                :loading="library.libraryId === uninstallingId"
                @click="handleUninstall(library)"
              >
                <el-icon size="14" class="mr-1"><Delete /></el-icon>
                卸载
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传图元库对话框 -->
    <el-dialog
      v-model="uploadDialogVisible"
      title="上传图元库"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="mb-4">
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
          请选择要上传的图元库文件，仅支持 .jar 格式的文件。
        </p>
        <el-upload
          class="upload-demo"
          drag
          action="#"
          :auto-upload="false"
          :on-change="handleFileChange"
          :show-file-list="false"
          accept=".jar"
        >
          <div class="py-12">
            <el-icon size="32" class="text-gray-400 mb-4">
              <UploadFilled />
            </el-icon>
            <div class="text-gray-600 dark:text-gray-400">
              拖拽文件到此处或 <span class="text-blue-500">点击上传</span>
            </div>
            <div class="text-xs text-gray-400 mt-2">仅支持 .jar 文件</div>
          </div>
        </el-upload>
        <div
          v-if="uploadFile"
          class="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg"
        >
          <div class="flex items-center text-sm">
            <el-icon size="16" class="text-blue-500 mr-2"><Document /></el-icon>
            <span class="text-blue-700 dark:text-blue-300">
              {{ uploadFile.name }}
            </span>
            <span class="text-gray-500 ml-2">
              ({{ (uploadFile.size / 1024 / 1024).toFixed(2) }} MB)
            </span>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end gap-3">
          <el-button @click="uploadDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="uploading"
            :disabled="!uploadFile"
            @click="submitUpload"
          >
            {{ uploading ? "上传中..." : "确认上传" }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import {
  Plus,
  Search,
  Loading,
  Box,
  Delete,
  CircleCheck,
  CircleClose,
  Switch,
  UploadFilled,
  Document
} from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  getStreamLibraryList,
  uploadStreamLibrary,
  unloadStreamLibrary,
  enableStreamLibrary,
  disableStreamLibrary,
  type StreamLibraryInfo
} from "@/api/stream-library";

// 路由
const router = useRouter();

// 状态定义
const loading = ref(false);
const uploading = ref(false);
const searchQuery = ref("");
const statusFilter = ref<string>("all");
const libraries = ref<StreamLibraryInfo[]>([]);
const uninstallingId = ref<string>("");
const switchingId = ref<string>("");
const uploadDialogVisible = ref(false);
const uploadFile = ref<File | null>(null);

// 过滤后的图元库列表
const filteredLibraries = computed(() => {
  let result = libraries.value;

  // 按状态过滤
  if (statusFilter.value !== "all") {
    const enabled = statusFilter.value === "enabled";
    result = result.filter(library => library.enable === enabled);
  }

  // 按搜索关键词过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(
      library =>
        library.libraryName.toLowerCase().includes(query) ||
        (library.libraryProvider &&
          library.libraryProvider.toLowerCase().includes(query)) ||
        library.libraryId.toLowerCase().includes(query)
    );
  }

  return result;
});

// 生命周期钩子
onMounted(() => {
  fetchLibraryList();
});

// 获取图元库列表
const fetchLibraryList = async () => {
  loading.value = true;
  try {
    const res = await getStreamLibraryList();
    if (res.state) {
      libraries.value = res.data;
    } else {
      ElMessage.error(res.err_msg || "获取图元库列表失败");
    }
  } catch (error) {
    ElMessage.error("请求异常");
    console.error(error);
  } finally {
    loading.value = false;
  }
};

// 显示上传对话框
const showUploadDialog = () => {
  uploadDialogVisible.value = true;
  uploadFile.value = null;
};

// 处理文件选择
const handleFileChange = (file: any) => {
  if (!file?.raw) return;

  // 验证文件类型
  const fileName = file.name.toLowerCase();
  if (!fileName.endsWith(".jar")) {
    ElMessage.error("只支持上传 .jar 格式的图元库文件");
    return;
  }

  // 验证文件大小 (限制100MB)
  const maxSize = 100 * 1024 * 1024;
  if (file.size > maxSize) {
    ElMessage.error("文件大小不能超过 100MB");
    return;
  }

  uploadFile.value = file.raw;
};

// 提交上传
const submitUpload = async () => {
  if (!uploadFile.value) {
    ElMessage.warning("请选择要上传的图元库文件");
    return;
  }

  uploading.value = true;
  try {
    const res = await uploadStreamLibrary(uploadFile.value);
    if (res.state) {
      ElMessage.success("图元库上传成功");
      uploadDialogVisible.value = false;
      await fetchLibraryList(); // 刷新列表
    } else {
      ElMessage.error(res.err_msg || "图元库上传失败");
    }
  } catch (error) {
    ElMessage.error("上传过程中发生错误");
    console.error(error);
  } finally {
    uploading.value = false;
  }
};

// 查看详情
const handleViewDetail = (library: StreamLibraryInfo) => {
  router.push(`/stream-computation/plugin-detail/${library.libraryId}`);
};

// 切换图元库状态
const handleToggleStatus = async (library: StreamLibraryInfo) => {
  const isCurrentlyEnabled = library.enable;
  const action = isCurrentlyEnabled ? "禁用" : "启用";
  const actionDescription = isCurrentlyEnabled
    ? "禁用后该图元库的所有节点将不可用于新的流程配置"
    : "启用后该图元库的节点将可用于流程配置";

  try {
    await ElMessageBox.confirm(
      `确定要${action}图元库 "${library.libraryName}" 吗？${actionDescription}。`,
      `确认${action}`,
      {
        confirmButtonText: `确定${action}`,
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    switchingId.value = library.libraryId;
    const res = isCurrentlyEnabled
      ? await disableStreamLibrary(library.libraryId)
      : await enableStreamLibrary(library.libraryId);

    if (res.state) {
      ElMessage.success(`图元库${action}成功`);
      await fetchLibraryList(); // 刷新列表
    } else {
      ElMessage.error(res.err_msg || `${action}图元库失败`);
    }
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("请求异常");
      console.error(error);
    }
  } finally {
    switchingId.value = "";
  }
};

// 卸载图元库
const handleUninstall = async (library: StreamLibraryInfo) => {
  try {
    await ElMessageBox.confirm(
      `确定要卸载图元库 "${library.libraryName}" 吗？卸载后该图元库的所有节点将不可用。`,
      "确认卸载",
      {
        confirmButtonText: "确定卸载",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    uninstallingId.value = library.libraryId;
    const res = await unloadStreamLibrary(library.libraryId);
    if (res.state) {
      ElMessage.success("图元库卸载成功");
      await fetchLibraryList(); // 刷新列表
    } else {
      ElMessage.error(res.err_msg || "卸载图元库失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("请求异常");
      console.error(error);
    }
  } finally {
    uninstallingId.value = "";
  }
};

// 格式化日期
const formatDate = (dateStr?: string) => {
  if (!dateStr) return "--";
  try {
    const date = new Date(dateStr);
    return date.toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit"
    });
  } catch {
    return dateStr;
  }
};
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
