# 图元库管理系统需求文档

## 1. 项目概述

### 1.1 项目背景

图元库管理系统是流计算平台的核心组件之一，负责管理可用于流程配置的插件包。每个插件包含多个shape节点，这些节点可以在流程配置页面中使用。插件的加载和卸载会直接影响流程配置页面可用的shape节点数量。

### 1.2 项目目标

- 提供简洁易用的插件管理界面
- 支持插件的添加和卸载操作
- 提供插件详情查看功能，展示插件内置的shape节点
- 实现节点的详细信息展示，包括基础信息和悬浮详情

## 2. 功能需求

### 2.1 插件管理主页面

#### 2.1.1 页面布局

- **页面标题**: "图元库管理"
- **页面描述**: "管理流计算插件的安装、配置和状态"
- **搜索功能**: 支持按插件名称和描述进行搜索
- **统计信息**: 显示当前插件总数
- **操作按钮**: 添加插件按钮

#### 2.1.2 插件卡片展示

每个插件以卡片形式展示，包含以下信息：

- **插件图标**: 统一的插件图标
- **插件名称**: 插件的显示名称
- **插件描述**: 插件的简要说明
- **版本号**: 插件的当前版本
- **操作按钮**:
  - 查看详情（跳转到插件详情页）
  - 卸载插件

#### 2.1.3 插件状态说明

- 插件本身**无运行状态**，因为插件只是节点的容器
- 移除"运行中/已停止"的状态显示
- 移除"类型"字段显示

#### 2.1.4 操作功能

- **添加插件**: 点击添加按钮，支持上传插件包
- **卸载插件**: 从系统中移除插件及其所有节点
- **查看详情**: 跳转到插件详情页面

### 2.2 插件详情页面

#### 2.2.2 页面布局

- **插件基本信息区域**:
  - 插件名称
  - 插件描述
  - 版本号
  - 作者信息
  - 创建时间
- **节点列表区域**: 展示插件包含的所有shape节点

#### 2.2.3 节点列表展示

采用网格布局展示节点，每个节点卡片包含：

- **节点图标**: 根据节点类型显示相应图标
- **节点名称**: 节点的显示名称
- **节点描述**: 节点的简要说明
- **鼠标悬浮详情**: 使用tooltip展示详细信息

### 2.3 节点详情tooltip

#### 2.3.1 基础信息展示

- **节点标题**: 节点名称
- **节点描述**: 详细描述信息
- **作者信息**: 节点开发者
- **版本号**: 节点版本
- **标签**: 节点分类标签

#### 2.3.2 端口信息展示

- **输入端口**:
  - 端口名称
  - 支持的数据类型（显示简化的类型名）
  - 端口数量
- **输出端口**:
  - 端口名称
  - 支持的数据类型（显示简化的类型名）
  - 端口数量

#### 2.3.3 文档预览

- **文档内容**: 支持Markdown格式的文档展示
- **展开/收起**: 长文档支持展开收起功能
- **格式化显示**: 支持代码块、列表、表格等格式

## 3. 页面设计规范

### 3.1 视觉风格

- 使用项目统一的设计语言
- 采用卡片式布局，提升视觉层次
- 使用适当的阴影和圆角效果
- 支持明暗主题切换

### 3.2 交互设计

- **悬浮效果**: 卡片悬浮时有轻微上升效果
- **加载状态**: 数据加载时显示loading动画
- **空状态**: 无插件时显示友好的空状态提示
- **响应式布局**: 支持不同屏幕尺寸的适配

### 4.3 数据结构

#### 4.3.1 插件数据结构

```typescript
interface Plugin {
  id: string; // 插件唯一标识
  name: string; // 插件名称
  description: string; // 插件描述
  version: string; // 版本号
  author?: string; // 作者
  createTime?: string; // 创建时间
  nodes: ShapeNode[]; // 包含的节点列表
}
```

#### 4.3.2 节点数据结构

```typescript
interface ShapeNode {
  id: string; // 节点唯一标识
  name: string; // 节点名称
  description: string; // 节点描述
  author?: string; // 作者
  version?: string; // 版本
  tags?: string[]; // 标签
  document?: string; // 文档内容（Markdown格式）
  inputs: PortConfig[]; // 输入端口配置
  outputs: PortConfig[]; // 输出端口配置
  icon?: any; // 节点图标
  color?: string; // 节点颜色
  originalData?: any; // 原始数据
}

interface PortConfig {
  label: string; // 端口标签
  letId?: number; // 端口ID
  letName?: string; // 端口名称
  dataType?: string[]; // 支持的数据类型
  maxFan?: number; // 最大连接数
  dynamic?: boolean; // 是否动态端口
}
```

### 4.4 API接口设计

#### 4.4.1 插件管理接口

- `GET /api/plugins` - 获取插件列表
- `POST /api/plugins` - 添加新插件
- `DELETE /api/plugins/:id` - 卸载插件
- `GET /api/plugins/:id` - 获取插件详情

#### 4.4.2 节点管理接口

- `GET /api/plugins/:id/nodes` - 获取插件的节点列表
- `GET /api/nodes/:id` - 获取节点详细信息

，shape假数据参考

# ==================================================

# Comment Component

# ==================================================

streams.shapes.comment.name=注释
streams.shapes.comment.alias=文本注释
streams.shapes.comment.tooltip=注释节点，在图上增加注释，支持markdown。
streams.shapes.comment.groups=基础
streams.shapes.comment.tags=注释,注解

# ==================================================

# Switch Component

# ==================================================

streams.shapes.data-switch.name=数据条件分支
streams.shapes.data-switch.alias=条件分支
streams.shapes.data-switch.tooltip=通过条件控制数据流走向
streams.shapes.data-switch.groups=基础
streams.shapes.data-switch.tags=Switch,分支,判断,条件
streams.shapes.data-switch.inlet1.name=In
streams.shapes.data-switch.outlet1.name=Out-{index}

# ==================================================

# Timer Component

# ==================================================

streams.shapes.fixed-timer.name=固定间隔定时器
streams.shapes.fixed-timer.alias=定时器
streams.shapes.fixed-timer.tooltip=固定间隔的定时器组件
streams.shapes.fixed-timer.groups=基础
streams.shapes.fixed-timer.tags=定时器,Time,Timer
streams.shapes.fixed-timer.outlet1.name=滴答事件
streams.shapes.fixed-timer.outlet2.name=启动事件
streams.shapes.fixed-timer.outlet3.name=停止事件

# ==================================================

# Tracer Component

# ==================================================

streams.shapes.data-tracer.name=数据跟踪器
streams.shapes.data-tracer.alias=跟踪器
streams.shapes.data-tracer.tooltip=可以实时查看数据流数据
streams.shapes.data-tracer.groups=基础
streams.shapes.data-tracer.tags=调试,跟踪
streams.shapes.data-tracer.inlet1.name=输入

# ==================================================

# DataMapper Component

# ==================================================

streams.shapes.data-mapper.name=数据映射
streams.shapes.data-mapper.alias=映射
streams.shapes.data-mapper.tooltip=数据对象格式根据设置条件映射转换
streams.shapes.data-mapper.groups=基础
streams.shapes.data-mapper.tags=Mapper,映射,转换
streams.shapes.data-mapper.inlet1.name=输入
streams.shapes.data-mapper.outlet1.name=输出

# ==================================================

# ChangeFilter Component

# ==================================================

streams.shapes.data-filter.name=变更过滤器
streams.shapes.data-filter.alias=变更
streams.shapes.data-filter.tooltip=用于过滤未变更的数据
streams.shapes.data-filter.groups=基础
streams.shapes.data-filter.tags=变更,过滤
streams.shapes.data-filter.inlet1.name=数据输入
streams.shapes.data-filter.outlet1.name=数据输出

# ==================================================

# DataProcess Component

# ==================================================

streams.shapes.data-process.name=数据处理
streams.shapes.data-process.alias=处理
streams.shapes.data-process.tooltip=测试的
streams.shapes.data-process.groups=基础
streams.shapes.data-process.tags=处理,转换
streams.shapes.data-process.inlet1.name=数据输入
streams.shapes.data-process.outlet1.name=数据输出

# ==================================================

# Delay Component

# ==================================================

streams.shapes.delay.name=延迟处理
streams.shapes.delay.alias=延迟
streams.shapes.delay.tooltip=延迟数据流中数据的处理
streams.shapes.delay.groups=基础
streams.shapes.delay.tags=处理,转换
streams.shapes.delay.inlet1.name=数据输入
streams.shapes.delay.outlet1.name=数据输出

# ==================================================

# TCP Server Component

# ==================================================

streams.shapes.tcp-server.name=TCP服务器
streams.shapes.tcp-server.alias=TCP服务器
streams.shapes.tcp-server.tooltip=提供TCP服务器功能，监听指定端口并接收TCP连接
streams.shapes.tcp-server.groups=基础
streams.shapes.tcp-server.tags=网络,TCP,服务器
streams.shapes.tcp-server.outlet1.name=数据输出

# ==================================================

# TCP Server Component

# ==================================================

streams.shapes.property-inject.name=属性注入
streams.shapes.property-inject.alias=注入
streams.shapes.property-inject.tooltip=提供属性字段的动态注入功能
streams.shapes.property-inject.groups=基础
streams.shapes.property-inject.tags=属性,字段,注入
streams.shapes.property-inject.inlet1.name=数据输入
streams.shapes.property-inject.outlet1.name=数据输出
