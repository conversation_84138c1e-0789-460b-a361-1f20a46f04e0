<template>
  <el-dialog
    :model-value="visible"
    :title="isEdit ? '编辑部门' : '新增部门'"
    width="600px"
    @update:model-value="handleClose"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      size="default"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="部门名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入部门名称"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="部门编码" prop="code">
            <el-input
              v-model="formData.code"
              placeholder="请输入部门编码"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="上级部门" prop="parentId">
            <el-tree-select
              v-model="formData.parentId"
              :data="departmentTreeData"
              :props="{ label: 'name', value: 'id' }"
              placeholder="请选择上级部门"
              clearable
              check-strictly
              :render-after-expand="false"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="负责人" prop="leader">
            <el-input
              v-model="formData.leader"
              placeholder="请输入负责人"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系电话" prop="phone">
            <el-input
              v-model="formData.phone"
              placeholder="请输入联系电话"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input
              v-model="formData.email"
              placeholder="请输入邮箱"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="启用状态" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio :value="1">启用</el-radio>
              <el-radio :value="0">停用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sort">
            <el-input-number
              v-model="formData.sort"
              :min="0"
              :max="999"
              placeholder="排序"
              controls-position="right"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入部门备注"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="submitLoading"
          @click="handleSubmit"
        >
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, onMounted } from "vue";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import {
  getAllDepartments,
  createDepartment,
  updateDepartment,
  checkCodeExists,
  type DepartmentInfo,
  type CreateDepartmentRequest,
  type UpdateDepartmentRequest
} from "@/api/department";

// 表单数据类型
interface FormData {
  id?: number;
  name: string;
  code: string;
  parentId?: number;
  leader?: string;
  phone?: string;
  email?: string;
  status: number;
  sort: number;
  remark?: string;
}

// Props定义
interface Props {
  visible: boolean;
  departmentData?: DepartmentInfo | null;
  parentDepartment?: DepartmentInfo | null;
  isEdit: boolean;
}

// Emits定义
interface Emits {
  (e: "update:visible", value: boolean): void;
  (e: "refresh"): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  departmentData: null,
  parentDepartment: null,
  isEdit: false
});

const emit = defineEmits<Emits>();

// 响应式数据
const formRef = ref<FormInstance>();
const submitLoading = ref(false);

// 表单数据
const formData = reactive<FormData>({
  name: "",
  code: "",
  parentId: undefined,
  leader: "",
  phone: "",
  email: "",
  status: 1,
  sort: 0,
  remark: ""
});

// 部门树数据
const departmentTreeData = ref<DepartmentInfo[]>([]);

// 加载部门树数据
const loadDepartmentTree = async () => {
  try {
    const response = await getAllDepartments();
    if (response.state) {
      departmentTreeData.value = response.data || [];
    } else {
      console.error("获取部门列表失败:", response.err_msg);
    }
  } catch (error) {
    console.error("加载部门树数据失败:", error);
  }
};

// 表单验证规则
const formRules = computed<FormRules>(() => ({
  name: [
    { required: true, message: "请输入部门名称", trigger: "blur" },
    { min: 2, max: 50, message: "部门名称长度在 2 到 50 个字符", trigger: "blur" }
  ],
  code: [
    { required: true, message: "请输入部门编码", trigger: "blur" },
    { pattern: /^[A-Z0-9_]+$/, message: "部门编码只能包含大写字母、数字和下划线", trigger: "blur" }
  ],
  leader: [
    { max: 20, message: "负责人姓名不能超过20个字符", trigger: "blur" }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
  ],
  email: [
    { type: "email", message: "请输入正确的邮箱地址", trigger: "blur" }
  ],
  sort: [
    { required: true, message: "请输入排序", trigger: "blur" }
  ]
}));

// 监听对话框显示状态
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      resetForm();
      if (props.isEdit && props.departmentData) {
        // 编辑模式，回填数据
        Object.assign(formData, props.departmentData);
      } else if (props.parentDepartment) {
        // 新增子部门模式，设置父部门
        formData.parentId = props.parentDepartment.id;
      }
    }
  }
);

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: "",
    code: "",
    parentId: undefined,
    leader: "",
    phone: "",
    email: "",
    status: 1,
    sort: 0,
    remark: ""
  });
  formRef.value?.clearValidate();
};

// 关闭对话框
const handleClose = () => {
  emit("update:visible", false);
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    
    submitLoading.value = true;
    
    let response;
    if (props.isEdit) {
      // 编辑部门
      const updateData: UpdateDepartmentRequest = {
        id: formData.id!,
        name: formData.name,
        code: formData.code,
        parentId: formData.parentId,
        leader: formData.leader,
        phone: formData.phone,
        email: formData.email,
        status: formData.status,
        sort: formData.sort,
        remark: formData.remark
      };
      response = await updateDepartment(updateData);
    } else {
      // 新增部门
      const createData: CreateDepartmentRequest = {
        name: formData.name,
        code: formData.code,
        parentId: formData.parentId,
        leader: formData.leader,
        phone: formData.phone,
        email: formData.email,
        status: formData.status,
        sort: formData.sort,
        remark: formData.remark
      };
      response = await createDepartment(createData);
    }
    
    if (response.state) {
      ElMessage.success(props.isEdit ? "编辑部门成功" : "新增部门成功");
      emit("refresh");
      handleClose();
    } else {
      ElMessage.error(response.err_msg || "操作失败");
    }
  } catch (error) {
    console.error("表单操作失败:", error);
    ElMessage.error("操作失败");
  } finally {
    submitLoading.value = false;
  }
};

// 组件挂载时加载部门树数据
onMounted(() => {
  loadDepartmentTree();
});
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>