<template>
  <div class="service-management-container">
    <div class="header">
      <h2>{{ $t("middleware.serviceManagement") }}</h2>
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        {{ $t("middleware.button.addService") }}
      </el-button>
    </div>

    <!-- 搜索筛选区域 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" inline>
        <el-form-item :label="$t('middleware.table.name')">
          <el-input
            v-model="searchForm.name"
            :placeholder="$t('middleware.search.name')"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item :label="$t('middleware.table.serviceType')">
          <el-select
            v-model="searchForm.serviceId"
            :placeholder="$t('middleware.search.type')"
            clearable
            style="width: 200px"
          >
            <el-option :label="$t('middleware.search.all')" value="" />
            <el-option
              v-for="type in serviceTypes"
              :key="type.id"
              :label="type.name"
              :value="type.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('middleware.table.status')">
          <el-select
            v-model="searchForm.status"
            :placeholder="$t('middleware.search.status')"
            clearable
            style="width: 150px"
          >
            <el-option :label="$t('middleware.search.all')" value="" />
            <el-option :label="$t('middleware.status.enabled')" value="ENABLED" />
            <el-option :label="$t('middleware.status.disabled')" value="DISABLED" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            {{ $t("middleware.button.search") }}
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            {{ $t("middleware.button.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <el-table
        :data="tableData"
        v-loading="loading"
        border
        stripe
        style="width: 100%"
      >
        <el-table-column
          prop="id"
          :label="$t('middleware.table.id')"
          min-width="100"
        >
          <template #default="{ row }">
            <el-button
              link
              type="primary"
              @click="handleDetail(row)"
            >
              {{ row.id }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="name" :label="$t('middleware.table.name')" min-width="150" />
        <el-table-column prop="serviceId" :label="$t('middleware.table.serviceType')" width="150">
          <template #default="{ row }">
            {{ getServiceTypeName(row.serviceId) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" :label="$t('middleware.table.status')" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === 'ENABLED' ? 'success' : 'info'">
              {{ row.status === 'ENABLED' ? $t('middleware.status.enabled') : $t('middleware.status.disabled') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="$t('middleware.table.instantiated')" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="row.instantiated ? 'success' : 'warning'">
              {{ row.instantiated ? $t('middleware.status.instantiated') : $t('middleware.status.notInstantiated') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="resourceConfigurationId" :label="$t('middleware.table.resourceConfig')" width="180">
          <template #default="{ row }">
            {{ getResourceConfigName(row.resourceConfigurationId) }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" :label="$t('middleware.table.createTime')" width="160" />
        <el-table-column prop="createdBy" :label="$t('middleware.table.createdBy')" width="100" />
        <el-table-column
          :label="$t('middleware.table.operation')"
          width="200"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button
              size="small"
              type="primary"
              @click="handleEdit(row)"
            >
              {{ $t("middleware.button.edit") }}
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(row)"
            >
              {{ $t("middleware.button.delete") }}
            </el-button>
            <el-button
              size="small"
              :type="row.status === 'ENABLED' ? 'warning' : 'success'"
              @click="handleToggleStatus(row)"
            >
              {{ row.status === "ENABLED" ? $t("middleware.button.disable") : $t("middleware.button.enable") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus, Search, Refresh } from "@element-plus/icons-vue";
import {
  getServiceConfigPage,
  getServiceTypeList,
  getResourceConfigList,
  deleteServiceConfig,
  enableServiceConfig,
  disableServiceConfig,
  instantiateService,
  destroyService,
  isServiceInstantiated,
  batchCheckServiceInstantiated,
  type ServiceConfiguration,
  type ServiceType,
  type ResourceConfiguration
} from "@/api/system/middleware";

defineOptions({
  name: "ServiceManagement"
});

const { t } = useI18n();
const router = useRouter();

// 搜索表单
const searchForm = reactive({
  name: "",
  serviceId: "",
  status: ""
});

// 分页参数
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
});

// 表格数据
const tableData = ref<(ServiceConfiguration & { instantiated?: boolean })[]>([]);
const loading = ref(false);
const serviceTypes = ref<ServiceType[]>([]);
const resourceConfigs = ref<ResourceConfiguration[]>([]);

// 获取服务类型名称
const getServiceTypeName = (serviceId: string) => {
  const type = serviceTypes.value.find(t => t.id === serviceId);
  return type ? type.name : serviceId;
};

// 获取资源配置名称
const getResourceConfigName = (resourceConfigId: string) => {
  if (!resourceConfigId) return '-';
  const config = resourceConfigs.value.find(c => c.id === resourceConfigId);
  return config ? config.name : resourceConfigId;
};

// 获取服务配置列表
const fetchServiceConfigs = async () => {
  loading.value = true;
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      name: searchForm.name || undefined,
      serviceId: searchForm.serviceId || undefined,
      status: searchForm.status || undefined
    };

    const res = await getServiceConfigPage(params);
    console.log("服务配置API响应:", res); // 调试信息

    if (res.state) { // 使用 state 而不是 success
      // 处理不同的响应结构
      if (res.data && Array.isArray(res.data.records)) {
        tableData.value = res.data.records;
        pagination.total = res.data.total || 0;
      } else if (Array.isArray(res.data)) {
        tableData.value = res.data;
        pagination.total = res.data.length;
      } else {
        console.warn("未知的服务配置响应结构:", res.data);
        tableData.value = [];
        pagination.total = 0;
      }

      // 检查每个服务的实例化状态
      if (tableData.value.length > 0) {
        try {
          const instantiatedRes = await batchCheckServiceInstantiated(
            tableData.value.map(item => item.id)
          );
          if (instantiatedRes.state) {
            // 更新每个服务的实例化状态
            tableData.value.forEach(item => {
              item.instantiated = instantiatedRes.data[item.id] || false;
            });
          }
        } catch (error) {
          console.warn("批量检查服务实例化状态失败:", error);
          // 如果批量检查失败，将所有服务的实例化状态设为 false
          tableData.value.forEach(item => {
            item.instantiated = false;
          });
        }
      }
    } else {
      console.error("获取服务配置失败:", res);
      tableData.value = [];
      pagination.total = 0;
    }
  } catch (error) {
    console.error("获取服务配置列表失败:", error);
    ElMessage.error(t("middleware.message.operationFailed"));
  } finally {
    loading.value = false;
  }
};

// 获取服务类型列表
const fetchServiceTypes = async () => {
  try {
    const res = await getServiceTypeList();
    if (res.state) { // 使用 state 而不是 success
      serviceTypes.value = res.data;
    }
  } catch (error) {
    console.error("获取服务类型列表失败:", error);
  }
};

// 获取资源配置列表
const fetchResourceConfigs = async () => {
  try {
    const res = await getResourceConfigList();
    if (res.state) { // 使用 state 而不是 success
      resourceConfigs.value = res.data;
    }
  } catch (error) {
    console.error("获取资源配置列表失败:", error);
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchServiceConfigs();
};

// 重置
const handleReset = () => {
  searchForm.name = "";
  searchForm.serviceId = "";
  searchForm.status = "";
  pagination.current = 1;
  fetchServiceConfigs();
};

// 新增
const handleAdd = () => {
  router.push("/system-management/middleware-management/service-management/new");
};

// 编辑
const handleEdit = (row: ServiceConfiguration) => {
  router.push(`/system-management/middleware-management/service-management/${row.id}/edit`);
};

// 详情
const handleDetail = (row: ServiceConfiguration) => {
  router.push(`/system-management/middleware-management/service-management/${row.id}/detail`);
};

// 切换状态
const handleToggleStatus = async (row: ServiceConfiguration) => {
  const isEnable = row.status !== 'ENABLED';
  const action = isEnable ? 'enable' : 'disable';
  const confirmMessage = isEnable ? 'confirmEnable' : 'confirmDisable';

  try {
    await ElMessageBox.confirm(
      t(`middleware.message.${confirmMessage}`),
      t("middleware.button.confirm"),
      { type: "warning" }
    );

    const res = isEnable ? await enableServiceConfig(row.id) : await disableServiceConfig(row.id);
    if (res.state) { // 使用 state 而不是 success
      ElMessage.success(t(`middleware.message.${action}Success`));
      fetchServiceConfigs();
    } else {
      ElMessage.error(t("middleware.message.operationFailed"));
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${action}服务配置失败:`, error);
      ElMessage.error(t("middleware.message.operationFailed"));
    }
  }
};

// 删除
const handleDelete = async (row: ServiceConfiguration) => {
  try {
    await ElMessageBox.confirm(
      t("middleware.message.confirmDelete"),
      t("middleware.button.confirm"),
      { type: "warning" }
    );

    const res = await deleteServiceConfig(row.id);
    if (res.state) { // 使用 state 而不是 success
      ElMessage.success(t("middleware.message.deleteSuccess"));
      fetchServiceConfigs();
    } else {
      ElMessage.error(t("middleware.message.operationFailed"));
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error("删除服务配置失败:", error);
      ElMessage.error(t("middleware.message.operationFailed"));
    }
  }
};

// 实例化
const handleInstantiate = async (row: ServiceConfiguration) => {
  try {
    await ElMessageBox.confirm(
      t("middleware.message.confirmInstantiate"),
      t("middleware.button.confirm"),
      { type: "warning" }
    );

    const res = await instantiateService(row.id);
    if (res.state) { // 使用 state 而不是 success
      ElMessage.success(t("middleware.message.instantiateSuccess"));
      fetchServiceConfigs();
    } else {
      ElMessage.error(t("middleware.message.operationFailed"));
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error("实例化服务失败:", error);
      ElMessage.error(t("middleware.message.operationFailed"));
    }
  }
};

// 销毁
const handleDestroy = async (row: ServiceConfiguration) => {
  try {
    await ElMessageBox.confirm(
      t("middleware.message.confirmDestroy"),
      t("middleware.button.confirm"),
      { type: "warning" }
    );

    const res = await destroyService(row.id);
    if (res.state) { // 使用 state 而不是 success
      ElMessage.success(t("middleware.message.destroySuccess"));
      fetchServiceConfigs();
    } else {
      ElMessage.error(t("middleware.message.operationFailed"));
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error("销毁服务失败:", error);
      ElMessage.error(t("middleware.message.operationFailed"));
    }
  }
};

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size;
  pagination.current = 1;
  fetchServiceConfigs();
};

// 当前页改变
const handleCurrentChange = (current: number) => {
  pagination.current = current;
  fetchServiceConfigs();
};

onMounted(() => {
  fetchServiceTypes();
  fetchResourceConfigs();
  fetchServiceConfigs();
});
</script>

<style scoped>
.service-management-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 500;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
