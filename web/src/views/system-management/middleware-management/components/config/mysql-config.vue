<template>
  <div class="mysql-config">
    <el-form :model="configData" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="$t('middleware.config.host')" required>
            <el-input
              v-model="configData.host"
              placeholder="localhost"
              @input="handleConfigChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('middleware.config.port')" required>
            <el-input-number
              v-model="configData.port"
              :min="1"
              :max="65535"
              placeholder="3306"
              style="width: 100%"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="$t('middleware.config.username')" required>
            <el-input
              v-model="configData.username"
              placeholder="root"
              @input="handleConfigChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('middleware.config.password')" required>
            <el-input
              v-model="configData.password"
              type="password"
              placeholder="请输入密码"
              show-password
              @input="handleConfigChange"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item :label="$t('middleware.config.database')" required>
        <el-input
          v-model="configData.database"
          placeholder="tcs_middleware"
          @input="handleConfigChange"
        />
      </el-form-item>

      <el-form-item label="服务器时区">
        <el-input
          v-model="configData.serverTimezone"
          placeholder="UTC"
          @input="handleConfigChange"
        />
      </el-form-item>

      <el-divider content-position="left">连接池配置</el-divider>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="$t('middleware.config.minConnections')">
            <el-input-number
              v-model="configData.minIdle"
              :min="1"
              :max="100"
              placeholder="5"
              style="width: 100%"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('middleware.config.maxConnections')">
            <el-input-number
              v-model="configData.maxPoolSize"
              :min="1"
              :max="1000"
              placeholder="20"
              style="width: 100%"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="连接超时(ms)">
            <el-input-number
              v-model="configData.connectionTimeout"
              :min="1000"
              :max="300000"
              placeholder="30000"
              style="width: 100%"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="空闲超时(ms)">
            <el-input-number
              v-model="configData.idleTimeout"
              :min="10000"
              :max="600000"
              placeholder="600000"
              style="width: 100%"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="连接最大生命周期(ms)">
        <el-input-number
          v-model="configData.maxLifetime"
          :min="60000"
          :max="7200000"
          placeholder="1800000"
          style="width: 200px"
          @change="handleConfigChange"
        />
        <div class="form-tip">连接在池中的最大生命周期，默认30分钟</div>
      </el-form-item>

      <el-divider content-position="left">高级配置</el-divider>

      <el-form-item label="字符编码">
        <el-select
          v-model="configData.characterEncoding"
          placeholder="请选择字符编码"
          style="width: 200px"
          @change="handleConfigChange"
        >
          <el-option label="UTF-8" value="utf8" />
          <el-option label="UTF-8MB4" value="utf8mb4" />
          <el-option label="GBK" value="gbk" />
        </el-select>
      </el-form-item>

      <el-form-item label="连接测试查询">
        <el-input
          v-model="configData.connectionTestQuery"
          placeholder="SELECT 1"
          @input="handleConfigChange"
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="SSL连接">
            <el-switch
              v-model="configData.useSSL"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="使用Unicode">
            <el-switch
              v-model="configData.useUnicode"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="自动提交">
            <el-switch
              v-model="configData.autoCommit"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="允许公钥检索">
            <el-switch
              v-model="configData.allowPublicKeyRetrieval"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="允许多查询">
            <el-switch
              v-model="configData.allowMultiQueries"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from "vue";
import { useI18n } from "vue-i18n";

defineOptions({
  name: "MySQLConfig"
});

const { t } = useI18n();

// Props
interface Props {
  modelValue?: Record<string, any>;
  defaultConfig?: Record<string, any>;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({}),
  defaultConfig: () => ({})
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: Record<string, any>];
  'test-connection': [];
}>();

// 配置数据 - 与后端default_config的18个属性保持一致
const configData = reactive({
  host: 'localhost',
  port: 3306,
  username: 'root',
  password: '',
  database: 'tcs_middleware',
  connectionTimeout: 30000,
  idleTimeout: 600000,
  minIdle: 5,
  maxPoolSize: 20,
  characterEncoding: 'utf8mb4',
  useSSL: false,
  useUnicode: true,
  autoCommit: true,
  allowPublicKeyRetrieval: true,
  allowMultiQueries: false,
  serverTimezone: 'UTC',
  connectionTestQuery: 'SELECT 1',
  maxLifetime: 1800000 // 30分钟，单位毫秒
});

// 处理配置变化
const handleConfigChange = () => {
  // 直接使用配置数据，确保所有18个字段都包含
  const config = { ...configData };

  emit('update:modelValue', config);
};

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (newValue && Object.keys(newValue).length > 0) {
    Object.assign(configData, newValue);
  }
}, { immediate: true, deep: true });

// 监听默认配置变化
watch(() => props.defaultConfig, (newValue) => {
  if (newValue && Object.keys(newValue).length > 0) {
    // 只在当前配置为空时应用默认配置
    if (!props.modelValue || Object.keys(props.modelValue).length === 0) {
      Object.assign(configData, newValue);
      handleConfigChange();
    }
  }
}, { immediate: true, deep: true });

onMounted(() => {
  // 初始化时触发一次配置变化
  handleConfigChange();
});
</script>

<style scoped>
.mysql-config {
  padding: 20px;
  background-color: #fafafa;
  border-radius: 6px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.4;
}

:deep(.el-divider__text) {
  font-weight: 500;
  color: #303133;
}
</style>
