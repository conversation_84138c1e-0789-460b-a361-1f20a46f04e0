.detail-container {
  padding: 20px;

  .header {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
      color: var(--el-text-color-primary);
    }

    .header-actions {
      display: flex;
      gap: 10px;
    }
  }

  .detail-section {
    margin-bottom: 24px;

    h3 {
      margin: 0 0 16px;
      font-size: 16px;
      font-weight: 500;
      color: var(--el-text-color-primary);
      position: relative;
      padding-left: 12px;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 16px;
        background-color: var(--el-color-primary);
        border-radius: 2px;
      }
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h3 {
        margin: 0;
      }
    }
  }

  .config-card {
    background-color: var(--el-bg-color-page);
    border: 1px solid var(--el-border-color-light);

    .config-json {
      margin: 0;
      padding: 16px;
      font-family: monospace;
      font-size: 14px;
      line-height: 1.5;
      color: var(--el-text-color-regular);
      white-space: pre-wrap;
      word-break: break-all;
    }
  }

  .health-card {
    background-color: var(--el-bg-color-page);
    border: 1px solid var(--el-border-color-light);

    .metrics-section {
      margin-top: 20px;

      h4 {
        margin: 0 0 16px;
        font-size: 14px;
        font-weight: 500;
        color: var(--el-text-color-primary);
      }
    }

    .errors-section {
      margin-top: 20px;

      h4 {
        margin: 0 0 16px;
        font-size: 14px;
        font-weight: 500;
        color: var(--el-text-color-primary);
      }
    }
  }

  .log-view-section {
    .log-output {
      background-color: var(--el-bg-color-page);
      border: 1px solid var(--el-border-color-light);
      border-radius: 4px;
      padding: 16px;
      height: 400px;
      overflow-y: auto;

      .log-entry {
        font-family: monospace;
        font-size: 14px;
        line-height: 1.5;
        margin-bottom: 4px;

        .log-level {
          display: inline-block;
          padding: 2px 6px;
          border-radius: 2px;
          margin-right: 8px;
          font-size: 12px;
          font-weight: 500;

          &.info {
            background-color: var(--el-color-info-light-9);
            color: var(--el-color-info);
          }

          &.warn {
            background-color: var(--el-color-warning-light-9);
            color: var(--el-color-warning);
          }

          &.error {
            background-color: var(--el-color-danger-light-9);
            color: var(--el-color-danger);
          }

          &.debug {
            background-color: var(--el-color-info-light-9);
            color: var(--el-color-info);
          }
        }

        .log-timestamp {
          color: var(--el-text-color-secondary);
          margin-right: 8px;
        }

        .log-message {
          color: var(--el-text-color-regular);
        }
      }

      .no-logs {
        color: var(--el-text-color-secondary);
        text-align: center;
        padding: 20px;
      }
    }
  }
} 