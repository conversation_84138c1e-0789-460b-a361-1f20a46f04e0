<template>
  <div class="resource-management-container">
    <div class="header">
      <h2>{{ $t("middleware.resourceManagement") }}</h2>
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        {{ $t("middleware.button.addResource") }}
      </el-button>
    </div>

    <!-- 搜索筛选区域 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" inline>
        <el-form-item :label="$t('middleware.table.name')">
          <el-input
            v-model="searchForm.name"
            :placeholder="$t('middleware.search.name')"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item :label="$t('middleware.table.resourceType')">
          <el-select
            v-model="searchForm.resourceTypeId"
            :placeholder="$t('middleware.search.type')"
            clearable
            style="width: 200px"
          >
            <el-option :label="$t('middleware.search.all')" value="" />
            <el-option
              v-for="type in resourceTypes"
              :key="type.id"
              :label="type.name"
              :value="type.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            {{ $t("middleware.button.search") }}
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            {{ $t("middleware.button.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <el-table
        :data="tableData"
        v-loading="loading"
        border
        stripe
        style="width: 100%"
      >
        <el-table-column
          prop="id"
          :label="$t('middleware.table.id')"
          min-width="100"
        >
          <template #default="{ row }">
            <el-button
              link
              type="primary"
              @click="handleDetail(row)"
            >
              {{ row.id }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="name" :label="$t('middleware.table.name')" min-width="150" />
        <el-table-column prop="resourceId" :label="$t('middleware.table.resourceType')" width="120">
          <template #default="{ row }">
            {{ getResourceTypeName(row.resourceId) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" :label="$t('middleware.table.enabled')" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === 'ENABLED' ? 'success' : 'info'">
              {{ row.status === 'ENABLED' ? $t('middleware.status.enabled') : $t('middleware.status.disabled') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="$t('middleware.table.instantiated')" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="row.instantiated ? 'success' : 'warning'">
              {{ row.instantiated ? $t('middleware.status.instantiated') : $t('middleware.status.notInstantiated') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" :label="$t('middleware.table.createTime')" width="160" />
        <el-table-column prop="createdBy" :label="$t('middleware.table.createdBy')" width="100" />
        <el-table-column
          :label="$t('middleware.table.operation')"
          width="200"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button
              size="small"
              type="primary"
              @click="handleEdit(row)"
            >
              {{ $t("middleware.button.edit") }}
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(row)"
            >
              {{ $t("middleware.button.delete") }}
            </el-button>
            <el-button
              size="small"
              :type="row.status === 'ENABLED' ? 'warning' : 'success'"
              @click="handleToggleStatus(row)"
            >
              {{ row.status === "ENABLED" ? $t("middleware.button.disable") : $t("middleware.button.enable") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus, Search, Refresh } from "@element-plus/icons-vue";
import {
  getResourceConfigPage,
  getResourceTypeList,
  deleteResourceConfig,
  enableResourceConfig,
  disableResourceConfig,
  instantiateResource,
  destroyResource,
  isResourceInstantiated,
  batchCheckResourceInstantiated,
  type ResourceConfiguration,
  type ResourceType
} from "@/api/system/middleware";

defineOptions({
  name: "ResourceManagement"
});

const { t } = useI18n();
const router = useRouter();

// 搜索表单
const searchForm = reactive({
  name: "",
  resourceTypeId: ""
});

// 分页参数
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
});

// 表格数据
const tableData = ref<(ResourceConfiguration & { instantiated?: boolean })[]>([]);
const loading = ref(false);
const resourceTypes = ref<ResourceType[]>([]);

// 获取资源类型名称
const getResourceTypeName = (resourceId: string) => {
  const type = resourceTypes.value.find(t => t.id === resourceId);
  return type ? type.name : resourceId;
};

// 获取资源配置列表
const fetchResourceConfigs = async () => {
  loading.value = true;
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      name: searchForm.name || undefined,
      resourceTypeId: searchForm.resourceTypeId || undefined
    };

    const res = await getResourceConfigPage(params);
    console.log("API响应:", res); // 调试信息

    if (res.state) {
      // 处理不同的响应结构
      if (res.data && Array.isArray(res.data.records)) {
        // 标准分页响应结构
        tableData.value = res.data.records;
        pagination.total = res.data.total || 0;
      } else if (res.data && Array.isArray(res.data)) {
        // 直接数组响应
        tableData.value = res.data;
        pagination.total = res.data.length;
      } else if (Array.isArray(res.data)) {
        // 其他数组响应
        tableData.value = res.data;
        pagination.total = res.data.length;
      } else {
        console.warn("未知的响应结构:", res.data);
        tableData.value = [];
        pagination.total = 0;
      }

      console.log("处理后的表格数据:", tableData.value); // 调试信息

      // 批量检查实例化状态
      if (tableData.value.length > 0) {
        try {
          const instantiatedRes = await batchCheckResourceInstantiated(
            tableData.value.map(item => item.id)
          );
          if (instantiatedRes.state) {
            // 更新每个资源的实例化状态
            tableData.value.forEach(item => {
              item.instantiated = instantiatedRes.data[item.id] || false;
            });
          }
        } catch (error) {
          console.warn("批量检查资源实例化状态失败:", error);
          // 如果批量检查失败，将所有资源的实例化状态设为 false
          tableData.value.forEach(item => {
            item.instantiated = false;
          });
        }
      }
    } else {
      console.error("API调用失败:", res);
      tableData.value = [];
      pagination.total = 0;
    }
  } catch (error) {
    console.error("获取资源配置列表失败:", error);
    ElMessage.error(t("middleware.message.operationFailed"));
  } finally {
    loading.value = false;
  }
};

// 获取资源类型列表
const fetchResourceTypes = async () => {
  try {
    const res = await getResourceTypeList();
    console.log("资源类型API响应:", res); // 调试信息

    if (res.state) { // 使用 state 而不是 success
      if (Array.isArray(res.data)) {
        resourceTypes.value = res.data;
      } else {
        console.warn("资源类型响应不是数组:", res.data);
        resourceTypes.value = [];
      }
    } else {
      console.error("获取资源类型失败:", res);
      resourceTypes.value = [];
    }
  } catch (error) {
    console.error("获取资源类型列表失败:", error);
    resourceTypes.value = [];
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchResourceConfigs();
};

// 重置
const handleReset = () => {
  searchForm.name = "";
  searchForm.resourceTypeId = "";
  pagination.current = 1;
  fetchResourceConfigs();
};

// 新增
const handleAdd = () => {
  router.push("/system-management/middleware-management/resource-management/new");
};

// 编辑
const handleEdit = (row: ResourceConfiguration) => {
  router.push(`/system-management/middleware-management/resource-management/${row.id}/edit`);
};

// 详情
const handleDetail = (row: ResourceConfiguration) => {
  router.push(`/system-management/middleware-management/resource-management/${row.id}/detail`);
};

// 删除
const handleDelete = async (row: ResourceConfiguration) => {
  try {
    await ElMessageBox.confirm(
      t("middleware.message.confirmDelete"),
      t("middleware.button.confirm"),
      { type: "warning" }
    );

    const res = await deleteResourceConfig(row.id);
    if (res.state) { // 使用 state 而不是 success
      ElMessage.success(t("middleware.message.deleteSuccess"));
      fetchResourceConfigs();
    } else {
      ElMessage.error(t("middleware.message.operationFailed"));
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error("删除资源配置失败:", error);
      ElMessage.error(t("middleware.message.operationFailed"));
    }
  }
};

// 实例化
const handleInstantiate = async (row: ResourceConfiguration) => {
  try {
    await ElMessageBox.confirm(
      t("middleware.message.confirmInstantiate"),
      t("middleware.button.confirm"),
      { type: "warning" }
    );

    const res = await instantiateResource(row.id);
    if (res.state) { // 使用 state 而不是 success
      ElMessage.success(t("middleware.message.instantiateSuccess"));
      fetchResourceConfigs();
    } else {
      ElMessage.error(t("middleware.message.operationFailed"));
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error("实例化资源失败:", error);
      ElMessage.error(t("middleware.message.operationFailed"));
    }
  }
};

// 销毁
const handleDestroy = async (row: ResourceConfiguration) => {
  try {
    await ElMessageBox.confirm(
      t("middleware.message.confirmDestroy"),
      t("middleware.button.confirm"),
      { type: "warning" }
    );

    const res = await destroyResource(row.id);
    if (res.state) { // 使用 state 而不是 success
      ElMessage.success(t("middleware.message.destroySuccess"));
      fetchResourceConfigs();
    } else {
      ElMessage.error(t("middleware.message.operationFailed"));
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error("销毁资源失败:", error);
      ElMessage.error(t("middleware.message.operationFailed"));
    }
  }
};

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size;
  pagination.current = 1;
  fetchResourceConfigs();
};

// 当前页改变
const handleCurrentChange = (current: number) => {
  pagination.current = current;
  fetchResourceConfigs();
};

// 切换状态
const handleToggleStatus = async (row: ResourceConfiguration) => {
  try {
    if (row.status === 'ENABLED') {
      await ElMessageBox.confirm(
        t("middleware.message.confirmDisable"),
        t("middleware.button.confirm"),
        { type: "warning" }
      );
      const res = await disableResourceConfig(row.id);
      if (res.state) {
        ElMessage.success(t("middleware.message.disableSuccess"));
        fetchResourceConfigs();
      } else {
        ElMessage.error(t("middleware.message.operationFailed"));
      }
    } else {
      await ElMessageBox.confirm(
        t("middleware.message.confirmEnable"),
        t("middleware.button.confirm"),
        { type: "warning" }
      );
      const res = await enableResourceConfig(row.id);
      if (res.state) {
        ElMessage.success(t("middleware.message.enableSuccess"));
        fetchResourceConfigs();
      } else {
        ElMessage.error(t("middleware.message.operationFailed"));
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error("切换状态失败:", error);
      ElMessage.error(t("middleware.message.operationFailed"));
    }
  }
};

onMounted(() => {
  fetchResourceTypes();
  fetchResourceConfigs();
});
</script>

<style scoped>
.resource-management-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 500;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
