<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑用户' : '新增用户'"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      size="default"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户名称" prop="userName">
            <el-input
              v-model="formData.userName"
              placeholder="请输入用户名称"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="登录账号" prop="loginId">
            <el-input
              v-model="formData.loginId"
              placeholder="请输入登录账号"
              :disabled="isEdit"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" v-if="!isEdit">
        <el-col :span="12">
          <el-form-item label="登录密码" prop="password">
            <el-input
              v-model="formData.password"
              type="password"
              placeholder="请输入登录密码"
              show-password
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
              v-model="formData.confirmPassword"
              type="password"
              placeholder="请再次输入密码"
              show-password
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="最大错误次数" prop="maxError">
            <el-input-number
              v-model="formData.maxError"
              :min="1"
              :max="100"
              placeholder="最大错误次数"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否启用" prop="enable">
            <el-switch
              v-model="formData.enable"
              active-text="启用"
              inactive-text="停用"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="账号有效期" prop="validTime">
            <el-date-picker
              v-model="formData.validTime"
              type="datetime"
              placeholder="选择账号有效期"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="密码有效期" prop="passwordValidTime">
            <el-date-picker
              v-model="formData.passwordValidTime"
              type="datetime"
              placeholder="选择密码有效期"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="w-full"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户别名" prop="alias">
            <el-input
              v-model="formData.alias"
              placeholder="请输入用户别名"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="登录类型" prop="loginType">
            <el-select
              v-model="formData.loginType"
              placeholder="请选择登录类型"
              class="w-full"
            >
              <el-option label="Web登录" value="web" />
              <el-option label="移动端登录" value="mobile" />
              <el-option label="API登录" value="api" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="用户描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入用户描述"
          clearable
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from "vue";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import { createAccount, updateAccount, type AccountInfo, type CreateAccountRequest, type UpdateAccountRequest } from "@/api/account";

interface Props {
  visible: boolean;
  userData?: AccountInfo | null;
  isEdit: boolean;
}

interface Emits {
  (e: "update:visible", value: boolean): void;
  (e: "refresh"): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  userData: null,
  isEdit: false
});

const emit = defineEmits<Emits>();

// 响应式数据
const formRef = ref<FormInstance>();
const submitLoading = ref(false);
const dialogVisible = ref(false);

// 表单数据
const formData = reactive({
  userName: "",
  loginId: "",
  password: "",
  confirmPassword: "",
  enable: true,
  maxError: 10,
  validTime: "",
  passwordValidTime: "",
  description: "",
  alias: "",
  loginType: "web"
});

// 表单验证规则
const formRules: FormRules = {
  userName: [
    { required: true, message: "请输入用户名称", trigger: "blur" },
    { min: 2, max: 50, message: "用户名称长度在 2 到 50 个字符", trigger: "blur" }
  ],
  loginId: [
    { required: true, message: "请输入登录账号", trigger: "blur" },
    { min: 3, max: 50, message: "登录账号长度在 3 到 50 个字符", trigger: "blur" },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: "登录账号只能包含字母、数字、下划线和横线", trigger: "blur" }
  ],
  password: [
    { required: true, message: "请输入登录密码", trigger: "blur" },
    { min: 6, max: 20, message: "密码长度在 6 到 20 个字符", trigger: "blur" }
  ],
  confirmPassword: [
    { required: true, message: "请再次输入密码", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value !== formData.password) {
          callback(new Error("两次输入的密码不一致"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  maxError: [
    { required: true, message: "请输入最大错误次数", trigger: "blur" },
    { type: "number", min: 1, max: 100, message: "最大错误次数必须在 1 到 100 之间", trigger: "blur" }
  ]
};

// 监听对话框显示状态
watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val;
    if (val) {
      nextTick(() => {
        resetForm();
        if (props.isEdit && props.userData) {
          loadUserData();
        }
      });
    }
  },
  { immediate: true }
);

// 监听内部对话框状态
watch(dialogVisible, (val) => {
  emit("update:visible", val);
});

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields();
  Object.assign(formData, {
    userName: "",
    loginId: "",
    password: "",
    confirmPassword: "",
    enable: true,
    maxError: 10,
    validTime: "",
    passwordValidTime: "",
    description: "",
    alias: "",
    loginType: "web"
  });
};

// 加载用户数据
const loadUserData = () => {
  if (props.userData) {
    Object.assign(formData, {
      userName: props.userData.userName || "",
      loginId: props.userData.loginId || "",
      password: "",
      confirmPassword: "",
      enable: props.userData.enable ?? true,
      maxError: props.userData.maxError || 10,
      validTime: props.userData.validTime || "",
      passwordValidTime: props.userData.passwordValidTime || "",
      description: props.userData.description || "",
      alias: props.userData.alias || "",
      loginType: props.userData.loginType || "web"
    });
  }
};

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    
    submitLoading.value = true;
    
    if (props.isEdit) {
      // 更新用户
      const updateData: UpdateAccountRequest = {
        userId: props.userData!.userId,
        userName: formData.userName,
        enable: formData.enable,
        maxError: formData.maxError,
        validTime: formData.validTime || undefined,
        passwordValidTime: formData.passwordValidTime || undefined,
        description: formData.description || undefined,
        alias: formData.alias || undefined
      };
      
      const response = await updateAccount(updateData);
      if (response.state) {
        ElMessage.success("更新用户成功");
        handleClose();
        emit("refresh");
      } else {
        ElMessage.error(response.err_msg || "更新用户失败");
      }
    } else {
      // 创建用户
      const createData: CreateAccountRequest = {
        userName: formData.userName,
        loginId: formData.loginId,
        password: formData.password,
        enable: formData.enable,
        maxError: formData.maxError,
        validTime: formData.validTime || undefined,
        passwordValidTime: formData.passwordValidTime || undefined,
        description: formData.description || undefined,
        alias: formData.alias || undefined,
        loginType: formData.loginType
      };
      
      const response = await createAccount(createData);
      if (response.state) {
        ElMessage.success("创建用户成功");
        handleClose();
        emit("refresh");
      } else {
        ElMessage.error(response.err_msg || "创建用户失败");
      }
    }
  } catch (error) {
    console.error("表单验证失败:", error);
  } finally {
    submitLoading.value = false;
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  resetForm();
};
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

:deep(.el-input-number) {
  width: 100%;
}
</style>