<template>
  <el-dialog
    v-model="dialogVisible"
    title="分配角色"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="mb-4">
      <div class="text-sm text-gray-600 mb-2">用户信息：</div>
      <div class="bg-gray-50 p-3 rounded">
        <div><strong>用户名称：</strong>{{ userData?.userName }}</div>
        <div><strong>登录账号：</strong>{{ userData?.loginId }}</div>
      </div>
    </div>

    <div class="flex gap-4 h-96">
      <!-- 可用角色 -->
      <div class="flex-1">
        <div class="font-medium mb-2">可用角色</div>
        <el-input
          v-model="searchKeyword"
          placeholder="搜索角色"
          clearable
          class="mb-2"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <div class="border rounded p-2 h-72 overflow-y-auto">
          <el-checkbox-group v-model="selectedRoles" @change="handleRoleChange">
            <div
              v-for="role in filteredAvailableRoles"
              :key="role.roleId"
              class="flex items-center p-2 hover:bg-gray-50 rounded"
            >
              <el-checkbox :label="role.roleId">
                <div>
                  <div class="font-medium">{{ role.roleName }}</div>
                  <div class="text-sm text-gray-500">{{ role.description || '暂无描述' }}</div>
                </div>
              </el-checkbox>
            </div>
          </el-checkbox-group>
        </div>
      </div>

      <!-- 已分配角色 -->
      <div class="flex-1">
        <div class="font-medium mb-2">已分配角色</div>
        <div class="border rounded p-2 h-80 overflow-y-auto">
          <div v-if="assignedRoles.length === 0" class="text-center text-gray-500 py-8">
            暂无分配的角色
          </div>
          <div
            v-for="role in assignedRoles"
            :key="role.roleId"
            class="flex items-center justify-between p-2 bg-blue-50 rounded mb-2"
          >
            <div>
              <div class="font-medium">{{ role.roleName }}</div>
              <div class="text-sm text-gray-500">{{ role.description || '暂无描述' }}</div>
            </div>
            <el-button
              type="danger"
              size="small"
              link
              @click="removeRole(role.roleId)"
            >
              移除
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
          确认分配
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import { getAllRoles, getUserRoles, type RoleInfo } from "@/api/role";
import { updateAccount, type AccountInfo } from "@/api/account";

interface Props {
  visible: boolean;
  userData?: AccountInfo | null;
}

interface Emits {
  (e: "update:visible", value: boolean): void;
  (e: "refresh"): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  userData: null
});

const emit = defineEmits<Emits>();

// 响应式数据
const dialogVisible = ref(false);
const submitLoading = ref(false);
const searchKeyword = ref("");
const allRoles = ref<RoleInfo[]>([]);
const userRoles = ref<RoleInfo[]>([]);
const selectedRoles = ref<number[]>([]);

// 计算属性
const filteredAvailableRoles = computed(() => {
  return allRoles.value.filter(role => {
    const matchesSearch = !searchKeyword.value || 
      role.roleName.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      (role.description && role.description.toLowerCase().includes(searchKeyword.value.toLowerCase()));
    return matchesSearch;
  });
});

const assignedRoles = computed(() => {
  return allRoles.value.filter(role => selectedRoles.value.includes(role.roleId));
});

// 监听对话框显示状态
watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val;
    if (val) {
      nextTick(() => {
        loadData();
      });
    }
  },
  { immediate: true }
);

// 监听内部对话框状态
watch(dialogVisible, (val) => {
  emit("update:visible", val);
});

// 加载数据
const loadData = async () => {
  await Promise.all([
    loadAllRoles(),
    loadUserRoles()
  ]);
};

// 加载所有角色
const loadAllRoles = async () => {
  try {
    const response = await getAllRoles();
    if (response.state) {
      allRoles.value = response.data || [];
    } else {
      ElMessage.error(response.err_msg || "获取角色列表失败");
      allRoles.value = [];
    }
  } catch (error) {
    console.error("获取角色列表失败:", error);
    ElMessage.error("获取角色列表失败");
    allRoles.value = [];
  }
};

// 加载用户角色
const loadUserRoles = async () => {
  if (!props.userData?.userId) return;
  
  try {
    const response = await getUserRoles(props.userData.userId);
    if (response.state) {
      userRoles.value = response.data || [];
      selectedRoles.value = userRoles.value.map(role => role.roleId);
    } else {
      // 如果接口不存在，从用户数据中解析角色ID
      if (props.userData.roleIds) {
        const roleIdArray = props.userData.roleIds.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
        selectedRoles.value = roleIdArray;
      } else {
        selectedRoles.value = [];
      }
    }
  } catch (error) {
    console.error("获取用户角色失败:", error);
    // 降级处理：从用户数据中解析角色ID
    if (props.userData?.roleIds) {
      const roleIdArray = props.userData.roleIds.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
      selectedRoles.value = roleIdArray;
    } else {
      selectedRoles.value = [];
    }
  }
};

// 角色选择变化
const handleRoleChange = (value: number[]) => {
  selectedRoles.value = value;
};

// 移除角色
const removeRole = (roleId: number) => {
  const index = selectedRoles.value.indexOf(roleId);
  if (index > -1) {
    selectedRoles.value.splice(index, 1);
  }
};

// 提交分配
const handleSubmit = async () => {
  try {
    submitLoading.value = true;
    
    const roleIds = selectedRoles.value.join(',');
    const response = await updateAccount({
      userId: props.userData!.userId,
      roleIds: roleIds
    });
    
    if (response.state) {
      ElMessage.success("角色分配成功");
      handleClose();
      emit("refresh");
    } else {
      ElMessage.error(response.err_msg || "角色分配失败");
    }
  } catch (error) {
    console.error("角色分配失败:", error);
    ElMessage.error("角色分配失败");
  } finally {
    submitLoading.value = false;
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  searchKeyword.value = "";
  selectedRoles.value = [];
  allRoles.value = [];
  userRoles.value = [];
};
</script>

<style scoped>
.text-gray-600 {
  color: var(--el-text-color-secondary);
}

.text-gray-500 {
  color: var(--el-text-color-placeholder);
}

.bg-gray-50 {
  background-color: var(--el-bg-color-page);
}

.hover\:bg-gray-50:hover {
  background-color: var(--el-bg-color-page);
}

.bg-blue-50 {
  background-color: var(--el-color-primary-light-9);
}

.border {
  border: 1px solid var(--el-border-color);
}

.dialog-footer {
  text-align: right;
}
</style>