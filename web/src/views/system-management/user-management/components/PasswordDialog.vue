<template>
  <el-dialog
    v-model="dialogVisible"
    title="密码管理"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="mb-4">
      <div class="text-sm text-gray-600 mb-2">用户信息：</div>
      <div class="bg-gray-50 p-3 rounded">
        <div><strong>用户名称：</strong>{{ userData?.userName }}</div>
        <div><strong>登录账号：</strong>{{ userData?.loginId }}</div>
      </div>
    </div>

    <el-tabs v-model="activeTab" type="border-card">
      <!-- 重置密码（管理员操作） -->
      <el-tab-pane label="重置密码" name="reset" v-perms="['system:user:password:reset']">
        <el-form
          ref="resetFormRef"
          :model="resetForm"
          :rules="resetRules"
          label-width="100px"
          size="default"
        >
          <el-form-item label="新密码" prop="newPassword">
            <el-input
              v-model="resetForm.newPassword"
              type="password"
              placeholder="请输入新密码"
              show-password
              clearable
            />
          </el-form-item>
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
              v-model="resetForm.confirmPassword"
              type="password"
              placeholder="请再次输入新密码"
              show-password
              clearable
            />
          </el-form-item>
        </el-form>
        <div class="flex justify-end mt-4">
          <el-button @click="handleClose">取消</el-button>
          <el-button 
            type="primary" 
            :loading="resetLoading" 
            @click="handleReset"
          >
            重置密码
          </el-button>
        </div>
      </el-tab-pane>

      <!-- 修改密码（用户自己操作） -->
      <el-tab-pane label="修改密码" name="edit" v-perms="['system:user:password:edit']">
        <el-form
          ref="editFormRef"
          :model="editForm"
          :rules="editRules"
          label-width="100px"
          size="default"
        >
          <el-form-item label="原密码" prop="oldPassword">
            <el-input
              v-model="editForm.oldPassword"
              type="password"
              placeholder="请输入原密码"
              show-password
              clearable
            />
          </el-form-item>
          <el-form-item label="新密码" prop="newPassword">
            <el-input
              v-model="editForm.newPassword"
              type="password"
              placeholder="请输入新密码"
              show-password
              clearable
            />
          </el-form-item>
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
              v-model="editForm.confirmPassword"
              type="password"
              placeholder="请再次输入新密码"
              show-password
              clearable
            />
          </el-form-item>
        </el-form>
        <div class="flex justify-end mt-4">
          <el-button @click="handleClose">取消</el-button>
          <el-button 
            type="primary" 
            :loading="editLoading" 
            @click="handleEdit"
          >
            修改密码
          </el-button>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from "vue";
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from "element-plus";
import { resetPassword, editPassword, type AccountInfo } from "@/api/account";

interface Props {
  visible: boolean;
  userData?: AccountInfo | null;
}

interface Emits {
  (e: "update:visible", value: boolean): void;
  (e: "refresh"): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  userData: null
});

const emit = defineEmits<Emits>();

// 响应式数据
const dialogVisible = ref(false);
const activeTab = ref("reset");
const resetFormRef = ref<FormInstance>();
const editFormRef = ref<FormInstance>();
const resetLoading = ref(false);
const editLoading = ref(false);

// 重置密码表单
const resetForm = reactive({
  newPassword: "",
  confirmPassword: ""
});

// 修改密码表单
const editForm = reactive({
  oldPassword: "",
  newPassword: "",
  confirmPassword: ""
});

// 重置密码验证规则
const resetRules: FormRules = {
  newPassword: [
    { required: true, message: "请输入新密码", trigger: "blur" },
    { min: 6, max: 20, message: "密码长度在 6 到 20 个字符", trigger: "blur" }
  ],
  confirmPassword: [
    { required: true, message: "请再次输入新密码", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value !== resetForm.newPassword) {
          callback(new Error("两次输入的密码不一致"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ]
};

// 修改密码验证规则
const editRules: FormRules = {
  oldPassword: [
    { required: true, message: "请输入原密码", trigger: "blur" }
  ],
  newPassword: [
    { required: true, message: "请输入新密码", trigger: "blur" },
    { min: 6, max: 20, message: "密码长度在 6 到 20 个字符", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value === editForm.oldPassword) {
          callback(new Error("新密码不能与原密码相同"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  confirmPassword: [
    { required: true, message: "请再次输入新密码", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value !== editForm.newPassword) {
          callback(new Error("两次输入的密码不一致"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ]
};

// 监听对话框显示状态
watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val;
    if (val) {
      nextTick(() => {
        resetForms();
      });
    }
  },
  { immediate: true }
);

// 监听内部对话框状态
watch(dialogVisible, (val) => {
  emit("update:visible", val);
});

// 重置表单
const resetForms = () => {
  resetFormRef.value?.resetFields();
  editFormRef.value?.resetFields();
  
  Object.assign(resetForm, {
    newPassword: "",
    confirmPassword: ""
  });
  
  Object.assign(editForm, {
    oldPassword: "",
    newPassword: "",
    confirmPassword: ""
  });
};

// 重置密码
const handleReset = async () => {
  try {
    await resetFormRef.value?.validate();
    
    await ElMessageBox.confirm(
      `确认重置用户 "${props.userData?.userName}" 的密码吗？`,
      "确认重置密码",
      {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }
    );
    
    resetLoading.value = true;
    
    const response = await resetPassword({
      userId: props.userData!.userId,
      newPassword: resetForm.newPassword
    });
    
    if (response.state) {
      ElMessage.success("重置密码成功");
      handleClose();
      emit("refresh");
    } else {
      ElMessage.error(response.err_msg || "重置密码失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("重置密码失败:", error);
      ElMessage.error("重置密码失败");
    }
  } finally {
    resetLoading.value = false;
  }
};

// 修改密码
const handleEdit = async () => {
  try {
    await editFormRef.value?.validate();
    
    editLoading.value = true;
    
    const response = await editPassword({
      userId: props.userData!.userId,
      oldPassword: editForm.oldPassword,
      newPassword: editForm.newPassword
    });
    
    if (response.state) {
      ElMessage.success("修改密码成功");
      handleClose();
      emit("refresh");
    } else {
      ElMessage.error(response.err_msg || "修改密码失败");
    }
  } catch (error) {
    console.error("修改密码失败:", error);
    ElMessage.error("修改密码失败");
  } finally {
    editLoading.value = false;
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  resetForms();
};
</script>

<style scoped>
.text-gray-600 {
  color: var(--el-text-color-secondary);
}

.bg-gray-50 {
  background-color: var(--el-bg-color-page);
}
</style>