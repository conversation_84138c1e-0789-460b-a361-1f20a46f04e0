<template>
  <div class="main">
    <div class="w-full p-4">
      <!-- 搜索工具栏 -->
      <div class="flex justify-between items-center mb-4">
        <div class="flex gap-4 items-center">
          <el-input
            v-model="searchForm.name"
            placeholder="搜索角色名称"
            style="width: 200px"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-input
            v-model="searchForm.code"
            placeholder="搜索角色标识"
            style="width: 200px"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Key /></el-icon>
            </template>
          </el-input>
          <el-select
            v-model="searchForm.status"
            placeholder="启用状态"
            clearable
            style="width: 150px"
          >
            <el-option label="全部" value="" />
            <el-option label="启用" :value="1" />
            <el-option label="停用" :value="0" />
          </el-select>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </div>
        <div class="flex gap-2">
          <el-button
            type="primary"
            @click="handleAdd"
          >
            <el-icon><Plus /></el-icon>
            新增角色
          </el-button>
          <el-button
            type="danger"
            :disabled="!hasSelection"
            @click="handleBatchDelete"
          >
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>
        </div>
      </div>

      <!-- 角色表格 -->
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        @selection-change="handleSelectionChange"
        stripe
        border
        size="default"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="角色ID" width="80" />
        <el-table-column prop="name" label="角色名称" min-width="150">
          <template #default="{ row }">
            <div class="flex items-center">
              <el-icon class="mr-2 text-blue-500"><UserFilled /></el-icon>
              <span class="font-medium">{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="code" label="角色标识" width="150" />
        <el-table-column prop="description" label="角色描述" min-width="200" show-overflow-tooltip />
        <el-table-column label="启用状态" width="100" align="center">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleToggleStatus(row)"
              :loading="row._switching"
            />
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" width="80" align="center" />
        <el-table-column prop="createTime" label="创建时间" width="180" show-overflow-tooltip />
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="{ row }">
            <el-button
              link
              type="primary"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              link
              type="success"
              @click="handlePermission(row)"
            >
              权限
            </el-button>
            <el-button
              link
              type="danger"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="flex justify-end mt-4">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 角色表单对话框 -->
    <RoleFormDialog
      v-model:visible="formDialogVisible"
      :role-data="currentRole"
      :is-edit="isEdit"
      @refresh="handleRefresh"
    />

    <!-- 权限配置对话框 -->
    <PermissionDialog
      v-model:visible="permissionDialogVisible"
      :role-data="currentRole"
      @refresh="handleRefresh"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Search, Refresh, Plus, Delete, Key, UserFilled } from "@element-plus/icons-vue";
import RoleFormDialog from "./components/RoleFormDialog.vue";
import PermissionDialog from "./components/PermissionDialog.vue";

defineOptions({
  name: "RoleManagement"
});

// 角色数据类型
interface RoleInfo {
  id: number;
  name: string;
  code: string;
  description?: string;
  status: number;
  sort: number;
  createTime: string;
  _switching?: boolean;
}

// 响应式数据
const tableLoading = ref(false);
const tableData = ref<RoleInfo[]>([]);
const selectedRoles = ref<RoleInfo[]>([]);
const formDialogVisible = ref(false);
const permissionDialogVisible = ref(false);
const currentRole = ref<RoleInfo | null>(null);
const isEdit = ref(false);

// 搜索表单
const searchForm = reactive({
  name: "",
  code: "",
  status: ""
});

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
});

// 计算属性
const hasSelection = computed(() => selectedRoles.value.length > 0);

// 模拟数据
const mockRoles: RoleInfo[] = [
  {
    id: 1,
    name: "超级管理员",
    code: "SUPER_ADMIN",
    description: "系统超级管理员，拥有所有权限",
    status: 1,
    sort: 1,
    createTime: "2024-01-01 10:00:00"
  },
  {
    id: 2,
    name: "系统管理员",
    code: "SYSTEM_ADMIN",
    description: "系统管理员，负责系统配置和用户管理",
    status: 1,
    sort: 2,
    createTime: "2024-01-01 11:00:00"
  },
  {
    id: 3,
    name: "部门经理",
    code: "DEPT_MANAGER",
    description: "部门经理，负责部门内人员和事务管理",
    status: 1,
    sort: 3,
    createTime: "2024-01-01 12:00:00"
  },
  {
    id: 4,
    name: "普通用户",
    code: "NORMAL_USER",
    description: "普通用户，基础查看权限",
    status: 1,
    sort: 4,
    createTime: "2024-01-01 13:00:00"
  },
  {
    id: 5,
    name: "访客用户",
    code: "GUEST_USER",
    description: "访客用户，受限访问权限",
    status: 0,
    sort: 5,
    createTime: "2024-01-01 14:00:00"
  },
  {
    id: 6,
    name: "财务专员",
    code: "FINANCE_STAFF",
    description: "财务专员，负责财务相关操作",
    status: 1,
    sort: 6,
    createTime: "2024-01-01 15:00:00"
  },
  {
    id: 7,
    name: "人事专员",
    code: "HR_STAFF",
    description: "人事专员，负责人力资源管理",
    status: 1,
    sort: 7,
    createTime: "2024-01-01 16:00:00"
  },
  {
    id: 8,
    name: "技术负责人",
    code: "TECH_LEADER",
    description: "技术负责人，负责技术相关管理",
    status: 1,
    sort: 8,
    createTime: "2024-01-01 17:00:00"
  },
  {
    id: 9,
    name: "产品经理",
    code: "PRODUCT_MANAGER",
    description: "产品经理，负责产品规划和设计",
    status: 0,
    sort: 9,
    createTime: "2024-01-01 18:00:00"
  },
  {
    id: 10,
    name: "测试工程师",
    code: "QA_ENGINEER",
    description: "测试工程师，负责产品质量保证",
    status: 1,
    sort: 10,
    createTime: "2024-01-01 19:00:00"
  }
];

// 获取角色列表
const getTableData = async () => {
  try {
    tableLoading.value = true;
    
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    
    let data = [...mockRoles];
    
    // 搜索过滤
    if (searchForm.name) {
      data = data.filter(role => role.name.includes(searchForm.name));
    }
    
    if (searchForm.code) {
      data = data.filter(role => role.code.includes(searchForm.code));
    }
    
    if (searchForm.status !== "") {
      data = data.filter(role => role.status === Number(searchForm.status));
    }
    
    pagination.total = data.length;
    
    // 分页
    const start = (pagination.page - 1) * pagination.size;
    const end = start + pagination.size;
    tableData.value = data.slice(start, end);
  } catch (error) {
    console.error("获取角色列表失败:", error);
    ElMessage.error("获取角色列表失败");
    tableData.value = [];
  } finally {
    tableLoading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.page = 1;
  getTableData();
};

// 重置搜索
const handleReset = () => {
  searchForm.name = "";
  searchForm.code = "";
  searchForm.status = "";
  pagination.page = 1;
  getTableData();
};

// 新增角色
const handleAdd = () => {
  currentRole.value = null;
  isEdit.value = false;
  formDialogVisible.value = true;
};

// 编辑角色
const handleEdit = (role: RoleInfo) => {
  currentRole.value = { ...role };
  isEdit.value = true;
  formDialogVisible.value = true;
};

// 启用/停用角色
const handleToggleStatus = async (role: RoleInfo) => {
  try {
    role._switching = true;
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800));
    
    ElMessage.success(`${role.status === 1 ? '启用' : '停用'}角色成功`);
  } catch (error) {
    role.status = role.status === 1 ? 0 : 1; // 回滚状态
    console.error("切换角色状态失败:", error);
    ElMessage.error("操作失败");
  } finally {
    role._switching = false;
  }
};

// 权限配置
const handlePermission = (role: RoleInfo) => {
  currentRole.value = { ...role };
  permissionDialogVisible.value = true;
};

// 删除角色
const handleDelete = async (role: RoleInfo) => {
  try {
    await ElMessageBox.confirm(
      `确认删除角色 "${role.name}" 吗？此操作不可逆！`,
      "确认删除",
      {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }
    );
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));
    
    ElMessage.success("删除角色成功");
    getTableData();
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除角色失败:", error);
      ElMessage.error("删除角色失败");
    }
  }
};

// 批量删除角色
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确认删除选中的 ${selectedRoles.value.length} 个角色吗？此操作不可逆！`,
      "确认批量删除",
      {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }
    );
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800));
    
    ElMessage.success(`成功删除 ${selectedRoles.value.length} 个角色`);
    selectedRoles.value = [];
    getTableData();
  } catch (error) {
    if (error !== "cancel") {
      console.error("批量删除角色失败:", error);
      ElMessage.error("批量删除失败");
    }
  }
};

// 表格选择变化
const handleSelectionChange = (selection: RoleInfo[]) => {
  selectedRoles.value = selection;
};

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.size = size;
  pagination.page = 1;
  getTableData();
};

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page;
  getTableData();
};

// 刷新数据
const handleRefresh = () => {
  getTableData();
};

// 页面挂载时获取数据
onMounted(() => {
  getTableData();
});
</script>

<style scoped>
.main {
  background: var(--el-bg-color);
  min-height: 100vh;
}
</style>