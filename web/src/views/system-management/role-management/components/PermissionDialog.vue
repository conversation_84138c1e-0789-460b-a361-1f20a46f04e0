<template>
  <el-dialog
    :model-value="visible"
    title="权限配置"
    width="800px"
    @update:model-value="handleClose"
    @close="handleClose"
  >
    <div class="permission-config">
      <div class="role-info mb-4">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="角色名称">{{ roleData?.name }}</el-descriptions-item>
          <el-descriptions-item label="角色标识">{{ roleData?.code }}</el-descriptions-item>
        </el-descriptions>
      </div>
      
      <div class="permission-tree">
        <div class="tree-header flex items-center justify-between mb-4">
          <div class="flex items-center gap-4">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索权限"
              style="width: 300px"
              clearable
              @input="handleSearchChange"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
          <div class="flex items-center gap-4">
            <el-checkbox v-model="expandAll" @change="handleExpandChange">展开/折叠</el-checkbox>
            <el-checkbox v-model="checkAll" @change="handleCheckAllChange">全选/全不选</el-checkbox>
            <el-checkbox v-model="linkage">父子联动</el-checkbox>
          </div>
        </div>
        
        <el-tree
          ref="treeRef"
          :data="permissionTreeData"
          :props="treeProps"
          show-checkbox
          node-key="id"
          :default-expanded-keys="expandedKeys"
          :default-checked-keys="checkedKeys"
          :check-strictly="!linkage"
          :filter-node-method="filterNode"
          class="permission-tree"
        >
          <template #default="{ node, data }">
            <div class="tree-node flex items-center">
              <el-icon class="mr-2">
                <component :is="getNodeIcon(data.type)" />
              </el-icon>
              <span>{{ node.label }}</span>
              <el-tag
                v-if="data.code"
                size="small"
                type="info"
                class="ml-2"
              >
                {{ data.code }}
              </el-tag>
            </div>
          </template>
        </el-tree>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="submitLoading"
          @click="handleSubmit"
        >
          保存权限
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, nextTick } from "vue";
import { ElMessage, ElTree } from "element-plus";
import { Search, Menu, Document, Key } from "@element-plus/icons-vue";

// 角色数据类型
interface RoleInfo {
  id: number;
  name: string;
  code: string;
  description?: string;
  status: number;
  sort: number;
  createTime: string;
}

// 权限节点类型
interface PermissionNode {
  id: number;
  label: string;
  code?: string;
  type: 'menu' | 'button' | 'api';
  children?: PermissionNode[];
}

// Props定义
interface Props {
  visible: boolean;
  roleData?: RoleInfo | null;
}

// Emits定义
interface Emits {
  (e: "update:visible", value: boolean): void;
  (e: "refresh"): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  roleData: null
});

const emit = defineEmits<Emits>();

// 响应式数据
const treeRef = ref<InstanceType<typeof ElTree>>();
const submitLoading = ref(false);
const searchKeyword = ref("");
const expandAll = ref(false);
const checkAll = ref(false);
const linkage = ref(true);
const expandedKeys = ref<number[]>([]);
const checkedKeys = ref<number[]>([]);

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'label'
};

// 模拟权限树数据
const permissionTreeData = ref<PermissionNode[]>([
  {
    id: 1,
    label: "系统管理",
    code: "system",
    type: "menu",
    children: [
      {
        id: 11,
        label: "用户管理",
        code: "system:user",
        type: "menu",
        children: [
          { id: 111, label: "新增用户", code: "system:user:add", type: "button" },
          { id: 112, label: "编辑用户", code: "system:user:edit", type: "button" },
          { id: 113, label: "删除用户", code: "system:user:delete", type: "button" },
          { id: 114, label: "重置密码", code: "system:user:password", type: "button" },
          { id: 115, label: "分配角色", code: "system:user:role", type: "button" }
        ]
      },
      {
        id: 12,
        label: "角色管理",
        code: "system:role",
        type: "menu",
        children: [
          { id: 121, label: "新增角色", code: "system:role:add", type: "button" },
          { id: 122, label: "编辑角色", code: "system:role:edit", type: "button" },
          { id: 123, label: "删除角色", code: "system:role:delete", type: "button" },
          { id: 124, label: "配置权限", code: "system:role:permission", type: "button" }
        ]
      },
      {
        id: 13,
        label: "部门管理",
        code: "system:dept",
        type: "menu",
        children: [
          { id: 131, label: "新增部门", code: "system:dept:add", type: "button" },
          { id: 132, label: "编辑部门", code: "system:dept:edit", type: "button" },
          { id: 133, label: "删除部门", code: "system:dept:delete", type: "button" }
        ]
      }
    ]
  },
  {
    id: 2,
    label: "流计算管理",
    code: "stream",
    type: "menu",
    children: [
      {
        id: 21,
        label: "计算图管理",
        code: "stream:graph",
        type: "menu",
        children: [
          { id: 211, label: "新建计算图", code: "stream:graph:add", type: "button" },
          { id: 212, label: "编辑计算图", code: "stream:graph:edit", type: "button" },
          { id: 213, label: "删除计算图", code: "stream:graph:delete", type: "button" },
          { id: 214, label: "启动计算图", code: "stream:graph:start", type: "button" },
          { id: 215, label: "停止计算图", code: "stream:graph:stop", type: "button" }
        ]
      },
      {
        id: 22,
        label: "插件管理",
        code: "stream:plugin",
        type: "menu",
        children: [
          { id: 221, label: "安装插件", code: "stream:plugin:install", type: "button" },
          { id: 222, label: "卸载插件", code: "stream:plugin:uninstall", type: "button" },
          { id: 223, label: "启用插件", code: "stream:plugin:enable", type: "button" },
          { id: 224, label: "停用插件", code: "stream:plugin:disable", type: "button" }
        ]
      }
    ]
  },
  {
    id: 3,
    label: "设备管理",
    code: "device",
    type: "menu",
    children: [
      {
        id: 31,
        label: "设备监控",
        code: "device:monitor",
        type: "menu",
        children: [
          { id: 311, label: "查看设备", code: "device:monitor:view", type: "button" },
          { id: 312, label: "设备控制", code: "device:monitor:control", type: "button" }
        ]
      }
    ]
  }
]);

// 根据节点类型获取图标
const getNodeIcon = (type: string) => {
  switch (type) {
    case 'menu':
      return Menu;
    case 'button':
      return Key;
    case 'api':
      return Document;
    default:
      return Document;
  }
};

// 监听对话框显示状态
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      initPermissionData();
    }
  }
);

// 监听展开状态变化
watch(expandAll, (val) => {
  handleExpandChange(val);
});

// 初始化权限数据
const initPermissionData = () => {
  // 模拟获取角色已有权限
  if (props.roleData) {
    // 根据角色模拟不同的权限
    if (props.roleData.code === 'SUPER_ADMIN') {
      checkedKeys.value = getAllNodeIds(permissionTreeData.value);
      checkAll.value = true;
    } else if (props.roleData.code === 'SYSTEM_ADMIN') {
      checkedKeys.value = [1, 11, 111, 112, 113, 12, 121, 122, 123, 13, 131, 132];
    } else if (props.roleData.code === 'NORMAL_USER') {
      checkedKeys.value = [2, 21, 211, 3, 31, 311];
    } else {
      checkedKeys.value = [];
    }
    
    // 默认展开第一级
    expandedKeys.value = [1, 2, 3];
  }
};

// 获取所有节点ID
const getAllNodeIds = (nodes: PermissionNode[]): number[] => {
  const ids: number[] = [];
  const traverse = (nodeList: PermissionNode[]) => {
    nodeList.forEach(node => {
      ids.push(node.id);
      if (node.children) {
        traverse(node.children);
      }
    });
  };
  traverse(nodes);
  return ids;
};

// 处理展开/折叠变化
const handleExpandChange = (val: boolean) => {
  if (val) {
    expandedKeys.value = getAllNodeIds(permissionTreeData.value);
  } else {
    expandedKeys.value = [];
  }
  
  nextTick(() => {
    if (treeRef.value) {
      if (val) {
        // 展开所有节点
        const allKeys = getAllNodeIds(permissionTreeData.value);
        allKeys.forEach(key => {
          treeRef.value!.store.nodesMap[key]?.expand();
        });
      } else {
        // 折叠所有节点
        const allKeys = getAllNodeIds(permissionTreeData.value);
        allKeys.forEach(key => {
          treeRef.value!.store.nodesMap[key]?.collapse();
        });
      }
    }
  });
};

// 处理全选/全不选变化
const handleCheckAllChange = (val: boolean) => {
  if (!treeRef.value) return;
  
  if (val) {
    const allKeys = getAllNodeIds(permissionTreeData.value);
    treeRef.value.setCheckedKeys(allKeys);
    checkedKeys.value = allKeys;
  } else {
    treeRef.value.setCheckedKeys([]);
    checkedKeys.value = [];
  }
};

// 搜索变化处理
const handleSearchChange = (value: string) => {
  if (treeRef.value) {
    treeRef.value.filter(value);
  }
};

// 树形筛选方法
const filterNode = (value: string, data: PermissionNode) => {
  if (!value) return true;
  return data.label.includes(value) || (data.code && data.code.includes(value));
};

// 关闭对话框
const handleClose = () => {
  searchKeyword.value = "";
  expandAll.value = false;
  checkAll.value = false;
  emit("update:visible", false);
};

// 提交表单
const handleSubmit = async () => {
  if (!treeRef.value) return;

  try {
    submitLoading.value = true;
    
    // 获取选中的权限节点
    const checkedNodes = treeRef.value.getCheckedKeys();
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log("保存权限:", {
      roleId: props.roleData?.id,
      permissions: checkedNodes
    });
    
    ElMessage.success("保存权限成功");
    emit("refresh");
    handleClose();
  } catch (error) {
    console.error("保存权限失败:", error);
    ElMessage.error("保存权限失败");
  } finally {
    submitLoading.value = false;
  }
};
</script>

<style scoped>
.permission-config {
  max-height: 600px;
  overflow-y: auto;
}

.permission-tree {
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  padding: 12px;
  max-height: 400px;
  overflow-y: auto;
}

.tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-tree-node__content) {
  height: 32px;
}
</style>