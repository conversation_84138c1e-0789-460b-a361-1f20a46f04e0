<template>
  <el-dialog
    :model-value="visible"
    :title="isEdit ? '编辑角色' : '新增角色'"
    width="600px"
    @update:model-value="handleClose"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      size="default"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="角色名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入角色名称"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="角色标识" prop="code">
            <el-input
              v-model="formData.code"
              placeholder="请输入角色标识"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="启用状态" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio :value="1">启用</el-radio>
              <el-radio :value="0">停用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sort">
            <el-input-number
              v-model="formData.sort"
              :min="0"
              :max="999"
              placeholder="排序"
              controls-position="right"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="角色描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入角色描述"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="submitLoading"
          @click="handleSubmit"
        >
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from "vue";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";

// 角色数据类型
interface RoleInfo {
  id?: number;
  name: string;
  code: string;
  description?: string;
  status: number;
  sort: number;
  createTime?: string;
}

// Props定义
interface Props {
  visible: boolean;
  roleData?: RoleInfo | null;
  isEdit: boolean;
}

// Emits定义
interface Emits {
  (e: "update:visible", value: boolean): void;
  (e: "refresh"): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  roleData: null,
  isEdit: false
});

const emit = defineEmits<Emits>();

// 响应式数据
const formRef = ref<FormInstance>();
const submitLoading = ref(false);

// 表单数据
const formData = reactive<RoleInfo>({
  name: "",
  code: "",
  description: "",
  status: 1,
  sort: 0
});

// 表单验证规则
const formRules = computed<FormRules>(() => ({
  name: [
    { required: true, message: "请输入角色名称", trigger: "blur" },
    { min: 2, max: 50, message: "角色名称长度在 2 到 50 个字符", trigger: "blur" }
  ],
  code: [
    { required: true, message: "请输入角色标识", trigger: "blur" },
    { pattern: /^[A-Z_][A-Z0-9_]*$/, message: "角色标识只能包含大写字母、数字和下划线，且以字母或下划线开头", trigger: "blur" }
  ],
  sort: [
    { required: true, message: "请输入排序", trigger: "blur" }
  ],
  description: [
    { max: 200, message: "角色描述不能超过200个字符", trigger: "blur" }
  ]
}));

// 监听对话框显示状态
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      resetForm();
      if (props.isEdit && props.roleData) {
        // 编辑模式，回填数据
        Object.assign(formData, props.roleData);
      }
    }
  }
);

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: "",
    code: "",
    description: "",
    status: 1,
    sort: 0
  });
  formRef.value?.clearValidate();
};

// 关闭对话框
const handleClose = () => {
  emit("update:visible", false);
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    
    submitLoading.value = true;
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    ElMessage.success(props.isEdit ? "编辑角色成功" : "新增角色成功");
    emit("refresh");
    handleClose();
  } catch (error) {
    console.error("表单验证失败:", error);
  } finally {
    submitLoading.value = false;
  }
};
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>