<template>
  <div
    class="plugin-management-container bg-gray-50 dark:bg-gray-900 p-4 flex flex-col"
    style="height: calc(100vh - 48px)"
  >
    <!-- 页面头部 -->
    <div class="mb-4 flex-shrink-0">
      <div class="flex items-center justify-between mb-3">
        <div class="flex items-center">
          <div class="w-1 h-6 bg-primary rounded-full mr-3" />
          <h1 class="text-xl font-bold text-gray-900 dark:text-white">
            插件管理
          </h1>
        </div>
        <span class="text-sm text-gray-600 dark:text-gray-400">
          共 {{ filteredPlugins.length }} 个插件
        </span>
      </div>

      <!-- 工具栏 -->
      <div
        class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4"
      >
        <!-- 操作按钮 -->
        <div class="flex items-center space-x-3">
          <el-input
            v-model="searchQuery"
            placeholder="搜索插件名称或ID..."
            clearable
            :prefix-icon="Search"
            @input="filterPlugins"
            class="w-64"
          />
          <el-button
            type="primary"
            size="small"
            :disabled="loading"
            @click="showUploadDialog"
          >
            <el-icon size="14" class="mr-1"><Upload /></el-icon>
            上传插件
          </el-button>
        </div>
      </div>
    </div>

    <!-- 插件列表容器 -->
    <div
      class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-3 flex-1 min-h-0"
    >
      <!-- 加载状态 -->
      <div v-if="loading" class="flex justify-center items-center h-full">
        <div class="text-center">
          <el-icon size="32" class="text-blue-500 animate-spin mb-4">
            <Loading />
          </el-icon>
          <p class="text-gray-500 dark:text-gray-400">正在加载插件列表...</p>
        </div>
      </div>

      <!-- 空状态 -->
      <div
        v-else-if="filteredPlugins.length === 0"
        class="flex justify-center items-center h-full"
      >
        <div class="text-center">
          <div
            class="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-3"
          >
            <el-icon size="20" class="text-gray-400"><Box /></el-icon>
          </div>
          <h3 class="text-base font-medium text-gray-900 dark:text-white mb-2">
            {{ searchQuery ? "未找到相关插件" : "暂无插件" }}
          </h3>
          <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
            {{ searchQuery ? "请尝试其他搜索关键词" : "开始上传您的第一个插件" }}
          </p>
          <el-button v-if="!searchQuery" type="primary" size="small" @click="showUploadDialog">
            <el-icon size="14" class="mr-1"><Upload /></el-icon>
            上传插件
          </el-button>
        </div>
      </div>

      <!-- 插件列表 -->
      <div
        v-else
        class="h-full overflow-auto"
      >
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4"
        >
          <div
            v-for="plugin in filteredPlugins"
            :key="plugin.pluginId"
            class="group bg-gray-50 dark:bg-gray-700 rounded-lg shadow-sm hover:shadow-md border border-gray-200 dark:border-gray-600 transition-all duration-200 overflow-hidden"
          >
            <!-- 卡片头部 -->
            <div class="p-4 pb-3">
              <div class="flex items-start justify-between mb-3">
                <div class="flex items-center flex-1 min-w-0">
                  <div
                    class="w-8 h-8 bg-primary rounded flex items-center justify-center mr-2 flex-shrink-0"
                  >
                    <el-icon size="16"><Box /></el-icon>
                  </div>
                  <div class="min-w-0 flex-1">
                    <h3
                      class="text-sm font-semibold text-gray-900 dark:text-white truncate"
                    >
                      {{ plugin.pluginName }}
                    </h3>
                    <p class="text-xs text-gray-500 dark:text-gray-400 truncate">
                      {{ plugin.pluginId }}
                    </p>
                  </div>
                </div>
                <el-tooltip placement="top">
                  <template #content>
                    <div class="text-xs">
                      <div>
                        应用名称: {{ plugin.applicationName || plugin.pluginName }}
                      </div>
                      <div>插件位置: {{ plugin.location }}</div>
                      <div>文件名称: {{ plugin.fileName }}</div>
                      <div>插件类名: {{ plugin.className }}</div>
                      <div>技术支持: {{ plugin.provider || "" }}</div>
                      <div>上传时间: {{ plugin.uploadDate }}</div>
                    </div>
                  </template>
                  <el-icon
                    size="14"
                    class="text-gray-400 hover:text-blue-500 cursor-pointer transition-colors"
                  >
                    <InfoFilled />
                  </el-icon>
                </el-tooltip>
              </div>

              <!-- 状态标签 -->
              <div class="flex gap-1 mb-3">
                <el-tag
                  :type="plugin.state === 'STARTED' ? 'success' : 'info'"
                  effect="light"
                  size="small"
                  class="border-0 text-xs"
                >
                  <div class="flex items-center">
                    <div
                      :class="[
                        'w-1.5 h-1.5 rounded-full mr-1',
                        plugin.state === 'STARTED'
                          ? 'bg-green-500 animate-pulse'
                          : 'bg-gray-400'
                      ]"
                    />
                    {{ plugin.state === "STARTED" ? "运行中" : "已停止" }}
                  </div>
                </el-tag>
                <el-tag
                  :type="plugin.enabled === 1 ? 'success' : 'danger'"
                  effect="light"
                  size="small"
                  class="border-0 text-xs"
                >
                  {{ plugin.enabled === 1 ? "已启用" : "已禁用" }}
                </el-tag>
              </div>

              <!-- 插件信息 -->
              <div class="space-y-1 text-xs">
                <div class="flex justify-between">
                  <span class="text-gray-500 dark:text-gray-400">版本</span>
                  <span class="text-gray-900 dark:text-white font-medium">
                    {{ plugin.version }}
                  </span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-500 dark:text-gray-400">启动时间</span>
                  <span class="text-gray-900 dark:text-white">
                    {{ plugin.bootTime || "-" }}
                  </span>
                </div>
              </div>
            </div>

            <!-- 卡片底部操作区 -->
            <div
              class="px-4 py-2 bg-gray-100 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-600"
            >
              <div class="flex justify-between items-center">
                <el-button
                  size="small"
                  type="primary"
                  text
                  class="text-blue-500 hover:text-blue-600 text-xs"
                  @click="handleViewDetail(plugin)"
                >
                  查看详情
                </el-button>
                <el-dropdown
                  trigger="click"
                  @command="(command: string) => handleCommand(command, plugin)"
                >
                  <el-button size="small" type="primary" class="text-xs">
                    操作
                    <el-icon size="12" class="ml-1"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item
                        v-if="plugin.state === 'STARTED'"
                        command="stop"
                      >
                        <el-icon size="14" class="mr-1 text-orange-500">
                          <VideoPause />
                        </el-icon>
                        停止
                      </el-dropdown-item>
                      <el-dropdown-item v-else command="start">
                        <el-icon size="14" class="mr-1 text-green-500"
                          ><VideoPlay
                        /></el-icon>
                        启动
                      </el-dropdown-item>
                      <el-dropdown-item
                        v-if="plugin.enabled === 1"
                        command="disable"
                      >
                        <el-icon size="14" class="mr-1 text-red-500"
                          ><Close
                        /></el-icon>
                        禁用
                      </el-dropdown-item>
                      <el-dropdown-item v-else command="enable">
                        <el-icon size="14" class="mr-1 text-blue-500"
                          ><Check
                        /></el-icon>
                        启用
                      </el-dropdown-item>
                      <el-dropdown-item command="uninstall" class="text-red-500">
                        <el-icon size="14" class="mr-1"><Delete /></el-icon>
                        卸载
                      </el-dropdown-item>
                      <el-dropdown-item command="detail">
                        <el-icon size="14" class="mr-1 text-blue-500"
                          ><View
                        /></el-icon>
                        详情
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传插件对话框 -->
    <el-dialog
      v-model="uploadDialogVisible"
      title="上传插件"
      width="450px"
      :close-on-click-modal="false"
    >
      <div class="space-y-3">
        <p class="text-sm text-gray-600 dark:text-gray-400">
          请选择要上传的插件文件，仅支持 .zip 格式的压缩包。
        </p>
        <el-upload
          class="upload-demo"
          drag
          action="#"
          :auto-upload="false"
          :on-change="handleFileChange"
          :show-file-list="false"
        >
          <div class="py-8 px-4 text-center">
            <el-icon size="28" class="text-gray-400 mb-3">
              <UploadFilled />
            </el-icon>
            <div class="text-sm text-gray-600 dark:text-gray-400 mb-1">
              拖拽文件到此处或 <span class="text-blue-500">点击上传</span>
            </div>
            <div class="text-xs text-gray-400">仅支持 .zip 文件</div>
          </div>
        </el-upload>
        <div
          v-if="uploadFile"
          class="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg"
        >
          <div class="flex items-center text-sm">
            <el-icon size="16" class="text-blue-500 mr-2"><Document /></el-icon>
            <span class="text-blue-700 dark:text-blue-300">
              {{ uploadFile.name }}
            </span>
            <span class="text-gray-500 ml-2">
              ({{ (uploadFile.size / 1024 / 1024).toFixed(2) }} MB)
            </span>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end gap-3">
          <el-button size="small" @click="uploadDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            size="small"
            :loading="uploading"
            :disabled="!uploadFile"
            @click="submitUpload"
          >
            {{ uploading ? "上传中..." : "确认上传" }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import {
  Upload,
  InfoFilled,
  ArrowDown,
  UploadFilled,
  Search,
  Loading,
  Box,
  VideoPause,
  VideoPlay,
  Close,
  Check,
  Delete,
  View,
  Document,
  CircleCheck,
  Folder,
  Clock
} from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  type PluginInfo,
  getPluginList,
  uploadPlugin,
  uninstallPlugin,
  startPlugin,
  stopPlugin,
  enablePlugin,
  disablePlugin
} from "@/api/plugin";
import { useRouter } from "vue-router";

// 加载状态
const loading = ref(true);
const uploading = ref(false);

// 搜索查询
const searchQuery = ref("");

// 插件列表数据
const plugins = ref<PluginInfo[]>([]);

// 当前上传的文件
const uploadFile = ref<File | null>(null);

// 过滤后的插件列表
const filteredPlugins = computed(() => {
  if (!searchQuery.value) return plugins.value;
  const query = searchQuery.value.toLowerCase();
  return plugins.value.filter(
    plugin =>
      plugin.pluginName.toLowerCase().includes(query) ||
      plugin.pluginId.toLowerCase().includes(query)
  );
});

// 当前选中的插件
const currentPlugin = ref<PluginInfo | null>(null);

// 对话框显示控制
const uploadDialogVisible = ref(false);
const detailDialogVisible = ref(false);

// 获取插件列表
const fetchPluginList = async () => {
  loading.value = true;
  try {
    const res = await getPluginList();
    if (res.state) {
      plugins.value = res.data;
    } else {
      ElMessage.error(res.err_msg || "获取插件列表失败");
    }
  } catch (error) {
    ElMessage.error("获取插件列表失败");
    console.error(error);
  } finally {
    loading.value = false;
  }
};

// 获取插件详情提示
const getPluginDetail = (plugin: PluginInfo) => {
  return [
    `应用名称: ${plugin.applicationName || plugin.pluginName}`,
    `插件位置: ${plugin.location}`,
    `文件名称: ${plugin.fileName}`,
    `插件类名: ${plugin.className}`,
    `技术支持: ${plugin.provider || ""}`,
    `上传时间: ${plugin.uploadDate}`
  ].join("\n");
};

// 搜索过滤
const filterPlugins = () => {
  // 搜索逻辑已通过计算属性实现
};

// 上传插件
const showUploadDialog = () => {
  uploadDialogVisible.value = true;
};

// 文件变更处理
const handleFileChange = (file: any) => {
  uploadFile.value = file.raw;
};

// 提交上传
const submitUpload = async () => {
  if (!uploadFile.value) {
    ElMessage.warning("请选择要上传的插件文件");
    return;
  }

  if (!uploadFile.value.name.endsWith(".zip")) {
    ElMessage.warning("仅支持 .zip 格式的插件文件");
    return;
  }

  uploading.value = true;
  try {
    const res = await uploadPlugin(uploadFile.value);
    if (res.state) {
      ElMessage.success("插件上传成功");
      uploadDialogVisible.value = false;
      // 刷新插件列表
      fetchPluginList();
    } else {
      ElMessage.error(res.err_msg || "插件上传失败");
    }
  } catch (error) {
    ElMessage.error("插件上传失败");
    console.error(error);
  } finally {
    uploading.value = false;
  }
};

// 查看详情
const handleViewDetail = (plugin: PluginInfo) => {
  router.push({
    name: "PluginManagementDetail",
    params: {
      pluginId: plugin.pluginId
    }
  });
};

// 处理操作命令
const handleCommand = async (command: string, plugin: PluginInfo) => {
  switch (command) {
    case "detail":
      handleViewDetail(plugin);
      break;
    case "start":
      ElMessageBox.confirm(`确定要启动插件 ${plugin.pluginName} 吗?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(async () => {
          try {
            const res = await startPlugin(plugin.pluginId);
            if (res.state) {
              ElMessage.success("插件已启动");
              // 刷新页面
              setTimeout(() => {
                window.location.reload();
              }, 1000);
            } else {
              ElMessage.error(res.err_msg || "启动插件失败");
            }
          } catch (error) {
            ElMessage.error("启动插件失败");
            console.error(error);
          }
        })
        .catch(() => {});
      break;
    case "stop":
      ElMessageBox.confirm(`确定要停止插件 ${plugin.pluginName} 吗?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(async () => {
          try {
            const res = await stopPlugin(plugin.pluginId);
            if (res.state) {
              ElMessage.success("插件已停止");
              // 刷新页面
              setTimeout(() => {
                window.location.reload();
              }, 1000);
            } else {
              ElMessage.error(res.err_msg || "停止插件失败");
            }
          } catch (error) {
            ElMessage.error("停止插件失败");
            console.error(error);
          }
        })
        .catch(() => {});
      break;
    case "disable":
      ElMessageBox.confirm(`确定要禁用插件 ${plugin.pluginName} 吗?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(async () => {
          try {
            const res = await disablePlugin(plugin.pluginId);
            if (res.state) {
              ElMessage.success("插件已禁用");
              // 刷新页面
              setTimeout(() => {
                window.location.reload();
              }, 1000);
            } else {
              ElMessage.error(res.err_msg || "禁用插件失败");
            }
          } catch (error) {
            ElMessage.error("禁用插件失败");
            console.error(error);
          }
        })
        .catch(() => {});
      break;
    case "enable":
      ElMessageBox.confirm(`确定要启用插件 ${plugin.pluginName} 吗?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(async () => {
          try {
            const res = await enablePlugin(plugin.pluginId);
            if (res.state) {
              ElMessage.success("插件已启用");
              // 刷新页面
              setTimeout(() => {
                window.location.reload();
              }, 1000);
            } else {
              ElMessage.error(res.err_msg || "启用插件失败");
            }
          } catch (error) {
            ElMessage.error("启用插件失败");
            console.error(error);
          }
        })
        .catch(() => {});
      break;
    case "uninstall":
      ElMessageBox.confirm(`确定要卸载插件 ${plugin.pluginName} 吗?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(async () => {
          try {
            const res = await uninstallPlugin(plugin.pluginId);
            if (res.state) {
              ElMessage.success("插件已卸载");
              // 刷新页面
              setTimeout(() => {
                window.location.reload();
              }, 1000);
            } else {
              ElMessage.error(res.err_msg || "卸载插件失败");
            }
          } catch (error) {
            ElMessage.error("卸载插件失败");
            console.error(error);
          }
        })
        .catch(() => {});
      break;
  }
};

// 组件挂载时获取插件列表
onMounted(() => {
  fetchPluginList();
});

const router = useRouter();
</script>
