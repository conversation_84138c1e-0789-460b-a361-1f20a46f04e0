import { http } from "@/utils/http";

// 临时导入mock数据用于开发测试
import {
  mockPlugins,
  getMockPluginDetail,
  updateMockPluginStatus
} from "@/views/stream-computation/stream-plugin-management/mock-data";

// 端口配置接口
export interface PortConfig {
  label: string; // 端口标签
  letId?: number; // 端口ID
  letName?: string; // 端口名称
  dataType?: string[]; // 支持的数据类型
  maxFan?: number; // 最大连接数
  dynamic?: boolean; // 是否动态端口
}

// 节点数据结构
export interface ShapeNode {
  id: string; // 节点唯一标识
  name: string; // 节点名称
  description: string; // 节点描述
  author?: string; // 作者
  version?: string; // 版本
  tags?: string[]; // 标签
  document?: string; // 文档内容（Markdown格式）
  inputs: PortConfig[]; // 输入端口配置
  outputs: PortConfig[]; // 输出端口配置
  icon?: any; // 节点图标
  color?: string; // 节点颜色
  originalData?: any; // 原始数据
}

// 插件状态枚举
export enum PluginStatus {
  ENABLED = "enabled", // 启用
  DISABLED = "disabled" // 禁用
}

// 插件数据结构
export interface Plugin {
  id: string; // 插件唯一标识
  name: string; // 插件名称
  description: string; // 插件描述
  version: string; // 版本号
  author?: string; // 作者
  createTime?: string; // 创建时间
  status: PluginStatus; // 插件状态
  nodes: ShapeNode[]; // 包含的节点列表
}

// API响应格式
export type ApiResponse<T> = {
  state: boolean;
  timestamp: number;
  data: T;
  err_msg: string | null;
  err_code: string | null;
};

// 开发模式标志
const isDevelopment = import.meta.env.DEV;

/**
 * 获取插件列表
 */
export const getPluginList = (): Promise<ApiResponse<Plugin[]>> => {
  if (isDevelopment) {
    // 开发环境使用mock数据
    return new Promise(resolve => {
      setTimeout(() => {
        resolve({
          state: true,
          timestamp: Date.now(),
          data: mockPlugins,
          err_msg: null,
          err_code: null
        });
      }, 800); // 模拟网络延迟
    });
  }
  return http.request<ApiResponse<Plugin[]>>("get", "/api/thing/plugins/list");
};

/**
 * 获取插件详情
 * @param pluginId 插件ID
 */
export const getPluginDetail = (
  pluginId: string
): Promise<ApiResponse<Plugin>> => {
  if (isDevelopment) {
    // 开发环境使用mock数据
    return new Promise(resolve => {
      setTimeout(() => {
        const plugin = getMockPluginDetail(pluginId);
        if (plugin) {
          resolve({
            state: true,
            timestamp: Date.now(),
            data: plugin,
            err_msg: null,
            err_code: null
          });
        } else {
          resolve({
            state: false,
            timestamp: Date.now(),
            data: {} as Plugin,
            err_msg: "插件不存在",
            err_code: "PLUGIN_NOT_FOUND"
          });
        }
      }, 600);
    });
  }
  return http.request<ApiResponse<Plugin>>(
    "get",
    `/api/thing/plugins/${pluginId}`
  );
};

/**
 * 上传安装插件
 * @param file 插件文件
 */
export const uploadPlugin = (file: File): Promise<ApiResponse<any>> => {
  if (isDevelopment) {
    // 开发环境模拟上传
    return new Promise(resolve => {
      setTimeout(() => {
        // 模拟上传成功
        resolve({
          state: true,
          timestamp: Date.now(),
          data: { pluginId: `uploaded-${Date.now()}` },
          err_msg: null,
          err_code: null
        });
      }, 2000); // 模拟上传耗时
    });
  }
  const formData = new FormData();
  formData.append("file", file);
  return http.request<ApiResponse<any>>("post", "/api/thing/plugins/install", {
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
};

/**
 * 卸载插件
 * @param pluginId 插件ID
 */
export const uninstallPlugin = (
  pluginId: string
): Promise<ApiResponse<any>> => {
  if (isDevelopment) {
    // 开发环境模拟卸载
    return new Promise(resolve => {
      setTimeout(() => {
        resolve({
          state: true,
          timestamp: Date.now(),
          data: { message: "卸载成功" },
          err_msg: null,
          err_code: null
        });
      }, 1000);
    });
  }
  return http.request<ApiResponse<any>>(
    "delete",
    `/api/thing/plugins/${pluginId}`
  );
};

/**
 * 获取插件的节点列表
 * @param pluginId 插件ID
 */
export const getPluginNodes = (pluginId: string) => {
  return http.request<ApiResponse<ShapeNode[]>>(
    "get",
    `/api/thing/plugins/${pluginId}/nodes`
  );
};

/**
 * 获取节点详细信息
 * @param nodeId 节点ID
 */
export const getNodeDetail = (nodeId: string) => {
  return http.request<ApiResponse<ShapeNode>>(
    "get",
    `/api/thing/nodes/${nodeId}`
  );
};

/**
 * 启用插件
 * @param pluginId 插件ID
 */
export const enablePlugin = (pluginId: string): Promise<ApiResponse<any>> => {
  if (isDevelopment) {
    // 开发环境模拟启用
    return new Promise(resolve => {
      setTimeout(() => {
        const success = updateMockPluginStatus(pluginId, PluginStatus.ENABLED);
        resolve({
          state: success,
          timestamp: Date.now(),
          data: { message: success ? "插件启用成功" : "插件不存在" },
          err_msg: success ? null : "插件不存在",
          err_code: success ? null : "PLUGIN_NOT_FOUND"
        });
      }, 800);
    });
  }
  return http.request<ApiResponse<any>>(
    "put",
    `/api/thing/plugins/${pluginId}/enable`
  );
};

/**
 * 禁用插件
 * @param pluginId 插件ID
 */
export const disablePlugin = (pluginId: string): Promise<ApiResponse<any>> => {
  if (isDevelopment) {
    // 开发环境模拟禁用
    return new Promise(resolve => {
      setTimeout(() => {
        const success = updateMockPluginStatus(pluginId, PluginStatus.DISABLED);
        resolve({
          state: success,
          timestamp: Date.now(),
          data: { message: success ? "插件禁用成功" : "插件不存在" },
          err_msg: success ? null : "插件不存在",
          err_code: success ? null : "PLUGIN_NOT_FOUND"
        });
      }, 800);
    });
  }
  return http.request<ApiResponse<any>>(
    "put",
    `/api/thing/plugins/${pluginId}/disable`
  );
};
