import { http } from "@/utils/http";
import type { ApiResponse } from "./account";

// 角色数据模型
export interface RoleInfo {
  roleId: number;
  roleName: string;
  description?: string;
}

// 创建角色请求参数
export interface CreateRoleRequest {
  roleName: string;
  description?: string;
}

// 更新角色请求参数
export interface UpdateRoleRequest {
  roleId: number;
  roleName: string;
  description?: string;
}

// 获取所有角色列表
export const getAllRoles = () => {
  return http.request<ApiResponse<RoleInfo[]>>("get", "/api/thing/role/all");
};

// 获取角色信息
export const getRoleInfo = (roleId: number) => {
  return http.request<ApiResponse<RoleInfo>>("get", `/api/thing/role/${roleId}`);
};

// 获取用户角色
export const getUserRoles = (userId: number) => {
  return http.request<ApiResponse<RoleInfo[]>>("get", `/api/thing/role/user/${userId}`);
};

// 创建角色
export const createRole = (data: CreateRoleRequest) => {
  return http.request<ApiResponse<RoleInfo>>("post", "/api/thing/role/create", {
    data
  });
};

// 更新角色
export const updateRole = (data: UpdateRoleRequest) => {
  return http.request<ApiResponse<RoleInfo>>("put", "/api/thing/role/update", {
    data
  });
};

// 删除角色
export const deleteRole = (roleId: number) => {
  return http.request<ApiResponse<boolean>>("delete", `/api/thing/role/${roleId}`);
};