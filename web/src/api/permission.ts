import { http } from "@/utils/http";
import type { ApiResponse } from "./account";

// 菜单树数据模型
export interface MenuTreeItem {
  menuItemId: number;
  menuItemName: string;
  parentMenuItemId: number;
  path: string;
  fullPath?: string;
  pluginId: string;
  idPath?: string;
  layoutPosition?: number;
  children?: MenuTreeItem[];
}

// 区域数据模型
export interface RegionItem {
  regionId: number;
  parentId?: number;
  regionName: string;
  description?: string;
  displayIndex?: number;
  children?: RegionItem[];
  items?: RegionResourceItem[];
}

// 区域项数据模型
export interface RegionResourceItem {
  regionItemId: number;
  regionId: number;
  itemName: string;
  pluginId: string;
  itemType: string;
}

// 角色权限映射请求参数
export interface RolePermissionMapRequest {
  pluginId: string;
  roleId: number;
  bindIds: string; // 逗号分隔的ID列表
}

// 获取完整菜单树（无权限过滤）
export const getFullMenuTree = () => {
  return http.request<ApiResponse<MenuTreeItem>>("get", "/api/thing/permission/menutree");
};

// 根据角色ID获取菜单权限
export const getMenuPermissionsByRoleId = (roleId: number) => {
  return http.request<ApiResponse<MenuTreeItem[]>>("get", "/api/thing/permission/permmenutreebyroleid", {
    params: { roleId }
  });
};

// 根据用户ID获取菜单树
export const getMenuTreeByUserId = (userId: number) => {
  return http.request<ApiResponse<MenuTreeItem>>("get", "/api/thing/permission/menutreebyuserid", {
    params: { userId }
  });
};

// 角色菜单权限授权
export const assignMenuPermission = (data: RolePermissionMapRequest) => {
  return http.request<ApiResponse<number>>("post", "/api/thing/permission/rolemenumap", {
    data
  });
};

// 根据角色ID获取区域权限
export const getRegionPermissionsByRoleId = (roleId: number) => {
  return http.request<ApiResponse<RegionItem[]>>("get", "/api/thing/permission/permregiontreebyroleid", {
    params: { roleId }
  });
};

// 根据用户ID获取区域树
export const getRegionTreeByUserId = (pluginId?: string) => {
  const params = pluginId ? { pluginId } : {};
  return http.request<ApiResponse<RegionItem[]>>("get", "/api/thing/permission/regiontreebyuserid", {
    params
  });
};

// 角色区域权限授权
export const assignRegionPermission = (data: RolePermissionMapRequest) => {
  return http.request<ApiResponse<number>>("post", "/api/thing/permission/roleregionmap", {
    data
  });
};