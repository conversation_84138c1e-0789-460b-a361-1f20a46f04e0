import { http } from "@/utils/http";

// API响应通用格式
export interface ApiResponse<T = any> {
  state: boolean;
  code: number;
  data: T;
  err_msg?: string;
  err_code?: string;
  timestamp: number;
}

// 用户账号数据模型
export interface AccountInfo {
  userId: number;
  userName: string;
  loginId: string;
  enable: boolean;
  maxError: number;
  locked: boolean;
  validTime?: string;
  passwordValidTime?: string;
  description?: string;
  roleIds?: string;
  alias?: string;
  loginType?: string;
}

// 创建用户请求参数
export interface CreateAccountRequest {
  userName: string;
  loginId: string;
  password: string;
  enable?: boolean;
  maxError?: number;
  validTime?: string;
  passwordValidTime?: string;
  description?: string;
  roleIds?: string;
  alias?: string;
  loginType?: string;
}

// 更新用户请求参数
export interface UpdateAccountRequest {
  userId: number;
  userName?: string;
  enable?: boolean;
  maxError?: number;
  validTime?: string;
  passwordValidTime?: string;
  description?: string;
  roleIds?: string;
  alias?: string;
}

// 修改密码请求参数
export interface EditPasswordRequest {
  userId: number;
  oldPassword: string;
  newPassword: string;
}

// 重置密码请求参数
export interface ResetPasswordRequest {
  userId: number;
  newPassword: string;
}

// 登录请求参数
export interface LoginRequest {
  requestId: string; // Base64编码的"username:password"
  loginType?: string;
}

// 登录响应数据
export interface LoginResult {
  token: string;
  UserName: string;
  LoginId: string;
  role: string;
  userId: number;
  personId: number;
}

// 获取所有用户列表
export const getAllAccounts = () => {
  return http.request<ApiResponse<AccountInfo[]>>("get", "/api/thing/account/allaccounts");
};

// 获取用户信息
export const getAccountInfo = (userId: number) => {
  return http.request<ApiResponse<AccountInfo>>("get", "/api/thing/account/accountinfo", {
    params: { userId }
  });
};

// 创建用户账号
export const createAccount = (data: CreateAccountRequest) => {
  return http.request<ApiResponse<AccountInfo>>("post", "/api/thing/account/createaccount", {
    data
  });
};

// 更新用户账号
export const updateAccount = (data: UpdateAccountRequest) => {
  return http.request<ApiResponse<AccountInfo>>("put", "/api/thing/account/updateaccount", {
    data
  });
};

// 修改用户密码
export const editPassword = (data: EditPasswordRequest) => {
  return http.request<ApiResponse<boolean>>("post", "/api/thing/account/editpassword", {
    data
  });
};

// 重置用户密码
export const resetPassword = (data: ResetPasswordRequest) => {
  return http.request<ApiResponse<boolean>>("post", "/api/thing/account/resetpassword", {
    data
  });
};

// 启用/停用用户账号
export const editEnable = (userId: number, enable: boolean) => {
  return http.request<ApiResponse<boolean>>("put", "/api/thing/account/eidtenable", {
    params: { userId, enable }
  });
};

// 删除用户账号
export const deleteAccount = (userId: number) => {
  return http.request<ApiResponse<boolean>>("delete", `/api/thing/account/deleteaccount/${userId}`);
};

// 用户登录（更新现有登录方法以支持TCS2 API格式）
export const userLogin = (data: LoginRequest) => {
  return http.request<ApiResponse<LoginResult>>("post", "/api/thing/login", { data });
};

// 辅助函数：创建Base64编码的登录凭据
export const createLoginCredentials = (username: string, password: string): string => {
  const credentials = `${username}:${password}`;
  return btoa(credentials);
};