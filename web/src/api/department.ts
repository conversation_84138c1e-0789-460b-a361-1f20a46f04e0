import { http } from "@/utils/http";

// API响应通用格式
export interface ApiResponse<T = any> {
  state: boolean;
  code: number;
  data: T;
  err_msg?: string;
  err_code?: string;
  timestamp: number;
}

// 部门数据模型
export interface DepartmentInfo {
  id: number;
  name: string;
  code: string;
  parentId?: number;
  parentName?: string;
  leader?: string;
  phone?: string;
  email?: string;
  status: number;
  sort: number;
  createTime: string;
  updateTime?: string;
  remark?: string;
  children?: DepartmentInfo[] | null;
  hasChildren?: boolean;
  _switching?: boolean; // 用于控制状态切换时的loading状态
}

// 创建部门请求参数
export interface CreateDepartmentRequest {
  name: string;
  code: string;
  parentId?: number;
  leader?: string;
  phone?: string;
  email?: string;
  status?: number;
  sort?: number;
  remark?: string;
}

// 更新部门请求参数
export interface UpdateDepartmentRequest {
  id: number;
  name?: string;
  code?: string;
  parentId?: number;
  leader?: string;
  phone?: string;
  email?: string;
  status?: number;
  sort?: number;
  remark?: string;
}

// 更新部门状态请求参数
export interface UpdateDepartmentStatusRequest {
  id: number;
  status: number;
  cascadeChildren?: boolean;
}

// 获取部门树形结构列表
export const getDepartmentTree = (params?: { status?: number; name?: string }) => {
  return http.request<ApiResponse<DepartmentInfo[]>>("get", "/api/thing/department/tree", {
    params
  });
};

// 获取所有部门列表（扁平结构，用于下拉选择）
export const getAllDepartments = () => {
  return http.request<ApiResponse<DepartmentInfo[]>>("get", "/api/thing/department/list");
};

// 根据ID获取部门详情
export const getDepartmentById = (id: number) => {
  return http.request<ApiResponse<DepartmentInfo>>("get", `/api/thing/department/${id}`);
};

// 创建新部门
export const createDepartment = (data: CreateDepartmentRequest) => {
  return http.request<ApiResponse<DepartmentInfo>>("post", "/api/thing/department/create", {
    data
  });
};

// 更新部门信息
export const updateDepartment = (data: UpdateDepartmentRequest) => {
  return http.request<ApiResponse<DepartmentInfo>>("put", "/api/thing/department/update", {
    data
  });
};

// 更新部门状态
export const updateDepartmentStatus = (data: UpdateDepartmentStatusRequest) => {
  return http.request<ApiResponse<boolean>>("put", "/api/thing/department/status", {
    data
  });
};

// 删除部门
export const deleteDepartment = (id: number) => {
  return http.request<ApiResponse<boolean>>("delete", `/api/thing/department/${id}`);
};

// 检查部门编码是否存在
export const checkCodeExists = (code: string, excludeId?: number) => {
  return http.request<ApiResponse<boolean>>("get", "/api/thing/department/check-code", {
    params: { code, excludeId }
  });
};

// 检查部门是否有子部门
export const hasChildren = (parentId: number) => {
  return http.request<ApiResponse<boolean>>("get", `/api/thing/department/has-children/${parentId}`);
};

// 获取部门的所有子部门ID（递归）
export const getAllChildrenIds = (parentId: number) => {
  return http.request<ApiResponse<number[]>>("get", `/api/thing/department/children-ids/${parentId}`);
};

// 验证父部门关系是否合法（避免循环引用）
export const validateParentRelation = (departmentId: number, parentId: number) => {
  return http.request<ApiResponse<boolean>>("get", "/api/thing/department/validate-parent", {
    params: { departmentId, parentId }
  });
};