export default {
  path: "/system-management",
  name: "SystemManagement",
  redirect: "/system-management/plugin-management",
  meta: {
    icon: "ep:monitor",
    title: "系统管理",
    rank: 10
  },
  children: [
    {
      path: "/system-management/plugin-management",
      name: "PluginManagementPage",
      component: () =>
        import("@/views/system-management/plugin-management/index.vue"),
      meta: {
        icon: "mingcute:plugin-2-line",
        showParent: true,
        title: "插件管理"
      }
    },
    {
      path: "/system-management/plugin-management/detail/:pluginId",
      name: "PluginManagementDetail",
      component: () =>
        import("@/views/system-management/plugin-management/detail/index.vue"),
      meta: {
        title: "插件详情",
        showLink: false,
        activePath: "/system-management/plugin-management"
      }
    },
    {
      path: "/system-management/user-management",
      name: "UserManagement",
      component: () =>
        import("@/views/system-management/user-management/index.vue"),
      meta: {
        icon: "ep:user",
        showParent: true,
        title: "用户管理",
        auths: ["system:user:list"]
      }
    },
    {
      path: "/system-management/department-management",
      name: "DepartmentManagement",
      component: () =>
        import("@/views/system-management/department-management/index.vue"),
      meta: {
        icon: "ep:office-building",
        showParent: true,
        title: "部门管理",
        auths: ["system:dept:list"]
      }
    },
    {
      path: "/system-management/role-management",
      name: "RoleManagement",
      component: () =>
        import("@/views/system-management/role-management/index.vue"),
      meta: {
        icon: "ep:user-filled",
        showParent: true,
        title: "角色管理",
        auths: ["system:role:list"]
      }
    },
    {
      path: "/system-management/middleware-management",
      name: "MiddlewareManagement",
      component: () => 
        import("@/views/system-management/middleware-management/index.vue"),
      meta: {
        icon: "mingcute:package-2-line",
        showParent: true,
        title: "中间件管理"
      },
      children: [
        {
          path: "/system-management/middleware-management/resource-management",
          name: "ResourceManagement",
          component: () => 
            import("@/views/system-management/middleware-management/resource-management/index.vue"),
          meta: {
            title: "资源管理"
          }
        },
        {
          path: "/system-management/middleware-management/resource-management/new",
          name: "ResourceManagementNew",
          component: () => 
            import("@/views/system-management/middleware-management/resource-management/form.vue"),
          meta: {
            title: "新增资源配置",
            showLink: false,
            activePath: "/system-management/middleware-management/resource-management"
          }
        },
        {
          path: "/system-management/middleware-management/resource-management/:id/edit",
          name: "ResourceManagementEdit",
          component: () => 
            import("@/views/system-management/middleware-management/resource-management/form.vue"),
          meta: {
            title: "编辑资源配置",
            showLink: false,
            activePath: "/system-management/middleware-management/resource-management"
          }
        },
        {
          path: "/system-management/middleware-management/resource-management/:id/detail",
          name: "ResourceManagementDetail",
          component: () => 
            import("@/views/system-management/middleware-management/resource-management/detail.vue"),
          meta: {
            title: "资源详情",
            showLink: false,
            activePath: "/system-management/middleware-management/resource-management"
          }
        },
        {
          path: "/system-management/middleware-management/service-management",
          name: "ServiceManagement",
          component: () => 
            import("@/views/system-management/middleware-management/service-management/index.vue"),
          meta: {
            title: "服务管理"
          }
        },
        {
          path: "/system-management/middleware-management/service-management/new",
          name: "ServiceManagementNew",
          component: () => 
            import("@/views/system-management/middleware-management/service-management/form.vue"),
          meta: {
            title: "新增服务配置",
            showLink: false,
            activePath: "/system-management/middleware-management/service-management"
          }
        },
        {
          path: "/system-management/middleware-management/service-management/:id/edit",
          name: "ServiceManagementEdit",
          component: () => 
            import("@/views/system-management/middleware-management/service-management/form.vue"),
          meta: {
            title: "编辑服务配置",
            showLink: false,
            activePath: "/system-management/middleware-management/service-management"
          }
        },
        {
          path: "/system-management/middleware-management/service-management/:id/detail",
          name: "ServiceManagementDetail",
          component: () => 
            import("@/views/system-management/middleware-management/service-management/detail.vue"),
          meta: {
            title: "服务详情",
            showLink: false,
            activePath: "/system-management/middleware-management/service-management"
          }
        }
      ]
    }
  ]
} satisfies RouteConfigsTable;
