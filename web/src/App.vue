<template>
  <el-config-provider :locale="currentLocale">
    <router-view />
    <ReDialog />
    <!-- Stagewise工具栏，仅在开发模式下显示 -->
    <StagewiseToolbar v-if="isDev" :config="stageWiseConfig" />
  </el-config-provider>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import { ElConfigProvider } from "element-plus";
import { ReDialog } from "@/components/ReDialog";
import en from "element-plus/es/locale/lang/en";
import zhCn from "element-plus/es/locale/lang/zh-cn";
// 仅在开发模式下导入stagewise相关组件
import { StagewiseToolbar } from "@stagewise/toolbar-vue";
import { VuePlugin } from "@stagewise-plugins/vue";

export default defineComponent({
  name: "app",
  components: {
    [ElConfigProvider.name]: ElConfigProvider,
    ReDialog,
    ...(import.meta.env.DEV ? { StagewiseToolbar } : {})
  },
  data() {
    return {
      // stagewise配置
      stageWiseConfig: {
        plugins: [VuePlugin]
      }
    };
  },
  computed: {
    currentLocale() {
      return this.$storage.locale?.locale === "zh" ? zhCn : en;
    },
    // 检查是否为开发模式
    isDev() {
      return import.meta.env.DEV;
    }
  }
});
</script>
