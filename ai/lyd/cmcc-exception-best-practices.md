# TCS 异常框架详细说明文档

## 1. 异常框架概述

本包提供了一个全面的 TCS 系统异常框架。它被设计为灵活、可扩展，并支持系统的插件架构。异常框架是系统稳定性和可维护性的重要组成部分，通过规范化的异常处理机制，可以提高系统的健壮性和用户体验。

### 1.1 设计目标

- **统一性**：提供统一的异常处理机制，确保系统各部分的异常处理一致
- **可扩展性**：支持自定义异常和错误码，适应不同业务场景
- **可追溯性**：异常包含足够的上下文信息，便于问题定位
- **用户友好**：向最终用户提供清晰、有用的错误信息
- **插件支持**：为插件系统提供专门的异常处理机制

### 1.2 核心功能

- 分层的异常类型（业务异常、技术异常、插件异常）
- 标准化的错误码系统
- 全局异常处理
- 异常转换工具
- 插件异常支持

## 2. 异常层次结构详解

异常层次结构设计遵循了关注点分离原则，将异常分为不同类型，以便于处理和管理。
位于：src/main/java/com/siteweb/tcs/common/exception/code

### 2.1 基础异常类


- **`TCSException`** - 所有 TCS 异常的基础异常
  - 作用：作为系统中所有自定义异常的基类
  - 属性：
    - `code`：错误码
    - `message`：错误消息
    - `details`：错误详情
    - `source`：错误来源
  - 应用场景：通常不直接使用，而是使用其子类

### 2.2 业务异常

- **`BusinessException`** - 用于业务逻辑相关的异常
  - 作用：表示业务规则验证失败、业务操作无法完成等业务层面的问题
  - 特点：通常可以由用户操作解决，应向用户提供明确的错误信息
  - 应用场景：
    - 数据验证失败
    - 业务规则冲突
    - 资源不存在
    - 权限不足
    - 业务操作失败

### 2.3 技术异常

- **`TechnicalException`** - 用于技术/系统相关的异常
  - 作用：表示系统内部错误、基础设施问题等技术层面的问题
  - 特点：通常需要系统管理员或开发人员介入解决
  - 应用场景：
    - 数据库连接失败
    - 网络通信错误
    - 文件系统错误
    - 配置错误
    - 外部服务调用失败

### 2.4 插件异常

- **`PluginException`** - 用于插件相关的异常
  - 作用：表示插件加载、初始化、执行过程中的通用错误
  - 特点：包含插件ID和名称信息，便于定位问题
  - 应用场景：
    - 插件加载失败
    - 插件初始化错误
    - 插件执行错误

- **`PluginBusinessException`** - 用于插件中的业务逻辑相关异常
  - 作用：表示插件中的业务规则验证失败、业务操作无法完成等
  - 特点：继承自BusinessException，额外包含插件信息
  - 应用场景：插件执行业务逻辑时的业务错误

- **`PluginTechnicalException`** - 用于插件中的技术/系统相关异常
  - 作用：表示插件中的系统内部错误、基础设施问题等
  - 特点：继承自TechnicalException，额外包含插件信息
  - 应用场景：插件执行过程中的技术错误

## 3. 错误码系统详解

错误码系统提供了一种标准化的方式来表示和分类错误，便于错误处理和问题追踪。

## 使用示例

### 创建业务异常

```java
// 使用标准错误码
BusinessException ex = StandardBusinessErrorCode.DATA_NOT_FOUND.toException();

// 带详情
BusinessException ex = StandardBusinessErrorCode.DATA_NOT_FOUND.toException("用户 ID: 123");

// 带原因
BusinessException ex = StandardBusinessErrorCode.DATA_NOT_FOUND.toException(cause);

// 带详情和原因
BusinessException ex = StandardBusinessErrorCode.DATA_NOT_FOUND.toException("用户 ID: 123", cause);

// 使用构造函数
BusinessException ex = new BusinessException("DATA_NOT_FOUND", "数据未找到");
```

### 创建技术异常

```java
// 使用标准错误码
TechnicalException ex = StandardTechnicalErrorCode.DATABASE_ERROR.toException();

// 带组件
TechnicalException ex = StandardTechnicalErrorCode.DATABASE_ERROR.toException("UserRepository");

// 带详情
TechnicalException ex = StandardTechnicalErrorCode.DATABASE_ERROR.toException("连接失败");

// 带原因
TechnicalException ex = StandardTechnicalErrorCode.DATABASE_ERROR.toException(cause);

// 带详情和原因
TechnicalException ex = StandardTechnicalErrorCode.DATABASE_ERROR.toException("连接失败", cause);

// 带详情、原因和组件
TechnicalException ex = StandardTechnicalErrorCode.DATABASE_ERROR.toException("连接失败", cause, "UserRepository");
```

### 创建插件异常

```java
// 使用标准错误码
PluginException ex = StandardPluginErrorCode.PLUGIN_LOAD_ERROR.toException("my-plugin");

// 带插件名称
PluginException ex = StandardPluginErrorCode.PLUGIN_LOAD_ERROR.toException("my-plugin", "我的插件");

// 带详情
PluginException ex = StandardPluginErrorCode.PLUGIN_LOAD_ERROR.toException("类未找到", "my-plugin");

// 带原因
PluginException ex = StandardPluginErrorCode.PLUGIN_LOAD_ERROR.toException(cause, "my-plugin");

// 带详情和原因
PluginException ex = StandardPluginErrorCode.PLUGIN_LOAD_ERROR.toException("类未找到", cause, "my-plugin");

// 带详情、原因、插件 ID 和插件名称
PluginException ex = StandardPluginErrorCode.PLUGIN_LOAD_ERROR.toException("类未找到", cause, "my-plugin", "我的插件");
```

### 使用插件异常工厂

```java
// 创建插件异常
PluginException ex = PluginExceptionFactory.createPluginException(StandardPluginErrorCode.PLUGIN_LOAD_ERROR, plugin);

// 创建插件业务异常
PluginBusinessException ex = PluginExceptionFactory.createBusinessException(StandardBusinessErrorCode.DATA_NOT_FOUND, plugin);

// 创建插件技术异常
PluginTechnicalException ex = PluginExceptionFactory.createTechnicalException(StandardTechnicalErrorCode.DATABASE_ERROR, plugin);
```

### 使用异常转换器

```java
try {
    // 可能抛出异常的代码
} catch (Throwable throwable) {
    throw ExceptionTranslator.translate(throwable);
}
```

### 使用异常工具

```java
// 执行函数并转换任何异常
User user = ExceptionUtils.executeAndTranslate(() -> userRepository.findById(id));

// 执行函数并将任何异常转换为业务异常
User user = ExceptionUtils.executeAndTranslateToBusiness(() -> userRepository.findById(id));

// 执行函数并将任何异常转换为技术异常
User user = ExceptionUtils.executeAndTranslateToTechnical(() -> userRepository.findById(id));
```