# 后端 i18n 规范

## 1. 简介

本规范定义了后端项目中基于命名空间的国际化（i18n）使用方式，旨在避免多模块翻译键冲突，确保模块化管理翻译资源。

## 2. 命名空间规范

### 2.1 格式

i18n 键格式：`{moduleNamespace}.{category}.{key}`

示例：`south.seed.common.add`
- `south.seed`：模块命名空间
- `common`：分类
- `add`：键名

### 2.2 模块命名空间

| 模块名称 | 命名空间前缀 |
|---------|------------|
| tcs-core | core |
| tcs-hub | hub |
| tcs-south-seed | south.seed |
| tcs-north-etl | north.etl |
| tcs-tracking | tracking |

新模块需遵循命名模式，确保无冲突。

## 3. 实现指南（Spring Boot）

### 3.1 创建 i18n 资源

在模块 `src/main/resources/i18n/` 下创建属性文件：

```properties
# messages_zh_CN.properties
your.module.category.key1=翻译1
your.module.category.key2=翻译2

# messages_en_US.properties
your.module.category.key1=Translation 1
your.module.category.key2=Translation 2
```

### 3.2 直接使用 MessageSource

```java
@Autowired
private MessageSource messageSource;

public String getMessage() {
  return messageSource.getMessage("your.module.category.key1", null, Locale.getDefault());
}
```

### 3.3 命名空间辅助类（推荐）

创建辅助类简化使用：

```java
@Component
public class NamespacedMessageSource {
  private final MessageSource messageSource;
  private final String namespace;

  public NamespacedMessageSource(MessageSource messageSource, String namespace) {
    this.messageSource = messageSource;
    this.namespace = namespace;
  }

  public String getMessage(String code) {
    return getMessage(code, null);
  }

  public String getMessage(String code, Object[] args) {
    return messageSource.getMessage(namespace + "." + code, args, Locale.getDefault());
  }
}
```

在服务中使用：

```java
@Service
public class YourModuleService {
  @Autowired
  private  NamespacedMessageSource messages;

  public void someMethod() {
    String message = messages.getMessage("category.key1");

    String dynamicKey = "your.module." + category + "." + key;
    String translation = messageSource.getMessage(dynamicKey, null, Locale.getDefault());

    String message = messageSource.getMessage("your.module.greeting", new Object[]{"John"}, Locale.getDefault());
  }
}
```

## 4. 最佳实践

- 使用分类（如 `common`, `error`, `menu`）组织键
- 保持键名一致性，嵌套不超过 3 层
- 翻译文本简洁，使用占位符处理变量
- 所有语言需完整翻译
- 避免硬编码文本，使用 i18n 系统
- 定期检查缺失翻译