# Stream库开发规范

## 开发工作流程规范

当拿到需求文档后，必须严格按照以下5个阶段进行工作，确保输出的准确性、完整性和高质量。

### 阶段1：需求分析与理解

#### 1.1 需求文档深度分析
- 仔细阅读需求文档的每一段，理解业务背景、技术要求、功能规格
- 识别并记录以下关键信息
  - 业务场景和应用背景
  - 数据输入/输出格式和协议
  - 性能要求和约束条件
  - 集成接口和依赖系统
  - 特殊处理逻辑和业务规则

#### 1.2 技术方案确认
- 确定使用的通信协议（HTTP/HTTPS, TCP, WebSocket等）
- 明确消息格式（XML, JSON, 二进制等）
- 分析与现有系统的集成方式
- 确定所需的第三方库和依赖

#### 1.3 功能分解和优先级
- 将需求拆解为具体的功能点和技术实现点
- 区分核心功能、扩展功能和可选功能
- 识别技术难点和潜在风险点

### 阶段2：架构设计与规划

#### 2.1 Shape功能设计
- 根据功能特性确定Shape的type名称
- 明确Shape的单一职责和核心功能
- 规划Shape的启动、运行、停止和错误处理流程

#### 2.2 消息结构设计
- 根据数据源确定输入消息的结构和字段
- 根据处理结果确定输出消息的结构
- 确定消息之间的继承关系和转换逻辑

#### 2.3 配置选项设计
- 确定Shape正常工作必须的配置项
- 设计性能调优和功能开关配置
- 为所有配置项设定合理的默认值
- 为配置项定义验证规则和约束条件

#### 2.4 端口和连接设计
- 确定输入端口的数量、类型和用途
- 确定输出端口的数量、类型和用途（包括错误输出）
- 设计Shape内部的数据处理流程

### 阶段3：代码实现与开发

#### 3.1 项目结构创建
- 按照标准模板创建完整的项目目录结构
- 配置Maven依赖和构建库
- 创建Shape、消息、配置等基础类的骨架

#### 3.2 核心功能实现
- Shape类实现，严格按照代码模板实现Shape类
  - 正确使用@Shape注解和相关注解
  - 实现所有必需的生命周期方法
  - 添加@Recoverable注解到需要恢复的字段
- 消息类实现，实现输入输出消息类
  - 使用正确的Jackson XML注解
  - 添加必要的验证注解
  - 实现完整的数据结构
- 配置类实现，实现配置选项类
  - 继承StreamShapeOption基类
  - 添加验证注解和自定义验证逻辑
  - 实现默认配置提供者

#### 3.3 业务逻辑实现
- 实现核心的业务处理逻辑
- 按照错误处理规范实现异常处理
- 正确管理临时资源和连接
- 应用性能最佳实践

#### 3.4 集成功能实现
- 实现协议相关的编解码逻辑
- 实现连接池和连接健康检查

### 阶段4：文档化

#### 4.1 技术文档编写
- 编写详细的API使用文档
- 编写配置选项的详细说明
- 提供完整的使用示例
- 编写常见问题和解决方案

#### 4.2 国际化和本地化
- 创建i18n多语言支持文件
- 创建配置的JSON Schema文件
- 创建用户帮助文档


## 工作质量检查清单

### 代码质量检查
- [ ] 所有类都有完整的JavaDoc注释
- [ ] 所有注解使用正确且完整
- [ ] 错误处理机制完善
- [ ] 资源管理正确（正确关闭连接等）
- [ ] 性能考虑充分（批量处理、缓存等）
- [ ] 线程安全性考虑

### 功能完整性检查
- [ ] 所有需求功能点都已实现
- [ ] 输入输出端口配置正确
- [ ] 配置选项完整且有默认值
- [ ] 消息格式符合协议要求
- [ ] 集成接口工作正常

### 文档完整性检查
- [ ] 技术文档完整清晰
- [ ] 配置说明详细准确
- [ ] 使用示例可运行
- [ ] 国际化文件完整
- [ ] JSON Schema正确

## 输出标准化格式

每个阶段完成后，必须按照以下格式输出工作成果：

### 阶段1输出格式
```markdown
## 需求分析结果

### 功能概述
[简要描述Shape的核心功能和作用]

### 技术要求
- 协议: [协议类型]
- 数据格式: [格式类型]
- 性能要求: [具体指标]
- 集成方式: [集成方式说明]

### 功能分解
1. 核心功能
   - [功能点1描述]
   - [功能点2描述]
2. 扩展功能
   - [功能点1描述]
3. 技术难点
   - [难点1及解决方案]
```

### 阶段2输出格式
```markdown
## 架构设计结果

### Shape设计

#### {Shape名称}
- Type: [shape-type]
- 职责: [单一职责描述]

## 概述
{Shape功能的简要描述}

## 配置选项

### 必需配置

| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| requiredConfig | string | 必需的配置项 | "config-value" |

### 可选配置

| 参数名 | 类型 | 说明 | 默认值 | 示例 |
|--------|------|------|--------|------|
| optionalNumber | integer | 可选数值配置 | 100 | 200 |
| timeoutMs | integer | 超时时间(毫秒) | 30000 | 60000 |

## 输入输出

### 输入端口

| 端口ID | 名称 | 消息类型 | 说明 |
|--------|------|----------|------|
| 0x01 | 数据输入 | {InputMessage} | 输入数据描述 |

### 输出端口

| 端口ID | 名称 | 消息类型 | 说明 |
|--------|------|----------|------|
| 0x01 | 处理结果 | {OutputMessage} | 输出数据描述 |

```

### 阶段3输出格式
```markdown
## 代码实现结果

### 项目结构
```
项目名称/
├── pom.xml
├── src/main/java/com/siteweb/stream/south/{module}/
│   ├── shapes/[ShapeName]Shape.java
│   ├── messages/[MessageName]Message.java
│   ├── options/[ShapeName]ShapeOption.java
│   └── options/defaults/[ShapeName]DefaultOption.java
├── doc
├── src/main/resources/
│   ├── i18n/shapes_zh_CN.properties
│   ├── i18n/shapes_en_US.properties
│   └── schema/[shape-type].json
└── src/test/java/...
```

### 核心类实现清单
- [x] Shape主类: [类名] - [功能说明]
- [x] 输入消息: [类名] - [字段数量]个字段
- [x] 输出消息: [类名] - [字段数量]个字段  
- [x] 配置选项: [类名] - [配置项数量]个配置项
- [x] 默认配置: [类名] - 默认值已设置

### 关键功能实现
- [功能点1]:  已实现
- [功能点2]:  已实现
- [错误处理]:  已实现
- [资源管理]:  已实现
```

### 阶段4输出格式
```markdown
## 测试验证结果

### 单元测试覆盖
- 测试类数量: [数量]
- 测试方法数量: [数量]  
- 代码覆盖率: [百分比]%
- 测试通过率: [百分比]%

### 功能测试结果
- [x] 正常流程测试: 通过
- [x] 异常处理测试: 通过
- [x] 边界条件测试: 通过
- [x] 性能基准测试: 通过 ([具体指标])

### 质量检查结果
- [x] 代码规范检查: 通过
- [x] 安全扫描: 通过
- [x] 依赖检查: 通过
- [x] 静态分析: 通过

### 发现问题及解决
1. [问题描述] - [解决方案]
2. [问题描述] - [解决方案]
```

### 阶段5输出格式

```markdown
## 交付成果清单

### 代码交付
- [ ] 源代码: 完整且可编译
- [ ] 单元测试: 覆盖率达标
- [ ] 构建配置: Maven POM配置正确
- [ ] 依赖管理: 版本兼容性确认

### 文档交付  
- [ ] API文档: [页数]页，内容完整
- [ ] 配置说明: [配置项数量]个配置项详细说明
- [ ] 使用示例: [示例数量]个完整示例
- [ ] 故障排除: 常见问题和解决方案

### 资源文件交付
- [ ] 国际化文件: 中英文支持
- [ ] JSON Schema: 配置验证模式
- [ ] 图标资源: Shape图标文件
- [ ] 帮助文档: 用户操作指南

```

## 工作流程执行要点

### 质量控制原则
1. 不允许带着问题进入下一阶段
2. 代码实现的同时完善文档

### 常见错误避免
1. 注解使用错误
   - 确保@Shape注解的type值符合命名规范
   - @Recoverable注解只用于需要恢复的状态字段
   - Jackson XML注解配置正确

2. 生命周期方法实现不当
   - onStart()和onStop()必须成对实现
   - onDestroy()中必须释放所有资源
   - onRestart()要重新初始化临时资源

3. 错误处理不完善
   - 不能忽略任何异常
   - 必须区分可重试和不可重试错误
   - 错误信息要提供足够的调试信息

4. 性能问题忽视
   - 大数据量处理时考虑批量操作
   - 合理使用缓存和连接池
   - 避免在循环中创建大量对象

### 交付验收标准
1. 所有需求功能点100%实现
2. 通过静态代码分析，无严重问题
3. 单元测试覆盖率≥80%，集成测试通过
4. 关键性能指标满足需求
5. 技术文档、用户文档、API文档齐全
6. 代码结构清晰，注释完整，易于扩展

### 持续改进机制
1. 每个阶段完成后进行代码审查
2. 收集使用反馈，持续优化
3. 记录开发过程中的经验教训
4. 根据实践经验更新开发模板

---



## 快速参考手册

### Shape类注解
```java
@Shape(type = "module-shape-name")           // 必需：Shape类型标识
@ShapeVersion(major = 1, minor = 0, patch = 0)  // 必需：版本信息
@ShapeIcon("icon-name")                      // 必需：图标名称
@ShapeAuthor("作者名称")                     // 必需：作者信息
@ShapeColor(bkColor = "#ffffff")             // 必需：背景颜色
@ShapeDefaultOptions(DefaultOptionClass.class)  // 必需：默认配置类
@ShapeInlet(id = 0x01, type = InputMessage.class, desc = "输入描述")    // 输入端口
@ShapeOutlet(id = 0x01, type = OutputMessage.class, desc = "输出描述")  // 输出端口
```


### 核心类继承关系
```
AbstractShape                     // Shape基类
├── processMessage()             // 消息处理入口
├── onStart() / onStop()        // 生命周期管理
├── onInitialize() / onDestroy() // 初始化和销毁
└── onRestart()                 // 重启处理

StreamMessage                    // 消息基类
├── timestamp                   // 时间戳
└── messageId                   // 消息ID

StreamShapeOption               // 配置基类
├── name                       // Shape名称
├── bkColor                    // 背景颜色
└── validate()                 // 验证方法
```

### 项目目录结构模板
```
stream-south-{module}/
├── pom.xml                                    // Maven配置
├── src/main/java/com/siteweb/stream/south/{module}/
│                                          ├── shapes/                               // Shape实现
│                                          │   └── {ShapeName}Shape.java
│                                          ├── messages/                             // 消息定义
│                                          │   ├── {InputMessage}Message.java
│                                          │   └── {OutputMessage}Message.java
│                                          ├── options/                              // 配置选项
│                                          │   ├── {ShapeName}ShapeOption.java
│                                          │   └── defaults/
│                                          │       └── {ShapeName}DefaultOption.java
│                                          └── util/                                 // 工具类
│                                              └── {Module}MessageFactory.java
├── src/main/resources/
│            ├── document/                             // 文档
│            │   ├── zh-CN/
│            │   │   └── {shape-type}.md
│            │   └── en-US/
│            │       └── {shape-type}.md
│            ├── i18n/                                 // 国际化
│            │   ├── shapes_zh_CN.properties
│            │   └── shapes_en_US.properties
│            └── schema/                               // JSON Schema
│                └── {shape-type}.json
└── src/test/java/                            // 测试代码
             └── com/siteweb/stream/south/{module}/
                 └── shapes/
                     └── {ShapeName}ShapeTest.java
```

### 命名规范速查
| 组件类型 | 命名规范 | 示例 |
|---------|----------|------|
| Module名称 | 小写字母+连字符 | cmcc, modbus, opcua |
| Shape Type | {module}-{function} | cmcc-send-data |
| Shape类名 | {Function}Shape | SendDataShape |
| 消息类名 | {Purpose}Message | SendDataMessage |
| 配置类名 | {Shape}Option | SendDataShapeOption |
| 包名 | com.siteweb.stream.south.{module} | com.siteweb.stream.south.cmcc |

### Actor集群路径规范

以下符号在 Actor 名称中是被禁止的：
1. /：路径分隔符
2. #：用于指定远程 actor 的 UID
3. %：用于 URL 编码
4. 空格及其他不符合 URI 安全字符集的内容


可以使用以下字符组合作为Actor名称：
1. 字母（a-z, A-Z）
2. 数字（0-9）
3. 一些安全符号（如 -, _, .）
4. 使用 URL 编码：例如 "name#1" 可以编码为 "name%231"，但要小心路径解析（不推荐）

#### 分片路径规范

- 下面示例路径中被{}包裹的为变量，可变的。
- sharding-type：集群分片名称，相同业务的多台机器指向同一个分片名称
- sharding-id: 分片id（这段路径由分片管理器创建）
- gateway-id: 由分片管理器（gateway-sharding）管理创建的分片Proxy，名称不可以改 默认为对象的Key
- graph-instance-id 流计算图的实例
- shape-instance-id 流计算shape实例

``` url
tcp://*************/tcsSystem/system/sharding/{sharding-type}/{sharding-id}/{gateway-id}/{graph-instance-id}/flow/{shape-instance-id}
tcp://*************/tcsSystem/system/sharding/{sharding-type}/{sharding-id}/{gateway-id}/pipeline/real-data-all
tcp://*************/tcsSystem/system/sharding/{sharding-type}/{sharding-id}/{gateway-id}/pipeline/real-data-100
```

#### 每个插件应建立自己的Gateway分片策略

##### Modbus插件创建的“modbus-gateway-sharding”分片

tcp://*************/tcsSystem/system/sharding/{modbus-gateway-sharding}/{sharding-id}/{gateway-id}/..
tcp://*************/tcsSystem/system/sharding/{modbus-gateway-sharding}/{sharding-id}/{gateway-id}/..

##### ctcc插件创建的“ctcc-gateway-sharding”分片

tcp://*************/tcsSystem/system/sharding/{ctcc-gateway-sharding}/{sharding-id}/{gateway-id}/..
tcp://*************/tcsSystem/system/sharding/{ctcc-gateway-sharding}/{sharding-id}/{gateway-id}/..