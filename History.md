# Thing Connect Server 更新历史

## [Unreleased]

### [3.1.3] - 2025-4-14

### Changed

- 优化GatewayLCM中的消息处理逻辑，使用泛型方法合并相似函数，提高代码可维护性。

主要优化内容包括：

1. 将`sendForeignDeviceChange`、`sendForeignAlarmChange`和`sendForeignDeviceControlCommandResponse`三个相似方法合并为一个泛型方法`sendForeignMessageToDevice`
2. 优化`onUnhandledMessage`方法，使用新的泛型方法处理不同类型的消息
3. 添加了详细的JavaDoc注释，提高代码可读性

这些优化带来的好处：

1. 减少了代码重复，提高了代码的可维护性
2. 统一了消息处理逻辑，使代码更加简洁
3. 提高了代码的可读性和可理解性
4. 为未来添加新的消息类型提供了更好的扩展性

### [3.1.2] - 2025-4-13

### Changed

- 将所有Actor类从直接使用ProbeManagerAdapter静态方法重构为继承ProbeActor基类，提高代码可维护性和一致性。

主要重构内容包括：

1. 创建ProbeActor基类，封装ActorProbe的创建、管理和销毁逻辑
2. 将tcs-south-cmcc和tcs-hub模块中的所有Actor类修改为继承ProbeActor
3. 移除对createProbe和removeProbe静态方法的直接调用
4. 使用getProbe()方法替代直接访问probe字段
5. 在postStop方法中调用super.postStop()以确保正确清理资源
6. 在createReceive方法中添加.orElse(super.createReceive())以确保基类消息处理器被正确链接

这些重构带来的好处：

1. 减少了代码重复，提高了代码的可维护性
2. 统一了Actor类的监控和日志记录方式
3. 确保了资源的正确释放，避免了内存泄漏
4. 简化了新Actor类的开发，只需继承ProbeActor即可获得监控能力
5. 为未来的监控系统升级提供了更好的扩展性


## [3.1.1] - 2025-4-12

### Added

- 引入distribute pub/sub 实现pipe line 集群消息消费

### Changed

- 重构Datapipeline实现简单的LCM机制，支持集群


## [3.2.0] - 2025-4-11

### Added

- 实现多语言i18n支持
- 引入flyway多数据库管理
- 支持h2/mysql/opengauss数据库

## [3.1.0] - 2025-4-21

### Added

- 实现多数据库支持

## [3.0.0] - 2025-4-3

### Added

- 实现gateway集群架构

### Changed

- 迁移akka 到开源版本 pekko

### Deprecated

- 原有的CTCC插件，旧版需要重构迁移。

## [2.0.0] - 2025-03-25

### Added

- 实现stream server流计算框架
- 实现了modbus协议基于流计算的开发框架

### Fixed

- 在spout变更consumer时，重新生成route actor

### Security

- 增加了用户登录和权限模块

## [1.1.0] - 2024-09-10

### Added

- 实现CTCC电信B接口开发框架
- 增加Actor System的conf和plugin的conf文件加载

## [1.0.0] - 2024-05-01

### Added

- 项目初次发布。
- 包含核心功能 Data pipeline 和 Akka Actor MDA 架构。
- spring admin 进行health监控能力