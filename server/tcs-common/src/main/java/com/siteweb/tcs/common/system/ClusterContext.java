package com.siteweb.tcs.common.system;

import lombok.Getter;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.ActorSystem;
import org.apache.pekko.actor.Props;
import org.apache.pekko.cluster.Cluster;
import org.apache.pekko.cluster.sharding.ClusterSharding;
import org.apache.pekko.cluster.sharding.ClusterShardingSettings;
import org.apache.pekko.cluster.sharding.ShardRegion;
import org.apache.pekko.http.javadsl.Http;
import org.apache.pekko.stream.Materializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import scala.concurrent.ExecutionContextExecutor;

/**
 * 应用集群统一上下文
 * 每个TCS应用实例都有一个集群上下文
 *
 * <AUTHOR> (2025-04-18)
 **/
@Component
public class ClusterContext {

    /**
     * 获取本机ActorSystem环境
     */
    @Getter
    private static ActorSystem actorSystem;

    @Getter
    private static ExecutionContextExecutor dispatcher;



    @Getter
    public static ApplicationContext context;

    /**
     * 获取集群管理器
     */
    @Getter
    private static Cluster cluster;

    /**
     * 获取集群Sharding管理器
     */
    @Getter
    private static ClusterSharding clusterSharding;

    /**
     * 获取本地 Materializer
     */
    @Getter
    private static Materializer materializer;

    /**
     * 获取本地 HttpSingen
     */
    @Getter
    private static Http http;



    /**
     * 获取AkkaSystem
     * @return ActorSystem
     */
    public static ActorSystem system() {
        return actorSystem;
    }

    /**
     * 获取ActorSystem 异步上下文执行器
     */
    public static ExecutionContextExecutor dispatcher(){
        return dispatcher;
    }




    /***
     * 使用自定义规则创建集群分片 Sharding
     * <AUTHOR> (2025/4/19)
     * @param shardingName 分片名称
     * @param proxyProps 分片的Actor
     * @param extractor 分片策略
     */
    public static ActorRef createSharding(String shardingName, Props proxyProps, ShardRegion.MessageExtractor extractor) {
        var shardingSettings = ClusterShardingSettings.create(actorSystem)
                .withRememberEntities(true)  // 启用实体持久化，确保故障恢复
                .withRole("compute");  // 指定分片角色，用于负载均衡
        return clusterSharding.start(shardingName, proxyProps, shardingSettings, extractor);
    }

    /**
     * 获取当前机器的指定名称分片
     * @param typeName 分片名称
     * @return
     */
    public static ActorRef shardRegion(String typeName){
        return clusterSharding.shardRegion(typeName);
    }

    /***
     * 注册集群上线事件
     * <AUTHOR> (2025/4/19)
     * @param callback
     */
    public void registerOnMemberUp(final Runnable callback) {
        cluster.registerOnMemberUp(callback);
    }

    /**
     * 注册集群离线事件
     * @param callback
     */
    public void registerOnMemberRemoved(final Runnable callback) {
        cluster.registerOnMemberRemoved(callback);
    }




    /**
     * 关闭 Akka System
     */
    public static void shutdown() {

        if (cluster != null) {
            cluster.shutdown();
            cluster = null;
        }

        if (actorSystem != null) {
            actorSystem.terminate();
            actorSystem = null;
        }
    }



    @Autowired
    private void setApplicationContext(ApplicationContext appContext) {
        context = appContext;
    }

    @Autowired
    private void setBeanAkkaSystem(ActorSystem system) {
        actorSystem = system;
        dispatcher = system.getDispatcher();
    }

    @Autowired
    private void setBeanCluster(Cluster _cluster) {
        cluster = _cluster;
    }

    @Autowired
    private void setBeanClusterSharding(ClusterSharding sharding) {
        clusterSharding = sharding;
    }

    @Autowired
    private void setBeanMaterializer(Materializer mater) {
        materializer = mater;
    }

    @Autowired
    private void setBeanMaterializer(Http httpSingle) {
        http = httpSingle;
    }

}
