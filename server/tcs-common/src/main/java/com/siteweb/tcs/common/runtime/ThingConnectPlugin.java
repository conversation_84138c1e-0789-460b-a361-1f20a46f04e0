package com.siteweb.tcs.common.runtime;

import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import cn.hutool.core.util.ReflectUtil;
import com.siteweb.tcs.common.actions.CreatePluginActorAction;
import com.siteweb.tcs.common.actions.DeletePluginActorAction;
import com.siteweb.tcs.common.annotations.PluginConfiguration;
import com.siteweb.tcs.common.util.PathUtil;
import com.siteweb.tcs.common.util.YamlUtil;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.servlet.ServletContext;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.pekko.actor.Actor;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.ActorSystem;
import org.apache.pekko.actor.Props;
import org.apache.pekko.japi.Creator;
import org.apache.pekko.pattern.Patterns;
import org.apache.pekko.util.Timeout;
import org.pf4j.Plugin;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.web.accept.ContentNegotiationManager;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.HandlerMapping;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.handler.SimpleUrlHandlerMapping;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;
import org.springframework.web.util.UrlPathHelper;
import org.springframework.web.util.pattern.PathPattern;
import scala.concurrent.Await;
import scala.concurrent.Future;
import scala.concurrent.duration.FiniteDuration;

import java.io.IOException;
import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Predicate;


/**
 * <a href="https://pf4j.org/doc/packaging.html">...</a>
 */
@Slf4j
public abstract class ThingConnectPlugin extends Plugin {

    public enum CurrentState {
        Started("STARTED"),
        Stopped("STOPPED"),
        Starting("STARTING"),
        Stopping("STOPPING");

        private final String status;

        CurrentState(String status) {
            this.status = status;
        }

        @Override
        public String toString() {
            return status;
        }
    }


    /**
     * 创建Actor的超时时间(单位秒)  默认为10秒</br>
     * 可在调用createRootActor函数之前修改此值
     */
    protected int createRootActorTimeOut = 30;
    protected int stopRootActorTimeOut = 30;

    /**
     * 插件启动时间
     */
    @Getter
    private LocalDateTime bootDateTime;
    @Getter
    private LocalDateTime downDateTime;

    @Autowired
    private List<ThingConnectPluginEvent> pluginEvents;

    @Getter
    private final PluginLogCollector logCollector = new PluginLogCollector(this.getClass().getPackage().getName());


    public Path getPluginFolder() {
        return context.getPluginFolder();
    }


    public Path getWorkspaceFolder() {
        return context.getWorkspaceFolder();
    }


    @Autowired
    private MeterRegistry meterRegistry;

    /**
     * 获取插件自己的应用上下文
     */
    @Getter
    private ApplicationContext applicationContext;

    /**
     * 插件上下文对象
     */
    @Getter
    protected final PluginContext context;

    /***
     * ActorSystem系统实例
     */
    @Autowired
    protected ActorSystem actorSystem;

    @Getter
    private CurrentState currentState = CurrentState.Stopped;


    /***
     * 插件系统的根
     * <AUTHOR> (2024/5/11)
     * @param null
     */
//    @Autowired
//    @Qualifier("plugin-system-root")
//    protected ActorRef pluginSystemActor;

    /***
     * 当前插件的根Actor
     */
    protected ActorRef selfRootActor;


    private final List<RequestMappingInfo> restControllerMappings = new ArrayList<>();
    
    private String pluginInstanceId;


    public String getPluginId() {
        return context.getDescriptor().getPluginId();
    }

    //每次启动一样，pf4j插件不支持多实例，所以一个tcs只能接一个同类插件
    //如果需要同类型多入（比如C接口，需要进行流计算设计，实现集群化）
    public String getPluginInstanceId() {
        return new TCSConfig().getTcsId() + "-" + getPluginId();
    }

    public String getPluginName() {
        return context.getDescriptor().getPluginName();
    }


    /***
     * 插件构造函数
     * <AUTHOR> (2024/5/9)
     * @param context  插件上下文对象
     */
    protected ThingConnectPlugin(PluginContext context) {
        super();
        this.context = context;
        this.wrapper = context.getPluginWrapper();
    }


    private boolean matchConstructor(Constructor<?> constructor, Class<?>[] argTypes) {
        Parameter[] params = constructor.getParameters();
        if (params.length != argTypes.length) return false;
        for (int n = 0; n < argTypes.length; n++) {
            Class<?> argType = argTypes[n];
            if (argType == null) continue;
            Class<?> paramType = params[n].getType();
            if (!paramType.isAssignableFrom(argType)) {
                return false;
            }
        }
        return true;
    }


//    /**
//     * 创建北向管道指定通道的数据处理Actor <br/>
//     * 用于订阅数据通道内变更的数据消息<br/>
//     * 当Actor信息处理超时后返回异常
//     *
//     * @param actorClass 自己实现的数据订阅Actor
//     * @param args       Actor的构造参数，可传入注入对象
//     * <AUTHOR> (2024/5/10)
//     */
//    protected <T extends Actor> ActorRef createRootActor(final Class<T> actorClass, Object... args) throws InterruptedException, TimeoutException {
//        val action = new CreatePluginActorAction();
//        final Creator<T> creator = () -> {
//            Class<?>[] argTypes = new Class[args.length];
//            for (int i = 0; i < args.length; i++) {
//                if (args[i] != null) {
//                    argTypes[i] = args[i].getClass();
//                }
//            }
//            Constructor<?>[] constructors = actorClass.getConstructors();
//            for (int i = 0; i < constructors.length; i++) {
//                Constructor<T> constructor = (Constructor<T>) constructors[i];
//                if (matchConstructor(constructor, argTypes)) {
//                    T actor = constructor.newInstance(args);
//                    this.applicationContext.getAutowireCapableBeanFactory().autowireBean(actor);
//                    return actor;
//                }
//            }
//            throw new IllegalArgumentException(String.format("No suitable constructor found %s", actorClass.getName()));
//        };
//        Props props = Props.<T>create(actorClass, creator);
//        action.setProps(props);
//        action.setPluginId(context.getDescriptor().getPluginId());
//        FiniteDuration duration = FiniteDuration.create(createRootActorTimeOut, TimeUnit.SECONDS);
//        Future<Object> future = Patterns.ask(pluginSystemActor, action, Timeout.durationToTimeout(duration));
//        ActorRef result = (ActorRef) Await.result(future, duration);
//        return result;
//        // return (ActorRef) future.value().get().get();
//    }
//
//
//    /**
//     * 删除插件创建的根Actor <br/>
//     * 插件Stop时会自动调用 <br/>
//     * 可选择手动删除根节点
//     *
//     * <AUTHOR> (2024/5/10)
//     */
//    protected Boolean deleteRootActor() throws InterruptedException, TimeoutException {
//        val action = new DeletePluginActorAction();
//        action.setPluginId(context.getDescriptor().getPluginId());
//        FiniteDuration duration = FiniteDuration.create(stopRootActorTimeOut, TimeUnit.SECONDS);
//        Future<Object> future = Patterns.ask(pluginSystemActor, action, Timeout.durationToTimeout(duration));
//        Boolean result = (Boolean) Await.result(future, duration);
//        return result;
//    }


    /***
     * 创建插件自己的上应用下文对象并加载依赖注入
     * <AUTHOR> (2024/5/9)
     */
    protected ApplicationContext createApplicationContext() throws IOException {
        //  目录是否存在，不存在则创建
        log.info("[{}] Initializes the application context", this.getPluginId());
        PathUtil.ensureDirectoryExists(this.getWorkspaceFolder());
        //
        AnnotationConfigApplicationContext applicationContext = new AnnotationConfigApplicationContext();
        applicationContext.setDisplayName("Plugin: " + this.getPluginId());
        applicationContext.setParent(context.getHostApplicationContext());
        applicationContext.setClassLoader(context.getPluginClassLoader());
        Path configFile = this.context.getConfigFile();
        if (Files.exists(configFile)) {
            log.info("Loading Plugin File: {}", configFile);
            YamlUtil.appendToApplicationContext(applicationContext, this.getPluginId(), configFile);
        } else {
            log.info("No plug-in configuration file found: {}", configFile);
        }
        String packagePath = this.getClass().getPackage().getName();
        log.info("Scanning plugin “{}” Beans from package “{}”", context.getDescriptor().getPluginId(), packagePath);
        applicationContext.scan(packagePath);
        PluginConfiguration scan = this.getClass().getAnnotation(PluginConfiguration.class);
        if (scan != null && scan.value() != null) {
            applicationContext.register(scan.value());
        }
        // 注册 MessageSource bean 下面注册了当前的classLoader 所以应该是用不到了？  需要测试下
        // PluginI18nMessageSource messageSource = new PluginI18nMessageSource(context.getPluginClassLoader());
        // applicationContext.getBeanFactory().registerSingleton("messageSource", messageSource);
        applicationContext.getBeanFactory().registerSingleton("classLoader", context.getPluginClassLoader());
        applicationContext.getBeanFactory().registerSingleton("pluginContext", context);
        applicationContext.refresh();
        log.info("[{}] Application context initialization is complete", this.getPluginId());
        return applicationContext;
    }


    private void configureLogInterceptor() {
        log.info("[{}] configure log interceptor", this.getPluginId());
        String pkg = this.getClass().getPackage().getName();
        // 获取 Logger 上下文
        LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
        // 获取指定命名空间的 Logger
        Logger namespaceLogger = loggerContext.getLogger(Logger.ROOT_LOGGER_NAME);
        // 创建一个 QueueAppender 实例

//      Appender<?> appender = namespaceLogger.getAppender("file");
        logCollector.setName(this.getPluginId());
        logCollector.setContext(loggerContext);
        logCollector.start();

        // 将 QueueAppender 添加到根 Logger
        namespaceLogger.addAppender(logCollector);
        // 设置命名空间 Logger 的日志级别
        // namespaceLogger.setLevel(Level.TRACE);
        // 确保 Logger 向上传播到根 Logger
        namespaceLogger.setAdditive(true);
    }


    private void cleanLogInterceptor() {
        log.info("[{}] clean log interceptor", this.getPluginId());
        String pkg = this.getClass().getPackage().getName();
        // 获取 Logger 上下文
        LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
        // 获取指定命名空间的 Logger
        Logger namespaceLogger = loggerContext.getLogger(pkg);
        // 创建一个 QueueAppender 实例
        namespaceLogger.detachAppender(this.getPluginId());
        logCollector.stop();
    }


    /**
     * 扫描插件上下文内的Controllers
     *
     * @param applicationContext
     * <AUTHOR> (2024/5/24)
     */
    private void scanController(ApplicationContext applicationContext) {
        log.info("[{}] scanning controller", this.getPluginId());
        var pluginBeans = applicationContext.getBeansOfType(Object.class).values();
        for (Object bean : pluginBeans) {
            Class<?> _class = bean.getClass();
            if (_class.isAnnotationPresent(RestController.class) && _class.isAnnotationPresent(RequestMapping.class)) {
                registerBeanController(bean);
            }
        }
    }


    private void registerBeanController(Object bean) {
        RequestMappingHandlerMapping requestMappingHandlerMapping = context.getRequestMappingHandlerMapping();
        Class<?> beanClass = bean.getClass();
        RequestMapping mapping = beanClass.getAnnotation(RequestMapping.class);
        for (Method method : beanClass.getMethods()) {
            String PathPrefix = getPathPrefix(beanClass);
            RequestMappingInfo requestMappingInfo = detectMethod(PathPrefix, mapping, method);
            if (requestMappingInfo != null) {
                log.info("[{}] mapping controller {} {}", this.getPluginId(), method, requestMappingInfo.getDirectPaths());
                requestMappingHandlerMapping.registerMapping(requestMappingInfo, bean, method);
                restControllerMappings.add(requestMappingInfo);
            }
        }
    }

    private void cleanController() {
        log.info("[{}] clean controller", this.getPluginId());
        RequestMappingHandlerMapping requestMappingHandlerMapping = context.getRequestMappingHandlerMapping();
        for (RequestMappingInfo info : restControllerMappings) {
            requestMappingHandlerMapping.unregisterMapping(info);
        }
        restControllerMappings.clear();
    }


    private String getPathPrefix(Class<?> beanClass) {
        RequestMappingHandlerMapping requestMappingHandlerMapping = context.getRequestMappingHandlerMapping();
        for (Map.Entry<String, Predicate<Class<?>>> entry : requestMappingHandlerMapping.getPathPrefixes().entrySet()) {
            String key = entry.getKey();
            Predicate<Class<?>> value = entry.getValue();
            if (value.test(beanClass)) {
                return key;
            }
        }
        return null;
    }

    private RequestMappingInfo detectMethod(String pathPrefix, RequestMapping mapping, Method method) {
        String basePath = (pathPrefix + "/" + context.getDescriptor().getPluginId()
                + "/" + (mapping.value().length > 0 ? mapping.value()[0] : ""))
                .replaceAll("/+", "/");

        if (method.isAnnotationPresent(GetMapping.class)) {
            GetMapping ann = method.getAnnotation(GetMapping.class);
            return buildInfo(basePath, ann.value(), RequestMethod.GET);
        }
        if (method.isAnnotationPresent(PutMapping.class)) {
            PutMapping ann = method.getAnnotation(PutMapping.class);
            return buildInfo(basePath, ann.value(), RequestMethod.PUT);
        }
        if (method.isAnnotationPresent(PostMapping.class)) {
            PostMapping ann = method.getAnnotation(PostMapping.class);
            return buildInfo(basePath, ann.value(), RequestMethod.POST);
        }
        if (method.isAnnotationPresent(DeleteMapping.class)) {
            DeleteMapping ann = method.getAnnotation(DeleteMapping.class);
            return buildInfo(basePath, ann.value(), RequestMethod.DELETE);
        }
        return null;
    }

    private RequestMappingInfo buildInfo(String base, String[] values, RequestMethod rm) {
        String sub = (values != null && values.length > 0) ? values[0] : "";
        String path = (sub.isEmpty() ? base : base + "/" + sub)
                .replaceAll("/+", "/");

        // 你可以选择映射两个路径：带斜杠 和 不带斜杠
        return RequestMappingInfo
                .paths(path, path + "/")
                .methods(rm)
                .build();
    }



    private void registerStaticResourceHandler() {
        log.info("[{}] register Static resource serve", this.getPluginId());
        final UrlPathHelper mvcUrlPathHelper = applicationContext.getBean("mvcUrlPathHelper", UrlPathHelper.class);
        final ContentNegotiationManager mvcContentNegotiationManager = applicationContext.getBean("mvcContentNegotiationManager", ContentNegotiationManager.class);
        final ServletContext servletContext = applicationContext.getBean(ServletContext.class);
        final HandlerMapping resourceHandlerMapping = applicationContext.getBean("resourceHandlerMapping", HandlerMapping.class);
        @SuppressWarnings("unchecked") final Map<String, Object> handlerMap = (Map<String, Object>) ReflectUtil.getFieldValue(resourceHandlerMapping, "handlerMap");
        final ResourceHandlerRegistry resourceHandlerRegistry = new ResourceHandlerRegistry(applicationContext, servletContext, mvcContentNegotiationManager, mvcUrlPathHelper);
        handlerMap.remove("/plugins/" + this.getPluginId() + "/**");
        resourceHandlerRegistry.addResourceHandler("/plugins/" + this.getPluginId() + "/**")
                .addResourceLocations(PathDefine.CLASSPATH_EXTENSION_RESOURCE_LOCATION + this.getPluginId() + "/")
                .resourceChain(false)
                .addResolver(new PluginResourceResolver(this.getApplicationContext()));
        final Map<String, ?> additionalUrlMap = ReflectUtil.<SimpleUrlHandlerMapping>invoke(resourceHandlerRegistry, "getHandlerMapping").getUrlMap();
        ReflectUtil.<Void>invoke(resourceHandlerMapping, "registerHandlers", additionalUrlMap);
    }

    private void unRegisterStaticResourceHandler() {
        if (applicationContext == null) return;
        String headerName = "/plugins/" + this.getPluginId() + "/**";
        final HandlerMapping resourceHandlerMapping = applicationContext.getBean("resourceHandlerMapping", HandlerMapping.class);
        if (resourceHandlerMapping instanceof SimpleUrlHandlerMapping mapping) {
            //
            @SuppressWarnings("unchecked") final Map<String, Object> handlerMap = (Map<String, Object>) ReflectUtil.getFieldValue(mapping, "handlerMap");
            handlerMap.remove(headerName);
            //
            @SuppressWarnings("unchecked") final Map<PathPattern, Object> pathPatternHandlerMap = (Map<PathPattern, Object>) ReflectUtil.getFieldValue(mapping, "pathPatternHandlerMap");
            var record = pathPatternHandlerMap.keySet().stream().filter(e -> e.getPatternString().equals(headerName)).findFirst();
            record.ifPresent(pathPatternHandlerMap::remove);
        }
    }

    /***
     * 插件启动事件
     * <AUTHOR> (2024/11/12)
     */
    public abstract void onStart();

    /***
     * 插件停止事件
     * <AUTHOR> (*)
     */
    public abstract void onStop();


    private void checkIllegalState() {
        if (this.currentState.equals(CurrentState.Stopping)) {
            throw new RuntimeException("插件正在停止中，无法执行此操作。");
        }
        if (this.currentState.equals(CurrentState.Starting)) {
            throw new RuntimeException("插件正在启动中，无法执行此操作。");
        }
    }


    @Override
    public final void stop() {
        checkIllegalState();
        if (!this.currentState.equals(CurrentState.Started)) {
            throw new RuntimeException("插件正已停止，无法执行此操作。");
        }
        try {
            this.currentState = CurrentState.Stopping;
            this.downDateTime = LocalDateTime.now();
            log.info("[{}] stopping", this.getPluginId());
            meterRegistry.counter("plugins.started.count").increment(-1);
            this.cleanController();
//            try {
//                this.deleteRootActor();
//            } catch (InterruptedException | TimeoutException ignored) {
//            }
            this.unRegisterStaticResourceHandler();
            // 插件停止 销毁插件上下文对象
            if (this.applicationContext instanceof ConfigurableApplicationContext) {
                ((ConfigurableApplicationContext) this.applicationContext).close();
                log.info("[{}] destroy context", this.getPluginId());
            }
            this.applicationContext = null;
            log.info("Plugin Stop {}", context.getDescriptor().getPluginId());
            pluginEvents.forEach(e -> e.pluginStop(this));
            cleanLogInterceptor();
            this.onStop();
        } catch (Exception ex) {
            this.wrapper.setFailedException(ex);
        } finally {
            this.currentState = CurrentState.Stopped;
        }
    }


    @Override
    public final void start() {
        checkIllegalState();
        if (!this.currentState.equals(CurrentState.Stopped)) {
            throw new RuntimeException("插件正已启动，无法执行此操作。");
        }
        try {
            this.currentState = CurrentState.Starting;
            this.bootDateTime = LocalDateTime.now();
            log.info("[{}] starting", this.getPluginId());
            this.configureLogInterceptor();
            this.applicationContext = this.createApplicationContext();
            this.scanController(this.applicationContext);
            this.registerStaticResourceHandler();
            if (this.applicationContext instanceof ConfigurableApplicationContext configurableApplicationContext) {
                ConfigurableListableBeanFactory configureBeanFactory = configurableApplicationContext.getBeanFactory();
                configureBeanFactory.autowireBean(this);
            }
            pluginEvents.forEach(e -> e.pluginStart(this));
            this.onStart();
            meterRegistry.counter("plugins.started.count").increment();
            log.info("Plugin Start {}", context.getDescriptor().getPluginId());

            this.currentState = CurrentState.Started;
        } catch (Exception e) {
            log.error("[{}] error of starting", this.getPluginId(), e);
            this.cleanLogInterceptor();
            this.cleanController();
            this.unRegisterStaticResourceHandler();
            // 销毁插件上下文对象
            if (this.applicationContext instanceof ConfigurableApplicationContext) {
                ((ConfigurableApplicationContext) this.applicationContext).close();
            }
            this.currentState = CurrentState.Stopped;
            this.wrapper.setFailedException(e);
            throw new RuntimeException(e);
        }

    }


}
