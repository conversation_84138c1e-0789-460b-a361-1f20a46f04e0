package com.siteweb.tcs.common.runtime;


import com.siteweb.tcs.common.o11y.ProbeActor;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.Props;

public class GuardActor extends ProbeActor {
    public static Props props() {
        return Props.create(GuardActor.class, GuardActor::new);
    }

    @Override
    public Receive createReceive() {

        return super.createReceive();
    }

}

