package com.siteweb.tcs.common.o11y;

import lombok.Getter;

import java.time.LocalDateTime;

public class Actor<PERSON>ogI<PERSON> implements WindowLogItem{

    private final String log;
    @Getter
    private final ActorLogLevel level;
    private LocalDateTime timestamp;

    public ActorLogItem(ActorLogLevel level, String message) {
        this.level = level;
        this.log = message;
    }

    @Override
    public String getWindowLogString() {
        return log;
    }
}

