package com.siteweb.tcs.common.util.scripts;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorRuntimeJavaType;

import java.util.Map;

/**
 * <AUTHOR> (2025-02-28)
 **/
public class JsonFunction extends AbstractFunction {

    public static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public String getName() {
        return "JSON$";
    }

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject arg1) {
        var param = arg1.getValue(env);

        try {
            var value = objectMapper.readValue(String.valueOf(param), Object.class);
            return AviatorRuntimeJavaType.valueOf(value);
        } catch (JsonProcessingException e) {
            return null;
        }
    }
}
