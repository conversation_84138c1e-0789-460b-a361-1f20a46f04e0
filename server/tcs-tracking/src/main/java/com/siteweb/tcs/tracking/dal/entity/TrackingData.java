package com.siteweb.tcs.tracking.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 埋点数据实体类
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("tcs_tracking_data")
public class TrackingData {
    
    /**
     * 数据ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 埋点点位ID
     */
    private Integer pointId;
    
    /**
     * 策略ID
     */
    private Integer strategyId;
    
    /**
     * 埋点时间
     */
    private LocalDateTime timestamp;
    
    /**
     * 数据来源ID
     */
    private String sourceId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 埋点数据JSON
     */
    private String data;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 关联的埋点点位（非数据库字段）
     */
    @TableField(exist = false)
    private TrackingPoint trackingPoint;
    
    /**
     * 关联的埋点策略（非数据库字段）
     */
    @TableField(exist = false)
    private TrackingStrategy trackingStrategy;
}
