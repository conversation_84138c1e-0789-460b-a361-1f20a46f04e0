package com.siteweb.tcs.backend.plugin;

import com.siteweb.tcs.backend.config.ThingConnectPluginConfiguration;
import com.siteweb.tcs.backend.dto.BackupRequest;
import com.siteweb.tcs.backend.entity.TcsPlugin;
import com.siteweb.tcs.backend.entity.TcsPluginBackup;
import com.siteweb.tcs.backend.mapper.TcsPluginBackupMapper;
import com.siteweb.tcs.backend.mapper.TcsPluginMapper;
import com.siteweb.tcs.backend.utils.ZipUtils;
import com.siteweb.tcs.common.runtime.ThingConnectPluginDescriptor;
import com.siteweb.tcs.common.runtime.ThingConnectPluginWrapper;
import com.siteweb.tcs.common.util.PathUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.pf4j.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.FileVisitResult;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.SimpleFileVisitor;
import java.nio.file.attribute.BasicFileAttributes;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.zip.ZipFile;

/**
 * <AUTHOR> (2024-05-08)
 **/
@Slf4j
@Component
public class ThingConnectPluginManager extends DefaultPluginManager {
    @Autowired
    private TcsPluginMapper tcsPluginMapper;

    @Autowired
    private TcsPluginBackupMapper tcsPluginBackupMapper;

    @Autowired
    public void setSpringPluginFactory(ThingConnectPluginFactory factory) {
        this.pluginFactory = factory;
    }

    @Autowired
    public void setPluginStatusProvider(ThingConnectPluginStatusProvider provider) {
        this.pluginStatusProvider = provider;
    }

    @Autowired
    private ThingConnectPluginConfiguration thingConnectPluginConfiguration;

    public ThingConnectPluginManager() {

    }

    @SneakyThrows
    public long backupPlugin(BackupRequest request) {
        Path backupFolder = Path.of(thingConnectPluginConfiguration.getBackupPath()).resolve(request.getPluginId());
        Path pluginFolder = Path.of(thingConnectPluginConfiguration.getPath()).resolve(request.getPluginId());

        // 确保插件文件夹存在
        if (!Files.exists(pluginFolder)) {
            throw new IllegalStateException("插件文件夹不存在: " + pluginFolder);
        }

        // 创建备份文件夹（如果不存在）
        Files.createDirectories(backupFolder);

        // 备份文件
        String timestamp = new SimpleDateFormat("yyyyMMdd-HHmmss").format(new Date());
        Path backupFile = backupFolder.resolve(request.getBackupName() + "-" + timestamp + ".zip");

        // 需要压缩的path列表
        List<Path> paths = new ArrayList<>();
        // 遍历pluginFolder下的所有文件和文件夹
        try (var stream = Files.list(pluginFolder)) {
            stream.forEach(paths::add);
        }

        // 压缩文件
        ZipUtils.zipFiles(paths, backupFile);

        TcsPluginBackup backup = new TcsPluginBackup();
        ThingConnectPluginWrapper plugin = this.getPlugin(request.getPluginId());
        backup.setPluginId(request.getPluginId());
        backup.setBackupName(request.getBackupName());
        backup.setVersion(plugin.getDescriptor().getVersion());
        backup.setBackupDate(LocalDateTime.now());
        backup.setFileName(backupFile.getFileName().toString());
        // 计算备份文件大小
        long backupSize = Files.size(backupFile);
        String readableSize = PathUtil.getReadableSize(backupSize);
        backup.setBackupSize(readableSize);
        return tcsPluginBackupMapper.insert(backup);
    }

    public List<TcsPluginBackup> getBackupList() {
        return tcsPluginBackupMapper.selectList(null);
    }

    public void deleteBackup(Long backupId) {
        TcsPluginBackup backup = tcsPluginBackupMapper.selectById(backupId);
        if (backup != null) {
            Path backupFile = Path.of(thingConnectPluginConfiguration.getBackupPath()).resolve(backup.getPluginId()).resolve(backup.getFileName());
            try {
                Files.deleteIfExists(backupFile);
            } catch (IOException e) {
                log.error("删除备份文件失败", e);
            }
            tcsPluginBackupMapper.deleteById(backupId);
        }
    }

    @SneakyThrows
    public void restorePlugin(BackupRequest request) {
        // 获取备份文件
        TcsPluginBackup backup = tcsPluginBackupMapper.selectById(request.getBackupId());
        if (backup == null) {
            throw new IllegalArgumentException("备份不存在");
        }

        Path backupFile = Path.of(thingConnectPluginConfiguration.getBackupPath())
                .resolve(backup.getPluginId())
                .resolve(backup.getFileName());
        //插件文件夹
        Path pluginFolder = Path.of(thingConnectPluginConfiguration.getPath())
                .resolve(backup.getPluginId());

        // 确保备份文件存在
        if (!Files.exists(backupFile)) {
            throw new FileNotFoundException("备份文件不存在: " + backupFile);
        }

        try {
            //1 判断是否有插件，如果有则卸载
            if (this.getPlugin(backup.getPluginId()) != null) {
                this.unInstallPlugin(request.getPluginId());
            }
            //2 删除插件文件夹
            if (Files.exists(pluginFolder)) {
                Files.walkFileTree(pluginFolder, new SimpleFileVisitor<Path>() {
                    @Override
                    public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                        Files.delete(file);
                        return FileVisitResult.CONTINUE;
                    }

                    @Override
                    public FileVisitResult postVisitDirectory(Path dir, IOException exc) throws IOException {
                        Files.delete(dir);
                        return FileVisitResult.CONTINUE;
                    }
                });
            }
            ZipFile zipFile = new ZipFile(backupFile.toFile());
            ZipUtils.JARPlugin jarPlugin = ZipUtils.findJARPlugin(zipFile);
            //3 安装插件
            this.installPlugin(zipFile, jarPlugin);
            System.out.println("插件恢复成功: " + backup.getPluginId());
        } catch (IOException e) {
            throw new RuntimeException("恢复插件失败: " + backup.getPluginId(), e);
        }
    }


    @Override
    protected PluginDescriptorFinder createPluginDescriptorFinder() {
        return new CompoundPluginDescriptorFinder()
                .add(new ThingConnectPropertiesPluginDescriptorFinder())
                .add(new ThingConnectManifestPluginDescriptorFinder());
    }


    @Override
    protected PluginRepository createPluginRepository() {
        return null;
    }


    @Override
    protected PluginFactory createPluginFactory() {
        return this.pluginFactory;
    }

    @Override
    protected PluginStatusProvider createPluginStatusProvider() {
        return this.pluginStatusProvider;
    }

    @Override
    public ThingConnectPluginWrapper getPlugin(String pluginId) {
        return (ThingConnectPluginWrapper) plugins.get(pluginId);
    }


    public List<ThingConnectPluginWrapper> getAllPlugins() {
        return plugins.values().stream()
                .filter(ThingConnectPluginWrapper.class::isInstance)
                .map(ThingConnectPluginWrapper.class::cast)
                .toList();
    }


    public List<PluginInfo> getAllInfo() {
        List<TcsPlugin> plugins = tcsPluginMapper.selectList(null);
        List<PluginInfo> infos = plugins.stream()
                .map(e -> {
                    var info = PluginInfo.fromEntity(e);
                    var plugin = this.getPlugin(e.getPluginId());
                    if (plugin != null) info.copyFrom(plugin);
                    return info;
                })
                .toList();
        if (this.isDevelopment()) {
            infos = infos.stream().filter(e -> this.getPlugin(e.getPluginId()) != null).toList();
        }
        return infos;
    }


    @Override
    protected PluginWrapper createPluginWrapper(PluginDescriptor pluginDescriptor, Path pluginPath, ClassLoader pluginClassLoader) {
        // create the plugin wrapper
        log.debug("Creating wrapper for plugin '{}'", pluginPath);
        Path resourceFolder = Path.of(thingConnectPluginConfiguration.getPath()).resolve(pluginDescriptor.getPluginId()).normalize().toAbsolutePath();
        ThingConnectPluginWrapper pluginWrapper = new ThingConnectPluginWrapper(this, pluginDescriptor, pluginPath, pluginClassLoader, resourceFolder);
        pluginWrapper.setPluginFactory(getPluginFactory());
        return pluginWrapper;
    }

    public Path getPluginsAbsolutePath() {
        return Path.of(thingConnectPluginConfiguration.getPath()).toAbsolutePath();
    }


    @Autowired
    private PluginMigrationService pluginMigrationService;

    public TcsPlugin installPlugin(ZipFile zipFile, ZipUtils.JARPlugin jarPlugin) throws IOException {
        ThingConnectPluginDescriptor descriptor = jarPlugin.getDescriptor();
        // build path
        Path pluginRoot = Path.of(thingConnectPluginConfiguration.getPath());
        Path pluginFolder = pluginRoot.resolve(descriptor.getPluginId());
        Path pluginFile = pluginFolder.resolve(jarPlugin.getEntry().getName());
        Path pluginWorkspace = pluginFolder.resolve("workspace");
        TcsPlugin plugin = tcsPluginMapper.selectById(descriptor.getPluginId());
        boolean isNew = plugin == null;
        if (plugin == null) plugin = new TcsPlugin();
        plugin.setPluginId(descriptor.getPluginId());
        plugin.setPluginName(descriptor.getPluginName());
        plugin.setVersion(descriptor.getVersion());
        plugin.setClassName(descriptor.getPluginClass());
        plugin.setFileName(jarPlugin.getEntry().getName());
        plugin.setProvider(descriptor.getProvider());
        plugin.setDescription(descriptor.getPluginDescription());
        plugin.setBuildTime(descriptor.getBuildTime());
        plugin.setEnabled(false);
        plugin.setUpdateJARDate(LocalDateTime.now());
        plugin.setChangeDate(null);
        plugin.setOperateDate(null);
        plugin.setApplicationName(descriptor.getApplicationName());
        try {
            // 执行数据库迁移
            boolean migrationResult = pluginMigrationService.migratePluginDatabase(descriptor.getPluginId(), pluginFile.toString());
            if (!migrationResult) {
                log.error("插件 {} 的数据库迁移失败", descriptor.getPluginId());
                throw new RuntimeException("Database migration failed");
            }
            if (isNew) {
                plugin.setUploadJARDate(LocalDateTime.now());
                tcsPluginMapper.insert(plugin);
            } else {
                tcsPluginMapper.updateById(plugin);
            }
            // write files
            Files.createDirectories(pluginWorkspace);
            ZipUtils.unZip(zipFile, pluginFolder);
            // 加载插件
            this.loadPlugin(pluginFile);
            return plugin;
        } catch (Exception e) {
            // 发生异常时进行回滚
            log.error("安装插件 {} 失败", descriptor.getPluginId(), e);
            pluginMigrationService.cleanPluginDatabase(descriptor.getPluginId());
            throw new RuntimeException("Failed to install plugin: " + e.getMessage(), e);
        }
    }


    public void unInstallPlugin(String pluginId) {
        TcsPlugin plugin = tcsPluginMapper.selectById(pluginId);
        if (plugin != null) {
            this.stopPlugin(pluginId);
            this.unloadPlugin(pluginId);
            tcsPluginMapper.deleteById(pluginId);
        }
    }


    public void start() {
        List<Path> paths = new ArrayList<>();
        System.setProperty(AbstractPluginManager.MODE_PROPERTY_NAME, thingConnectPluginConfiguration.getRuntimeMode());
        super.runtimeMode = RuntimeMode.byName(thingConnectPluginConfiguration.getRuntimeMode());
        if (this.isDevelopment()) {
            // TODO 调试模式下 直接读取启用了哪些Maven的Profile 从 Profile的 properties内读取
            paths = thingConnectPluginConfiguration.getDevPath().stream().map(Path::of).toList();
        } else {
            paths.add(Path.of(thingConnectPluginConfiguration.getPath()));
        }
        super.pluginsRoots.clear();
        super.pluginsRoots.addAll(paths);
        super.initialize();

        this.pluginRepository = new CompoundPluginRepository()
                .add(new DevelopmentPluginRepository(getPluginsRoots()), this::isDevelopment)
//                .add(new JarPluginRepository(getPluginsRoots()), this::isNotDevelopment)
                .add(new ThingDBPluginRepository(Path.of(thingConnectPluginConfiguration.getPath()), tcsPluginMapper), this::isNotDevelopment);

        this.loadPlugins();
        this.startPlugins();
    }


    @Override
    public PluginState startPlugin(String pluginId) {
        checkPluginId(pluginId);
        ThingConnectPluginWrapper pluginWrapper = getPlugin(pluginId);
        try {
            pluginWrapper.setFailedException(null);
            return super.startPlugin(pluginId);
        } catch (Exception ex) {
            pluginWrapper.setFailedException(ex);
            throw ex;
        }
    }


    @Override
    public PluginState stopPlugin(String pluginId) {
        checkPluginId(pluginId);
        ThingConnectPluginWrapper pluginWrapper = getPlugin(pluginId);
        try {
            pluginWrapper.setFailedException(null);
            return super.stopPlugin(pluginId, true);
        } catch (Exception ex) {
            pluginWrapper.setFailedException(ex);
            throw ex;
        }
    }

}
