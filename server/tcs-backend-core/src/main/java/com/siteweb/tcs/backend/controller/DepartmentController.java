package com.siteweb.tcs.backend.controller;

import com.siteweb.tcs.common.exception.code.StandardBusinessErrorCode;
import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.hub.dal.dto.DepartmentDTO;
import com.siteweb.tcs.hub.service.DepartmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 部门管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/department")
public class DepartmentController {

    @Autowired
    private DepartmentService departmentService;

    /**
     * 获取部门树形结构列表
     *
     * @param status 状态筛选，可选
     * @param name 名称筛选，可选
     * @return 部门树形结构列表
     */
    @GetMapping(value = "/tree", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getDepartmentTree(
            @RequestParam(value = "status", required = false) Integer status,
            @RequestParam(value = "name", required = false) String name) {
        try {
            List<DepartmentDTO> result = departmentService.getDepartmentTree(status, name);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("获取部门树形结构失败", e);
            return ResponseHelper.failed(StandardBusinessErrorCode.OPERATION_FAILED, e.getMessage());
        }
    }

    /**
     * 获取所有部门列表（扁平结构，用于下拉选择）
     *
     * @return 部门列表
     */
    @GetMapping(value = "/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllDepartments() {
        try {
            List<DepartmentDTO> result = departmentService.getAllDepartments();
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("获取部门列表失败", e);
            return ResponseHelper.failed(StandardBusinessErrorCode.OPERATION_FAILED, e.getMessage());
        }
    }

    /**
     * 根据ID获取部门详情
     *
     * @param id 部门ID
     * @return 部门详情
     */
    @GetMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getDepartmentById(@PathVariable("id") Integer id) {
        try {
            DepartmentDTO result = departmentService.getById(id);
            if (result == null) {
                return ResponseHelper.failed(StandardBusinessErrorCode.DATA_NOT_FOUND, "部门不存在: " + id);
            }
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("获取部门详情失败: id={}", id, e);
            return ResponseHelper.failed(StandardBusinessErrorCode.OPERATION_FAILED, e.getMessage());
        }
    }

    /**
     * 创建新部门
     *
     * @param departmentDTO 部门信息
     * @return 创建成功返回部门信息，失败返回错误信息
     */
    @PostMapping(value = "/create", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createDepartment(@RequestBody DepartmentDTO departmentDTO) {
        try {
            DepartmentDTO result = departmentService.createDepartment(departmentDTO);
            return ResponseHelper.successful(result);
        } catch (IllegalArgumentException e) {
            log.error("创建部门失败: 参数无效", e);
            return ResponseHelper.failed(StandardBusinessErrorCode.REQUEST_PARAMETER_INVALID, e.getMessage());
        } catch (IllegalStateException e) {
            log.error("创建部门失败: 部门编码重复", e);
            return ResponseHelper.failed(StandardBusinessErrorCode.RECORD_REPEAT_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("创建部门失败", e);
            return ResponseHelper.failed(StandardBusinessErrorCode.RECORD_CREATE_ERROR, e.getMessage());
        }
    }

    /**
     * 更新部门信息
     *
     * @param departmentDTO 需要更新的部门信息
     * @return 更新成功返回更新后的部门信息，失败返回错误信息
     */
    @PutMapping(value = "/update", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateDepartment(@RequestBody DepartmentDTO departmentDTO) {
        try {
            DepartmentDTO result = departmentService.updateDepartment(departmentDTO);
            if (result == null) {
                return ResponseHelper.failed(StandardBusinessErrorCode.DATA_NOT_FOUND, "部门不存在: " + departmentDTO.getId());
            }
            return ResponseHelper.successful(result);
        } catch (IllegalArgumentException e) {
            log.error("更新部门失败: 参数无效", e);
            return ResponseHelper.failed(StandardBusinessErrorCode.REQUEST_PARAMETER_INVALID, e.getMessage());
        } catch (IllegalStateException e) {
            log.error("更新部门失败: 部门编码重复或循环引用", e);
            return ResponseHelper.failed(StandardBusinessErrorCode.RECORD_UPDATE_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("更新部门失败", e);
            return ResponseHelper.failed(StandardBusinessErrorCode.RECORD_UPDATE_ERROR, e.getMessage());
        }
    }

    /**
     * 更新部门状态
     *
     * @param requestBody 请求体，包含部门ID、状态和是否级联更新子部门
     * @return 更新结果
     */
    @PutMapping(value = "/status", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateDepartmentStatus(@RequestBody Map<String, Object> requestBody) {
        try {
            Integer id = (Integer) requestBody.get("id");
            Integer status = (Integer) requestBody.get("status");
            Boolean cascadeChildren = (Boolean) requestBody.getOrDefault("cascadeChildren", false);
            
            boolean result = departmentService.updateStatus(id, status, cascadeChildren);
            return ResponseHelper.successful(result);
        } catch (IllegalArgumentException e) {
            log.error("更新部门状态失败: 参数无效", e);
            return ResponseHelper.failed(StandardBusinessErrorCode.REQUEST_PARAMETER_INVALID, e.getMessage());
        } catch (Exception e) {
            log.error("更新部门状态失败", e);
            return ResponseHelper.failed(StandardBusinessErrorCode.RECORD_UPDATE_ERROR, e.getMessage());
        }
    }

    /**
     * 删除部门
     *
     * @param id 要删除的部门ID
     * @return 删除结果
     */
    @DeleteMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteDepartment(@PathVariable("id") Integer id) {
        try {
            boolean result = departmentService.deleteDepartment(id);
            if (!result) {
                return ResponseHelper.failed(StandardBusinessErrorCode.DATA_NOT_FOUND, "部门不存在: " + id);
            }
            return ResponseHelper.successful(true);
        } catch (IllegalArgumentException e) {
            log.error("删除部门失败: 参数无效", e);
            return ResponseHelper.failed(StandardBusinessErrorCode.REQUEST_PARAMETER_INVALID, e.getMessage());
        } catch (IllegalStateException e) {
            log.error("删除部门失败: 存在子部门", e);
            return ResponseHelper.failed(StandardBusinessErrorCode.RECORD_DELETE_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("删除部门失败: id={}", id, e);
            return ResponseHelper.failed(StandardBusinessErrorCode.RECORD_DELETE_ERROR, e.getMessage());
        }
    }

    /**
     * 检查部门编码是否存在
     *
     * @param code 部门编码
     * @param excludeId 排除的部门ID（用于编辑时排除自身）
     * @return 是否存在
     */
    @GetMapping(value = "/check-code", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> checkCodeExists(
            @RequestParam("code") String code,
            @RequestParam(value = "excludeId", required = false) Integer excludeId) {
        try {
            boolean exists = departmentService.checkCodeExists(code, excludeId);
            return ResponseHelper.successful(exists);
        } catch (Exception e) {
            log.error("检查部门编码失败: code={}, excludeId={}", code, excludeId, e);
            return ResponseHelper.failed(StandardBusinessErrorCode.OPERATION_FAILED, e.getMessage());
        }
    }

    /**
     * 检查部门是否有子部门
     *
     * @param parentId 父部门ID
     * @return 是否有子部门
     */
    @GetMapping(value = "/has-children/{parentId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> hasChildren(@PathVariable("parentId") Integer parentId) {
        try {
            boolean hasChildren = departmentService.hasChildren(parentId);
            return ResponseHelper.successful(hasChildren);
        } catch (Exception e) {
            log.error("检查子部门失败: parentId={}", parentId, e);
            return ResponseHelper.failed(StandardBusinessErrorCode.OPERATION_FAILED, e.getMessage());
        }
    }

    /**
     * 获取部门的所有子部门ID（递归）
     *
     * @param parentId 父部门ID
     * @return 子部门ID列表
     */
    @GetMapping(value = "/children-ids/{parentId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllChildrenIds(@PathVariable("parentId") Integer parentId) {
        try {
            List<Integer> childrenIds = departmentService.getAllChildrenIds(parentId);
            return ResponseHelper.successful(childrenIds);
        } catch (Exception e) {
            log.error("获取子部门ID列表失败: parentId={}", parentId, e);
            return ResponseHelper.failed(StandardBusinessErrorCode.OPERATION_FAILED, e.getMessage());
        }
    }

    /**
     * 验证父部门关系是否合法（避免循环引用）
     *
     * @param departmentId 部门ID
     * @param parentId 父部门ID
     * @return 是否合法
     */
    @GetMapping(value = "/validate-parent", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> validateParentRelation(
            @RequestParam("departmentId") Integer departmentId,
            @RequestParam("parentId") Integer parentId) {
        try {
            boolean valid = departmentService.validateParentRelation(departmentId, parentId);
            return ResponseHelper.successful(valid);
        } catch (Exception e) {
            log.error("验证父部门关系失败: departmentId={}, parentId={}", departmentId, parentId, e);
            return ResponseHelper.failed(StandardBusinessErrorCode.OPERATION_FAILED, e.getMessage());
        }
    }
}