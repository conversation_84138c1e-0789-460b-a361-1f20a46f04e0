# ==================================================
# cmcc-get-data
# ==================================================
cmcc-get-data.name=获取实时数据
cmcc-get-data.alias=条件分支
cmcc-get-data.groups=移动B接口
cmcc-get-data.tags=移动B接口
cmcc-get-data.inlet1.name=触发事件
cmcc-get-data.inlet2.name=GET_DATA_ACK报文
cmcc-get-data.outlet1.name=GET_DATA报文




# ==================================================
# cmcc-send-data
# ==================================================
cmcc-send-data.name=数据上报处理
cmcc-send-data.alias=CMCC数据上报
cmcc-send-data.groups=移动B接口
cmcc-send-data.tags=移动B接口
cmcc-send-data.inlet1.name=SendData报文
cmcc-send-data.outlet1.name=SendDataAck报文

# CMCC Send Alarm Shape - 上报告警信息
cmcc-send-alarm.name=上报告警信息
cmcc-send-alarm.description=处理FSU向SC上报的告警信息，根据中国移动B接口规范5.6.2章节实现
cmcc-send-alarm.category=CMCC告警

# Configuration Properties
cmcc-send-alarm.config.fsuId.title=FSU标识
cmcc-send-alarm.config.fsuId.description=FSU设备的唯一标识，最大长度20字节

cmcc-send-alarm.config.alarmProcessing.title=告警处理配置
cmcc-send-alarm.config.alarmProcessing.enableValidation.title=启用验证
cmcc-send-alarm.config.alarmProcessing.enableValidation.description=是否启用告警数据验证
cmcc-send-alarm.config.alarmProcessing.enableTimeNormalization.title=启用时间标准化
cmcc-send-alarm.config.alarmProcessing.enableTimeNormalization.description=是否自动标准化告警时间格式
cmcc-send-alarm.config.alarmProcessing.maxAlarmsPerMessage.title=单次消息最大告警数
cmcc-send-alarm.config.alarmProcessing.maxAlarmsPerMessage.description=单个消息中允许的最大告警数量

cmcc-send-alarm.config.output.title=输出配置
cmcc-send-alarm.config.output.enablePipelineForward.title=启用管道转发
cmcc-send-alarm.config.output.enablePipelineForward.description=是否将告警数据转发到处理管道
cmcc-send-alarm.config.output.enableResponseGeneration.title=启用响应生成
cmcc-send-alarm.config.output.enableResponseGeneration.description=是否生成告警确认响应

cmcc-send-alarm.config.logging.title=日志配置
cmcc-send-alarm.config.logging.logLevel.title=日志级别
cmcc-send-alarm.config.logging.logLevel.description=设置日志记录级别
cmcc-send-alarm.config.logging.enableDetailedLogging.title=启用详细日志
cmcc-send-alarm.config.logging.enableDetailedLogging.description=是否记录详细的告警处理日志

# Error Messages
cmcc-send-alarm.error.fsuIdRequired=FSU ID是必填项
cmcc-send-alarm.error.alarmListEmpty=至少需要一个告警信息
cmcc-send-alarm.error.serialNoRequired=告警序号是必填项
cmcc-send-alarm.error.deviceIdRequired=设备ID是必填项
cmcc-send-alarm.error.alarmLevelRequired=告警级别是必填项
cmcc-send-alarm.error.alarmFlagRequired=告警标志是必填项
cmcc-send-alarm.error.processingFailed=告警处理失败

