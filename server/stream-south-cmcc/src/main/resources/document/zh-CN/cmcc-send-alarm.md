# CMCC 上报告警信息 (Send Alarm)

## 概述

根据中国移动B接口技术规范5.6.2章节实现的告警上报功能。FSU根据设备产生告警或者根据遥测量判断有告警需上报时，向SC上报告警信息。

## 功能特性

- **告警数据验证**: 自动验证告警消息的必填字段和数据格式
- **时间标准化**: 自动标准化告警时间格式为 `yyyy-MM-dd HH:mm:ss`
- **状态管理**: 支持告警开始(BEGIN)和结束(END)状态管理
- **批量处理**: 支持单个消息中包含多个告警信息
- **错误处理**: 完善的错误处理和响应机制

## 消息格式

### 请求消息 (SendAlarmMessage)

```xml
<Request>
  <PkType>
    <Name>SEND_ALARM</Name>
  </PkType>
  <Info>
    <FSUID>FSU标识</FSUID>
    <Values>
      <TAlarmList>
        <TAlarm SerialNo="告警序号" 
                NMAlarmID="网管告警编号"
                DeviceID="设备ID"
                TSignalId="信号点标识"
                AlarmTime="告警时间"
                AlarmLevel="告警级别"
                AlarmFlag="告警标志"
                AlarmDesc="告警描述"
                EventValue="触发值"
                AlarmRemark="备注"/>
      </TAlarmList>
    </Values>
  </Info>
</Request>
```

### 响应消息 (SendAlarmAckMessage)

```xml
<Response>
  <PkType>
    <Name>SEND_ALARM_ACK</Name>
  </PkType>
  <Info>
    <Result>SUCCESS/FAILURE</Result>
    <FailureCause>失败原因</FailureCause>
  </Info>
</Response>
```

## 告警级别 (AlarmLevel)

| 值 | 名称 | 描述 |
|----|------|------|
| 0 | NOALARM | 正常数据 |
| 1 | CRITICAL | 一级告警 |
| 2 | MAJOR | 二级告警 |
| 3 | MINOR | 三级告警 |
| 4 | HINT | 四级告警 |
| 5 | OPEVENT | 操作事件 |
| 6 | INVALID | 无效数据 |

## 告警标志 (AlarmFlag)

| 值 | 名称 | 描述 |
|----|------|------|
| 0 | BEGIN | 告警开始 |
| 1 | END | 告警结束 |

## 配置参数

### 基本配置

- **fsuId**: FSU标识，必填，最大长度20字节

### 告警处理配置

- **enableValidation**: 是否启用告警数据验证，默认true
- **enableTimeNormalization**: 是否自动标准化告警时间格式，默认true
- **maxAlarmsPerMessage**: 单个消息中允许的最大告警数量，默认100

### 输出配置

- **enablePipelineForward**: 是否将告警数据转发到处理管道，默认true
- **enableResponseGeneration**: 是否生成告警确认响应，默认true

### 日志配置

- **logLevel**: 日志级别 (DEBUG/INFO/WARN/ERROR)，默认INFO
- **enableDetailedLogging**: 是否记录详细的告警处理日志，默认false

## 使用示例

### 1. 基本告警上报

```java
// 创建告警消息
SendAlarmMessage alarmMessage = new SendAlarmMessage();
alarmMessage.setFsuId("FSU001");

// 创建告警信息
TAlarm alarm = new TAlarm();
alarm.setSerialNo("ALM001");
alarm.setDeviceId("DEV001");
alarm.setAlarmLevel(EnumState.CRITICAL);
alarm.setAlarmFlag(EnumFlag.BEGIN);
alarm.setAlarmDesc("设备温度过高");
alarm.setAlarmTime("2024-01-01 10:30:00");

// 添加告警到消息
alarmMessage.addAlarm(alarm);
```

### 2. 批量告警上报

```java
SendAlarmMessage alarmMessage = new SendAlarmMessage();
alarmMessage.setFsuId("FSU001");

// 添加多个告警
for (int i = 0; i < 5; i++) {
    TAlarm alarm = new TAlarm();
    alarm.setSerialNo("ALM00" + i);
    alarm.setDeviceId("DEV00" + i);
    alarm.setAlarmLevel(EnumState.MAJOR);
    alarm.setAlarmFlag(EnumFlag.BEGIN);
    alarmMessage.addAlarm(alarm);
}
```

### 3. 告警清除

```java
TAlarm clearAlarm = new TAlarm();
clearAlarm.setSerialNo("ALM001");
clearAlarm.setDeviceId("DEV001");
clearAlarm.setAlarmLevel(EnumState.NOALARM);
clearAlarm.setAlarmFlag(EnumFlag.END);
clearAlarm.setAlarmDesc("设备温度恢复正常");
```

## 错误处理

### 常见错误

1. **FSU ID为空**: "FSU ID is required"
2. **告警列表为空**: "At least one alarm is required"
3. **告警序号为空**: "Alarm SerialNo is required"
4. **设备ID为空**: "Alarm DeviceID is required"
5. **告警级别为空**: "Alarm Level is required"
6. **告警标志为空**: "Alarm Flag is required"

### 错误响应示例

```xml
<Response>
  <PkType>
    <Name>SEND_ALARM_ACK</Name>
  </PkType>
  <Info>
    <Result>FAILURE</Result>
    <FailureCause>FSU ID is required</FailureCause>
  </Info>
</Response>
```

## 最佳实践

1. **数据验证**: 在发送告警前确保所有必填字段都已填写
2. **时间格式**: 使用标准时间格式 `yyyy-MM-dd HH:mm:ss`
3. **批量处理**: 合理控制单次上报的告警数量，避免消息过大
4. **状态管理**: 正确使用告警开始和结束标志
5. **错误处理**: 妥善处理响应中的错误信息

## 注意事项

- 告警时间如果为空，系统会自动设置为当前时间
- 对于非监控点越限类告警，TSignalId字段应设置为"NULL"
- EventValue字段仅对监控点越限类告警有效
- 系统会自动管理告警的生命周期状态 