package com.siteweb.stream.south.cmcc.shapes;

import com.siteweb.stream.common.annotations.*;
import com.siteweb.stream.common.messages.StreamMessage;
import com.siteweb.stream.common.runtime.AbstractShape;
import com.siteweb.stream.common.stream.ShapeRuntimeContext;
import com.siteweb.stream.common.stream.StreamShapeOption;
import com.siteweb.stream.south.cmcc.options.MobileParserShapeOption;
import com.siteweb.stream.south.cmcc.util.MobileBMessageFactory;
import com.siteweb.tcs.cmcc.common.message.MobileBRawMessage;
import com.siteweb.tcs.cmcc.common.message.MobileBRequestMessage;
import lombok.extern.slf4j.Slf4j;

/**
 * 原始报文解析器
 *
 * <AUTHOR> (2025-05-08)
 **/
@Slf4j
@Shape(type = "cmcc-package-parser")
@ShapeVersion(major = 1, minor = 0, patch = 0)
@ShapeIcon("icon-font")
@ShapeAuthor("Vertiv")
@ShapeColor(bkColor = "#c0edc0")
//@ShapeDefaultOptions(SwitchDefaultOption.class)
@ShapeInlet(id = 0x01, type = MobileBRawMessage.class)
@ShapeOutlet(id = 0x01, type = MobileBRequestMessage.class)
public class CMCCParserShape extends AbstractShape {

    @Recoverable
    private MobileParserShapeOption option;


    public CMCCParserShape(ShapeRuntimeContext context) {
        super(context);
    }

    @Override
    protected void onOptionReset(StreamShapeOption options) {
        if (options instanceof MobileParserShapeOption sendDataShapeOption) {
            option = sendDataShapeOption;
        }
    }

    @Override
    protected void processMessage(StreamMessage in) {
        if (in instanceof MobileBRawMessage rawMessage) {
            var message = MobileBMessageFactory.parseRequest(rawMessage);
            message.setResponseActor(rawMessage.getOriginalAsker());
            context.getOutLet(0x01).broadcast(message, self());
        }
    }


    @Override
    protected void onStart() {
        context.subscribeEntryMessage(self());
        super.onStart();
    }

    @Override
    protected void onStop() {
        context.unSubscribeEntryMessage(self());
        super.onStop();
    }
}
