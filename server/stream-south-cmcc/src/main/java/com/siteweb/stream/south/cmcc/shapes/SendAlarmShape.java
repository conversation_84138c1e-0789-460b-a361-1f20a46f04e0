package com.siteweb.stream.south.cmcc.shapes;

import com.siteweb.stream.common.annotations.*;
import com.siteweb.stream.common.messages.EventMessage;
import com.siteweb.stream.common.messages.StreamMessage;
import com.siteweb.stream.common.runtime.AbstractShape;
import com.siteweb.stream.common.stream.ShapeRuntimeContext;
import com.siteweb.stream.common.stream.StreamShapeOption;
import com.siteweb.stream.south.cmcc.messages.SendAlarmMessage;
import com.siteweb.stream.south.cmcc.options.SendAlarmShapeOption;
import com.siteweb.tcs.cmcc.common.protocol.EnumFlag;
import com.siteweb.tcs.cmcc.common.protocol.EnumState;
import com.siteweb.tcs.cmcc.common.protocol.TAlarm;
import com.siteweb.tcs.common.util.ActorPathBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorSelection;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * 上报告警信息处理Shape
 * <p>
 * 根据中国移动B接口技术规范5.6.2章节实现
 * 负责处理从FSU发送到SC的告警信息，包括：
 * 1. 接收并解析告警数据
 * 2. 验证告警信息格式
 * 3. 转发告警到下游系统
 * 4. 返回确认响应
 *
 * <AUTHOR> from CMCC B Interface Specification 5.6.2
 */
@Slf4j
@Shape(type = "cmcc-send-alarm")
@ShapeVersion(major = 1, minor = 0, patch = 0)
@ShapeIcon("icon-alarm")
@ShapeAuthor("Siteweb")
@ShapeColor(bkColor = "#ffcccc")
@ShapeInlet(id = 0x01, type = SendAlarmMessage.class)
public class SendAlarmShape extends AbstractShape {
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private final ActorSelection pipeline;
    private SendAlarmShapeOption options;

    public SendAlarmShape(ShapeRuntimeContext context) {
        super(context);
        pipeline = ActorPathBuilder.from(this).fallback(2).append("pipeline").toSelection();
        this.options = new SendAlarmShapeOption();
    }

    @Override
    protected void onOptionReset(StreamShapeOption options) {
        if (options instanceof SendAlarmShapeOption alarmOptions) {
            this.options = alarmOptions;
            log.info("Updated SendAlarmShape options: FSU={}, LogLevel={}",
                    alarmOptions.getFsuId(),
                    alarmOptions.getLogging().getLogLevel());
        }
    }

    @Override
    protected void processMessage(StreamMessage in) {
        try {
            if (options.getLogging().isEnableDetailedLogging()) {
                log.debug("Processing alarm message: {}", in);
            }
            if (in instanceof SendAlarmMessage alarmMessage) {
                processAlarmMessage(alarmMessage);
            }
        } catch (Exception e) {
            log.error("Error processing alarm message", e);
            // 发送失败响应
        }
    }

    /**
     * 处理标准告警消息
     */
    private void processAlarmMessage(SendAlarmMessage alarmMessage) {
        log.info("Processing alarm message from FSU: {}, alarm count: {}",
                alarmMessage.getInfo().getFsuId(), alarmMessage.getAlarmCount());

        try {
            // 处理每个告警
            for (TAlarm alarm : alarmMessage.getInfo().getValues().getAlarmList()) {
                processSingleAlarm(alarm, alarmMessage.getInfo().getFsuId());
            }
            // 返回成功响应
            alarmMessage.responseSuccess();
            log.info("Successfully processed {} alarms from FSU: {}", alarmMessage.getAlarmCount(), alarmMessage.getInfo().getFsuId());
        } catch (Exception e) {
            log.error("Error processing alarm message", e);

        }
    }


    /**
     * 处理单个告警
     */
    private void processSingleAlarm(TAlarm alarm, String fsuId) {
        if (options.getLogging().isEnableDetailedLogging()) {
            log.debug("Processing alarm: SerialNo={}, DeviceID={}, AlarmLevel={}, AlarmFlag={}", alarm.getSerialNo(), alarm.getDeviceId(), alarm.getAlarmLevel(), alarm.getAlarmFlag());
        }

        // 创建输出数据
        Map<String, Object> outputData = Map.of(
                "fsuId", fsuId,
                "alarm", alarm,
                "processTime", LocalDateTime.now().format(TIME_FORMATTER),
                "messageType", "ALARM"
        );

        // 发送到pipeline处理
        pipeline.tell(outputData, self());
    }

} 