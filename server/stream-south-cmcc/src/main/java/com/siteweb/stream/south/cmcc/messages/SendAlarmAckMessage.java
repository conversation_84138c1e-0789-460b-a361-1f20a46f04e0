package com.siteweb.stream.south.cmcc.messages;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.siteweb.tcs.cmcc.common.message.MobileBResponseMessage;
import com.siteweb.tcs.cmcc.common.protocol.EnumResult;
import com.siteweb.tcs.cmcc.common.protocol.PK_TypeName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 上报告警信息应答报文
 * 
 * 根据中国移动B接口技术规范5.6.2章节实现
 * SC返回告警信息接收确认，包含处理结果和失败原因
 * 
 * <AUTHOR> from CMCC B Interface Specification 5.6.2
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JacksonXmlRootElement(localName = "Response")
public class SendAlarmAckMessage extends MobileBResponseMessage {

    public SendAlarmAckMessage() {
        super(PK_TypeName.SEND_ALARM_ACK);
    }

    /**
     * 构造失败响应
     * @param failureCause 失败原因
     * @return 失败响应消息
     */
    public static SendAlarmAckMessage failure(String failureCause) {
        SendAlarmAckMessage message = new SendAlarmAckMessage();
        message.getInfo().setResult(EnumResult.FAILURE);
        message.getInfo().setFailureCause(failureCause);
        return message;
    }

    /**
     * 构造成功响应
     * @return 成功响应消息
     */
    public static SendAlarmAckMessage success() {
        SendAlarmAckMessage message = new SendAlarmAckMessage();
        message.getInfo().setResult(EnumResult.SUCCESS);
        message.getInfo().setFailureCause("NULL");
        return message;
    }
} 