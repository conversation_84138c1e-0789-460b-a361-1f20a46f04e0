package com.siteweb.stream.south.cmcc.options;

import com.siteweb.stream.common.stream.StreamShapeOption;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 上报告警信息Shape配置选项
 * 
 * 根据中国移动B接口技术规范5.6.2章节实现
 * 
 * <AUTHOR> from CMCC B Interface Specification 5.6.2
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SendAlarmShapeOption extends StreamShapeOption {

    /**
     * FSU标识
     * 长度为20字节
     */
    private String fsuId;
    /**
     * 日志配置
     */
    private Logging logging = new Logging();


    @Data
    public static class Logging {
        /**
         * 日志级别
         */
        private String logLevel = "INFO";

        /**
         * 是否记录详细的告警处理日志
         */
        private boolean enableDetailedLogging = false;
    }
} 