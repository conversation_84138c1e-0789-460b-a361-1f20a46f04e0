package com.siteweb.stream.south.cmcc.messages;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.siteweb.tcs.cmcc.common.message.MobileBRequestMessage;
import com.siteweb.tcs.cmcc.common.protocol.PK_TypeName;
import com.siteweb.tcs.cmcc.common.protocol.TAlarm;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 上报告警信息报文
 * 
 * 根据中国移动B接口技术规范5.6.2章节实现
 * FSU根据设备产生告警或者根据遥测量判断有告警需上报时，向SC上报告警信息
 * 
 * <AUTHOR> from CMCC B Interface Specification 5.6.2
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JacksonXmlRootElement(localName = "Request")
public class SendAlarmMessage extends MobileBRequestMessage {

    @JsonProperty("Info")
    @JacksonXmlProperty(localName = "Info")
    private Info info = new Info();

    public SendAlarmMessage() {
        super(PK_TypeName.SEND_ALARM);
    }

    @Setter
    @Getter
    public static class Info {
        /**
         * FSU ID号
         * 长度为FSUID_LEN (20字节)
         */
        @JsonProperty("FSUID")
        @JacksonXmlProperty(localName = "FSUID")
        private String fsuId;

        /**
         * 告警信息
         * 包含一个或多个TAlarm告警信息
         */
        @JsonProperty("Values")
        @JacksonXmlProperty(localName = "Values")
        private Values values = new Values();
    }

    @Setter
    @Getter
    public static class Values {
        /**
         * 告警信息列表
         */
        @JacksonXmlElementWrapper(localName = "TAlarmList")
        @JacksonXmlProperty(localName = "TAlarm")
        @JsonProperty("TAlarm")
        private List<TAlarm> alarmList = new ArrayList<>();
    }

    /**
     * 添加告警信息
     * @param alarm 告警对象
     */
    public void addAlarm(TAlarm alarm) {
        if (this.info.values.alarmList == null) {
            this.info.values.alarmList = new ArrayList<>();
        }
        this.info.values.alarmList.add(alarm);
    }

    /**
     * 设置FSU ID
     * @param fsuId FSU标识
     */
    public void setFsuId(String fsuId) {
        this.info.fsuId = fsuId;
    }

    /**
     * 获取告警数量
     * @return 告警数量
     */
    public int getAlarmCount() {
        return this.info.values.alarmList != null ? this.info.values.alarmList.size() : 0;
    }
} 