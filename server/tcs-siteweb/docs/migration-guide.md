我们正在进行配置工具迁移项目，将原有的tcs-config工具的接口集成到SitewebPersistentService的ConfigAPI子模块中。具体迁移策略如下：

## 迁移目标和架构
- **目标**：将tcs-config的核心接口迁移到tcs-siteweb项目的SitewebPersistentService.ConfigAPI中
- **架构分层**：不直接调用tcs-config，而是将完整的业务逻辑层（service/provider/manager → mapper → mapper.xml）复制到tcs-siteweb中
- **控制器层**：在tcs-south-omc-siteweb项目中创建对应的controller，移除原有的`/api/config`前缀

## 迁移步骤和检查点
1. **现状评估**：由于已经迁移了大量接口，需要先检查目标接口是否已经在tcs-siteweb中实现
2. **逐层迁移**：按照controller → service/provider/manager → mapper → mapper.xml的顺序进行迁移
3. **完整性验证**：确保每个接口的完整调用链都已正确迁移

## 技术约束和规范

### 1. 代码完整性要求
- 必须参考原tcs-config工具的完整逻辑和调用链
- 缺少的方法、字段、DTO、VO类必须完整添加
- **严禁使用弱类型**：禁止使用Object、通配符?等弱类型定义

### 2. Controller层规范
- 返回值类型：统一使用`ResponseEntity<ResponseResult>`
- 参考示例：参照tcs-south-omc-siteweb中现有的SamplerController实现
- URL映射：优先保持与原tcs-config工具的请求URL一致（移除/api/config前缀）

### 3. 事务管理规范
- 所有写操作方法（创建、更新、删除）必须在业务层（Service/Provider）的公开方法上添加`@Transactional`注解
- 确保数据一致性和事务回滚机制

### 4. ConfigAPI方法命名规范
- 在ConfigAPI中封装tcs-siteweb方法时，命名格式：`原方法名 + For + 实体名`
- 示例：创建设备接口命名为`createForEquipment`
- 保持方法语义清晰和命名一致性

## 实施注意事项
- 迁移前先检查目标功能是否已存在，避免重复工作
- 保持原有业务逻辑的完整性和正确性
- 确保新旧系统的接口兼容性
- 代码优美，避免冗余繁杂