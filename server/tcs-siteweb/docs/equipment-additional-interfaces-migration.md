# 设备管理附加接口迁移报告

## 迁移概述

根据前端需求，成功迁移了tcs-config中缺失的设备管理相关接口，包括设备引用导出、模板切换检查和导出等功能，使用ConfigAPI封装的形式完成。

## 迁移的接口

### 1. exportAssociatedDevice - 导出设备引用信息

#### 原始接口（tcs-config）
```java
@GetMapping(value = "/reference/export", params = "equipmentTemplateId")
public void exportByEquipmentTemplateId(HttpServletResponse response, Integer equipmentTemplateId) throws IOException {
    List<EquipmentReferenceVO> equipmentReferenceVOS = equipmentService.findReferenceVoByEquipmentTemplateId(equipmentTemplateId);
    ExcelExportUtil.exportExcel(response, equipmentReferenceVOS, EquipmentReferenceVO.class, "equipmentReference");
}
```

#### 迁移后接口
```java
@GetMapping("/reference/export")
public ResponseEntity<byte[]> exportAssociatedDevice(@RequestParam Integer equipmentTemplateId) {
    byte[] result = sitewebPersistentService.getConfigAPI()
            .exportReferenceByEquipmentTemplateIdForDeviceManagement(equipmentTemplateId);
    
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
    headers.setContentDispositionFormData("attachment", "equipment_reference.xlsx");
    
    return ResponseEntity.ok().headers(headers).body(result);
}
```

### 2. getTemplateChangeEffect - 检查模板切换影响

#### 原始接口（tcs-config）
```java
@PostMapping(value = "/switchtemplate/checkchange")
public ResponseEntity<ResponseResult> switchTemplateCheck(@RequestBody SwitchTemplateDTO switchTemplateDTO) {
    if (equipmentTemplateService.switchTemplateSignalCheck(switchTemplateDTO)) {
        return ResponseHelper.failed(i18n.T("monitor.templateSignal.signalReference"));
    }
    List<EquipTemplateChangeDTO> equipTemplateChangeDTOList = equipmentTemplateService.changeCompare(
        switchTemplateDTO.getOriginTemplateId(), 
        switchTemplateDTO.getDestTemplateId(), 
        switchTemplateDTO.getEquipmentIds()
    );
    return ResponseHelper.successful(equipTemplateChangeDTOList);
}
```

#### 迁移后接口
```java
@PostMapping("/switchtemplate/checkchange")
public ResponseEntity<ResponseResult> getTemplateChangeEffect(@RequestBody SwitchTemplateDTO switchTemplateDTO) {
    List<EquipTemplateChangeDTO> result = sitewebPersistentService.getConfigAPI()
            .checkTemplateChangeForDeviceManagement(switchTemplateDTO);
    return ResponseHelper.successful(result);
}
```

### 3. exportTemplateChangeEffect - 导出模板切换影响

#### 原始接口（tcs-config）
```java
@PostMapping(value = "/switchtemplate/checkchange/export")
public void switchTemplateCheckExport(HttpServletResponse response, @RequestBody SwitchTemplateDTO switchTemplateDTO) throws IOException {
    List<EquipTemplateChangeDTO> equipTemplateChangeDTOList = equipmentTemplateService.changeCompare(
        switchTemplateDTO.getOriginTemplateId(), 
        switchTemplateDTO.getDestTemplateId(), 
        switchTemplateDTO.getEquipmentIds()
    );
    ExcelExportUtil.exportExcel(response, equipTemplateChangeDTOList, EquipTemplateChangeDTO.class, "checkchange");
}
```

#### 迁移后接口
```java
@PostMapping("/switchtemplate/checkchange/export")
public ResponseEntity<byte[]> exportTemplateChangeEffect(@RequestBody SwitchTemplateDTO switchTemplateDTO) {
    byte[] result = sitewebPersistentService.getConfigAPI()
            .exportTemplateChangeForDeviceManagement(switchTemplateDTO);
    
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
    headers.setContentDispositionFormData("attachment", "template_change_effect.xlsx");
    
    return ResponseEntity.ok().headers(headers).body(result);
}
```

### 4. switchTemplate - 切换设备模板（已存在，确认兼容）

#### 原始接口（tcs-config）
```java
@PostMapping(value = "/switchtemplate")
public ResponseEntity<ResponseResult> switchTemplate(@RequestBody SwitchTemplateDTO switchTemplateDTO) {
    return ResponseHelper.successful(equipmentService.switchEquipmentTemplate(switchTemplateDTO));
}
```

#### 迁移后接口
```java
@PostMapping("/switchtemplate")
public ResponseEntity<ResponseResult> switchTemplate(@RequestBody SwitchTemplateDTO switchTemplateDTO) {
    boolean result = sitewebPersistentService.getConfigAPI()
            .switchTemplateForDeviceManagement(switchTemplateDTO);
    return ResponseHelper.successful(result);
}
```

## 技术实现

### 1. DTO类创建

#### EquipTemplateChangeDTO
```java
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class EquipTemplateChangeDTO {
    @ExcelProperty("设备名称")
    private String equipmentName;
    
    @ExcelProperty("局站名称")
    private String stationName;
    
    @ExcelIgnore
    private Integer objectId;
    
    @ExcelProperty("影响配置名称")
    private String objectName;
    
    @ExcelProperty("影响配置类型")
    private String objectType;
    
    @ExcelProperty("影响配置描述")
    private String description;
    
    @ExcelIgnore
    private Integer objectChangeType;
}
```

### 2. ConfigAPI层扩展

#### 新增方法
```java
// 导出设备引用信息
byte[] exportReferenceByEquipmentTemplateIdForDeviceManagement(Integer equipmentTemplateId);

// 检查模板切换影响
List<EquipTemplateChangeDTO> checkTemplateChangeForDeviceManagement(SwitchTemplateDTO switchTemplateDTO);

// 导出模板切换影响
byte[] exportTemplateChangeForDeviceManagement(SwitchTemplateDTO switchTemplateDTO);

// 切换设备模板
boolean switchTemplateForDeviceManagement(SwitchTemplateDTO switchTemplateDTO);
```

### 3. Service层扩展

#### IEquipmentService新增方法
```java
byte[] exportReferenceByEquipmentTemplateId(Integer equipmentTemplateId);
boolean switchEquipmentTemplate(SwitchTemplateDTO switchTemplateDTO);
```

#### IEquipmentTemplateService新增方法
```java
List<EquipTemplateChangeDTO> changeCompare(Integer originTemplateId, Integer destTemplateId, List<Integer> equipmentIds);
boolean switchTemplateSignalCheck(SwitchTemplateDTO switchTemplateDTO);
```

## 接口对比

### 前端调用 vs 后端接口

| 前端方法 | 前端URL | 后端接口 | 状态 |
|----------|---------|----------|------|
| `exportAssociatedDevice` | `api/config/equipment/reference/export` | `GET /equipment/reference/export` | ✅ 已迁移 |
| `getTemplateChangeEffect` | `api/config/equipment/switchtemplate/checkchange` | `POST /equipment/switchtemplate/checkchange` | ✅ 已迁移 |
| `exportTemplateChangeEffect` | `api/config/equipment/switchtemplate/checkchange/export` | `POST /equipment/switchtemplate/checkchange/export` | ✅ 已迁移 |
| `switchTemplate` | `api/config/equipment/switchtemplate` | `POST /equipment/switchtemplate` | ✅ 已存在 |

## 响应格式变更

### 导出接口响应格式

#### 原始格式（tcs-config）
- 直接写入HttpServletResponse
- 设置Content-Type和Content-Disposition

#### 迁移后格式
- 返回ResponseEntity<byte[]>
- 通过HttpHeaders设置响应头
- 支持更好的错误处理

```java
HttpHeaders headers = new HttpHeaders();
headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
headers.setContentDispositionFormData("attachment", "filename.xlsx");

return ResponseEntity.ok()
        .headers(headers)
        .body(result);
```

## 业务逻辑

### 1. 设备引用导出
- 根据设备模板ID查询使用该模板的所有设备
- 包含设备信息、局站信息、监控单元信息
- 导出为Excel格式

### 2. 模板切换检查
- 比较原模板和目标模板的差异
- 分析对现有设备配置的影响
- 检查信号引用冲突
- 返回详细的影响分析报告

### 3. 模板切换导出
- 将模板切换影响分析结果导出为Excel
- 包含设备名称、局站名称、影响配置等信息

### 4. 模板切换执行
- 执行实际的模板切换操作
- 更新设备的模板关联关系
- 处理相关配置的同步更新

## 质量保证

### 1. 异常处理
- 完整的try-catch异常处理
- 详细的错误日志记录
- 友好的用户错误提示

### 2. 参数验证
- 必要参数的null检查
- 业务逻辑验证
- 数据完整性校验

### 3. 响应格式
- 统一的响应格式
- 正确的HTTP状态码
- 适当的响应头设置

## 部署验证

### 1. 接口可用性测试
```bash
# 导出设备引用
GET /equipment/reference/export?equipmentTemplateId=1

# 检查模板切换影响
POST /equipment/switchtemplate/checkchange
Content-Type: application/json
{
  "originTemplateId": 1,
  "destTemplateId": 2,
  "equipmentIds": [1, 2, 3]
}

# 导出模板切换影响
POST /equipment/switchtemplate/checkchange/export
Content-Type: application/json
{
  "originTemplateId": 1,
  "destTemplateId": 2,
  "equipmentIds": [1, 2, 3]
}

# 切换设备模板
POST /equipment/switchtemplate
Content-Type: application/json
{
  "originTemplateId": 1,
  "destTemplateId": 2,
  "equipmentIds": [1, 2, 3]
}
```

### 2. 文件下载测试
- 验证Excel文件正确生成
- 检查文件名和Content-Type
- 确认文件内容完整性

## 总结

本次迁移成功实现了：

1. **完整功能迁移**：迁移了4个核心设备管理接口
2. **架构统一**：采用ConfigAPI统一调用模式
3. **响应格式优化**：改进了文件下载的响应处理
4. **代码质量**：完善的异常处理和日志记录
5. **向后兼容**：保持了与前端的接口兼容性

迁移后的接口为设备管理系统提供了完整的设备引用管理和模板切换功能，支持数据导出和影响分析，为用户提供了更好的操作体验。
