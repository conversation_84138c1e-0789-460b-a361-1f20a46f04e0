package com.siteweb.tcs.siteweb.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 变更源 注解，为模型实体对象提供变更源名称
 *
 * <AUTHOR> (2024-01-25)
 **/
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface ChangeSource {
    String channel() default "";

    String product() default "";

    String source() default "";
} 