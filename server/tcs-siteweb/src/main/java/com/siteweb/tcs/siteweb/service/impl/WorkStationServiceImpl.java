package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.dto.CenterDTO;
import com.siteweb.tcs.siteweb.entity.DataItem;
import com.siteweb.tcs.siteweb.entity.WorkStation;
import com.siteweb.tcs.siteweb.enums.DataEntryEnum;
import com.siteweb.tcs.siteweb.enums.TableIdentityEnum;
import com.siteweb.tcs.siteweb.enums.WorkStationStateEnum;
import com.siteweb.tcs.siteweb.enums.WorkStationTypeEnum;
import com.siteweb.tcs.siteweb.mapper.WorkStationMapper;
import com.siteweb.tcs.siteweb.service.IDataItemService;
import com.siteweb.tcs.siteweb.service.IPrimaryKeyValueService;
import com.siteweb.tcs.siteweb.service.IWorkStationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Work Station Service Implementation
 */
@Slf4j
@Service
public class WorkStationServiceImpl extends ServiceImpl<WorkStationMapper, WorkStation> implements IWorkStationService {

    @Autowired
    private WorkStationMapper workStationMapper;

    @Autowired
    private IPrimaryKeyValueService primaryKeyValueService;

    @Autowired
    private IDataItemService dataItemService;

    @Value("${server.replica-count:1}")
    private int replicaCount;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createDefaultWorkStation(CenterDTO centerDTO) {
        if (centerDTO == null) {
            log.warn("Cannot create default work station: center DTO is null");
            return;
        }

        try {
            Map<Integer, DataItem> dataItemMap = new HashMap<>();
            List<WorkStation> workStations = new ArrayList<>();
            List<DataItem> dataItems = dataItemService.list(new QueryWrapper<DataItem>().eq("EntryId", DataEntryEnum.WORKSTATION_CATEGORY.getValue()));
            for (DataItem dataItem : dataItems) {
                dataItemMap.put(dataItem.getItemId(), dataItem);
            }

            // 应用服务器
            WorkStation workStationAS = new WorkStation();
            int asWorkStationId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_WORKSTATION, 0);
            workStationAS.setWorkStationId(asWorkStationId);
            workStationAS.setWorkStationType(WorkStationTypeEnum.APPLICATION_SERVER.getValue());
            workStationAS.setWorkStationName(centerDTO.getCenterName().trim() + centerDTO.getCenterId() + " " + dataItemMap.get(WorkStationTypeEnum.APPLICATION_SERVER.getValue()).getItemValue());
            workStationAS.setConnectState(WorkStationStateEnum.HALT.getValue());
            workStationAS.setUpdateTime(LocalDateTime.now());
            workStationAS.setIpAddress(centerDTO.getCenterIp());
            workStationAS.setParentId(0);
            workStationAS.setIsUsed(Boolean.FALSE);
            workStations.add(workStationAS);

            // 备用应用服务器
            WorkStation workStationAS2 = new WorkStation();
            int as2WorkStationId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_WORKSTATION, 0);
            workStationAS2.setWorkStationId(as2WorkStationId);
            workStationAS2.setWorkStationType(WorkStationTypeEnum.APPLICATION_SERVER_II.getValue());
            workStationAS2.setWorkStationName(centerDTO.getCenterName().trim() + centerDTO.getCenterId() + " " + dataItemMap.get(WorkStationTypeEnum.APPLICATION_SERVER_II.getValue()).getItemValue());
            workStationAS2.setConnectState(WorkStationStateEnum.HALT.getValue());
            workStationAS2.setUpdateTime(LocalDateTime.now());
            workStationAS2.setIpAddress(centerDTO.getCenterIp());
            workStationAS2.setParentId(asWorkStationId);
            workStationAS2.setIsUsed(Boolean.FALSE);
            workStations.add(workStationAS2);

            // 业务服务器
            WorkStation workStationBS = new WorkStation();
            int bsWorkStationId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_WORKSTATION, 0);
            workStationBS.setWorkStationId(bsWorkStationId);
            workStationBS.setWorkStationType(WorkStationTypeEnum.BUSINESS_SERVER.getValue());
            workStationBS.setWorkStationName(centerDTO.getCenterName().trim() + centerDTO.getCenterId() + " " + dataItemMap.get(WorkStationTypeEnum.BUSINESS_SERVER.getValue()).getItemValue());
            workStationBS.setConnectState(WorkStationStateEnum.HALT.getValue());
            workStationBS.setUpdateTime(LocalDateTime.now());
            workStationBS.setIpAddress(centerDTO.getCenterIp());
            workStationBS.setParentId(asWorkStationId);
            workStationBS.setIsUsed(Boolean.TRUE);
            workStations.add(workStationBS);

            // 备用业务服务器
            WorkStation workStationBS2 = new WorkStation();
            int bs2WorkStationId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_WORKSTATION, 0);
            workStationBS2.setWorkStationId(bs2WorkStationId);
            workStationBS2.setWorkStationType(WorkStationTypeEnum.BUSINESS_SERVER_II.getValue());
            workStationBS2.setWorkStationName(centerDTO.getCenterName().trim() + centerDTO.getCenterId() + " " + dataItemMap.get(WorkStationTypeEnum.BUSINESS_SERVER_II.getValue()).getItemValue());
            workStationBS2.setConnectState(WorkStationStateEnum.HALT.getValue());
            workStationBS2.setUpdateTime(LocalDateTime.now());
            workStationBS2.setIpAddress(centerDTO.getCenterIp());
            workStationBS2.setParentId(asWorkStationId);
            workStationBS2.setIsUsed(Boolean.FALSE);
            workStations.add(workStationBS2);

            // 数据服务器
            WorkStation workStationDS = new WorkStation();
            int dsWorkStationId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_WORKSTATION, 0);
            workStationDS.setWorkStationId(dsWorkStationId);
            workStationDS.setWorkStationType(WorkStationTypeEnum.DATA_SERVER.getValue());
            workStationDS.setWorkStationName(centerDTO.getCenterName().trim() + centerDTO.getCenterId() + " " + dataItemMap.get(WorkStationTypeEnum.DATA_SERVER.getValue()).getItemValue());
            workStationDS.setConnectState(WorkStationStateEnum.HALT.getValue());
            workStationDS.setUpdateTime(LocalDateTime.now());
            workStationDS.setIpAddress(centerDTO.getCenterIp());
            workStationDS.setParentId(asWorkStationId);
            workStationDS.setIsUsed(Boolean.TRUE);
            workStations.add(workStationDS);

            // 空两个主键
            primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_WORKSTATION, 0);
            primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_WORKSTATION, 0);

            // 数据库服务器
            WorkStation workStationDB = new WorkStation();
            int dbWorkStationId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_WORKSTATION, 0);
            workStationDB.setWorkStationId(dbWorkStationId);
            workStationDB.setWorkStationType(WorkStationTypeEnum.DATABASE_SERVER.getValue());
            workStationDB.setWorkStationName(centerDTO.getCenterName().trim() + centerDTO.getCenterId() + " " + dataItemMap.get(WorkStationTypeEnum.DATABASE_SERVER.getValue()).getItemValue());
            workStationDB.setConnectState(WorkStationStateEnum.HALT.getValue());
            workStationDB.setUpdateTime(LocalDateTime.now());
            workStationDB.setIpAddress(centerDTO.getCenterIp());
            workStationDB.setParentId(asWorkStationId);
            workStationDB.setIsUsed(Boolean.FALSE);
            workStations.add(workStationDB);

            // 通知服务器
            WorkStation workStationNS = new WorkStation();
            int nsWorkStationId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_WORKSTATION, 0);
            workStationNS.setWorkStationId(nsWorkStationId);
            workStationNS.setWorkStationType(WorkStationTypeEnum.NOTIFICATION_SERVER.getValue());
            workStationNS.setWorkStationName(centerDTO.getCenterName().trim() + centerDTO.getCenterId() + dataItemMap.get(WorkStationTypeEnum.NOTIFICATION_SERVER.getValue()).getItemValue());
            workStationNS.setConnectState(WorkStationStateEnum.HALT.getValue());
            workStationNS.setUpdateTime(LocalDateTime.now());
            workStationNS.setIpAddress(centerDTO.getCenterIp());
            workStationNS.setParentId(asWorkStationId);
            workStationNS.setIsUsed(Boolean.FALSE);
            workStations.add(workStationNS);

            // 实时数据服务器
            WorkStation workStationRDS = new WorkStation();
            int rdsWorkStationId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_WORKSTATION, 0);
            workStationRDS.setWorkStationId(rdsWorkStationId);
            workStationRDS.setWorkStationType(WorkStationTypeEnum.REALTIME_DATA_SERVER.getValue());
            workStationRDS.setWorkStationName(centerDTO.getCenterName().trim() + centerDTO.getCenterId() + dataItemMap.get(WorkStationTypeEnum.REALTIME_DATA_SERVER.getValue()).getItemValue());
            workStationRDS.setConnectState(WorkStationStateEnum.HALT.getValue());
            workStationRDS.setUpdateTime(LocalDateTime.now());
            workStationRDS.setIpAddress(centerDTO.getCenterIp());
            workStationRDS.setParentId(asWorkStationId);
            workStationRDS.setIsUsed(Boolean.TRUE);
            workStations.add(workStationRDS);

            // 移动接口服务器
            WorkStation workStationMobile = new WorkStation();
            int mobileWorkStationId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_WORKSTATION, 0);
            workStationMobile.setWorkStationId(mobileWorkStationId);
            workStationMobile.setWorkStationType(WorkStationTypeEnum.MOBILE_INTERFACE_SERVER.getValue());
            workStationMobile.setWorkStationName(centerDTO.getCenterName().trim() + centerDTO.getCenterId() + dataItemMap.get(WorkStationTypeEnum.MOBILE_INTERFACE_SERVER.getValue()).getItemValue());
            workStationMobile.setConnectState(WorkStationStateEnum.HALT.getValue());
            workStationMobile.setUpdateTime(LocalDateTime.now());
            workStationMobile.setIpAddress(centerDTO.getCenterIp());
            workStationMobile.setParentId(asWorkStationId);
            workStationMobile.setIsUsed(Boolean.FALSE);
            workStations.add(workStationMobile);

            // 创建8个通知服务器
            for (int i = 0; i < 8; i++) {
                WorkStation ns = new WorkStation();
                int nsId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_WORKSTATION, 0);
                ns.setWorkStationId(nsId);
                ns.setWorkStationType(WorkStationTypeEnum.NOTIFICATION_SERVER.getValue());
                ns.setWorkStationName(centerDTO.getCenterName().trim() + centerDTO.getCenterId() + dataItemMap.get(WorkStationTypeEnum.NOTIFICATION_SERVER.getValue()).getItemValue() + (i + 1) + "#");
                ns.setConnectState(WorkStationStateEnum.HALT.getValue());
                ns.setUpdateTime(LocalDateTime.now());
                ns.setIpAddress(centerDTO.getCenterIp());
                ns.setParentId(asWorkStationId);
                ns.setIsUsed(Boolean.FALSE);
                workStations.add(ns);
            }

            // 创建48个远程监控单元
            for (int i = 0; i < 48; i++) {
                WorkStation rmu = new WorkStation();
                int rmuId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_WORKSTATION, 0);
                rmu.setWorkStationId(rmuId);
                rmu.setWorkStationType(WorkStationTypeEnum.RMU.getValue());
                rmu.setWorkStationName(centerDTO.getCenterName().trim() + " " + centerDTO.getCenterId() + " " + dataItemMap.get(WorkStationTypeEnum.RMU.getValue()).getItemValue() + (i + 1) + "#");
                rmu.setConnectState(WorkStationStateEnum.HALT.getValue());
                rmu.setUpdateTime(LocalDateTime.now());
                rmu.setIpAddress(centerDTO.getCenterIp());
                rmu.setParentId(asWorkStationId);
                rmu.setIsUsed(Boolean.FALSE);
                workStations.add(rmu);
            }

            // 创建后台应用服务器
            List<WorkStation> backendWorkStations = initBackendWorkStation(centerDTO, dataItemMap, asWorkStationId);
            workStations.addAll(backendWorkStations);

            // 批量插入工作站
            batchInsertWorkStation(workStations);

            log.info("Created {} default work stations for center: {}", workStations.size(), centerDTO.getCenterName());
        } catch (Exception e) {
            log.error("Failed to create default work stations: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 初始化后台工作站
     *
     * @param centerDTO 中心DTO
     * @param dataItemMap 数据项映射
     * @param asWorkStationId 应用服务器ID
     * @return 后台工作站列表
     */
    private List<WorkStation> initBackendWorkStation(CenterDTO centerDTO, Map<Integer, DataItem> dataItemMap, int asWorkStationId) {
        List<WorkStation> workStations = new ArrayList<>();
        String baseWorkStationName = centerDTO.getCenterName().trim() + centerDTO.getCenterId() + "-" + "SiteWeb";

        for (int i = 0; i < replicaCount; i++) {
            WorkStation siteWebWorkStation = new WorkStation();
            int siteWebWorkStationId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_WORKSTATION, 0);
            siteWebWorkStation.setWorkStationId(siteWebWorkStationId);
            siteWebWorkStation.setWorkStationType(WorkStationTypeEnum.BACKGROUD_SERVER.getValue());

            if (replicaCount == 1) {
                siteWebWorkStation.setWorkStationName(baseWorkStationName + dataItemMap.get(WorkStationTypeEnum.BACKGROUD_SERVER.getValue()).getItemValue());
            } else if (replicaCount == 2) {
                if (i == 0) {
                    siteWebWorkStation.setWorkStationName(baseWorkStationName + " " + "主" + dataItemMap.get(WorkStationTypeEnum.BACKGROUD_SERVER.getValue()).getItemValue());
                } else {
                    siteWebWorkStation.setWorkStationName(baseWorkStationName + " " + "从" + dataItemMap.get(WorkStationTypeEnum.BACKGROUD_SERVER.getValue()).getItemValue());
                }
            } else {
                siteWebWorkStation.setWorkStationName(baseWorkStationName + " " + dataItemMap.get(WorkStationTypeEnum.BACKGROUD_SERVER.getValue()).getItemValue() + (i + 1));
            }

            siteWebWorkStation.setConnectState(WorkStationStateEnum.HALT.getValue());
            siteWebWorkStation.setUpdateTime(LocalDateTime.now());
            siteWebWorkStation.setIpAddress(centerDTO.getCenterIp());
            siteWebWorkStation.setParentId(asWorkStationId);
            siteWebWorkStation.setIsUsed(Boolean.TRUE);
            workStations.add(siteWebWorkStation);
        }

        return workStations;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInsertWorkStation(List<WorkStation> workStations) {
        if (workStations == null || workStations.isEmpty()) {
            log.warn("Cannot batch insert work stations: work stations list is null or empty");
            return;
        }

        try {
            workStationMapper.batchInsert(workStations);
            log.info("Batch inserted {} work stations", workStations.size());
        } catch (Exception e) {
            log.error("Failed to batch insert work stations: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public WorkStation findByWorkStationId(int workStationId) {
        return workStationMapper.selectOne(new QueryWrapper<WorkStation>().eq("WorkStationId", workStationId));
    }

    @Override
    public List<WorkStation> findByWorkStationType(WorkStationTypeEnum workStationTypeEnum) {
        if (workStationTypeEnum == null) {
            log.warn("Cannot find work stations by type: work station type enum is null");
            return new ArrayList<>();
        }

        return workStationMapper.selectList(new QueryWrapper<WorkStation>().eq("WorkStationType", workStationTypeEnum.getValue()));
    }

    @Override
    public List<WorkStation> findDsWorkStations() {
        return workStationMapper.selectList(new QueryWrapper<WorkStation>()
                .eq("WorkStationType", WorkStationTypeEnum.DATA_SERVER.getValue())
                .eq("IsUsed", Boolean.TRUE));
    }

    @Override
    public List<WorkStation> findRDsWorkStations() {
        return workStationMapper.selectList(new QueryWrapper<WorkStation>()
                .eq("WorkStationType", WorkStationTypeEnum.REALTIME_DATA_SERVER.getValue())
                .eq("IsUsed", Boolean.TRUE));
    }
}
