package com.siteweb.tcs.siteweb.util;

import com.siteweb.tcs.siteweb.dto.ConfigImportResult;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * XML配置解析器
 */
@Slf4j
@Component
public class ConfigXmlParser {

    /**
     * 解析监控单元配置XML
     */
    public ConfigImportResult parseConfigXml(File xmlFile) {
        try {
            SAXReader reader = new SAXReader();
            Document document = reader.read(xmlFile);
            Element root = document.getRootElement();

            ConfigImportResult result = new ConfigImportResult();
            result.setXmlFilePath(xmlFile.getAbsolutePath());

            // 解析应用配置
            Element appConfiguration = root.element("AppConfiguration");
            if (appConfiguration != null) {
                result.setAppConfiguration(appConfiguration);
            }

            // 解析设备模板
            Element equipmentTemplates = root.element("EquipmentTemplates");
            if (equipmentTemplates != null) {
                result.setEquipmentTemplates(parseEquipmentTemplates(equipmentTemplates));
            }

            // 解析监控单元（从XML文件名和内容推断）
            ConfigImportResult.MonitorUnitImportData monitorUnit = parseMonitorUnitFromXml(xmlFile, root);
            if (monitorUnit != null) {
                result.setMonitorUnit(monitorUnit);
            }

            return result;

        } catch (Exception e) {
            log.error("解析XML配置文件失败: {}", xmlFile.getAbsolutePath(), e);
            throw new RuntimeException("解析XML配置文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析设备模板列表
     */
    private List<ConfigImportResult.EquipmentTemplateImportData> parseEquipmentTemplates(Element equipmentTemplates) {
        List<ConfigImportResult.EquipmentTemplateImportData> templates = new ArrayList<>();

        @SuppressWarnings("unchecked")
        List<Element> templateElements = equipmentTemplates.elements("EquipmentTemplate");

        for (Element templateElement : templateElements) {
            ConfigImportResult.EquipmentTemplateImportData template = new ConfigImportResult.EquipmentTemplateImportData();

            // 解析设备模板基本信息
            template.setEquipmentTemplateId(parseIntAttribute(templateElement, "EquipmentTemplateId"));
            template.setEquipmentTemplateName(templateElement.attributeValue("EquipmentTemplateName"));
            template.setProtocolCode(templateElement.attributeValue("ProtocolCode"));
            template.setEquipmentCategory(parseIntAttribute(templateElement, "EquipmentCategory"));
            template.setEquipmentType(parseIntAttribute(templateElement, "EquipmentType"));
            template.setMemo(templateElement.attributeValue("Memo"));
            template.setProperty(templateElement.attributeValue("Property"));
            template.setDescription(templateElement.attributeValue("Decription")); // 注意XML中是Decription
            template.setEquipmentBaseType(parseIntAttribute(templateElement, "EquipmentBaseType"));

            // 解析信号
            Element signalsElement = templateElement.element("Signals");
            if (signalsElement != null) {
                template.setSignals(parseSignals(signalsElement));
            }

            // 解析事件
            Element eventsElement = templateElement.element("Events");
            if (eventsElement != null) {
                template.setEvents(parseEvents(eventsElement));
            }

            // 解析控制
            Element controlsElement = templateElement.element("Controls");
            if (controlsElement != null) {
                template.setControls(parseControls(controlsElement));
            }

            templates.add(template);
        }

        return templates;
    }

    /**
     * 解析信号列表
     */
    private List<ConfigImportResult.SignalImportData> parseSignals(Element signalsElement) {
        List<ConfigImportResult.SignalImportData> signals = new ArrayList<>();

        @SuppressWarnings("unchecked")
        List<Element> signalElements = signalsElement.elements("Signal");

        for (Element signalElement : signalElements) {
            ConfigImportResult.SignalImportData signal = new ConfigImportResult.SignalImportData();

            signal.setSignalId(parseIntAttribute(signalElement, "SignalId"));
            signal.setSignalName(signalElement.attributeValue("SignalName"));
            signal.setSignalCategory(parseIntAttribute(signalElement, "SignalCategory"));
            signal.setSignalType(parseIntAttribute(signalElement, "SignalType"));
            signal.setChannelNo(parseIntAttribute(signalElement, "ChannelNo"));
            signal.setChannelType(parseIntAttribute(signalElement, "ChannelType"));
            signal.setExpression(signalElement.attributeValue("Expression"));
            signal.setDataType(signalElement.attributeValue("DataType"));
            signal.setShowPrecision(signalElement.attributeValue("ShowPrecision"));
            signal.setUnit(signalElement.attributeValue("Unit"));
            signal.setStoreInterval(signalElement.attributeValue("StoreInterval"));
            signal.setAbsValueThreshold(signalElement.attributeValue("AbsValueThreshold"));
            signal.setPercentThreshold(signalElement.attributeValue("PercentThreshold"));
            signal.setStaticsPeriod(signalElement.attributeValue("StaticsPeriod"));
            signal.setEnable(parseBooleanAttribute(signalElement, "Enable"));
            signal.setVisible(parseBooleanAttribute(signalElement, "Visible"));
            signal.setDescription(signalElement.attributeValue("Discription")); // 注意XML中是Discription
            signal.setBaseTypeId(signalElement.attributeValue("BaseTypeId"));
            signal.setChargeStoreInterVal(signalElement.attributeValue("ChargeStoreInterVal"));
            signal.setChargeAbsValue(signalElement.attributeValue("ChargeAbsValue"));
            signal.setDisplayIndex(parseIntAttribute(signalElement, "DisplayIndex"));
            signal.setSignalProperty(signalElement.attributeValue("SignalProperty"));
            signal.setSignalMeaning(signalElement.attributeValue("SignalMeaning"));
            signal.setModuleNo(parseIntAttribute(signalElement, "ModuleNo"));

            signals.add(signal);
        }

        return signals;
    }

    /**
     * 解析事件列表
     */
    private List<ConfigImportResult.EventImportData> parseEvents(Element eventsElement) {
        List<ConfigImportResult.EventImportData> events = new ArrayList<>();

        @SuppressWarnings("unchecked")
        List<Element> eventElements = eventsElement.elements("Event");

        for (Element eventElement : eventElements) {
            ConfigImportResult.EventImportData event = new ConfigImportResult.EventImportData();

            event.setEventId(parseIntAttribute(eventElement, "EventId"));
            event.setEventName(eventElement.attributeValue("EventName"));
            event.setEventCategory(parseIntAttribute(eventElement, "EventCategory"));
            event.setStartType(parseIntAttribute(eventElement, "StartType"));
            event.setEndType(parseIntAttribute(eventElement, "EndType"));
            event.setStartExpression(eventElement.attributeValue("StartExpression"));
            event.setSuppressExpression(eventElement.attributeValue("SuppressExpression"));
            event.setSignalId(parseIntAttribute(eventElement, "SignalId"));
            event.setEnable(parseBooleanAttribute(eventElement, "Enable"));
            event.setVisible(parseBooleanAttribute(eventElement, "Visible"));
            event.setDescription(eventElement.attributeValue("Description"));
            event.setTurnover(eventElement.attributeValue("Turnover"));
            event.setDisplayIndex(parseIntAttribute(eventElement, "DisplayIndex"));
            event.setModuleNo(parseIntAttribute(eventElement, "ModuleNo"));

            // 解析事件条件
            Element conditionsElement = eventElement.element("Conditions");
            if (conditionsElement != null) {
                event.setConditions(parseEventConditions(conditionsElement));
            }

            events.add(event);
        }

        return events;
    }

    /**
     * 解析事件条件列表
     */
    private List<ConfigImportResult.EventConditionImportData> parseEventConditions(Element conditionsElement) {
        List<ConfigImportResult.EventConditionImportData> conditions = new ArrayList<>();

        @SuppressWarnings("unchecked")
        List<Element> conditionElements = conditionsElement.elements("EventCondition");

        for (Element conditionElement : conditionElements) {
            ConfigImportResult.EventConditionImportData condition = new ConfigImportResult.EventConditionImportData();

            condition.setEventConditionId(parseIntAttribute(conditionElement, "EventConditionId"));
            condition.setEventSeverity(parseIntAttribute(conditionElement, "EventSeverity"));
            condition.setStartOperation(conditionElement.attributeValue("StartOperation"));
            condition.setStartCompareValue(conditionElement.attributeValue("StartCompareValue"));
            condition.setStartDelay(conditionElement.attributeValue("StartDelay"));
            condition.setEndOperation(conditionElement.attributeValue("EndOperation"));
            condition.setEndCompareValue(conditionElement.attributeValue("EndCompareValue"));
            condition.setEndDelay(conditionElement.attributeValue("EndDelay"));
            condition.setFrequency(conditionElement.attributeValue("Frequency"));
            condition.setFrequencyThreshold(conditionElement.attributeValue("FrequencyThreshold"));
            condition.setMeanings(conditionElement.attributeValue("Meanings"));
            condition.setEquipmentState(conditionElement.attributeValue("EquipmentState"));
            condition.setBaseTypeId(conditionElement.attributeValue("BaseTypeId"));
            condition.setStandardName(conditionElement.attributeValue("StandardName"));

            conditions.add(condition);
        }

        return conditions;
    }

    /**
     * 解析控制列表
     */
    private List<ConfigImportResult.ControlImportData> parseControls(Element controlsElement) {
        List<ConfigImportResult.ControlImportData> controls = new ArrayList<>();

        @SuppressWarnings("unchecked")
        List<Element> controlElements = controlsElement.elements("Control");

        for (Element controlElement : controlElements) {
            ConfigImportResult.ControlImportData control = new ConfigImportResult.ControlImportData();

            control.setControlId(parseIntAttribute(controlElement, "ControlId"));
            control.setControlName(controlElement.attributeValue("ControlName"));
            control.setControlCategory(parseIntAttribute(controlElement, "ControlCategory"));
            control.setCmdToken(controlElement.attributeValue("CmdToken"));
            control.setBaseTypeId(parseLongAttribute(controlElement, "BaseTypeId"));
            control.setControlSeverity(parseIntAttribute(controlElement, "ControlSeverity"));
            control.setSignalId(parseIntAttribute(controlElement, "SignalId"));
            control.setTimeOut(parseDoubleAttribute(controlElement, "TimeOut"));
            control.setRetry(parseIntAttribute(controlElement, "Retry"));
            control.setDescription(controlElement.attributeValue("Description"));
            control.setEnable(parseBooleanAttribute(controlElement, "Enable"));
            control.setVisible(parseBooleanAttribute(controlElement, "Visible"));
            control.setDisplayIndex(parseIntAttribute(controlElement, "DisplayIndex"));
            control.setCommandType(parseIntAttribute(controlElement, "CommandType"));
            control.setControlType(parseShortAttribute(controlElement, "ControlType"));
            control.setDataType(parseShortAttribute(controlElement, "DataType"));
            control.setMaxValue(parseDoubleAttribute(controlElement, "MaxValue"));
            control.setMinValue(parseDoubleAttribute(controlElement, "MinValue"));
            control.setDefaultValue(parseDoubleAttribute(controlElement, "DefaultValue"));
            control.setModuleNo(parseIntAttribute(controlElement, "ModuleNo"));

            controls.add(control);
        }

        return controls;
    }

    /**
     * 从XML文件解析监控单元信息
     */
    private ConfigImportResult.MonitorUnitImportData parseMonitorUnitFromXml(File xmlFile, Element root) {
        ConfigImportResult.MonitorUnitImportData monitorUnit = new ConfigImportResult.MonitorUnitImportData();

        // 从文件名解析监控单元名称
        String fileName = xmlFile.getName();
        if (fileName.startsWith("MonitorUnits") && fileName.endsWith(".xml")) {
            String monitorUnitName = fileName.substring("MonitorUnits".length(), fileName.length() - ".xml".length());
            monitorUnit.setMonitorUnitName(monitorUnitName);
        }

        // 从根元素解析StationId
        String stationIdStr = root.attributeValue("StationId");
        if (stationIdStr != null) {
            try {
                monitorUnit.setStationId(Integer.parseInt(stationIdStr));
            } catch (NumberFormatException e) {
                log.warn("无法解析StationId: {}", stationIdStr);
            }
        }

        return monitorUnit;
    }

    // 辅助方法
    private Integer parseIntAttribute(Element element, String attributeName) {
        String value = element.attributeValue(attributeName);
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        try {
            return Integer.parseInt(value.trim());
        } catch (NumberFormatException e) {
            log.warn("无法解析整数属性 {}: {}", attributeName, value);
            return null;
        }
    }

    private Long parseLongAttribute(Element element, String attributeName) {
        String value = element.attributeValue(attributeName);
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        try {
            return Long.parseLong(value.trim());
        } catch (NumberFormatException e) {
            log.warn("无法解析长整数属性 {}: {}", attributeName, value);
            return null;
        }
    }

    private Double parseDoubleAttribute(Element element, String attributeName) {
        String value = element.attributeValue(attributeName);
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        try {
            return Double.parseDouble(value.trim());
        } catch (NumberFormatException e) {
            log.warn("无法解析浮点数属性 {}: {}", attributeName, value);
            return null;
        }
    }

    private Short parseShortAttribute(Element element, String attributeName) {
        String value = element.attributeValue(attributeName);
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        try {
            return Short.parseShort(value.trim());
        } catch (NumberFormatException e) {
            log.warn("无法解析短整数属性 {}: {}", attributeName, value);
            return null;
        }
    }

    private Boolean parseBooleanAttribute(Element element, String attributeName) {
        String value = element.attributeValue(attributeName);
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        return "True".equalsIgnoreCase(value.trim()) || "true".equalsIgnoreCase(value.trim());
    }
}
