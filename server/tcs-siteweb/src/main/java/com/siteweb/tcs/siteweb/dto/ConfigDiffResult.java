package com.siteweb.tcs.siteweb.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 配置差异对比结果
 */
@Data
public class ConfigDiffResult {
    
    /**
     * 监控单元差异
     */
    private MonitorUnitDiff monitorUnitDiff;
    
    /**
     * 设备模板差异列表
     */
    private List<EquipmentTemplateDiff> equipmentTemplateDiffs = new ArrayList<>();
    
    /**
     * 端口差异列表
     */
    private List<PortDiff> portDiffs = new ArrayList<>();
    
    /**
     * 采集单元差异列表
     */
    private List<SamplerUnitDiff> samplerUnitDiffs = new ArrayList<>();
    
    /**
     * 设备差异列表
     */
    private List<EquipmentDiff> equipmentDiffs = new ArrayList<>();
    
    /**
     * 是否有差异
     */
    public boolean hasDifferences() {
        return (monitorUnitDiff != null && monitorUnitDiff.hasDifferences()) ||
               !equipmentTemplateDiffs.isEmpty() ||
               !portDiffs.isEmpty() ||
               !samplerUnitDiffs.isEmpty() ||
               !equipmentDiffs.isEmpty();
    }
    
    /**
     * 监控单元差异
     */
    @Data
    public static class MonitorUnitDiff {
        private Integer monitorUnitId;
        private String monitorUnitName;
        private List<FieldDiff> fieldDiffs = new ArrayList<>();
        
        public boolean hasDifferences() {
            return !fieldDiffs.isEmpty();
        }
    }
    
    /**
     * 设备模板差异
     */
    @Data
    public static class EquipmentTemplateDiff {
        private Integer equipmentTemplateId;
        private String equipmentTemplateName;
        private DiffType diffType; // NEW, DELETED, MODIFIED
        private List<FieldDiff> fieldDiffs = new ArrayList<>();
        
        /**
         * 信号差异列表
         */
        private List<SignalDiff> signalDiffs = new ArrayList<>();
        
        /**
         * 事件差异列表
         */
        private List<EventDiff> eventDiffs = new ArrayList<>();
        
        /**
         * 控制差异列表
         */
        private List<ControlDiff> controlDiffs = new ArrayList<>();
    }
    
    /**
     * 信号差异
     */
    @Data
    public static class SignalDiff {
        private Integer signalId;
        private String signalName;
        private DiffType diffType;
        private List<FieldDiff> fieldDiffs = new ArrayList<>();
    }
    
    /**
     * 事件差异
     */
    @Data
    public static class EventDiff {
        private Integer eventId;
        private String eventName;
        private DiffType diffType;
        private List<FieldDiff> fieldDiffs = new ArrayList<>();
    }
    
    /**
     * 控制差异
     */
    @Data
    public static class ControlDiff {
        private Integer controlId;
        private String controlName;
        private DiffType diffType;
        private List<FieldDiff> fieldDiffs = new ArrayList<>();
    }
    
    /**
     * 端口差异
     */
    @Data
    public static class PortDiff {
        private Integer portId;
        private String portName;
        private DiffType diffType;
        private List<FieldDiff> fieldDiffs = new ArrayList<>();
    }
    
    /**
     * 采集单元差异
     */
    @Data
    public static class SamplerUnitDiff {
        private Integer samplerUnitId;
        private String samplerUnitName;
        private DiffType diffType;
        private List<FieldDiff> fieldDiffs = new ArrayList<>();
    }
    
    /**
     * 设备差异
     */
    @Data
    public static class EquipmentDiff {
        private Integer equipmentId;
        private String equipmentName;
        private DiffType diffType;
        private List<FieldDiff> fieldDiffs = new ArrayList<>();
    }
    
    /**
     * 字段差异
     */
    @Data
    public static class FieldDiff {
        private String fieldName;
        private String fieldDisplayName;
        private Object oldValue;
        private Object newValue;
        
        public FieldDiff(String fieldName, String fieldDisplayName, Object oldValue, Object newValue) {
            this.fieldName = fieldName;
            this.fieldDisplayName = fieldDisplayName;
            this.oldValue = oldValue;
            this.newValue = newValue;
        }
    }
    
    /**
     * 差异类型枚举
     */
    public enum DiffType {
        NEW("新增"),
        DELETED("删除"),
        MODIFIED("修改");
        
        private final String description;
        
        DiffType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
