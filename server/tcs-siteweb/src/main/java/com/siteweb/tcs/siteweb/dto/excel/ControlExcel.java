package com.siteweb.tcs.siteweb.dto.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 控制Excel
 *
 * <AUTHOR>
 * @date 2025/05/06
 */
@Data
@ExcelIgnoreUnannotated
public class ControlExcel {
    @ExcelProperty("设备模板id")
    private Integer equipmentTemplateId;
    @ExcelProperty("设备id")
    private Integer equipmentId;
    @ExcelProperty("设备名称")
    private String equipmentName;
    @ExcelProperty("控制id")
    private Integer ControlId;
    @ExcelProperty("控制名称")
    private String ControlName;
    @ExcelProperty("命令种类")
    private String itemValue;
    @ExcelProperty("命令字符串")
    private String cmdToken;
    @ExcelProperty("最大值")
    private Double maxValue;
    @ExcelProperty("最小值")
    private Double minValue;
    @ExcelProperty("控制值")
    private String parameterValue;
    @ExcelProperty("控制含义")
    private String Meanings;
}
