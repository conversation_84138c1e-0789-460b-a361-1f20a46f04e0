package com.siteweb.tcs.siteweb.provider;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.siteweb.tcs.siteweb.entity.door.DoorTemplateControl;
import com.siteweb.tcs.siteweb.entity.door.DoorTemplateEvent;
import com.siteweb.tcs.siteweb.entity.door.DoorTemplateSignal;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: DoorTemplateProvider
 * @descriptions: 门禁设备模板业务提供类
 * @author: xsx
 * @date: 2024/9/23 11:26
 **/
@Slf4j
@Service
public class DoorTemplateProvider {

    @Autowired
    private ResourceLoader resourceLoader;

    private Map<String, DoorTemplateSignal> doorTemplateSignalMap = new HashMap<>();
    private Map<String, DoorTemplateEvent> doorTemplateEventMap = new HashMap<>();
    private Map<String, DoorTemplateControl> doorTemplateControlMap = new HashMap<>();
    private final static String path = "classpath:door_template/%s";

    @PostConstruct
    private void afterInit() throws Exception{
        List<DoorTemplateControl> doorTemplateControlList = this.<DoorTemplateControl>parseTemplate("control.json", DoorTemplateControl.class);
        if(CollectionUtil.isNotEmpty(doorTemplateControlList))
            doorTemplateControlList.forEach(e -> doorTemplateControlMap.put(e.getControlId(),e));
        List<DoorTemplateEvent> doorTemplateEventList = this.<DoorTemplateEvent>parseTemplate("event.json", DoorTemplateEvent.class);
        if(CollectionUtil.isNotEmpty(doorTemplateEventList))
            doorTemplateEventList.forEach(e -> doorTemplateEventMap.put(e.getEventId(),e));
        List<DoorTemplateSignal> doorTemplateSignalList = this.<DoorTemplateSignal>parseTemplate("signal.json", DoorTemplateSignal.class);
        if(CollectionUtil.isNotEmpty(doorTemplateSignalList))
            doorTemplateSignalList.forEach(e -> doorTemplateSignalMap.put(e.getSignalId(),e));
    }

    private <T> List<T> parseTemplate(String fileName,Class aClass){
        File file = null;
        try {
            String pathName = String.format(path, fileName);
            file = resourceLoader.getResource(pathName).getFile();
        }catch (IOException ex){
            log.error("[DoorTemplateProvider Init] can't load file {}",fileName);
            return null;
        }
        JSONArray jsonArray = JSONUtil.readJSONArray(file, Charset.forName("utf-8"));
        List<T> objList = jsonArray.toList(aClass);
        return objList;
    }

    public DoorTemplateSignal getTemplateSignal(String signalId){
        if(!doorTemplateSignalMap.containsKey(signalId)){
            return null;
        }
        return doorTemplateSignalMap.get(signalId);
    }

    public boolean existSignal(String signalId){
        return doorTemplateSignalMap.containsKey(signalId);
    }

    public DoorTemplateEvent getTemplateEvent(String eventId){
        if(!doorTemplateEventMap.containsKey(eventId)){
            return null;
        }
        return doorTemplateEventMap.get(eventId);
    }

    public boolean existEvent(String eventId){
        return doorTemplateEventMap.containsKey(eventId);
    }

    public DoorTemplateControl getTemplateControl(String controlId){
        if(!doorTemplateControlMap.containsKey(controlId)){
            return null;
        }
        return doorTemplateControlMap.get(controlId);
    }

    public boolean existControl(String controlId){
        return doorTemplateControlMap.containsKey(controlId);
    }
}
