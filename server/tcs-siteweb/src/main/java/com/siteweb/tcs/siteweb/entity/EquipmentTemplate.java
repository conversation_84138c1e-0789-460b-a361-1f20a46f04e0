package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.tcs.siteweb.annotation.ChangeSource;
import lombok.Data;

import java.io.Serializable;

/**
 * Equipment template entity
 */
@Data
@TableName("tbl_equipmenttemplate")
@ChangeSource(channel = "tcs", product = "siteweb", source = "equipment-template")
public class EquipmentTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "EquipmentTemplateId")
    private Integer equipmentTemplateId;

    @TableField("EquipmentTemplateName")
    private String equipmentTemplateName;

    @TableField("ParentTemplateId")
    private Integer parentTemplateId;

    @TableField("Memo")
    private String memo;

    @TableField("ProtocolCode")
    private String protocolCode;

    @TableField("EquipmentCategory")
    private Integer equipmentCategory;

    @TableField("EquipmentType")
    private Integer equipmentType;

    @TableField("Property")
    private String property;

    @TableField("Description")
    private String description;

    @TableField("EquipmentStyle")
    private String equipmentStyle;

    @TableField("Unit")
    private String unit;

    @TableField("Vendor")
    private String vendor;

    @TableField("Photo")
    private String photo;

    @TableField("EquipmentBaseType")
    private Integer equipmentBaseType;

    @TableField("StationCategory")
    private Integer stationCategory;

    @TableField("ExtendField1")
    private String extendField1;
}
