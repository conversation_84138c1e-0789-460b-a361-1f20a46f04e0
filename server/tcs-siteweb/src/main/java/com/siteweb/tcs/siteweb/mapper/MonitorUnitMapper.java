package com.siteweb.tcs.siteweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.siteweb.dto.MonitorUnitDTO;
import com.siteweb.tcs.siteweb.entity.MonitorUnit;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Monitor Unit Mapper
 */
@Mapper
@Repository
public interface MonitorUnitMapper extends BaseMapper<MonitorUnit> {

    MonitorUnitDTO selectByMonitorUnitId(Integer monitorUnitId);

    MonitorUnitDTO selectByMonitorUnitIdWithoutStation(Integer monitorUnitId);

    List<MonitorUnitDTO> selectByMonitorIds(List<Integer> monitorUnitIds);

    List<MonitorUnitDTO> selectAll();
    List<MonitorUnitDTO> selectAllWithoutStation();

    int insertDto(MonitorUnitDTO monitorUnit);

    int updateDto(MonitorUnitDTO monitorUnit);
}
