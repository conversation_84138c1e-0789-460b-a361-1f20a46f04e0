package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.entity.ControlMeanings;
import com.siteweb.tcs.siteweb.mapper.ControlMeaningsMapper;
import com.siteweb.tcs.siteweb.service.IControlMeaningsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Control Meanings Service Implementation
 */
@Service
public class ControlMeaningsServiceImpl extends ServiceImpl<ControlMeaningsMapper, ControlMeanings> implements IControlMeaningsService {

    @Autowired
    private ControlMeaningsMapper controlMeaningsMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByEquipmentTemplateIdAndControlId(Integer equipmentTemplateId, Integer controlId) {
        controlMeaningsMapper.deleteByEquipmentTemplateIdAndControlId(equipmentTemplateId, controlId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInsert(List<ControlMeanings> controlMeaningsList) {
        if (CollectionUtils.isEmpty(controlMeaningsList)) {
            return;
        }
        saveBatch(controlMeaningsList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateControlMeanings(List<ControlMeanings> controlMeaningsList) {
        if (CollectionUtils.isEmpty(controlMeaningsList)) {
            return;
        }
        // Delete all existing meanings for the given controlId and equipmentTemplateId
        deleteByEquipmentTemplateIdAndControlId(controlMeaningsList.get(0).getEquipmentTemplateId(), controlMeaningsList.get(0).getControlId());

        // Insert the new meanings
        for (ControlMeanings meaning : controlMeaningsList) {
            meaning.setId(null); // Ensure new IDs are generated
        }
        saveBatch(controlMeaningsList);
    }

    @Override
    public List<ControlMeanings> findByEquipmentTemplateIdAndControlId(Integer equipmentTemplateId, Integer controlId) {
        return controlMeaningsMapper.findByEquipmentTemplateIdAndControlId(equipmentTemplateId, controlId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchsaveControlMeanings() {
        try {
            List<ControlMeanings> controlMeaningsList = new ArrayList<>();

            // 创建控制含义
            // 这里需要实现批量保存控制含义的逻辑
            // 由于没有具体的数据，这里只提供一个空实现
            log.warn("batchsaveControlMeanings method is not fully implemented");
            return true;
        } catch (Exception e) {
            log.error("Failed to batch save control meanings", e);
            return false;
        }
    }

    @Override
    public List<ControlMeanings> findByEquipmentTemplateId(Integer equipmentTemplateId) {
        return controlMeaningsMapper.selectList(Wrappers.lambdaQuery(ControlMeanings.class)
                .eq(ControlMeanings::getEquipmentTemplateId, equipmentTemplateId));
    }

    @Override
    public void copyControlMeanings(Integer originEquipmentTemplateId, Integer destEquipmentTemplateId) {
        List<ControlMeanings> controlMeaningsList = findByEquipmentTemplateId(originEquipmentTemplateId);
        controlMeaningsList.forEach(meanings -> meanings.setEquipmentTemplateId(destEquipmentTemplateId));
        batchInsert(controlMeaningsList);
    }
}
