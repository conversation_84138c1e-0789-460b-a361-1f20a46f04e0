package com.siteweb.tcs.siteweb.util;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.crypto.symmetric.AES;

import java.nio.charset.StandardCharsets;

public class AESUtil {

    /**
     * AES加密 使用AES/ECB/PKCS7Padding方式
     * @param content 需要加密的内容
     * @return {@link String} 加密字符串 加密为BASE64表示
     */
    public static String encrypt(String content, String aesKey){
        //生成密钥 构建
        AES aes = new AES("ECB", "PKCS7Padding", aesKey.getBytes(StandardCharsets.UTF_8));
        //加密为Base64进制表示
        return aes.encryptBase64(content);
    }

    /**
     * AES解密 使用AES/ECB/PKCS7Padding方式
     * @param content 加密内容(加密为BASE64表示)
     * @return {@link String} 解密字符串
     */
    public static String decrypt(String content, String aesKey){
        //生成密钥 构建
        AES aes = new AES("ECB", "PKCS7Padding", aesKey.getBytes(StandardCharsets.UTF_8));
        //解密
        return aes.decryptStr(content, CharsetUtil.CHARSET_UTF_8);
    }
}
