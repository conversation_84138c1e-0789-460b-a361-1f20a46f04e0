package com.siteweb.tcs.siteweb.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.dto.excel.ControlExcel;
import com.siteweb.tcs.siteweb.dto.excel.EventExcel;
import com.siteweb.tcs.siteweb.dto.excel.SheetDataWrapper;
import com.siteweb.tcs.siteweb.dto.excel.SignalExcel;
import com.siteweb.tcs.siteweb.entity.*;
import com.siteweb.tcs.siteweb.entity.*;
import com.siteweb.tcs.siteweb.enums.TableIdentityEnum;
import com.siteweb.tcs.siteweb.exception.BusinessException;
import com.siteweb.tcs.siteweb.mapper.EquipmentMapper;
import com.siteweb.tcs.siteweb.mapper.EquipmentTemplateMapper;
import com.siteweb.tcs.siteweb.service.*;
import com.siteweb.tcs.siteweb.util.ExcelExportUtil;
import com.siteweb.tcs.siteweb.util.I18n;
import com.siteweb.tcs.siteweb.util.StrSplitUtil;
import com.siteweb.tcs.siteweb.vo.EquipmentTemplateBaseClassVO;
import com.siteweb.tcs.siteweb.vo.EquipmentTemplateVO;
import com.siteweb.tcs.siteweb.dto.EquipmentTemplateTreeDTO;
import com.siteweb.tcs.siteweb.dto.CopyEquipmentTemplateDTO;
import com.siteweb.tcs.siteweb.dto.StandardEquipmentTemplate;
import com.siteweb.tcs.siteweb.dto.EquipTemplateChangeDTO;
import com.siteweb.tcs.siteweb.dto.SwitchTemplateDTO;
import com.siteweb.tcs.siteweb.entity.Equipment;
import jakarta.servlet.http.HttpServletResponse;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * Equipment Template Service Implementation
 */
@Slf4j
@Service
public class EquipmentTemplateServiceImpl extends ServiceImpl<EquipmentTemplateMapper, EquipmentTemplate> implements IEquipmentTemplateService {

    @Autowired
    private EquipmentTemplateMapper equipmentTemplateMapper;

    @Autowired
    private IDataItemService dataItemService;

    @Autowired
    private I18n i18n;



    // Assuming these services will be available or implemented later
    @Autowired(required = false) // Marked as not required for now to avoid startup issues if not present
    private ISignalService signalService;

    @Autowired(required = false)
    private IEventService eventService;

    @Autowired(required = false)
    private IControlService controlService;

    @Autowired(required = false)
    private ICategoryIdMapService categoryIdMapService;

    @Autowired(required = false)
    private ISamplerService samplerService;

    @Autowired(required = false)
    private ISignalMeaningsService signalMeaningsService;

    @Autowired(required = false)
    private ISignalPropertyService signalPropertyService;

    @Autowired(required = false)
    private IEventConditionService eventConditionService;

    @Autowired(required = false)
    private IControlMeaningsService controlMeaningsService;

    @Autowired
    private IPrimaryKeyValueService primaryKeyValueService;

    @Autowired
    private EquipmentMapper equipmentMapper;

    @Autowired
    IChangeEventService changeEventService;



    // From siteweb6, the B interface root template has a specific name or property.
    // This might need to be a configurable value or a more robust query.
    // For now, using a placeholder name.
    private static final String B_INTERFACE_ROOT_TEMPLATE_NAME = "B_INTERFACE_ROOT";
    private static final String B_INTERFACE_PROTOCOL_CODE = "BInterfaceProtocol"; // Example protocol code

    @Override
    public Integer getBInterfaceDeviceTemplateRootId() {
        // Attempt to find by a specific known name or protocol code for B-Interface root templates.
        // This logic needs to match how it's identified in siteweb6-config-server.
        // Option 1: By a specific name if it's unique and known.
        // EquipmentTemplate rootTemplate = getOne(Wrappers.<EquipmentTemplate>lambdaQuery()
        //        .eq(EquipmentTemplate::getEquipmentTemplateName, B_INTERFACE_ROOT_TEMPLATE_NAME)
        //        .isNull(EquipmentTemplate::getParentTemplateId));
        // Option 2: By protocol code and being a root template
        EquipmentTemplate rootTemplate = getOne(Wrappers.<EquipmentTemplate>lambdaQuery()
                .eq(EquipmentTemplate::getProtocolCode, B_INTERFACE_PROTOCOL_CODE) // Assuming a specific protocol code
                .isNull(EquipmentTemplate::getParentTemplateId));

        if (rootTemplate != null) {
            return rootTemplate.getEquipmentTemplateId();
        }
        // Fallback or more complex query if needed based on original logic
        log.warn("BInterfaceDeviceTemplateRootId could not be found with current criteria.");
        return equipmentTemplateMapper.getBInterfaceDeviceTemplateRootId(); // Fallback to a direct mapper call if it has specific logic
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(EquipmentTemplate equipmentTemplate) {
        Integer result = equipmentTemplateMapper.insert(equipmentTemplate);
        return result == 1;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTemplateById(Integer equipmentTemplateId) {
        if (equipmentTemplateId == null) {
            return false;
        }
        // Cascade delete related entities
        // This order is generally safe, but dependencies might require adjustments
        if (signalService != null) {
            // signalService.deleteByEquipmentTemplateId(equipmentTemplateId); // Placeholder
            log.warn("SignalService.deleteByEquipmentTemplateId not fully implemented for cascading delete.");
        }
        if (eventService != null) {
            // eventService.deleteByEquipmentTemplateId(equipmentTemplateId); // Placeholder
            log.warn("EventService.deleteByEquipmentTemplateId not fully implemented for cascading delete.");
        }
        if (controlService != null) {
            // controlService.deleteByEquipmentTemplateId(equipmentTemplateId); // Placeholder
            log.warn("ControlService.deleteByEquipmentTemplateId not fully implemented for cascading delete.");
        }

        // Finally, delete the template itself
        return removeById(equipmentTemplateId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public EquipmentTemplate createEquipmentTemplate(EquipmentTemplate equipmentTemplate) {
        // Ensure equipmentTemplateId is null for auto-generation or assigned if input type.
        // Based on TblEquipmentTemplate, ID is IdType.INPUT, so it should be provided or handled by a sequence/generator if not.
        // For simplicity, if it's null, we'll assume we need to generate it or that the DB handles it.
        // If it's meant to be manually assigned, the caller should set it.

        // Check for unique name if required by business logic (not explicitly shown in controller snippet but good practice)
        // if (existsByEquipmentTemplateName(equipmentTemplate.getEquipmentTemplateName())) {
        //    throw new RuntimeException("Equipment template name already exists: " + equipmentTemplate.getEquipmentTemplateName());
        // }
//传入的equipmentTemplateId为null时自己生成
        if (Objects.isNull(equipmentTemplate.getEquipmentTemplateId())) {
            int equipmentTemplateId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_EQUIPMENT_TEMPLATE, 0);
            equipmentTemplate.setEquipmentTemplateId(equipmentTemplateId);
        }
        // 如果模板名称超过mysql数据的varchar(128)则提示
        if (CharSequenceUtil.isNotBlank(equipmentTemplate.getEquipmentTemplateName()) && equipmentTemplate.getEquipmentTemplateName().length() > 128) {
            throw new BusinessException(i18n.T("monitor.equipmentTemplate.name.length"));
        }
        // 如果memo为空则设置为空字符串
        if (StrUtil.isEmpty(equipmentTemplate.getMemo())) {
            equipmentTemplate.setMemo("init");
        }
        // 如果protocolCode为空则设置为空字符串
        if (StrUtil.isEmpty(equipmentTemplate.getProtocolCode())) {
            equipmentTemplate.setProtocolCode("init");
        }
        equipmentTemplateMapper.insert(equipmentTemplate);
        changeEventService.sendCreate(equipmentTemplate);
        return equipmentTemplate;
    }

    @Override
    public List<EquipmentTemplate> queryTemplate(EquipmentTemplateVO equipmentTemplateVO) {
        return equipmentTemplateMapper.queryTemplateByVO(equipmentTemplateVO);
    }

    @Override
    public boolean existsChildTemplate(Integer equipmentTemplateId) {
        if (equipmentTemplateId == null) return false;
        return equipmentTemplateMapper.countByParentTemplateId(equipmentTemplateId) > 0;
    }

    @Override
    public EquipmentTemplate findById(Integer equipmentTemplateId) {
        return equipmentTemplateMapper.selectById(equipmentTemplateId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateEquipmentTemplateCategoryByCategoryIdMap(Integer businessId, Integer categoryTypeId) {
        if (businessId == null || categoryTypeId == null) {
            log.warn("Cannot update equipment template category: businessId or categoryTypeId is null");
            return false;
        }

        try {
            // 获取所有设备模板
            List<EquipmentTemplate> templateList = list();
            if (templateList.isEmpty()) {
                log.info("No equipment template found to update category");
                return true;
            }

            // 获取分类映射
            List<CategoryIdMap> categoryIdMaps = categoryIdMapService.list(new LambdaQueryWrapper<CategoryIdMap>()
                    .eq(CategoryIdMap::getBusinessId, businessId)
                    .eq(CategoryIdMap::getCategoryTypeId, categoryTypeId));
            if (categoryIdMaps.isEmpty()) {
                log.warn("No category ID maps found for businessId {} and categoryTypeId {}", businessId, categoryTypeId);
                return false;
            }

            // 更新设备模板分类
            int updateCount = 0;
            for (EquipmentTemplate template : templateList) {
                Integer originalCategoryId = template.getEquipmentCategory();
                if (originalCategoryId == null) {
                    continue;
                }

                // 查找对应的分类映射
                for (CategoryIdMap categoryIdMap : categoryIdMaps) {
                    if (categoryIdMap.getOriginalCategoryId().equals(originalCategoryId)) {
                        template.setEquipmentCategory(categoryIdMap.getBusinessCategoryId());
                        updateById(template);
                        updateCount++;
                        break;
                    }
                }
            }

            log.info("Updated category for {} equipment templates with businessId {} and categoryTypeId {}",
                    updateCount, businessId, categoryTypeId);
            return true;
        } catch (Exception e) {
            log.error("Error updating equipment template category: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateEquipmentBaseTypeToNull() {
        return equipmentTemplateMapper.update(Wrappers.lambdaUpdate(EquipmentTemplate.class)
                .set(EquipmentTemplate::getEquipmentBaseType, null))>0;
    }

    @Override
    public List<Integer> findEquipmentTemplateIdByEquipmentTemplateIdDiv(Integer equipmentTemplateIdDiv) {
        if (equipmentTemplateIdDiv == null) {
            return new ArrayList<>();
        }

        // 查询符合条件的设备模板ID
        List<EquipmentTemplate> templateList = list(new LambdaQueryWrapper<EquipmentTemplate>()
                .eq(EquipmentTemplate::getEquipmentTemplateId, equipmentTemplateIdDiv));

        // 提取设备模板ID
        List<Integer> templateIds = new ArrayList<>();
        for (EquipmentTemplate template : templateList) {
            templateIds.add(template.getEquipmentTemplateId());
        }

        return templateIds;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchsaveLianTongEquipmentTemplate() {
        try {
            // 这里需要实现批量保存联通标准的设备模板的逻辑
            // 由于没有具体的数据，这里只提供一个空实现
            log.warn("batchsaveLianTongEquipmentTemplate method is not fully implemented");
            return true;
        } catch (Exception e) {
            log.error("Error batch saving LianTong equipment templates: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public EquipmentTemplate findByName(String templateName) {
        if (!StringUtils.hasText(templateName)) {
            log.warn("Template name is empty");
            return null;
        }

        try {
            LambdaQueryWrapper<EquipmentTemplate> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(EquipmentTemplate::getEquipmentTemplateName, templateName);

            EquipmentTemplate template = getOne(queryWrapper);

            if (template == null) {
                log.warn("No equipment template found for name: {}", templateName);
            } else {
                log.debug("Found equipment template with ID {} for name: {}", template.getEquipmentTemplateId(), templateName);
            }

            return template;
        } catch (Exception e) {
            log.error("Error finding equipment template by name: {}", templateName, e);
            return null;
        }
    }

    @Override
    public List<EquipmentTemplateVO> findVoAll() {
        try {
            List<EquipmentTemplateVO> result = equipmentTemplateMapper.findVoAll();
            log.debug("Found {} equipment template VOs", result.size());
            return result;
        } catch (Exception e) {
            log.error("Error finding all equipment template VOs", e);
            return List.of();
        }
    }

    // ==================== 设备管理相关方法实现 ====================

    @Override
    public EquipmentTemplate findTopParent(Integer equipmentTemplateId) {
        try {
            EquipmentTemplate equipmentTemplate = findById(equipmentTemplateId);
            if (Objects.isNull(equipmentTemplate)) {
                throw new BusinessException("equipmentTemplate info does not exist");
            }
            return equipmentTemplateMapper.selectOne(Wrappers.lambdaQuery(EquipmentTemplate.class)
                    .eq(EquipmentTemplate::getProtocolCode, equipmentTemplate.getProtocolCode())
                    .eq(EquipmentTemplate::getParentTemplateId, 0));
        } catch (Exception e) {
            log.error("Failed to find top parent equipment template for ID: {}", equipmentTemplateId, e);
            return null;
        }
    }

    @Override
    public List<EquipmentTemplateBaseClassVO> findBaseClassAll() {
        try {
            return equipmentTemplateMapper.findBaseClassAll();
        } catch (Exception e) {
            log.error("Failed to find base class equipment templates", e);
            return List.of();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateChildrenCategory(Integer equipmentTemplateId, Integer equipmentCategory) {
        try {
            if (ObjectUtil.hasNull(equipmentTemplateId, equipmentCategory)) {
                throw new BusinessException("equipmentTemplateId, equipmentCategory cannot be empty");
            }
            // 拿到所有子equipmentTemplateId
            EquipmentTemplate equipmentTemplate = findById(equipmentTemplateId);
            if (Objects.isNull(equipmentTemplate)) {
                throw new BusinessException("equipmentTemplate info does not exist");
            }
            List<EquipmentTemplate> equipmentTemplateList = findByProtocolCode(equipmentTemplate.getProtocolCode());
            List<Integer> equipmentTemplateIds = equipmentTemplateList.stream()
                    .map(EquipmentTemplate::getEquipmentTemplateId)
                    .toList();
            int equipmentTemplateCount = equipmentTemplateMapper.update(Wrappers.lambdaUpdate(EquipmentTemplate.class)
                    .set(EquipmentTemplate::getEquipmentCategory, equipmentCategory)
                    .in(EquipmentTemplate::getEquipmentTemplateId, equipmentTemplateIds));
            int equipmentCount = equipmentMapper.update(Wrappers.lambdaUpdate(Equipment.class)
                    .set(Equipment::getEquipmentCategory, equipmentCategory)
                    .in(Equipment::getEquipmentTemplateId, equipmentTemplateIds));
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    // 设备变更通知
                    List<Equipment> equipments = equipmentMapper.selectBatchIds(CollUtil.newHashSet(equipmentTemplateIds));
                    changeEventService.sendBatchUpdate(equipments);
                    List<EquipmentTemplate> eqList = equipmentTemplateMapper.selectBatchIds(equipmentTemplateList.stream().map(EquipmentTemplate::getEquipmentTemplateId).toList());
                    // 设备模板变更通知
                    changeEventService.sendBatchUpdate(eqList);
                }
            });
            return equipmentTemplateCount + equipmentCount;
        } catch (Exception e) {
            log.error("Failed to update children category for parentId: {}, category: {}", equipmentTemplateId, equipmentCategory, e);
            throw e;
        }
    }
    @Override
    public List<EquipmentTemplate> findByProtocolCode(String protocolCode) {
        return equipmentTemplateMapper.selectList(Wrappers.lambdaQuery(EquipmentTemplate.class)
                .eq(EquipmentTemplate::getProtocolCode, protocolCode));
    }

    // ==================== 新增设备模板迁移相关方法实现 ====================

    @Override
    public List<Tree<Integer>> findTree(Boolean hideDynamicConfigTemplate) {
        try {
            // 获取设备模板节点
            List<EquipmentTemplateTreeDTO> equipmenttemplateNodeList = equipmentTemplateMapper.findDynamicConfigTemplate(hideDynamicConfigTemplate)
                    .stream()
                    .map(EquipmentTemplateTreeDTO::new)
                    .collect(Collectors.toList());

            // 获取设备种类节点
            List<EquipmentTemplateTreeDTO> equipmentCategoryNodeList = getEquipmentCategoryNodeList();

            // 创建一个包含已有设备种类ID的集合，以便快速查找
            Set<Integer> existingCategoryIds = equipmentCategoryNodeList.stream()
                    .map(EquipmentTemplateTreeDTO::getId)
                    .collect(Collectors.toSet());

            // 创建未分类节点，ID为-1000
            Integer uncategorizedNodeId = -1000;
            EquipmentTemplateTreeDTO uncategorizedNode = new EquipmentTemplateTreeDTO();
            uncategorizedNode.setName("未分类");
            uncategorizedNode.setId(uncategorizedNodeId);
            uncategorizedNode.setParentId(-999);
            uncategorizedNode.setTemplate(false);

            // 处理设备模板
            equipmenttemplateNodeList.forEach(equipmentTemplate -> {
                // 如果设备模板的父节点是0，则将其父节点设置为种类或未分类
                if (Objects.equals(equipmentTemplate.getParentId(), 0)) {
                    // 如果种类存在于系统中，则使用它作为父节点
                    if (existingCategoryIds.contains(equipmentTemplate.getEquipmentCategory())) {
                        equipmentTemplate.setParentId(equipmentTemplate.getEquipmentCategory());
                    } else {
                        // 如果种类不存在，则将父节点设置为未分类节点
                        equipmentTemplate.setParentId(uncategorizedNodeId);
                    }
                }
            });

            // 将"监控设备"设置为根节点
            Integer rootId = -9999;
            EquipmentTemplateTreeDTO rootNode = getEquipmentTemplateTreeRoot(rootId);

            // 添加所有节点到列表中
            equipmenttemplateNodeList.add(rootNode);
            equipmenttemplateNodeList.add(uncategorizedNode);
            equipmenttemplateNodeList.addAll(equipmentCategoryNodeList);

            // 构建并返回树
            return TreeUtil.build(equipmenttemplateNodeList, rootId, (node, tree) -> {
                tree.setId(node.getId());
                tree.setParentId(node.getParentId());
                tree.setName(node.getName());
                tree.setWeight(node.getName());
                tree.putExtra("template", node.isTemplate());
                tree.putExtra("equipmentCategory", node.getEquipmentCategory());
            });
        } catch (Exception e) {
            log.error("Failed to find equipment template tree", e);
            return List.of();
        }
    }

    /**
     * 获取设备种类节点
     *
     * @return {@link List}<{@link EquipmentTemplateTreeDTO}>
     */
    private List<EquipmentTemplateTreeDTO> getEquipmentCategoryNodeList() {
        List<DataItem> dataItemList = dataItemService.findEquipmentCategory();
        return dataItemList.stream()
                .map(EquipmentTemplateTreeDTO::new)
                .toList();
    }

    /**
     * 获取设备模板树根节点
     *
     * @return {@link EquipmentTemplateTreeDTO}
     */
    private EquipmentTemplateTreeDTO getEquipmentTemplateTreeRoot(Integer rootId) {
        EquipmentTemplateTreeDTO rootNode = new EquipmentTemplateTreeDTO();
        rootNode.setName(i18n.T("monitor.equipment.monitoringEquipment"));
        rootNode.setId(-999);
        rootNode.setParentId(rootId);
        rootNode.setTemplate(false);
        return rootNode;
    }


    @Override
    public List<EquipmentTemplateTreeDTO> findTree(Integer equipmentCategory, String protocolCode, String equipmentTemplateName) {
        List<EquipmentTemplate> equipmentTemplateList = equipmentTemplateMapper.findByEquipmentCategoryAndProtocolCode(equipmentCategory,protocolCode, equipmentTemplateName);
        List<EquipmentTemplateTreeDTO> list = equipmentTemplateList.stream().map(EquipmentTemplateTreeDTO::new).toList();
        return buildTree(list);
    }
    /**
     * 构建树结构并按name升序排序
     */
    public List<EquipmentTemplateTreeDTO> buildTree(List<EquipmentTemplateTreeDTO> list) {
        // 创建ID到节点的映射
        Map<Integer, EquipmentTemplateTreeDTO> nodeMap = list.stream()
                .collect(Collectors.toMap(EquipmentTemplateTreeDTO::getId, node -> node, (v1, v2) -> v1));

        // 收集所有节点的子节点
        Map<Integer, List<EquipmentTemplateTreeDTO>> childrenMap = new HashMap<>();

        // 找出所有非根节点
        Set<Integer> nonRootIds = new HashSet<>();

        // 遍历所有节点，建立父子关系
        for (EquipmentTemplateTreeDTO node : list) {
            Integer parentId = node.getParentId();
            // 如果父节点ID存在于nodeMap中，则不是根节点
            if (nodeMap.containsKey(parentId)) {
                nonRootIds.add(node.getId());
                childrenMap.computeIfAbsent(parentId, k -> new ArrayList<>()).add(node);
            }
        }

        // 找出所有根节点（不在nonRootIds集合中的节点）
        List<EquipmentTemplateTreeDTO> roots = list.stream()
                .filter(node -> !nonRootIds.contains(node.getId()))
                .collect(Collectors.toList());

        // 递归设置子节点
        roots.forEach(root -> setChildren(root, childrenMap));

        // 递归排序
        sortTree(roots);

        return roots;
    }

    /**
     * 递归设置子节点
     */
    private void setChildren(EquipmentTemplateTreeDTO node, Map<Integer, List<EquipmentTemplateTreeDTO>> childrenMap) {
        List<EquipmentTemplateTreeDTO> children = childrenMap.get(node.getId());
        if (children != null && !children.isEmpty()) {
            node.setChildren(children);
            // 递归设置子节点的子节点
            children.forEach(child -> setChildren(child, childrenMap));
        }
    }
    /**
     * 递归排序树的每个层级
     */
    private void sortTree(List<EquipmentTemplateTreeDTO> nodes) {
        if (CollUtil.isEmpty(nodes)) {
            return;
        }

        // 按name升序排序当前层级
        nodes.sort(Comparator.comparing(EquipmentTemplateTreeDTO::getName));

        // 递归排序每个节点的子节点
        nodes.forEach(node -> {
            if (CollUtil.isNotEmpty(node.getChildren())) {
                sortTree(node.getChildren());
            }
        });
    }

    @Override
    public EquipmentTemplateVO findVoById(Integer equipmentTemplateId) {
        try {
            EquipmentTemplateVO equipmentTemplateVO = equipmentTemplateMapper.findVoByEquipmentTemplateId(equipmentTemplateId);
            if (equipmentTemplateVO != null && equipmentTemplateVO.getProperty() != null) {
                equipmentTemplateVO.setPropertyList(StrSplitUtil.splitToIntList(equipmentTemplateVO.getProperty(), "/"));
            }
            return equipmentTemplateVO;
        } catch (Exception e) {
            log.error("Failed to find equipment template VO by ID: {}", equipmentTemplateId, e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(EquipmentTemplate equipmentTemplate) {
        try {
            return equipmentTemplateMapper.updateById(equipmentTemplate) > 0;
        } catch (Exception e) {
            log.error("Failed to update equipment template: {}", equipmentTemplate.getEquipmentTemplateId(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean inheritUpdate(EquipmentTemplate equipmentTemplate) {
        try {
            // 继承更新：更新当前模板及其所有子模板
            boolean result = updateById(equipmentTemplate);
            if (result && equipmentTemplate.getEquipmentCategory() != null) {
                // 更新所有子模板的设备类别
                updateChildrenCategory(equipmentTemplate.getEquipmentTemplateId(), equipmentTemplate.getEquipmentCategory());
            }
            return result;
        } catch (Exception e) {
            log.error("Failed to inherit update equipment template: {}", equipmentTemplate.getEquipmentTemplateId(), e);
            return false;
        }
    }

    @Override
    public boolean doesTemplateNameExist(String templateName) {
        EquipmentTemplate tblEquipmentTemplate = findByName(templateName);
        if (Objects.nonNull(tblEquipmentTemplate)) {
            throw new BusinessException("monitor.equipmentTemplate.name.exist");
        }
        return false;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int copyTemplate(CopyEquipmentTemplateDTO copyEquipmentTemplateDTO) {
        try {
            doesTemplateNameExist(copyEquipmentTemplateDTO.getNewEquipmentTemplateName());
            EquipmentTemplate equipmentTemplate = equipmentTemplateMapper.selectById(copyEquipmentTemplateDTO.getOriginEquipmentTemplateId());
            if (equipmentTemplate == null) {
                throw new BusinessException("Source template not found");
            }
            equipmentTemplate.setDescription(copyEquipmentTemplateDTO.getReason());
            equipmentTemplate.setParentTemplateId(copyEquipmentTemplateDTO.getOriginEquipmentTemplateId());
            equipmentTemplate.setEquipmentTemplateName(copyEquipmentTemplateDTO.getNewEquipmentTemplateName());
            equipmentTemplate.setEquipmentTemplateId(null);
            createEquipmentTemplate(equipmentTemplate);
            signalService.copySignal(copyEquipmentTemplateDTO.getOriginEquipmentTemplateId(), equipmentTemplate.getEquipmentTemplateId());
            eventService.copyEvent(copyEquipmentTemplateDTO.getOriginEquipmentTemplateId(), equipmentTemplate.getEquipmentTemplateId());
            controlService.copyControl(copyEquipmentTemplateDTO.getOriginEquipmentTemplateId(), equipmentTemplate.getEquipmentTemplateId());
            changeEventService.sendCreate(equipmentTemplate);

            return equipmentTemplate.getEquipmentTemplateId();
        } catch (Exception e) {
            log.error("Failed to copy equipment template", e);
            return -1;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteById(Integer equipmentTemplateId) {
        try {
            // 检查是否有子模板
            if (existsChildTemplate(equipmentTemplateId)) {
                throw new BusinessException("Cannot delete template with child templates");
            }

            // 检查是否有设备引用
            long equipmentCount = equipmentMapper.selectCount(Wrappers.lambdaQuery(Equipment.class)
                    .eq(Equipment::getEquipmentTemplateId, equipmentTemplateId));
            if (equipmentCount > 0) {
                throw new BusinessException("Cannot delete template referenced by equipment");
            }

            return removeById(equipmentTemplateId);
        } catch (Exception e) {
            log.error("Failed to delete equipment template: {}", equipmentTemplateId, e);
            return false;
        }
    }

    @Override
    public String exportEquipmentTemplate(Integer equipmentTemplateId) {
        try {
            // 这里应该调用 EquipmentTemplateXmlService 来导出XML
            // 暂时返回空实现
            log.warn("exportEquipmentTemplate method is not fully implemented");
            return "";
        } catch (Exception e) {
            log.error("Failed to export equipment template: {}", equipmentTemplateId, e);
            return "";
        }
    }

    @Override
    public void exportExcel(HttpServletResponse response, Integer equipmentTemplateId) {
        try {
            // 这里应该实现Excel导出逻辑
            EquipmentTemplate tblEquipmentTemplate = equipmentTemplateMapper.selectById(equipmentTemplateId);
            if (Objects.isNull(tblEquipmentTemplate)) {
                log.error("设备模板不存在:{}", equipmentTemplateId);
                throw new BusinessException("error.device.template.notFound");
            }
            List<SignalExcel> signalExcelList = signalService.findExcelDtoByEquipmentTemplateId(equipmentTemplateId);
            List<EventExcel> eventExcelList = eventService.findExcelDtoByEquipmentTemplateId(equipmentTemplateId);
            List<ControlExcel> controlExcelList = controlService.findExcelDtoByEquipmentTemplateId(equipmentTemplateId);
            Map<String, SheetDataWrapper> sheetDataMap = new LinkedHashMap<>();
            sheetDataMap.put("信号", SheetDataWrapper.of(SignalExcel.class, signalExcelList));
            sheetDataMap.put("事件", SheetDataWrapper.of(EventExcel.class, eventExcelList));
            sheetDataMap.put("控制", SheetDataWrapper.of(ControlExcel.class, controlExcelList));
            ExcelExportUtil.exportExcelMultiSheet(response, sheetDataMap, tblEquipmentTemplate.getEquipmentTemplateName());
        } catch (Exception e) {
            log.error("Failed to export equipment template to Excel: {}", equipmentTemplateId, e);
        }
    }

    @Override
    public List<String> findDLlPathByEquipmentTemplateIds(List<Integer> equipmentTemplateIds) {
        try {
            if (equipmentTemplateIds == null || equipmentTemplateIds.isEmpty()) {
                return List.of();
            }
            return equipmentTemplateMapper.findDLlPathByEquipmentTemplateIds(equipmentTemplateIds);
        } catch (Exception e) {
            log.error("Failed to find DLL paths by equipment template IDs", e);
            return List.of();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateEquipmentCategory(Integer equipmentTemplateId, Integer equipmentCategory) {
        try {
            return equipmentTemplateMapper.updateEquipmentCategory(equipmentTemplateId, equipmentCategory);
        } catch (Exception e) {
            log.error("Failed to update equipment category for template: {}", equipmentTemplateId, e);
            return 0;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int clearEquipmentBaseType(Integer equipmentTemplateId) {
        try {
            return equipmentTemplateMapper.clearEquipmentBaseType(equipmentTemplateId);
        } catch (Exception e) {
            log.error("Failed to clear equipment base type for template: {}", equipmentTemplateId, e);
            return 0;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int autoSetEquipmentBaseType() {
        try {
            // 这里应该实现自动设置设备基类类型的逻辑
            log.warn("autoSetEquipmentBaseType method is not fully implemented");
            return 0;
        } catch (Exception e) {
            log.error("Failed to auto set equipment base type", e);
            return 0;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean upgradeToRootTemplate(Integer equipmentTemplateId) {
        try {
            return equipmentTemplateMapper.upgradeToRootTemplate(equipmentTemplateId) > 0;
        } catch (Exception e) {
            log.error("Failed to upgrade to root template: {}", equipmentTemplateId, e);
            return false;
        }
    }

    @Override
    public List<StandardEquipmentTemplate> findStandardEquipmentTemplateList() {
        try {
            // 这里应该实现查询标准设备模板的逻辑
            log.warn("findStandardEquipmentTemplateList method is not fully implemented");
            return List.of();
        } catch (Exception e) {
            log.error("Failed to find standard equipment template list", e);
            return List.of();
        }
    }

    @Override
    public List<EquipTemplateChangeDTO> changeCompare(Integer originTemplateId, Integer destTemplateId, List<Integer> equipmentIds) {
        try {
            if (originTemplateId == null || destTemplateId == null || equipmentIds == null || equipmentIds.isEmpty()) {
                log.warn("Invalid parameters for template change comparison");
                return List.of();
            }

            return equipmentTemplateMapper.compareTemplateChanges(originTemplateId, destTemplateId, equipmentIds);
        } catch (Exception e) {
            log.error("Failed to compare template changes: originTemplateId={}, destTemplateId={}", originTemplateId, destTemplateId, e);
            return List.of();
        }
    }

    @Override
    public boolean switchTemplateSignalCheck(SwitchTemplateDTO switchTemplateDTO) {
        try {
            if (switchTemplateDTO == null || switchTemplateDTO.getOriginTemplateId() == null ||
                switchTemplateDTO.getDestTemplateId() == null) {
                log.warn("Invalid switch template DTO for signal check");
                return false;
            }

            // 检查是否有信号引用冲突
            int conflictCount = equipmentTemplateMapper.checkSignalReferenceConflict(
                switchTemplateDTO.getOriginTemplateId(),
                switchTemplateDTO.getDestTemplateId()
            );

            return conflictCount > 0;
        } catch (Exception e) {
            log.error("Failed to check template signal reference", e);
            return false;
        }
    }
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean applyStandardizationToChildrenForEquipmentTemplate(Integer parentTemplateId) {
        try {
            log.info("开始应用标准化到子模版，父模板ID: {}", parentTemplateId);

            // 1. 查找所有子模版ID
            List<Integer> childTemplateIds = equipmentTemplateMapper.findAllChildId(parentTemplateId);
            if (childTemplateIds == null || childTemplateIds.isEmpty()) {
                log.warn("未找到父模板 {} 的子模版", parentTemplateId);
                return true; // 没有子模版也算成功
            }

            log.info("找到 {} 个子模版: {}", childTemplateIds.size(), childTemplateIds);

            // 2. 获取父模板的信号数据（只获取有描述的信号）
            List<Signal> parentSignals = signalService.list(
                    new QueryWrapper<Signal>()
                            .eq("EquipmentTemplateId", parentTemplateId)
                            .isNotNull("Description")
                            .ne("Description", "")
            );

            // 3. 获取父模板的控制数据（只获取有描述的控制）
            List<Control> parentControls = controlService.list(
                    new QueryWrapper<Control>()
                            .eq("EquipmentTemplateId", parentTemplateId)
                            .isNotNull("Description")
                            .ne("Description", "")
            );

            // 4. 获取父模板的事件数据（只获取有描述的事件）
            List<Event> parentEvents = eventService.list(
                    new QueryWrapper<Event>()
                            .eq("EquipmentTemplateId", parentTemplateId)
                            .isNotNull("Description")
                            .ne("Description", "")
            );

            log.info("父模板数据统计 - 信号: {}, 控制: {}, 事件: {}",
                    parentSignals.size(), parentControls.size(), parentEvents.size());

            // 5. 对每个子模版应用标准化
            for (Integer childTemplateId : childTemplateIds) {
                log.info("正在处理子模版: {}", childTemplateId);

                // 更新信号描述
                signalService.updateSignalDescriptions(childTemplateId, parentSignals);

                // 更新控制描述
                controlService.updateControlDescriptions(childTemplateId, parentControls);

                // 更新事件描述
                eventService.updateEventDescriptions(childTemplateId, parentEvents);
            }

            log.info("标准化应用完成，父模板ID: {}", parentTemplateId);
            return true;

        } catch (Exception e) {
            log.error("应用标准化到子模版失败，父模板ID: {}", parentTemplateId, e);
            throw new RuntimeException("应用标准化失败: " + e.getMessage(), e);
        }
    }
}