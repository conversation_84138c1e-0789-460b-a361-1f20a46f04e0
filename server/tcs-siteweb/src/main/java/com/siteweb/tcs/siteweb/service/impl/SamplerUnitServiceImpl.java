package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.entity.SamplerUnit;
import com.siteweb.tcs.siteweb.enums.OperationObjectTypeEnum;
import com.siteweb.tcs.siteweb.enums.TableIdentityEnum;
import com.siteweb.tcs.siteweb.mapper.SamplerUnitMapper;
import com.siteweb.tcs.siteweb.service.IChangeEventService;
import com.siteweb.tcs.siteweb.service.IOperationDetailService;
import com.siteweb.tcs.siteweb.service.IPrimaryKeyValueService;
import com.siteweb.tcs.siteweb.service.ISamplerUnitService;
import com.siteweb.tcs.siteweb.util.I18n;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Sampler Unit Service Implementation
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class SamplerUnitServiceImpl extends ServiceImpl<SamplerUnitMapper, SamplerUnit> implements ISamplerUnitService {

    @Autowired
    private SamplerUnitMapper samplerUnitMapper;
    
    @Autowired(required = false)
    private IPrimaryKeyValueService primaryKeyValueService;
    
    @Autowired(required = false)
    private IOperationDetailService operationDetailService;

    @Autowired
    private IChangeEventService changeEventService;

    @Autowired
    private I18n i18n;

    @Override
    public SamplerUnit findBySamplerUnitId(Integer samplerUnitId) {
        LambdaQueryWrapper<SamplerUnit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SamplerUnit::getSamplerUnitId, samplerUnitId);
        return getOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SamplerUnit createSamplerUnit(SamplerUnit samplerUnit) {
        // Validate sampler unit name
        if (samplerUnit.getSamplerUnitName() == null || samplerUnit.getSamplerUnitName().isEmpty()) {
            throw new RuntimeException("采集单元名称不能为空");
        }
        
        // Generate sampler unit ID if needed
        if (samplerUnit.getSamplerUnitId() == null || samplerUnit.getSamplerUnitId().equals(0)) {
            Integer samplerUnitId = primaryKeyValueService != null 
                    ? primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TSL_SAMPLER_UNIT, 0)
                    : null;
            if (samplerUnitId != null) {
                samplerUnit.setSamplerUnitId(samplerUnitId);
            }
        }
        
        // Set default values
        samplerUnit.setConnectState(0);
        samplerUnit.setUpdateTime(LocalDateTime.now());
        
        // Save the sampler unit
        boolean success = save(samplerUnit);
        
        // Record operation log
        if (success && operationDetailService != null) {
            try {
                operationDetailService.recordOperationLog(
                        -1, // User ID
                        samplerUnit.getSamplerUnitId().toString(),
                        OperationObjectTypeEnum.SAMPLER_UNIT,
                        "sampler.unit.name",
                        "添加",
                        "",
                        samplerUnit.getSamplerUnitName()
                );
            } catch (Exception e) {
                log.warn("Failed to record operation log", e);
            }
        }
        
        return samplerUnit;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSamplerUnit(SamplerUnit samplerUnit) {
        SamplerUnit originSamplerUnit = findBySamplerUnitId(samplerUnit.getSamplerUnitId());
        if (originSamplerUnit == null) {
            return false;
        }
        
        boolean success = updateById(samplerUnit);
        
        // Record operation log
        if (success && operationDetailService != null) {
            try {
                operationDetailService.compareEntitiesRecordLog(
                        null, // User ID
                        originSamplerUnit,
                        samplerUnit
                );
            } catch (Exception e) {
                log.warn("Failed to record operation log", e);
            }
        }
        
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteSamplerUnit(Integer samplerUnitId) {
        SamplerUnit originSamplerUnit = findBySamplerUnitId(samplerUnitId);
        if (originSamplerUnit == null) {
            return false;
        }
        
        LambdaQueryWrapper<SamplerUnit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SamplerUnit::getSamplerUnitId, samplerUnitId);
        boolean success = remove(queryWrapper);
        
        // Record operation log
        if (success && operationDetailService != null) {
            try {
                operationDetailService.recordOperationLog(
                        null, // User ID
                        originSamplerUnit.getSamplerUnitId().toString(),
                        OperationObjectTypeEnum.SAMPLER_UNIT,
                        "sampler.unit.name",
                        "删除",
                        originSamplerUnit.getSamplerUnitName(),
                        ""
                );
            } catch (Exception e) {
                log.warn("Failed to record operation log", e);
            }
        }
        
        return success;
    }
    @Override
    public void deleteByPortId(Integer portId) {
        samplerUnitMapper.selectList(Wrappers.lambdaQuery(SamplerUnit.class).eq(SamplerUnit::getPortId, portId)).forEach(samplerUnit -> {
            samplerUnitMapper.delete(Wrappers.lambdaQuery(SamplerUnit.class).eq(SamplerUnit::getSamplerUnitId, samplerUnit.getSamplerUnitId()));
            changeEventService.sendDelete(samplerUnit);
            operationDetailService.recordOperationLog(null, samplerUnit.getSamplerUnitId().toString(), OperationObjectTypeEnum.SAMPLER_UNIT, i18n.T("sampler.unit.name"), i18n.T("delete"), samplerUnit.getSamplerUnitName(),"");
        });
    }

    @Override
    public List<SamplerUnit> selectSamplerUnitWithPort(Integer monitorUnitId) {
        return samplerUnitMapper.selectSamplerUnitWithPort(monitorUnitId);
    }

    @Override
    public List<SamplerUnit> findByMonitorUnitId(Integer monitorUnitId) {
        LambdaQueryWrapper<SamplerUnit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SamplerUnit::getMonitorUnitId, monitorUnitId);
        return list(queryWrapper);
    }

    @Override
    public List<SamplerUnit> findByParentSamplerUnitIdAndSamplerUnitName(Integer parentSamplerUnitId, String samplerUnitName) {
        LambdaQueryWrapper<SamplerUnit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SamplerUnit::getParentSamplerUnitId, parentSamplerUnitId)
                .eq(SamplerUnit::getSamplerUnitName, samplerUnitName);
        return list(queryWrapper);
    }

    @Override
    public SamplerUnit findSamplerUnit(Integer monitorUnitId, String samplerUnitName, String portName) {
        return samplerUnitMapper.findSamplerUnit(monitorUnitId, samplerUnitName, portName);
    }

    @Override
    public List<SamplerUnit> findByMonitorUnitIdAndPortId(Integer monitorUnitId, Integer portId) {
        LambdaQueryWrapper<SamplerUnit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SamplerUnit::getMonitorUnitId, monitorUnitId)
                .eq(SamplerUnit::getPortId, portId);
        return list(queryWrapper);
    }

    // ==================== 设备管理相关方法实现 ====================

    @Override
    public List<com.siteweb.tcs.siteweb.vo.SamplerUnitWithPortVO> getSamplerUnitWithPort() {
        try {
            return samplerUnitMapper.getSamplerUnitWithPort();
        } catch (Exception e) {
            log.error("Failed to get sampler unit with port", e);
            return List.of();
        }
    }
}
