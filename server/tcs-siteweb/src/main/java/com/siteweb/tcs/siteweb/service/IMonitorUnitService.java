package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.dto.MonitorUnitDTO;
import com.siteweb.tcs.siteweb.entity.MonitorUnit;

import java.util.List;

/**
 * Monitor Unit Service Interface
 */
public interface IMonitorUnitService extends IService<MonitorUnit> {
    
    /**
     * Check if monitor unit is cross-site
     *
     * @param monitorUnitId Monitor unit ID
     * @return True if cross-site, false otherwise
     */
    boolean isCrossSite(Integer monitorUnitId);
    
    /**
     * Check if monitor unit is cross-site monitoring unit
     *
     * @param monitorUnitId Monitor unit ID
     * @return True if cross-site monitoring unit, false otherwise
     */
    boolean isCrossSiteMonitoringUnit(Integer monitorUnitId);
    
    /**
     * Find monitor unit by ID
     *
     * @param monitorUnitId Monitor unit ID
     * @return MonitorUnitDTO with monitor unit data
     */
    MonitorUnitDTO findById(Integer monitorUnitId);

    /**
     * Find monitor unit by ID
     *
     * @param monitorUnitId Monitor unit ID
     * @return MonitorUnitDTO with monitor unit data
     */
    MonitorUnitDTO findByIdWithoutStation(Integer monitorUnitId);

    /**
     * Update monitor unit
     *
     * @param monitorUnit Monitor unit DTO
     * @return true if updated successfully, false otherwise
     */
    boolean updateMonitorUnit(MonitorUnitDTO monitorUnit);
    
    /**
     * Delete monitor unit
     *
     * @param monitorUnitId Monitor unit ID
     * @return true if deleted successfully, false otherwise
     */
    boolean deleteMonitorUnit(Integer monitorUnitId);
    
    /**
     * Delete monitor unit and its associated equipment
     *
     * @param monitorUnitId Monitor unit ID
     * @return true if deleted successfully, false otherwise
     */
    boolean deleteMonitorUnitAndEqs(Integer monitorUnitId);
    
    /**
     * Validate monitor unit data
     *
     * @param monitorUnit Monitor unit DTO
     */
    void verification(MonitorUnitDTO monitorUnit);
    
    /**
     * Create a new monitor unit
     *
     * @param monitorUnit Monitor unit data
     * @param resourceStructureId Resource structure ID
     * @return true if created successfully, false otherwise
     */
    boolean createMonitorUnit(MonitorUnitDTO monitorUnit, Integer resourceStructureId);
    
    /**
     * Create a new monitor unit (V3 API)
     *
     * @param monitorUnit Monitor unit data
     * @return true if created successfully, false otherwise
     */
    boolean createMonitorUnitV3(MonitorUnitDTO monitorUnit);
    
    /**
     * Find monitor units by IDs
     *
     * @param monitorUnitIds List of monitor unit IDs
     * @return List of monitor unit DTOs
     */
    List<MonitorUnitDTO> findByIds(List<Integer> monitorUnitIds);
    
    /**
     * Find all monitor units
     *
     * @return List of all monitor unit DTOs
     */
    List<MonitorUnitDTO> findAllMonitorUnit();

    List<MonitorUnitDTO> findAllMonitorUnitWithoutStation();

    /**
     * 更新监控单元配置文件代码
     *
     * @param monitorUnitId 监控单元ID
     * @param configFileCode 配置文件代码
     */
    void updateMonitorUnitConfigFileCode(Integer monitorUnitId, String configFileCode);

    /**
     * 根据工作站ID查找监控单元
     *
     * @param workStationId 工作站ID
     * @return 监控单元列表
     */
    List<MonitorUnitDTO> findByWorkStationId(Integer workStationId);
}
