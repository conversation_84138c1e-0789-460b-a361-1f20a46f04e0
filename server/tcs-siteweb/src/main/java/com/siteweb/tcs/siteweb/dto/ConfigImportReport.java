package com.siteweb.tcs.siteweb.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 配置导入报告（简化版）
 * 由于在导入前已经进行了详细的差异对比，这里只需要记录导入结果
 */
@Data
public class ConfigImportReport {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 导入时间
     */
    private LocalDateTime importTime;

    /**
     * 导入模式
     */
    private String importMode;

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 错误信息（如果失败）
     */
    private String errorMessage;

    /**
     * 监控单元ID
     */
    private Integer monitorUnitId;

    /**
     * 监控单元名称
     */
    private String monitorUnitName;

    /**
     * 是否为新建监控单元
     */
    private boolean isNewMonitorUnit;

    /**
     * 处理的总项目数（基于差异对比结果）
     */
    private int totalProcessedItems = 0;

    /**
     * 成功处理的项目数
     */
    private int successfulItems = 0;

    /**
     * 失败的项目数
     */
    private int failedItems = 0;

    /**
     * 跳过的项目数
     */
    private int skippedItems = 0;

    /**
     * 导入耗时（毫秒）
     */
    private long durationMs = 0;

    /**
     * 简要描述
     */
    private String message;

    /**
     * 创建成功的报告
     */
    public static ConfigImportReport createSuccessReport(String taskId, Integer monitorUnitId,
                                                        String monitorUnitName, boolean isNewMonitorUnit) {
        ConfigImportReport report = new ConfigImportReport();
        report.setTaskId(taskId);
        report.setImportTime(LocalDateTime.now());
        report.setSuccess(true);
        report.setMonitorUnitId(monitorUnitId);
        report.setMonitorUnitName(monitorUnitName);
        report.setNewMonitorUnit(isNewMonitorUnit);

        if (isNewMonitorUnit) {
            report.setMessage("新建监控单元配置导入成功");
        } else {
            report.setMessage("监控单元配置更新成功");
        }

        return report;
    }

    /**
     * 创建失败的报告
     */
    public static ConfigImportReport createFailureReport(String taskId, String errorMessage) {
        ConfigImportReport report = new ConfigImportReport();
        report.setTaskId(taskId);
        report.setImportTime(LocalDateTime.now());
        report.setSuccess(false);
        report.setErrorMessage(errorMessage);
        report.setMessage("配置导入失败");
        return report;
    }

    /**
     * 设置处理统计信息
     */
    public void setProcessingStats(int total, int successful, int failed, int skipped) {
        this.totalProcessedItems = total;
        this.successfulItems = successful;
        this.failedItems = failed;
        this.skippedItems = skipped;
    }

    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        if (totalProcessedItems == 0) {
            return 0.0;
        }
        return (double) successfulItems / totalProcessedItems * 100;
    }
}
