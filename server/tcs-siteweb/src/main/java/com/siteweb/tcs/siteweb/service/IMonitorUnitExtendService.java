package com.siteweb.tcs.siteweb.service;

import com.siteweb.tcs.siteweb.entity.MonitorUnitExtend;
import com.siteweb.tcs.siteweb.vo.MonitorUnitPasswordVO;

import java.util.List;

/**
 * 监控单元扩展信息服务接口
 */
public interface IMonitorUnitExtendService {

    /**
     * 插入监控单元扩展信息
     * @param monitorUnitExtend 监控单元扩展信息
     * @return 影响行数
     */
    int insert(MonitorUnitExtend monitorUnitExtend);

    /**
     * 更新监控单元扩展信息
     * @param monitorUnitExtend 监控单元扩展信息
     * @return 影响行数
     */
    int update(MonitorUnitExtend monitorUnitExtend);

    /**
     * 删除监控单元扩展信息
     * @param monitorUnitId 监控单元ID
     * @return 影响行数
     */
    int delete(int monitorUnitId);

    /**
     * 根据监控单元ID列表查询是否设置了密码
     * @param monitorUnitIds 监控单元ID列表
     * @return 密码状态列表
     */
    List<MonitorUnitPasswordVO> findMonitorUnitPassword(List<Integer> monitorUnitIds);

    /**
     * 根据监控单元ID查询扩展信息
     * @param monitorUnitId 监控单元ID
     * @return 监控单元扩展信息
     */
    MonitorUnitExtend findByMonitorUnitId(int monitorUnitId);
}
