package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;

/**
 * 资源结构类型实体类
 * Resource structure type table
 */
@Data
@TableName("resourcestructuretype")
public class ResourceStructureType implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分类Id，主键
     */
    @TableId(value = "resourcestructuretypeid", type = IdType.INPUT) // Assuming ID is not auto-generated based on SQL
    private Integer resourceStructureTypeId;

    /**
     * 场景ID
     */
    @TableField("sceneid")
    private Integer sceneId;

    /**
     * 分类名
     */
    @TableField("resourcestructuretypename")
    private String resourceStructureTypeName;

    /**
     * 描述信息
     */
    @TableField("description")
    private String description;
}