package com.siteweb.tcs.siteweb.vo;


import com.siteweb.tcs.siteweb.entity.ControlMeanings;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 控制vo
 *
 * <AUTHOR>
 * @date 2024/03/28
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ControlVO {
    private Integer id;
    private Integer equipmentTemplateId;
    private Integer controlId;
    private String controlName;
    private Integer controlCategory;
    private String cmdToken;
    private Long baseTypeId;
    private String baseTypeName;
    private Integer controlSeverity;
    private Integer signalId;
    private Double timeOut;
    private Integer retry;
    private String description;
    private Boolean enable;
    private Boolean visible;
    private Integer displayIndex;
    private Integer commandType;
    private Short controlType;
    private Short dataType;
    private Double maxValue;
    private Double minValue;
    private Double defaultValue;
    private Integer moduleNo;
    private List<ControlMeanings> controlMeaningsList;
}
