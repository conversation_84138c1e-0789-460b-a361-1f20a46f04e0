package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.entity.ControlMeanings;

import java.util.List;

/**
 * Control Meanings Service Interface
 */
public interface IControlMeaningsService extends IService<ControlMeanings> {

    void deleteByEquipmentTemplateIdAndControlId(Integer equipmentTemplateId, Integer controlId);

    void batchInsert(List<ControlMeanings> controlMeaningsList);

    void batchUpdateControlMeanings(List<ControlMeanings> controlMeaningsList);

    List<ControlMeanings> findByEquipmentTemplateIdAndControlId(Integer equipmentTemplateId, Integer controlId);

    /**
     * 批量保存控制含义
     *
     * @return 是否保存成功
     */
    boolean batchsaveControlMeanings();

    List<ControlMeanings> findByEquipmentTemplateId(Integer equipmentTemplateId);

    void copyControlMeanings(Integer originEquipmentTemplateId, Integer destEquipmentTemplateId);

}
