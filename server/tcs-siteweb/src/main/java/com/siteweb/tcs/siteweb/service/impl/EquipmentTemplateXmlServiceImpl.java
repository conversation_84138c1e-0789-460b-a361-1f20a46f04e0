package com.siteweb.tcs.siteweb.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import com.siteweb.tcs.siteweb.constants.SignalConstant;
import com.siteweb.tcs.siteweb.dto.EventConfigItem;
import com.siteweb.tcs.siteweb.dto.SignalConfigItem;
import com.siteweb.tcs.siteweb.entity.*;
import com.siteweb.tcs.siteweb.enums.EquipmentCategoryEnum;
import com.siteweb.tcs.siteweb.enums.TableIdentityEnum;
import com.siteweb.tcs.siteweb.service.*;
import com.siteweb.tcs.siteweb.util.I18n;
import com.siteweb.tcs.siteweb.util.StrSplitUtil;
import com.siteweb.tcs.siteweb.vo.ControlVO;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.XMLWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Equipment Template XML Service Implementation
 */
@Slf4j
@Service
public class EquipmentTemplateXmlServiceImpl implements IEquipmentTemplateXmlService {
    
    @Autowired
    private IEquipmentTemplateService equipmentTemplateService;
    
    @Autowired
    private ISignalService signalService;
    
    @Autowired
    private ISamplerService samplerService;
    
    @Autowired
    private IEventService eventService;
    
    @Autowired
    private IControlService controlService;
    
    @Autowired
    private IPrimaryKeyValueService primaryKeyValueService;
    
    @Autowired
    private IChangeEventService changeEventService;
    
    @Autowired
    private ISignalPropertyService signalPropertyService;
    
    @Autowired
    private ISignalMeaningsService signalMeaningsService;
    
    @Autowired
    private IEventConditionService eventConditionService;
    
    @Autowired
    private IControlMeaningsService controlMeaningsService;
    
    @Autowired
    private I18n i18n;

    @Autowired
    @Qualifier("baseDicServiceImpl")
    private IBaseDicService baseDicService;

    @Value("${bytedance.generate-signal-standard:false}")
    private Boolean generateSignalStandard;

    @Override
    public String exportEquipmentTemplate(Integer equipmentTemplateId) {
        EquipmentTemplate equipmentTemplate = equipmentTemplateService.findById(equipmentTemplateId);

        // Create document using dom4j
        Document document = DocumentHelper.createDocument();

        // Create root element
        Element equipmentTemplates = document.addElement("EquipmentTemplates");
        equipmentTemplates.addAttribute("Name", "设备模板列表");

        // 获取采集器节点
        Element samplers = getSamplersElement(document, equipmentTemplate.getProtocolCode());
        equipmentTemplates.add(samplers);

        // 获取设备模板节点
        Element equipmentTemplateElement = getEquipmentTemplateElement(equipmentTemplate);
        equipmentTemplates.add(equipmentTemplateElement);

        // Output XML with pretty format
        OutputFormat format = OutputFormat.createPrettyPrint();
        StringWriter stringWriter = new StringWriter();
        XMLWriter writer = new XMLWriter(stringWriter, format);

        try {
            writer.write(document);
            return stringWriter.toString();
        } catch (IOException e) {
            throw new RuntimeException("Failed to export equipment template", e);
        }
    }
    private Element getSamplersElement(Document document, String protocolCode) {
        Sampler sampler = samplerService.findByProtocolCode(protocolCode);

        // Use DocumentHelper instead of document.addElement
        Element samplersElement = DocumentHelper.createElement("Samplers");

        if (Objects.isNull(sampler)) {
            log.warn("通过协议编码查询不到采集器:protocolCode:{}", protocolCode);
            return samplersElement;
        }

        samplersElement.addAttribute("Name", "采集器");
        Element samplerElement = samplersElement.addElement("Sampler");

        // 设置属性
        samplerElement.addAttribute("SamplerId", Convert.toStr(sampler.getSamplerId(), ""));
        samplerElement.addAttribute("SamplerName", sampler.getSamplerName());
        samplerElement.addAttribute("SamplerType", Convert.toStr(sampler.getSamplerType(), ""));
        samplerElement.addAttribute("ProtocolCode", sampler.getProtocolCode());
        samplerElement.addAttribute("DllCode", sampler.getDllCode());
        samplerElement.addAttribute("DLLVersion", sampler.getDllVersion());
        samplerElement.addAttribute("ProtocolFilePath", sampler.getProtocolFilePath());
        samplerElement.addAttribute("DLLFilePath", sampler.getDllFilePath());
        samplerElement.addAttribute("DllPath", sampler.getDllPath());
        samplerElement.addAttribute("Setting", sampler.getSetting());
        samplerElement.addAttribute("Description", sampler.getDescription());

        return samplersElement;
    }

    /**
     * 获取设备模板元素节点
     *
     * @param equipmentTemplate 设备模板
     * @return {@link Element}
     */
    private Element getEquipmentTemplateElement(EquipmentTemplate equipmentTemplate) {
        // Use DocumentHelper instead of document.addElement
        Element equipmentTemplateElement = DocumentHelper.createElement("EquipmentTemplate");

        equipmentTemplateElement.addAttribute("EquipmentTemplateId", Convert.toStr(equipmentTemplate.getEquipmentTemplateId()));
        equipmentTemplateElement.addAttribute("ParentTemplateId", "0");
        equipmentTemplateElement.addAttribute("EquipmentTemplateName", equipmentTemplate.getEquipmentTemplateName());
        equipmentTemplateElement.addAttribute("ProtocolCode", equipmentTemplate.getProtocolCode());
        equipmentTemplateElement.addAttribute("EquipmentCategory", Convert.toStr(equipmentTemplate.getEquipmentCategory()));
        equipmentTemplateElement.addAttribute("EquipmentType", Convert.toStr(equipmentTemplate.getEquipmentType()));
        equipmentTemplateElement.addAttribute("Memo", Convert.toStr(equipmentTemplate.getMemo(), ""));
        equipmentTemplateElement.addAttribute("Property", Convert.toStr(equipmentTemplate.getProperty(), ""));
        equipmentTemplateElement.addAttribute("Decription", Convert.toStr(equipmentTemplate.getDescription(), ""));
        equipmentTemplateElement.addAttribute("EquipmentStyle", Convert.toStr(equipmentTemplate.getEquipmentStyle(), ""));
        equipmentTemplateElement.addAttribute("Unit", Convert.toStr(equipmentTemplate.getUnit(), ""));
        equipmentTemplateElement.addAttribute("Vendor", Convert.toStr(equipmentTemplate.getVendor(), ""));
        equipmentTemplateElement.addAttribute("StationCategory", Convert.toStr(equipmentTemplate.getStationCategory(), "0"));
        equipmentTemplateElement.addAttribute("EquipmentBaseType", Convert.toStr(equipmentTemplate.getEquipmentBaseType(), ""));

        // 信号
        Element signalsElement = getTemplateSignalsElement(equipmentTemplate.getEquipmentTemplateId());
        equipmentTemplateElement.add(signalsElement);

        // 事件
        Element eventsElement = getEquipmentEventsElement(equipmentTemplate.getEquipmentTemplateId());
        equipmentTemplateElement.add(eventsElement);

        // 控制
        Element controlElement = getEquipmentControlElement(equipmentTemplate.getEquipmentTemplateId());
        equipmentTemplateElement.add(controlElement);

        return equipmentTemplateElement;
    }

    private Element getTemplateSignalsElement(Integer equipmentTemplateId) {
        Element signalsElement = DocumentHelper.createElement("Signals");
        signalsElement.addAttribute("Name", "模板信号");

        List<SignalConfigItem> signalList = signalService.findItemByEquipmentTemplateIdAndEquipmentId(equipmentTemplateId,null);
        for (SignalConfigItem signal : signalList) {
            Element signalElement = signalsElement.addElement("Signal");

            signalElement.addAttribute("SignalId", String.valueOf(signal.getSignalId()));
            signalElement.addAttribute("SignalName", signal.getSignalName());
            signalElement.addAttribute("SignalCategory", String.valueOf(signal.getSignalCategory()));
            signalElement.addAttribute("SignalType", String.valueOf(signal.getSignalType()));
            signalElement.addAttribute("ChannelNo", String.valueOf(signal.getChannelNo()));
            signalElement.addAttribute("ChannelType", String.valueOf(signal.getChannelType()));
            signalElement.addAttribute("Expression", Convert.toStr(signal.getExpression(), ""));
            signalElement.addAttribute("DataType", Convert.toStr(signal.getDataType(), ""));
            signalElement.addAttribute("ShowPrecision", Convert.toStr(signal.getShowPrecision(), ""));
            signalElement.addAttribute("Unit", Convert.toStr(signal.getUnit(), ""));
            signalElement.addAttribute("StoreInterval", formatNumberStr(signal.getStoreInterval(), ""));
            signalElement.addAttribute("AbsValueThreshold", formatNumberStr(signal.getAbsValueThreshold(), ""));
            signalElement.addAttribute("PercentThreshold", formatNumberStr(signal.getPercentThreshold(), ""));
            signalElement.addAttribute("StaticsPeriod", Convert.toStr(signal.getStaticsPeriod(), ""));
            signalElement.addAttribute("Enable", formatBooleanStr(signal.getEnable(), ""));
            signalElement.addAttribute("Visible", formatBooleanStr(signal.getVisible(), ""));
            if (generateSignalStandard && signal.getDescription().length() > 9) {
                signalElement.addAttribute("Discription", "bytedancebid=" + signal.getDescription().replaceAll("-", ""));
            } else {
                signalElement.addAttribute("Discription", String.valueOf(signal.getDescription()));
            }
            signalElement.addAttribute("BaseTypeId", Convert.toStr(signal.getBaseTypeId(), ""));
            signalElement.addAttribute("ChargeStoreInterVal", Convert.toStr(signal.getChargeStoreInterVal(), ""));
            signalElement.addAttribute("ChargeAbsValue", Convert.toStr(signal.getChargeAbsValue(), ""));
            signalElement.addAttribute("DisplayIndex", Convert.toStr(signal.getDisplayIndex()));
//            signalElement.addAttribute("MDBSignalId", Convert.toStr(signal.getMDBSignalId()));
            signalElement.addAttribute("SignalProperty", signal.getSignalPropertyXmlFormat());
            signalElement.addAttribute("SignalMeaning", signal.getSignalMeaningsXmlFormat());
            signalElement.addAttribute("ModuleNo", Convert.toStr(signal.getModuleNo(), ""));
        }

        return signalsElement;
    }

    private Element getEquipmentControlElement(Integer equipmentTemplateId) {
        Element controlsElement = DocumentHelper.createElement("Controls");
        controlsElement.addAttribute("Name", "模板控制");

        List<ControlVO> controlList = controlService.findVoByEquipmentTemplateId(equipmentTemplateId);
        for (ControlVO control : controlList) {
            Element controlElement = controlsElement.addElement("Control");

            controlElement.addAttribute("ControlId", Convert.toStr(control.getControlId()));
            controlElement.addAttribute("ControlName", control.getControlName());
            controlElement.addAttribute("ControlCategory", Convert.toStr(control.getControlCategory()));
            controlElement.addAttribute("CmdToken", control.getCmdToken());
            controlElement.addAttribute("BaseTypeId", Convert.toStr(control.getBaseTypeId()));
            controlElement.addAttribute("ControlSeverity", Convert.toStr(control.getControlSeverity()));
            controlElement.addAttribute("SignalId", Convert.toStr(control.getSignalId()));
            controlElement.addAttribute("TimeOut", Convert.toStr(control.getTimeOut()));
            controlElement.addAttribute("Retry", Convert.toStr(control.getRetry()));
            if (generateSignalStandard && control.getDescription().length() > 9) {
                controlElement.addAttribute("Description", control.getDescription().replaceAll("-", ""));
            } else {
                controlElement.addAttribute("Description", control.getDescription());
            }
            controlElement.addAttribute("Enable", Convert.toStr(control.getEnable()));
            controlElement.addAttribute("Visible", Convert.toStr(control.getVisible()));
            controlElement.addAttribute("DisplayIndex", Convert.toStr(control.getDisplayIndex()));
            controlElement.addAttribute("CommandType", Convert.toStr(control.getCommandType()));
            controlElement.addAttribute("ControlType", Convert.toStr(control.getControlType()));
            controlElement.addAttribute("DataType", Convert.toStr(control.getDataType()));
            controlElement.addAttribute("MaxValue", Convert.toStr(control.getMaxValue()));
            controlElement.addAttribute("MinValue", Convert.toStr(control.getMinValue()));
            controlElement.addAttribute("DefaultValue", Convert.toStr(control.getDefaultValue()));
            controlElement.addAttribute("ModuleNo", Convert.toStr(control.getModuleNo()));

            StringJoiner sj = new StringJoiner(";");
            for (ControlMeanings meanings : control.getControlMeaningsList()) {
                if (Objects.isNull(meanings.getParameterValue()) || Objects.isNull(meanings.getMeanings())) {
                    continue;
                }
                sj.add(meanings.getParameterValue() + ":" + meanings.getMeanings());
            }

            controlElement.addAttribute("ControlMeanings", sj.toString());
        }

        return controlsElement;
    }

    private Element getEquipmentEventsElement(Integer equipmentTemplateId) {
        Element eventsElement = DocumentHelper.createElement("Events");
        eventsElement.addAttribute("Name", "模板事件");

        List<EventConfigItem> eventList = eventService.findEventItemByEquipmentTemplateId(equipmentTemplateId);
        for (EventConfigItem event : eventList) {
            Element eventElement = eventsElement.addElement("Event");

            eventElement.addAttribute("EventId", Convert.toStr(event.getEventId()));
            eventElement.addAttribute("EventName", event.getEventName());
            eventElement.addAttribute("EventCategory", Convert.toStr(event.getEventCategory()));
            eventElement.addAttribute("StartType", Convert.toStr(event.getStartType()));
            eventElement.addAttribute("EndType", Convert.toStr(event.getEndType()));
            eventElement.addAttribute("StartExpression", event.getStartExpression());
            eventElement.addAttribute("SuppressExpression", event.getSuppressExpression());
            eventElement.addAttribute("SignalId", Convert.toStr(event.getSignalId(), ""));
            eventElement.addAttribute("Enable", formatBooleanStr(event.getEnable(), ""));
            eventElement.addAttribute("Visible", formatBooleanStr(event.getVisible(), ""));
            if (generateSignalStandard && event.getDescription().length() > 9) {
                eventElement.addAttribute("Description", "bytedancebid=" + event.getDescription().replaceAll("-", ""));
            } else {
                eventElement.addAttribute("Description", event.getDescription());
            }
            eventElement.addAttribute("Turnover", Convert.toStr(event.getTurnover(), ""));
            eventElement.addAttribute("DisplayIndex", Convert.toStr(event.getDisplayIndex()));
            eventElement.addAttribute("ModuleNo", Convert.toStr(event.getModuleNo(), ""));

            Element conditionsElement = eventElement.addElement("Conditions");

            for (EventCondition eventCondition : event.getEventCondition()) {
                Element eventConditionElement = conditionsElement.addElement("EventCondition");

                eventConditionElement.addAttribute("EventConditionId", Convert.toStr(eventCondition.getEventConditionId()));
                eventConditionElement.addAttribute("EventSeverity", Convert.toStr(eventCondition.getEventSeverity()));
                eventConditionElement.addAttribute("StartOperation", Convert.toStr(eventCondition.getStartOperation(), ""));
                eventConditionElement.addAttribute("StartCompareValue", formatNumberStr(eventCondition.getStartCompareValue(), ""));
                eventConditionElement.addAttribute("StartDelay", Convert.toStr(eventCondition.getStartDelay(), ""));
                eventConditionElement.addAttribute("EndOperation", Convert.toStr(eventCondition.getEndOperation(), ""));
                eventConditionElement.addAttribute("EndCompareValue", Convert.toStr(eventCondition.getEndCompareValue(), ""));
                eventConditionElement.addAttribute("EndDelay", Convert.toStr(eventCondition.getEndDelay(), ""));
                eventConditionElement.addAttribute("Frequency", Convert.toStr(eventCondition.getFrequency(), ""));
                eventConditionElement.addAttribute("FrequencyThreshold", Convert.toStr(eventCondition.getFrequencyThreshold(), ""));
                eventConditionElement.addAttribute("Meanings", eventCondition.getMeanings());
                eventConditionElement.addAttribute("EquipmentState", Convert.toStr(eventCondition.getEquipmentState(), ""));
                eventConditionElement.addAttribute("BaseTypeId", Convert.toStr(eventCondition.getBaseTypeId(), ""));
                eventConditionElement.addAttribute("StandardName", Convert.toStr(eventCondition.getStandardName(), ""));
            }
        }

        return eventsElement;
    }

    /**
     * 格式化数字为字符串，null值将返回指定的默认值
     * @param value
     * @param defaultValue
     * @return
     */
    private String formatNumberStr(Double value, String defaultValue) {
        if (value == null) {
            return defaultValue;
        }

        if (value % 1 == 0) {
            // 如果是整数，使用不带小数点的格式
            return NumberUtil.decimalFormat("#", value);
        }
        return Convert.toStr(value);
    }
    /**
     * 将Boolean值转换为"True"或"False"的字符串形式，null值将返回指定的默认值
     *
     * @param value Boolean类型的值
     * @param defaultValue 当值为null时返回的默认值
     * @return 转换后的字符串，非null值转为"True"或"False"
     */
    private String formatBooleanStr(Boolean value, String defaultValue) {
        if (value == null) {
            return defaultValue;
        }
        return value ? "True" : "False";
    }



    @Override
    public Element exportEquipmentTemplateElement(List<Integer> equipmentTemplateIds) {
        // TODO: Implement export functionality later
        log.warn("exportEquipmentTemplateElement not fully implemented yet");
        return DocumentHelper.createElement("EquipmentTemplates");
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public EquipmentTemplate importTemplate(Element equipmentTemplatesElement) {
        Element equipmentTemplateElement = equipmentTemplatesElement.element("EquipmentTemplate");
        EquipmentTemplate equipmentTemplate = generateTemplateFromXml(equipmentTemplateElement);
        if (Objects.isNull(equipmentTemplate)) {
            return null;
        }

        // 生成采集器(协议) - 简化实现
        if (equipmentTemplatesElement.element("Samplers") != null) {
            log.info("Samplers processing skipped in simplified implementation");
        }

        // 生成采集器(协议)
        samplerService.createSamplersFromXml(equipmentTemplate, equipmentTemplatesElement.element("Samplers"));

        // 产生设备模板信号
        generateSignalsFromXml(equipmentTemplate.getEquipmentTemplateId(), equipmentTemplateElement.element("Signals"));

        // 产生设备模板事件
        generateEventsFromXml(equipmentTemplate.getEquipmentTemplateId(), equipmentTemplateElement.element("Events"));

        // 产生设备控制
        generateCommandsFromXml(equipmentTemplate.getEquipmentTemplateId(), equipmentTemplateElement.element("Controls"));

        // 升级基类标准化字典
        // 升级基类标准化字典
        baseDicService.updateBaseClassStandardDictionary(equipmentTemplate.getEquipmentTemplateId());
        
        changeEventService.sendCreate(equipmentTemplate);
        return equipmentTemplate;
    }

    /**
     * 生成控制从xml
     */
    private void generateCommandsFromXml(Integer equipmentTemplateId, Element controlsElement) {
        if (Objects.isNull(controlsElement)) {
            return;
        }

        List<Element> controlElementList = controlsElement.elements("Control");
        List<Control> batchInsertControlList = new ArrayList<>(controlElementList.size());
        List<ControlMeanings> batchInsertControlMeaningsList = new ArrayList<>(controlElementList.size());

        for (Element element : controlElementList) {
            Control control = parseControlFromXml(equipmentTemplateId, element);
            batchInsertControlList.add(control);

            String controlMeaningsStr = element.attributeValue("ControlMeanings");
            if (controlMeaningsStr != null) {
                List<ControlMeanings> controlMeaningsList = createControlMeaningsFormXml(
                        equipmentTemplateId,
                        control.getControlId(),
                        controlMeaningsStr.split(";")
                );
                batchInsertControlMeaningsList.addAll(controlMeaningsList);
            }
        }

        controlService.saveBatch(batchInsertControlList);
        controlMeaningsService.saveBatch(batchInsertControlMeaningsList);
    }

    private List<ControlMeanings> createControlMeaningsFormXml(Integer equipmentTemplateId, Integer controlId, String[] controlMeaningsArr) {
        List<ControlMeanings> batchInsertControlMeanings = new ArrayList<>(controlMeaningsArr.length);
        for (String controlMeaningStr : controlMeaningsArr) {
            String[] meaningArr = controlMeaningStr.split(":");
            if (!NumberUtil.isNumber(meaningArr[0])) {
                continue;
            }
            ControlMeanings controlMeanings = new ControlMeanings();
            controlMeanings.setEquipmentTemplateId(equipmentTemplateId);
            controlMeanings.setControlId(controlId);
            controlMeanings.setParameterValue(Integer.valueOf(meaningArr[0]));
            controlMeanings.setMeanings(meaningArr[1]);
            if (meaningArr.length == 3) {
                controlMeanings.setBaseCondId(Integer.valueOf(meaningArr[2]));
            }
            batchInsertControlMeanings.add(controlMeanings);
        }
        return batchInsertControlMeanings;
    }

    private Control parseControlFromXml(Integer equipmentTemplateId, Element element) {
        Long baseTypeId = getAttributeAsLong(element, "BaseTypeId", 0L);
        String description = element.attributeValue("Description");
        if (description != null) {
            if (description.contains("bytedancebid=")) {
                description = description.replace("bytedancebid=", "");
            }
        } else {
            description = "";
        }
        
        Control control = new Control();
        control.setEquipmentTemplateId(equipmentTemplateId);
        control.setControlId(getAttributeAsInteger(element, "ControlId"));
        control.setControlName(element.attributeValue("ControlName"));
        control.setControlCategory(getAttributeAsInteger(element, "ControlCategory"));
        control.setCmdToken(element.attributeValue("CmdToken"));
        control.setBaseTypeId(Objects.equals(baseTypeId, 0L) ? null : baseTypeId);
        control.setControlSeverity(getAttributeAsInteger(element, "ControlSeverity"));
        control.setSignalId(getAttributeAsInteger(element, "SignalId"));
        control.setTimeOut(getAttributeAsDouble(element, "TimeOut"));
        control.setRetry(getAttributeAsInteger(element, "Retry"));
        control.setDescription(description);
        control.setEnable(getAttributeAsBoolean(element, "Enable"));
        control.setVisible(getAttributeAsBoolean(element, "Visible"));
        control.setDisplayIndex(getAttributeAsInteger(element, "DisplayIndex"));
        control.setCommandType(getAttributeAsInteger(element, "CommandType"));
        control.setControlType(getAttributeAsShort(element, "ControlType"));
        control.setDataType(getAttributeAsShort(element, "DataType"));
        control.setMaxValue(getAttributeAsDouble(element, "MaxValue"));
        control.setMinValue(getAttributeAsDouble(element, "MinValue"));
        control.setDefaultValue(getAttributeAsDouble(element, "DefaultValue"));
        control.setModuleNo(getAttributeAsInteger(element, "ModuleNo", 0));
        
        return control;
    }

    // Helper methods
    private Integer getAttributeAsInteger(Element element, String name) {
        String value = element.attributeValue(name);
        return (value != null && !value.isEmpty()) ? Integer.parseInt(value) : null;
    }

    private Integer getAttributeAsInteger(Element element, String name, Integer defaultValue) {
        String value = element.attributeValue(name);
        return (value != null && !value.isEmpty()) ? Integer.parseInt(value) : defaultValue;
    }

    private Long getAttributeAsLong(Element element, String name, Long defaultValue) {
        String value = element.attributeValue(name);
        return (value != null && !value.isEmpty()) ? Long.parseLong(value) : defaultValue;
    }

    private Double getAttributeAsDouble(Element element, String name) {
        String value = element.attributeValue(name);
        return (value != null && !value.isEmpty()) ? Double.parseDouble(value) : null;
    }

    private Boolean getAttributeAsBoolean(Element element, String name) {
        String value = element.attributeValue(name);
        return (value != null && !value.isEmpty()) ? Boolean.parseBoolean(value) : null;
    }

    private Short getAttributeAsShort(Element element, String name) {
        String value = element.attributeValue(name);
        return (value != null && !value.isEmpty()) ? Short.parseShort(value) : null;
    }

    /**
     * 通过xml解析信号
     */
    private void generateSignalsFromXml(Integer equipmentTemplateId, Element signalsElementList) {
        createSignalFormXml(equipmentTemplateId, signalsElementList);
        EquipmentTemplate equipmentTemplate = equipmentTemplateService.getById(equipmentTemplateId);
        //xiong 写死了99，记住，一一对应 【99 自诊断设备】
        if (Objects.equals(equipmentTemplate.getEquipmentCategory(), EquipmentCategoryEnum.SELF_DIAGNOSTICS.getValue())) {
            return;
        }
        //生成设备通信状态相关信号
        signalService.createCommunicationStateSignal(equipmentTemplateId);
    }

    /**
     * 通过xml解析事件
     */
    private void generateEventsFromXml(Integer equipmentTemplateId, Element eventsElement) {
        createEventFormXml(equipmentTemplateId, eventsElement);
        EquipmentTemplate equipmentTemplate = equipmentTemplateService.getById(equipmentTemplateId);
        //xiong 写死了99，记住，一一对应 【99 自诊断设备】
        if (Objects.equals(equipmentTemplate.getEquipmentCategory(), EquipmentCategoryEnum.SELF_DIAGNOSTICS.getValue())) {
            return;
        }
        //生成通讯状态相关事件
        eventService.createCommunicationStateEvent(equipmentTemplateId);
    }

    /**
     * 创建事件从xml
     */
    private void createEventFormXml(Integer equipmentTemplateId, Element eventsElement) {
        if (Objects.isNull(eventsElement)) {
            return;
        }
        
        List<Element> eventElementList = eventsElement.elements("Event");
        List<Event> batchInsertEventList = new ArrayList<>(eventElementList.size());
        List<EventCondition> batchInsertEventConditionList = new ArrayList<>(eventElementList.size());

        for (Element element : eventElementList) {
            Event event = parseEventXml(equipmentTemplateId, element);
            // insertBatchSomeColumn不允许字段为null，如果ModuleNo为null，默认设置为0。
            if (Objects.isNull(event.getModuleNo())) {
                event.setModuleNo(0);
            }
            batchInsertEventList.add(event);

            // 添加事件条件
            Element conditionsElement = element.element("Conditions");
            if (conditionsElement != null) {
                List<EventCondition> eventCondition = createEventConditionFormXml(
                        equipmentTemplateId,
                        event.getEventId(),
                        conditionsElement.elements("EventCondition")
                );
                batchInsertEventConditionList.addAll(eventCondition);
            }
        }

        eventService.saveBatch(batchInsertEventList);
        eventConditionService.saveBatch(batchInsertEventConditionList);
    }

    private List<EventCondition> createEventConditionFormXml(Integer equipmentTemplateId, Integer eventId, List<Element> eventConditionElements) {
        List<EventCondition> eventConditionList = new ArrayList<>(eventConditionElements.size());

        for (Element element : eventConditionElements) {
            Long baseTypeId = getAttributeAsLong(element, "BaseTypeId", 0L);
            EventCondition eventCondition = new EventCondition();
            eventCondition.setEquipmentTemplateId(equipmentTemplateId);
            eventCondition.setEventId(eventId);
            eventCondition.setEventConditionId(getAttributeAsInteger(element, "EventConditionId"));
            eventCondition.setEventSeverity(getAttributeAsInteger(element, "EventSeverity"));
            eventCondition.setStartOperation(element.attributeValue("StartOperation"));
            eventCondition.setStartCompareValue(getAttributeAsDouble(element, "StartCompareValue"));
            eventCondition.setStartDelay(getAttributeAsInteger(element, "StartDelay"));
            eventCondition.setEndOperation(element.attributeValue("EndOperation"));
            eventCondition.setEndCompareValue(getAttributeAsDouble(element, "EndCompareValue"));
            eventCondition.setEndDelay(getAttributeAsInteger(element, "EndDelay"));
            eventCondition.setFrequency(getAttributeAsInteger(element, "Frequency"));
            eventCondition.setFrequencyThreshold(getAttributeAsInteger(element, "FrequencyThreshold"));
            eventCondition.setMeanings(element.attributeValue("Meanings"));
            eventCondition.setEquipmentState(getAttributeAsInteger(element, "EquipmentState"));
            eventCondition.setBaseTypeId(Objects.equals(baseTypeId, 0L) ? null : baseTypeId);
            eventCondition.setStandardName(getAttributeAsInteger(element, "StandardName"));
            eventConditionList.add(eventCondition);
        }

        return eventConditionList;
    }

    private Event parseEventXml(Integer equipmentTemplateId, Element element) {
        String description = element.attributeValue("Description");
        if (description != null) {
            if (description.contains("bytedancebid=")) {
                description = description.replace("bytedancebid=", "");
            }
        } else {
            description = "";
        }
        
        Event event = new Event();
        event.setEquipmentTemplateId(equipmentTemplateId);
        event.setEventId(getAttributeAsInteger(element, "EventId"));
        event.setEventName(element.attributeValue("EventName"));
        event.setSignalId(getAttributeAsInteger(element, "SignalId"));
        event.setStartExpression(element.attributeValue("StartExpression"));
        event.setEventCategory(getAttributeAsInteger(element, "EventCategory"));
        event.setStartType(getAttributeAsInteger(element, "StartType"));
        event.setEndType(getAttributeAsInteger(element, "EndType"));
        event.setSuppressExpression(element.attributeValue("SuppressExpression"));
        event.setEnable(getAttributeAsBoolean(element, "Enable"));
        event.setVisible(getAttributeAsBoolean(element, "Visible"));
        event.setDescription(description);
        event.setDisplayIndex(getAttributeAsInteger(element, "DisplayIndex"));
        event.setModuleNo(getAttributeAsInteger(element, "ModuleNo"));
        
        return event;
    }

    private EquipmentTemplate generateTemplateFromXml(Element equipmentTemplateElement) {
        // 从XML中获取设备模板的基础属性
        EquipmentTemplate equipmentTemplate = parseEquipmentTemplateXml(equipmentTemplateElement);

        // 简化分类处理
        Integer equipmentCategory = equipmentTemplate.getEquipmentCategory();
        if (equipmentCategory != null) {
            equipmentTemplate.setEquipmentCategory(equipmentCategory);
        } else {
            equipmentTemplate.setEquipmentCategory(1); // 默认分类
        }

        int equipmentTemplateId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_EQUIPMENT_TEMPLATE, 0);
        
        // 检查模板名称是否已存在 - 简化实现
        if (equipmentTemplate.getEquipmentTemplateName() != null) {
            // 模板名称已存在，重置模板名称
            String equipmentTemplateName = equipmentTemplate.getEquipmentTemplateName() + equipmentTemplateId;
            equipmentTemplate.setEquipmentTemplateName(equipmentTemplateName);
        }

        equipmentTemplate.setEquipmentTemplateId(equipmentTemplateId);
            equipmentTemplateService.save(equipmentTemplate);
            
        // 记录维谛设备种类和电信移动联通种类对应 - 简化实现
        log.info("Origin business category mapping skipped in simplified implementation");

        return equipmentTemplate;
    }

    private EquipmentTemplate parseEquipmentTemplateXml(Element equipmentTemplateElement) {
        EquipmentTemplate equipmentTemplate = new EquipmentTemplate();
        equipmentTemplate.setParentTemplateId(0);
        equipmentTemplate.setEquipmentTemplateName(equipmentTemplateElement.attributeValue("EquipmentTemplateName"));
        equipmentTemplate.setProtocolCode(equipmentTemplateElement.attributeValue("ProtocolCode"));
        equipmentTemplate.setEquipmentCategory(getAttributeAsInteger(equipmentTemplateElement, "EquipmentCategory"));
        equipmentTemplate.setEquipmentType(getAttributeAsInteger(equipmentTemplateElement, "EquipmentType"));
        equipmentTemplate.setMemo(equipmentTemplateElement.attributeValue("Memo"));
        equipmentTemplate.setProperty(equipmentTemplateElement.attributeValue("Property"));
        equipmentTemplate.setDescription(equipmentTemplateElement.attributeValue("Decription"));
        equipmentTemplate.setEquipmentStyle(equipmentTemplateElement.attributeValue("EquipmentStyle"));
        equipmentTemplate.setUnit(equipmentTemplateElement.attributeValue("Unit"));
        equipmentTemplate.setVendor(equipmentTemplateElement.attributeValue("Vendor"));
        equipmentTemplate.setEquipmentBaseType(getAttributeAsInteger(equipmentTemplateElement, "EquipmentBaseType"));
        equipmentTemplate.setStationCategory(getAttributeAsInteger(equipmentTemplateElement, "StationCategory", 0));

        if (equipmentTemplate.getEquipmentTemplateName() != null && equipmentTemplate.getEquipmentTemplateName().length() > 128) {
            throw new RuntimeException("设备模板名称不能超过128个字符");
        }
            
            return equipmentTemplate;
    }

    private void createSignalFormXml(Integer equipmentTemplateId, Element signalsElement) {
        if (Objects.isNull(signalsElement)) {
            return;
        }
        
        List<Element> signalElementList = signalsElement.elements("Signal");
        List<Signal> batchSignalList = new ArrayList<>(signalElementList.size());
        List<SignalProperty> batchSignalPropertyList = new ArrayList<>(signalElementList.size());
        List<SignalMeanings> batchSignalMeaningsList = new ArrayList<>(signalElementList.size());
        AtomicInteger atomicSignalId = new AtomicInteger(-1);

        for (Element signalElement : signalElementList) {
            Signal signal = parserSignalXml(equipmentTemplateId, signalElement, atomicSignalId);
            batchSignalList.add(signal);

            List<SignalProperty> signalPropertyList = parserSignalPropertyXml(equipmentTemplateId, signalElement, signal.getSignalId());
            batchSignalPropertyList.addAll(signalPropertyList);

            List<SignalMeanings> signalMeaningsList = parserSignalMeaningsXml(equipmentTemplateId, signalElement, signal.getSignalId());
            batchSignalMeaningsList.addAll(signalMeaningsList);
        }

        signalService.batchInsertSignal(batchSignalList);
        if (!batchSignalPropertyList.isEmpty()) {
            signalPropertyService.saveBatch(batchSignalPropertyList);
        }
        if (!batchSignalMeaningsList.isEmpty()) {
            signalMeaningsService.saveBatch(batchSignalMeaningsList);
        }
    }

    private List<SignalMeanings> parserSignalMeaningsXml(Integer equipmentTemplateId, Element signalsElement, Integer signalId) {
        String signalMeanings = signalsElement.attributeValue("SignalMeanings");
        if (signalMeanings == null) return new ArrayList<>();
        
        // 简化实现 - 直接解析基本格式
        String[] meaningsArr = signalMeanings.split(";");
        List<SignalMeanings> result = new ArrayList<>();
        for (String meaningStr : meaningsArr) {
            if (meaningStr.trim().isEmpty()) continue;
            String[] parts = meaningStr.split(":");
            if (parts.length >= 2) {
                SignalMeanings meaning = new SignalMeanings();
                meaning.setEquipmentTemplateId(equipmentTemplateId);
                meaning.setSignalId(signalId);
                meaning.setStateValue(Integer.parseInt(parts[0]));
                meaning.setMeanings(parts[1]);
                result.add(meaning);
            }
        }
        return result;
    }

    private List<SignalProperty> parserSignalPropertyXml(Integer equipmentTemplateId, Element element, Integer signalId) {
        String attribute = element.attributeValue("SignalProperty");
        if (attribute == null || attribute.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        List<Integer> signalPropertyIdList = StrSplitUtil.splitToIntList(attribute);
        List<SignalProperty> signalPropertyList = new ArrayList<>(signalPropertyIdList.size());

        for (Integer propertyId : signalPropertyIdList) {
            SignalProperty signalProperty = new SignalProperty();
            signalProperty.setEquipmentTemplateId(equipmentTemplateId);
            signalProperty.setSignalId(signalId);
            signalProperty.setSignalPropertyId(propertyId);
            signalPropertyList.add(signalProperty);
        }

        return signalPropertyList;
    }

    private Signal parserSignalXml(Integer equipmentTemplateId, Element element, AtomicInteger atomicSignalId) {
        Signal signal = new Signal();
        signal.setEquipmentTemplateId(equipmentTemplateId);
        signal.setSignalId(getAttributeAsInteger(element, "SignalId"));
        signal.setSignalName(element.attributeValue("SignalName"));
        signal.setSignalCategory(getAttributeAsInteger(element, "SignalCategory"));
        signal.setSignalType(getAttributeAsInteger(element, "SignalType"));
        signal.setChannelNo(getAttributeAsInteger(element, "ChannelNo"));
        signal.setChannelType(getAttributeAsInteger(element, "ChannelType"));
        signal.setExpression(element.attributeValue("Expression"));
        signal.setDataType(getAttributeAsInteger(element, "DataType"));
        signal.setShowPrecision(element.attributeValue("ShowPrecision"));
        signal.setUnit(element.attributeValue("Unit"));
        signal.setStoreInterval(getAttributeAsDouble(element, "StoreInterval"));
        signal.setAbsValueThreshold(getAttributeAsDouble(element, "AbsValueThreshold"));
        signal.setPercentThreshold(getAttributeAsDouble(element, "PercentThreshold"));
        signal.setStaticsPeriod(getAttributeAsInteger(element, "StaticsPeriod"));
        signal.setEnable(getAttributeAsBoolean(element, "Enable"));
        signal.setVisible(getAttributeAsBoolean(element, "Visible"));
        String description = element.attributeValue("Discription");
        if (description != null) {
            if (description.contains("bytedancebid=")) {
                description = description.replace("bytedancebid=", "");
            }
            signal.setDescription(description);
        } else {
            // 处理 description 为 null 的情况
            // 可以设置一个默认值，或者记录日志，或者其他适当的处理
            signal.setDescription(""); // 例如，设置为空字符串
        }
        // 如果基类id等于0，则赋值为null,因为模板的错误，需要上层来补
        Long baseTypeId = getAttributeAsLong(element, "BaseTypeId", 0L);
        signal.setBaseTypeId(Objects.equals(baseTypeId, 0L) ? null : baseTypeId);

        signal.setChargeStoreInterVal(getAttributeAsDouble(element, "ChargeStoreInterVal"));
        signal.setChargeAbsValue(getAttributeAsDouble(element, "ChargeAbsValue"));
        signal.setDisplayIndex(getAttributeAsInteger(element, "DisplayIndex"));
        signal.setMdbSignalId(getAttributeAsInteger(element, "MDBSignalId"));
        signal.setModuleNo(getAttributeAsInteger(element, "ModuleNo"));

        // 需要自己生成信号id
        if (Objects.isNull(signal.getSignalId()) || Objects.equals(signal.getSignalId(), SignalConstant.GENERATE_SIGNAL_ID_FLAG)) {
            if (atomicSignalId.get() == -1) {
                atomicSignalId.set(signalService.findMaxSignalIdByEquipmentTemplateId(equipmentTemplateId));
                signal.setSignalId(atomicSignalId.get());
            } else {
                signal.setSignalId(atomicSignalId.incrementAndGet());
            }
        }

        return signal;
    }
}
