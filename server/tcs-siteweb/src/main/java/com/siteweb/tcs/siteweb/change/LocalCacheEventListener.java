package com.siteweb.tcs.siteweb.change;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * 本地缓存事件监听器
 *
 * <AUTHOR>
 * Creation Date: 2024/6/3
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class LocalCacheEventListener {
    private final ChangeMessageScheduler messageScheduler;

    /**
     * 注册监听实现方法
     */
    @EventListener
    @SneakyThrows
    public void handleLocalCacheEvent(LocalCacheEvent localCacheEvent) {
        log.debug("LocalCacheEvent => Topic: {}", localCacheEvent.getTopic());
        if (!StringUtils.hasText(localCacheEvent.getTopic())) {
            return;
        }
        String[] fragments = localCacheEvent.getTopic().split("/");
        ChangeRecord changeRecord = null;
        if (fragments.length >= 5 && fragments[0].equals("gateway")) {
            ChangeRecord cr = new ChangeRecord();
            cr.setChannel(fragments[1]);
            cr.setProduct(fragments[2]);
            cr.setDataSource(fragments[3]);
            cr.setOperator(fragments[4]);
            if (fragments.length > 5) {
                cr.setPrimaryKey(fragments[5]);
            }
            cr.setMessage(localCacheEvent.getMqttMessage());
            changeRecord = cr;
        }
        if (Objects.isNull(changeRecord)) {
            return;
        }
        // 更新缓存
        messageScheduler.dispatchEvent(changeRecord);
    }
} 