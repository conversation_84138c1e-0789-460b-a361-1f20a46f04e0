package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.dto.EquipmentCategoryMapDTO;
import com.siteweb.tcs.siteweb.entity.CategoryIdMap;

import java.util.List;
import java.util.Map;

/**
 * Category ID Map Service Interface
 */
public interface ICategoryIdMapService extends IService<CategoryIdMap> {

    /**
     * 电信业务分类从原始分类转换
     * @return 是否转换成功
     */
    boolean dianXinBusinessCategoryFromOriginCategory();

    /**
     * 根据原始设备类别获取业务类别
     *
     * @param originCategoryKey 原始设备类别key
     * @return 业务类别列表
     */
    Map<Integer, String> findBusinessCategoryFromOriginCategory(Integer originCategoryKey);

    EquipmentCategoryMapDTO findEquipmentCategoryMapDTO(Integer originCategory);
}
