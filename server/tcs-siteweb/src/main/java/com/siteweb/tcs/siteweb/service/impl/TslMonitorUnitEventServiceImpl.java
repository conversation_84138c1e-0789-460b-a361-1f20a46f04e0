package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.dto.MonitorUnitEventDTO;
import com.siteweb.tcs.siteweb.entity.Equipment;
import com.siteweb.tcs.siteweb.entity.Event;
import com.siteweb.tcs.siteweb.entity.TslMonitorUnitEvent;
import com.siteweb.tcs.siteweb.mapper.EquipmentMapper;
import com.siteweb.tcs.siteweb.mapper.EventMapper;
import com.siteweb.tcs.siteweb.mapper.TslMonitorUnitEventMapper;
import com.siteweb.tcs.siteweb.service.ITslMonitorUnitEventService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 监控单元事件服务实现类
 */
@Slf4j
@Service
public class TslMonitorUnitEventServiceImpl extends ServiceImpl<TslMonitorUnitEventMapper, TslMonitorUnitEvent> implements ITslMonitorUnitEventService {

    @Autowired
    private TslMonitorUnitEventMapper tslMonitorUnitEventMapper;

    @Autowired
    private EquipmentMapper equipmentMapper;

    @Autowired
    private EventMapper eventMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByStationId(Integer stationId) {
        if (stationId == null) {
            log.warn("Cannot delete monitor unit events: station ID is null");
            return false;
        }
        
        try {
            int count = (int) count(new LambdaQueryWrapper<TslMonitorUnitEvent>()
                    .eq(TslMonitorUnitEvent::getStationId, stationId));
            
            if (count == 0) {
                log.info("No monitor unit events found for station ID: {}", stationId);
                return true;
            }
            
            boolean result = remove(new LambdaQueryWrapper<TslMonitorUnitEvent>()
                    .eq(TslMonitorUnitEvent::getStationId, stationId));
            
            if (result) {
                log.info("Deleted {} monitor unit events for station ID: {}", count, stationId);
            } else {
                log.warn("Failed to delete monitor unit events for station ID: {}", stationId);
            }
            
            return result;
        } catch (Exception e) {
            log.error("Error deleting monitor unit events for station ID: {}", stationId, e);
            throw e;
        }
    }

    // ==================== 设备管理相关方法实现 ====================

    @Override
    public List<TslMonitorUnitEvent> findByEquipmentIdAndEventId(Integer equipmentId, Integer eventId) {
        try {
            return list(new LambdaQueryWrapper<TslMonitorUnitEvent>()
                    .eq(TslMonitorUnitEvent::getEquipmentId, equipmentId)
                    .eq(TslMonitorUnitEvent::getEventId, eventId));
        } catch (Exception e) {
            log.error("Failed to find monitor unit events by equipment ID {} and event ID {}", equipmentId, eventId, e);
            return List.of();
        }
    }
    private MonitorUnitEventDTO initMonitorUnitEventDTO(Integer equipmentId, Integer eventId) {
        Equipment equipment = equipmentMapper.selectById(equipmentId);
        MonitorUnitEventDTO monitorUnitEventDTO = new MonitorUnitEventDTO();
        monitorUnitEventDTO.setStationId(equipment.getStationId());
        monitorUnitEventDTO.setMonitorUnitId(equipment.getMonitorUnitId());
        monitorUnitEventDTO.setEquipmentId(equipmentId);
        monitorUnitEventDTO.setEquipmentName(equipment.getEquipmentName());
        Optional<Event> eventOptional = Optional.ofNullable(eventByEquipmentTemplateId(equipment.getEquipmentTemplateId(), eventId));
        monitorUnitEventDTO.setEventId(eventOptional.map(Event::getEventId).orElse(null));
        monitorUnitEventDTO.setEventName(eventOptional.map(Event::getEventName).orElse(""));
        return monitorUnitEventDTO;
    }
    private Event eventByEquipmentTemplateId(Integer equipmentTemplateId, Integer eventId) {
        return eventMapper.selectOne(Wrappers.lambdaQuery(Event.class)
                .select(Event::getEventId, Event::getEventName)
                .eq(Event::getEquipmentTemplateId, equipmentTemplateId)
                .eq(Event::getEventId, eventId));
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createOrUpdate(TslMonitorUnitEvent event) {
        try {
            if (event == null) {
                log.warn("Cannot create or update monitor unit event: event is null");
                return false;
            }

            // 检查是否已存在
            TslMonitorUnitEvent existing = getOne(new LambdaQueryWrapper<TslMonitorUnitEvent>()
                    .eq(TslMonitorUnitEvent::getEquipmentId, event.getEquipmentId())
                    .eq(TslMonitorUnitEvent::getEventId, event.getEventId()));

            boolean result;
            if (existing != null) {
                // 更新
                event.setId(existing.getId());
                result = updateById(event);
                log.info("Updated monitor unit event for equipment {} and event {}",
                        event.getEquipmentId(), event.getEventId());
            } else {
                // 创建
                result = save(event);
                log.info("Created monitor unit event for equipment {} and event {}",
                        event.getEquipmentId(), event.getEventId());
            }

            return result;
        } catch (Exception e) {
            log.error("Error creating or updating monitor unit event", e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByEquipmentIdAndEventId(Integer equipmentId, Integer eventId) {
        try {
            if (equipmentId == null || eventId == null) {
                log.warn("Cannot delete monitor unit event: equipment ID or event ID is null");
                return false;
            }

            boolean result = remove(new LambdaQueryWrapper<TslMonitorUnitEvent>()
                    .eq(TslMonitorUnitEvent::getEquipmentId, equipmentId)
                    .eq(TslMonitorUnitEvent::getEventId, eventId));

            if (result) {
                log.info("Successfully deleted monitor unit event for equipment {} and event {}", equipmentId, eventId);
            } else {
                log.warn("No monitor unit event found to delete for equipment {} and event {}", equipmentId, eventId);
            }

            return result;
        } catch (Exception e) {
            log.error("Error deleting monitor unit event for equipment {} and event {}", equipmentId, eventId, e);
            throw e;
        }
    }
}
