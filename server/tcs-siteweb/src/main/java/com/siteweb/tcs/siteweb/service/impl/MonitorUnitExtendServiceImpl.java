package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.siteweb.tcs.siteweb.entity.MonitorUnitExtend;
import com.siteweb.tcs.siteweb.mapper.MonitorUnitExtendMapper;
import com.siteweb.tcs.siteweb.service.IMonitorUnitExtendService;
import com.siteweb.tcs.siteweb.vo.MonitorUnitPasswordVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 监控单元扩展信息服务实现类
 * Description: 监控单元扩展信息相关业务逻辑实现
 * Author: <EMAIL>
 * Creation Date: 2024/6/5
 */
@Service
public class MonitorUnitExtendServiceImpl implements IMonitorUnitExtendService {

    @Autowired
    private MonitorUnitExtendMapper monitorUnitExtendMapper;

    @Override
    public int insert(MonitorUnitExtend monitorUnitExtend) {
        return monitorUnitExtendMapper.insert(monitorUnitExtend);
    }

    @Override
    public int update(MonitorUnitExtend monitorUnitExtend) {
        return monitorUnitExtendMapper.updateById(monitorUnitExtend);
    }

    @Override
    public int delete(int monitorUnitId) {
        return monitorUnitExtendMapper.deleteById(monitorUnitId);
    }

    @Override
    public List<MonitorUnitPasswordVO> findMonitorUnitPassword(List<Integer> monitorUnitIds) {
        List<MonitorUnitExtend> monitorUnitExtends = monitorUnitExtendMapper.selectList(
                new LambdaQueryWrapper<>(MonitorUnitExtend.class)
                        .in(MonitorUnitExtend::getMonitorUnitId, monitorUnitIds));
        
        List<MonitorUnitPasswordVO> monitorUnitPasswordVOS = new ArrayList<>();
        for (MonitorUnitExtend monitorUnitExtend : monitorUnitExtends) {
            MonitorUnitPasswordVO monitorUnitPasswordVO = new MonitorUnitPasswordVO();
            monitorUnitPasswordVO.setMonitorUnitId(monitorUnitExtend.getMonitorUnitId());
            monitorUnitPasswordVO.setHasPassword(monitorUnitExtend.getExtendFiled1() != null);
            monitorUnitPasswordVOS.add(monitorUnitPasswordVO);
        }
        return monitorUnitPasswordVOS;
    }

    @Override
    public MonitorUnitExtend findByMonitorUnitId(int monitorUnitId) {
        return monitorUnitExtendMapper.selectOne(
                new LambdaQueryWrapper<>(MonitorUnitExtend.class)
                        .eq(MonitorUnitExtend::getMonitorUnitId, monitorUnitId));
    }
}
