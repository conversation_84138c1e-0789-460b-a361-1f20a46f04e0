package com.siteweb.tcs.siteweb.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.dto.AccountDTO;
import com.siteweb.tcs.siteweb.entity.Account;
import com.siteweb.tcs.siteweb.mapper.AccountMapper;
import com.siteweb.tcs.siteweb.service.IAccountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 账户服务实现类
 */
@Service("SiteWebAccountService")
@Slf4j
public class AccountServiceImpl extends ServiceImpl<AccountMapper, Account> implements IAccountService {

    @Qualifier("SiteWebAccountMapper")
    @Autowired
    private AccountMapper accountMapper;

    @Override
    public List<AccountDTO> findByLogonId(String logonId) {
        List<AccountDTO> result = new ArrayList<>();
        List<Account> accountList = accountMapper.selectList(new QueryWrapper<Account>().eq("LogonId", logonId));
        for (Account account : accountList) {
            AccountDTO accountDTO = new AccountDTO();
            BeanUtils.copyProperties(account, accountDTO);
            // 注意：这里暂不处理roleIds，如果需要可以后续添加
            result.add(accountDTO);
        }
        return result;
    }

    @Override
    public List<AccountDTO> findByMobile(String mobile) {
        List<AccountDTO> result = new ArrayList<>();
        List<Account> accountList = accountMapper.findByMobile(mobile);
        for (Account account : accountList) {
            AccountDTO accountDTO = new AccountDTO();
            BeanUtils.copyProperties(account, accountDTO);
            result.add(accountDTO);
        }
        return result;
    }

    @Override
    public AccountDTO findByUserId(Integer userId) {
        Account account = accountMapper.selectById(userId);
        if (null == account) {
            return null;
        }
        AccountDTO accountDTO = new AccountDTO();
        BeanUtils.copyProperties(account, accountDTO);
        return accountDTO;
    }

    @Override
    public List<AccountDTO> findAll() {
        List<Account> accounts = accountMapper.selectList(null);
        if (CollUtil.isEmpty(accounts)) {
            return new ArrayList<>();
        }
        return BeanUtil.copyToList(accounts, AccountDTO.class);
    }

    @Override
    public void updateCenterIdForNegativeUserId(int centerId) {
        // 迁移到 Service 层：使用 LambdaUpdateWrapper 替代 XML 中的简单更新
        LambdaUpdateWrapper<Account> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(Account::getCenterId, centerId)
                     .lt(Account::getUserId, 0);
        this.update(updateWrapper);
    }
} 