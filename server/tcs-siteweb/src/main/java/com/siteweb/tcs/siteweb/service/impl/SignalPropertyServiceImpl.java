package com.siteweb.tcs.siteweb.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.entity.SignalProperty;
import com.siteweb.tcs.siteweb.mapper.SignalPropertyMapper;
import com.siteweb.tcs.siteweb.service.ISignalPropertyService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Signal Property Service Implementation
 */
@Service
public class SignalPropertyServiceImpl extends ServiceImpl<SignalPropertyMapper, SignalProperty> implements ISignalPropertyService {

    @Override
    public void batchCreateSignalProperty(List<SignalProperty> signalPropertyList) {
        if (CollUtil.isEmpty(signalPropertyList)) {
            return;
        }
        saveBatch(signalPropertyList);
    }

    @Override
    public void createSignalProperty(SignalProperty signalProperty) {
        save(signalProperty);
    }

    @Override
    public List<SignalProperty> findByEquipmentTemplateIdAndSignalId(Integer equipmentTemplateId, Integer signalId) {
        return list(new QueryWrapper<SignalProperty>()
                .eq("EquipmentTemplateId", equipmentTemplateId)
                .eq("SignalId", signalId));
    }

    @Override
    public void deleteByEquipmentTemplateId(Integer equipmentTemplateId) {
        remove(new QueryWrapper<SignalProperty>()
                .eq("EquipmentTemplateId", equipmentTemplateId));
    }

    @Override
    public void copySignalProperty(Integer originEquipmentTemplateId, Integer destEquipmentTemplateId) {
        List<SignalProperty> signalPropertyList = findByEquipmentTemplateId(originEquipmentTemplateId);
        signalPropertyList.forEach(property -> {
            property.setId(null); // 清空ID以便插入新记录
            property.setEquipmentTemplateId(destEquipmentTemplateId);
        });
        batchCreateSignalProperty(signalPropertyList);
    }

    @Override
    public void updateSignalProperty(Integer equipmentTemplateId, Integer signalId, List<SignalProperty> signalPropertyList) {
        deleteByEquipmentIdAndSignalId(equipmentTemplateId, signalId);
        if (CollUtil.isEmpty(signalPropertyList)) {
            return;
        }
        batchCreateSignalProperty(signalPropertyList);
    }

    @Override
    public boolean batchsaveLianTongSignalPropertys() {
        // 批量保存联通信号属性的实现
        // 这里需要根据具体业务需求实现
        return true;
    }

    private void deleteByEquipmentIdAndSignalId(Integer equipmentTemplateId, Integer signalId) {
        remove(new QueryWrapper<SignalProperty>()
                .eq("EquipmentTemplateId", equipmentTemplateId)
                .eq("SignalId", signalId));
    }

    private List<SignalProperty> findByEquipmentTemplateId(Integer equipmentTemplateId) {
        return list(new QueryWrapper<SignalProperty>()
                .eq("EquipmentTemplateId", equipmentTemplateId));
    }

    @Override
    public void deleteByEquipmentTemplateIdAndSignalId(Integer equipmentTemplateId, Integer signalId) {
        deleteByEquipmentIdAndSignalId(equipmentTemplateId, signalId);
    }
}
