package com.siteweb.tcs.siteweb.dto.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 事件Excel
 *
 * <AUTHOR>
 * @date 2025/05/06
 */
@Data
@ExcelIgnoreUnannotated
public class EventExcel {
    @ExcelProperty("设备id")
    private Integer equipmentId;
    @ExcelProperty("设备名称")
    private String equipmentName;
    @ExcelProperty("事件id")
    private Integer eventId;
    @ExcelProperty("事件名称")
    private String eventName;
    @ExcelProperty("事件条件主键id")
    private Integer id;
    @ExcelProperty("事件条件id")
    private Integer eventConditionId;
    @ExcelProperty("设备模板id")
    private Integer equipmentTemplateId;
    @ExcelProperty("开始操作符")
    private String startOperation;
    @ExcelProperty("开始比较值")
    private Integer startCompareValue;
    @ExcelProperty("开始延迟")
    private Integer startDelay;
    @ExcelProperty("结束操作符")
    private String endOperation;
    @ExcelProperty("结束比较值")
    private Integer endCompareValue;
    @ExcelProperty("结束延迟")
    private Integer endDelay;
    @ExcelProperty("频度事件持续事件")
    private Integer frequency;
    @ExcelProperty("频度事件阈值")
    private Integer frequencyThreshold;
    @ExcelProperty("告警含义")
    private String meanings;
    @ExcelProperty("告警基类")
    private String baseTypeId;
    @ExcelProperty("告警等级")
    private String eventSeverity;
}
