package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.dto.*;
import com.siteweb.tcs.siteweb.dto.excel.ControlExcel;
import com.siteweb.tcs.siteweb.entity.Control;
import com.siteweb.tcs.siteweb.vo.ControlVO;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Control Service Interface
 */
public interface IControlService extends IService<Control> {

    void createControl(ControlConfigItem controlConfigItem);

    void updateControlByControl(ControlConfigItem controlConfigItem);

    void deleteControl(int equipmentTemplateId, int controlId);

    void batchDeleteControl(int equipmentTemplateId, List<Integer> controlIds);

    List<ControlConfigItem> findItemByEquipmentTemplateId(Integer equipmentTemplateId);

    ControlConfigItem getControlInfo(Integer equipmentTemplateId, Integer controlId);

    Integer findMaxControlIdByEquipmentTemplateId(Integer equipmentTemplateId);

    int findMaxDisplayIndexByEquipmentTemplateId(Integer equipmentTemplateId);

    /**
     * 批量保存联通控制
     *
     * @return 是否保存成功
     */
    boolean batchsaveLianTongControls();

    /**
     * Find base type IDs not in control base dictionary for equipment template
     *
     * @param equipmentTemplateId Equipment template ID
     * @return List of base type IDs
     */
    List<Long> findBaseTypeIdsNotInControlBaseDicForEquipmentTemplate(Integer equipmentTemplateId);

    /**
     * 删除控制命令
     *
     * @param equipmentTemplateId 设备模板ID
     * @param controlId 控制ID
     * @return 删除数量
     */
    int deleteControl(Integer equipmentTemplateId, Integer controlId);

    /**
     * 应用控制标准化
     *
     * @param standardId 标准ID
     * @param equipmentTemplateIds 设备模板ID列表
     * @return 应用的数量
     */
    Long applyStandard(Integer standardId, List<Integer> equipmentTemplateIds);

    // ==================== 设备管理相关方法 ====================

    /**
     * 查询控制点（按tcs-config原始实现）
     *
     * @param equipmentTemplateId 设备模板ID
     * @return 控制配置点列表
     */
    List<ControlConfigPointDTO> findControlPoints(Integer equipmentTemplateId);

    /**
     * 字段复制
     *
     * @param commandFieldCopyList 命令字段复制列表
     * @return 是否复制成功
     */
    boolean fieldCopy(List<CommandFieldCopyDTO> commandFieldCopyList);

    /**
     * 处理相似控制（按tcs-config原始实现）
     *
     * @param similarDataDTO 相似数据DTO
     */
    void disposeSimilarControl(SimilarDataDTO similarDataDTO);

    List<ControlExcel> findExcelDtoByEquipmentTemplateId(Integer equipmentTemplateId);

    List<Control> findByEquipmentTemplateId(Integer equipmentTemplateId);

    void copyControl(Integer originEquipmentTemplateId, Integer destEquipmentTemplateId);

    List<ControlVO> findVoByEquipmentTemplateId(Integer equipmentTemplateId);


    void updateControlDescriptions(Integer childTemplateId, List<Control> parentControls);

    @Transactional(rollbackFor = Exception.class)
    void deleteByEquipmentTemplateId(Integer equipmentTemplateId);
}
