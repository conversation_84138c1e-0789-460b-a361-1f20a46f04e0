package com.siteweb.tcs.siteweb.vo;

import com.siteweb.tcs.siteweb.entity.EquipmentTemplate;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class EquipmentTemplateBaseClassVO {
    private Integer equipmentTemplateId;
    /**
     * 模板名
     */
    private String equipmentTemplateName;
    /**
     * 父模板id
     */
    private Integer parentTemplateId;
    /**
     * 父模板名
     */
    private String parentTemplateName;
    /**
     * 协议编码
     */
    private String protocolCode;
    /**
     * 设备种类
     */
    private Integer equipmentCategory;
    /**
     * 设备种类名称
     */
    private String equipmentCategoryName;
    /**
     * 基类设备类型
     */
    private Integer equipmentBaseType;
    /**
     * 基类设备类型名称
     */
    private String equipmentBaseTypeName;
    /**
     * 信号处理进度
     */
    private Double signalProgress;
    /**
     * 事件处理进度
     */
    private Double eventProgress;
    /**
     * 控制处理进度
     */
    private Double controlProgress;

    public static EquipmentTemplateBaseClassVO toEquipmentTemplateBaseClassVO(EquipmentTemplate eq) {
        EquipmentTemplateBaseClassVO equipmentTemplateBaseClassVO = new EquipmentTemplateBaseClassVO();
        equipmentTemplateBaseClassVO.setEquipmentTemplateId(eq.getEquipmentTemplateId());
        equipmentTemplateBaseClassVO.setEquipmentTemplateName(eq.getEquipmentTemplateName());
        equipmentTemplateBaseClassVO.setParentTemplateId(eq.getParentTemplateId());
        equipmentTemplateBaseClassVO.setProtocolCode(eq.getProtocolCode());
        equipmentTemplateBaseClassVO.setEquipmentCategory(eq.getEquipmentCategory());
        equipmentTemplateBaseClassVO.setEquipmentBaseType(eq.getEquipmentBaseType());
        return equipmentTemplateBaseClassVO;
    }
}
