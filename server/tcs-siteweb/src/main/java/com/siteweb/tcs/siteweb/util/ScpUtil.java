package com.siteweb.tcs.siteweb.util;

import com.jcraft.jsch.ChannelExec;
import com.jcraft.jsch.Session;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * 纯SCP协议工具类
 * 不依赖SFTP，最小化内存使用
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Slf4j
@UtilityClass
public class ScpUtil {

    /**
     * 通过纯SCP协议上传文件
     *
     * @param session SSH会话
     * @param localFile 本地文件
     * @param remoteFilePath 远程文件路径
     * @return 上传是否成功
     */
    public static boolean uploadFile(Session session, File localFile, String remoteFilePath) {
        ChannelExec channelExec = null;
        FileInputStream fis = null;
        OutputStream out = null;
        InputStream in = null;

        try {
            // 确保远程目录存在
            String remoteDir = getDirectoryFromPath(remoteFilePath);
            if (remoteDir != null && !remoteDir.isEmpty()) {
                ensureRemoteDirectoryExists(session, remoteDir);
            }

            // 创建SCP命令 - 使用引号包围路径以处理特殊字符和中文
            String command = String.format("scp -t '%s'", remoteFilePath);
            channelExec = (ChannelExec) session.openChannel("exec");
            channelExec.setCommand(command);

            // 设置环境变量支持UTF-8编码
            channelExec.setEnv("LC_ALL", "en_US.UTF-8");

            // 获取输入输出流
            out = channelExec.getOutputStream();
            in = channelExec.getInputStream();

            channelExec.connect();

            // 检查初始响应
            if (checkResponse(in) != 0) {
                log.error("SCP初始化失败: {}", remoteFilePath);
                return false;
            }

            // 发送文件信息 (权限 大小 文件名)
            // 使用原始文件名，让SSH服务器处理编码
            long fileSize = localFile.length();
            String fileName = getFileNameFromPath(remoteFilePath);
            String fileInfo = String.format("C0644 %d %s\n", fileSize, fileName);
            out.write(fileInfo.getBytes("UTF-8"));
            out.flush();

            // 检查文件信息响应
            if (checkResponse(in) != 0) {
                log.error("SCP文件信息发送失败: {}", remoteFilePath);
                return false;
            }

            // 发送文件内容
            fis = new FileInputStream(localFile);
            byte[] buffer = new byte[8192]; // 8KB缓冲区，平衡内存和性能
            int bytesRead;
            long totalBytes = 0;

            while ((bytesRead = fis.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
                totalBytes += bytesRead;
            }

            // 发送结束标志
            out.write(0);
            out.flush();

            // 检查传输完成响应
            if (checkResponse(in) != 0) {
                log.error("SCP文件传输失败: {}", remoteFilePath);
                return false;
            }

            log.info("SCP上传成功: {} -> {} ({}字节)", localFile.getName(), remoteFilePath, totalBytes);
            return true;

        } catch (Exception e) {
            log.error("SCP上传异常: {} -> {}", localFile.getName(), remoteFilePath, e);
            return false;
        } finally {
            // 安全关闭所有资源
            closeQuietly(fis);
            closeQuietly(out);
            closeQuietly(in);
            if (channelExec != null) {
                channelExec.disconnect();
            }
        }
    }

    /**
     * 批量上传文件到同一目录
     * 复用SSH连接，提高效率
     *
     * @param session SSH会话
     * @param localFiles 本地文件列表
     * @param remoteDirectory 远程目录
     * @return 全部上传是否成功
     */
    public static boolean uploadFiles(Session session, File[] localFiles, String remoteDirectory) {
        if (localFiles == null || localFiles.length == 0) {
            return true;
        }

        boolean allSuccess = true;
        for (File localFile : localFiles) {
            if (localFile.exists() && localFile.isFile()) {
                String remoteFilePath = remoteDirectory + "/" + localFile.getName();
                boolean success = uploadFile(session, localFile, remoteFilePath);
                if (!success) {
                    allSuccess = false;
                    log.error("批量上传中文件失败: {}", localFile.getName());
                }
            } else {
                log.warn("跳过无效文件: {}", localFile.getAbsolutePath());
                allSuccess = false;
            }
        }

        return allSuccess;
    }

    /**
     * 通过纯SCP协议下载文件
     *
     * @param session SSH会话
     * @param remoteFilePath 远程文件路径
     * @param localFile 本地文件
     * @return 下载是否成功
     */
    public static boolean downloadFile(Session session, String remoteFilePath, File localFile) {
        ChannelExec channelExec = null;
        java.io.FileOutputStream fos = null;
        InputStream in = null;
        OutputStream out = null;

        try {
            // 确保本地目录存在
            if (localFile.getParentFile() != null && !localFile.getParentFile().exists()) {
                localFile.getParentFile().mkdirs();
            }

            // 创建SCP下载命令
            String command = String.format("scp -f '%s'", remoteFilePath);
            channelExec = (ChannelExec) session.openChannel("exec");
            channelExec.setCommand(command);

            // 设置环境变量支持UTF-8编码
            channelExec.setEnv("LC_ALL", "en_US.UTF-8");

            // 获取输入输出流
            out = channelExec.getOutputStream();
            in = channelExec.getInputStream();

            channelExec.connect();

            // SCP协议握手
            byte[] buf = new byte[1024];

            // 发送确认
            buf[0] = 0;
            out.write(buf, 0, 1);
            out.flush();

            // 读取文件信息
            while (true) {
                int c = checkDownloadResponse(in);
                if (c != 'C') {
                    if (c == -1) {
                        log.error("SCP下载失败，连接关闭: {}", remoteFilePath);
                        return false;
                    }
                    break;
                }

                // 读取文件模式、大小和名称
                in.read(buf, 0, 5); // 跳过文件模式

                long filesize = 0L;
                while (true) {
                    if (in.read(buf, 0, 1) < 0) {
                        break;
                    }
                    if (buf[0] == ' ') break;
                    filesize = filesize * 10L + (long) (buf[0] - '0');
                }

                // 读取文件名（跳过，我们使用指定的本地文件名）
                for (int i = 0; ; i++) {
                    in.read(buf, i, 1);
                    if (buf[i] == (byte) 0x0a) {
                        break;
                    }
                }

                // 发送确认
                buf[0] = 0;
                out.write(buf, 0, 1);
                out.flush();

                // 接收文件内容
                fos = new java.io.FileOutputStream(localFile);
                long totalReceived = 0;
                while (totalReceived < filesize) {
                    int bufSize = (int) Math.min(buf.length, filesize - totalReceived);
                    int bytesRead = in.read(buf, 0, bufSize);
                    if (bytesRead < 0) {
                        log.error("SCP下载中断: {}", remoteFilePath);
                        return false;
                    }
                    fos.write(buf, 0, bytesRead);
                    totalReceived += bytesRead;
                }
                fos.close();
                fos = null;

                // 发送确认
                buf[0] = 0;
                out.write(buf, 0, 1);
                out.flush();

                // 检查最终确认
                if (checkDownloadResponse(in) != 0) {
                    log.error("SCP下载完成确认失败: {}", remoteFilePath);
                    return false;
                }

                log.info("SCP下载成功: {} -> {} ({}字节)", remoteFilePath, localFile.getAbsolutePath(), totalReceived);
                return true;
            }

            log.error("SCP下载失败，未找到文件: {}", remoteFilePath);
            return false;

        } catch (Exception e) {
            log.error("SCP下载异常: {} -> {}", remoteFilePath, localFile.getAbsolutePath(), e);
            return false;
        } finally {
            closeQuietly(fos);
            closeQuietly(out);
            closeQuietly(in);
            if (channelExec != null) {
                channelExec.disconnect();
            }
        }
    }

    /**
     * SCP下载协议响应检查
     */
    private static int checkDownloadResponse(InputStream in) throws Exception {
        int b = in.read();
        if (b == 0) return b;
        if (b == -1) return b;

        if (b == 1 || b == 2) {
            StringBuilder sb = new StringBuilder();
            int c;
            do {
                c = in.read();
                sb.append((char) c);
            } while (c != '\n');

            if (b == 1) { // error
                log.error("SCP下载错误: {}", sb.toString());
            }
            if (b == 2) { // fatal error
                log.error("SCP下载致命错误: {}", sb.toString());
            }
        }
        return b;
    }

    /**
     * 检查SCP协议响应
     *
     * @param in 输入流
     * @return 响应码 (0=成功, 1=警告, 2=错误, -1=连接关闭)
     */
    private static int checkResponse(InputStream in) throws Exception {
        int response = in.read();
        
        if (response == 0) {
            return 0; // 成功
        }
        
        if (response == 1 || response == 2) {
            // 读取错误消息
            StringBuilder errorMsg = new StringBuilder();
            int c;
            while ((c = in.read()) != '\n' && c != -1) {
                errorMsg.append((char) c);
            }
            
            String level = (response == 1) ? "警告" : "错误";
            log.error("SCP{}: {}", level, errorMsg.toString());
            return response;
        }
        
        if (response == -1) {
            log.error("SCP连接意外关闭");
            return -1;
        }
        
        log.warn("SCP未知响应码: {}", response);
        return response;
    }

    /**
     * 安全关闭资源
     */
    private static void closeQuietly(AutoCloseable resource) {
        if (resource != null) {
            try {
                resource.close();
            } catch (Exception e) {
                // 静默忽略关闭异常
            }
        }
    }

    /**
     * 验证远程文件是否存在
     * 
     * @param session SSH会话
     * @param remoteFilePath 远程文件路径
     * @return 文件是否存在
     */
    public static boolean fileExists(Session session, String remoteFilePath) {
        ChannelExec channelExec = null;
        try {
            channelExec = (ChannelExec) session.openChannel("exec");
            String command = String.format("test -f %s && echo 'EXISTS' || echo 'NOT_EXISTS'", remoteFilePath);
            channelExec.setCommand(command);
            
            InputStream in = channelExec.getInputStream();
            channelExec.connect();
            
            // 读取命令输出
            StringBuilder result = new StringBuilder();
            byte[] tmp = new byte[1024];
            while (true) {
                while (in.available() > 0) {
                    int i = in.read(tmp, 0, 1024);
                    if (i < 0) {
                        break;
                    }
                    result.append(new String(tmp, 0, i));
                }
                if (channelExec.isClosed()) {
                    if (in.available() > 0) {
                        continue;
                    }
                    break;
                }
                Thread.sleep(100);
            }
            
            String output = result.toString().trim();
            return "EXISTS".equals(output);
            
        } catch (Exception e) {
            log.error("检查远程文件存在性失败: {}", remoteFilePath, e);
            return false;
        } finally {
            if (channelExec != null) {
                channelExec.disconnect();
            }
        }
    }

    /**
     * 获取远程文件大小
     * 
     * @param session SSH会话
     * @param remoteFilePath 远程文件路径
     * @return 文件大小（字节），-1表示获取失败
     */
    public static long getFileSize(Session session, String remoteFilePath) {
        ChannelExec channelExec = null;
        try {
            channelExec = (ChannelExec) session.openChannel("exec");
            String command = String.format("stat -c%%s %s 2>/dev/null || echo -1", remoteFilePath);
            channelExec.setCommand(command);
            
            InputStream in = channelExec.getInputStream();
            channelExec.connect();
            
            // 读取文件大小
            StringBuilder result = new StringBuilder();
            byte[] tmp = new byte[1024];
            while (true) {
                while (in.available() > 0) {
                    int i = in.read(tmp, 0, 1024);
                    if (i < 0) {
                        break;
                    }
                    result.append(new String(tmp, 0, i));
                }
                if (channelExec.isClosed()) {
                    if (in.available() > 0) {
                        continue;
                    }
                    break;
                }
                Thread.sleep(100);
            }
            
            String output = result.toString().trim();
            return Long.parseLong(output);
            
        } catch (Exception e) {
            log.error("获取远程文件大小失败: {}", remoteFilePath, e);
            return -1;
        } finally {
            if (channelExec != null) {
                channelExec.disconnect();
            }
        }
    }

    /**
     * 从文件路径中提取目录部分
     */
    private static String getDirectoryFromPath(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return null;
        }

        String normalizedPath = filePath.replace("\\", "/");
        int lastSeparator = normalizedPath.lastIndexOf('/');

        if (lastSeparator > 0) {
            return normalizedPath.substring(0, lastSeparator);
        }

        return null;
    }

    /**
     * 从文件路径中提取文件名部分
     */
    private static String getFileNameFromPath(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return "";
        }

        String normalizedPath = filePath.replace("\\", "/");
        int lastSeparator = normalizedPath.lastIndexOf('/');

        if (lastSeparator >= 0 && lastSeparator < normalizedPath.length() - 1) {
            return normalizedPath.substring(lastSeparator + 1);
        }

        return normalizedPath;
    }

    /**
     * 确保远程目录存在
     */
    private static void ensureRemoteDirectoryExists(Session session, String remotePath) {
        try {
            // 使用SSH命令创建目录
            ChannelExec channelExec = (ChannelExec) session.openChannel("exec");
            String command = String.format("mkdir -p '%s'", remotePath);
            channelExec.setCommand(command);

            // 设置环境变量支持UTF-8编码
            channelExec.setEnv("LC_ALL", "en_US.UTF-8");

            channelExec.connect();

            // 等待命令执行完成
            while (!channelExec.isClosed()) {
                Thread.sleep(100);
            }

            int exitStatus = channelExec.getExitStatus();
            if (exitStatus == 0) {
                log.debug("SCP创建远程目录成功: {}", remotePath);
            } else {
                log.warn("SCP创建远程目录可能失败: {} (退出码: {})", remotePath, exitStatus);
            }

            channelExec.disconnect();

        } catch (Exception e) {
            log.warn("SCP创建远程目录异常: {} - {}", remotePath, e.getMessage());
        }
    }

    /**
     * 为SCP协议编码文件名
     * 处理中文文件名和特殊字符
     *
     * 注意：关于中文文件名乱码问题的说明
     * 当远程采集器的locale设置为POSIX时，中文文件名在文件系统中可能显示为乱码，
     * 但这不影响文件内容的完整性。SCP协议传输的是二进制文件内容，
     * 文件名编码问题只影响显示，不影响文件本身。
     * 因此MD5验证仍然能够通过，证明文件传输是成功的。
     */
    private static String encodeFileNameForScp(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return fileName;
        }

        // 检查是否包含中文或特殊字符
        boolean hasNonAscii = !fileName.matches("^[a-zA-Z0-9._-]+$");

        if (!hasNonAscii) {
            // 如果只包含ASCII安全字符，直接使用
            return fileName;
        }

        // 对于包含中文或特殊字符的文件名，使用引号包围并转义特殊字符
        // 现代Unix系统通常支持UTF-8编码，直接传输中文文件名
        // 即使在POSIX locale下显示为乱码，文件内容和MD5验证不受影响
        String escapedFileName = fileName
                .replace("\\", "\\\\")  // 转义反斜杠
                .replace("\"", "\\\"")  // 转义双引号
                .replace("'", "\\'")    // 转义单引号
                .replace("`", "\\`")    // 转义反引号
                .replace("$", "\\$")    // 转义美元符号
                .replace(" ", "\\ ");   // 转义空格

        // 使用双引号包围文件名
        return "\"" + escapedFileName + "\"";
    }

    /**
     * 解释中文文件名乱码现象
     *
     * @param originalFileName 原始文件名
     * @param remoteMd5 远程文件MD5
     * @param localMd5 本地文件MD5
     * @return 解释信息
     */
    public static String explainChineseFileNameIssue(String originalFileName, String remoteMd5, String localMd5) {
        boolean hasChineseChars = originalFileName.matches(".*[\\u4e00-\\u9fa5].*");
        boolean md5Match = remoteMd5 != null && remoteMd5.equals(localMd5);

        if (hasChineseChars && md5Match) {
            return String.format(
                "文件 '%s' 包含中文字符，在POSIX locale环境下可能显示为乱码，" +
                "但文件内容完整无损（MD5验证通过：%s），传输成功。",
                originalFileName, localMd5
            );
        }

        return null;
    }
}
