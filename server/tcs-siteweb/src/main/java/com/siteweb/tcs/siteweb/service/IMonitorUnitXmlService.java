package com.siteweb.tcs.siteweb.service;

import com.siteweb.tcs.siteweb.dto.MonitorUnitDTO;
import com.siteweb.tcs.siteweb.entity.WorkStation;

/**
 * @Description:
 */
public interface IMonitorUnitXmlService {
    /**
     * 生成监控单元配置文件
     *
     * @param tslMonitorUnits 监控单元
     * @param path
     */
    void createMonitorUnitConfigXML(MonitorUnitDTO tslMonitorUnits, String path);

    /**
     * 通过websocket返回进度信息
     *
     * @param msg
     * @param uniqueId
     * @return
     */
    void createMonitorUnitConfigXMLAsync(String msg, String uniqueId);

    void createMonitorUnitConfigXML(WorkStation workStation, String path);

    boolean sendMonitorUnitConfigXML(MonitorUnitDTO tslMonitorUnit, String uniqueId, String protocol, Integer ftpPort, String ftpUsername, String ftpPassword);

    void sendMonitorUnitConfigXMLAsync(String monitorUnitIds, Integer port, String username, String password, String protocol, String uniqueId);


}
