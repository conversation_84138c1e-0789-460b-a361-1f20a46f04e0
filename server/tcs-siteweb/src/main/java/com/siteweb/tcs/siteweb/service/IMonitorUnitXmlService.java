package com.siteweb.tcs.siteweb.service;

import com.siteweb.tcs.siteweb.dto.MonitorUnitDTO;
import com.siteweb.tcs.siteweb.entity.WorkStation;

import java.io.File;
import java.util.List;

/**
 * @Description:
 */
public interface IMonitorUnitXmlService {
    /**
     * 生成监控单元配置文件
     *
     * @param tslMonitorUnits 监控单元
     * @param path
     */
    void createMonitorUnitConfigXML(MonitorUnitDTO tslMonitorUnits, String path);

    /**
     * 通过websocket返回进度信息
     *
     * @param msg
     * @param uniqueId
     * @return
     */
    void createMonitorUnitConfigXMLAsync(String msg, String uniqueId);

    void createMonitorUnitConfigXML(WorkStation workStation, String path);

    boolean sendMonitorUnitConfigXML(MonitorUnitDTO tslMonitorUnit, String uniqueId, String protocol, Integer ftpPort, String ftpUsername, String ftpPassword);

    void sendMonitorUnitConfigXMLAsync(String monitorUnitIds, Integer port, String username, String password, String protocol, String uniqueId);

    /**
     * 多线程版本的配置下发方法
     * 使用线程池并行处理多个监控单元，提高处理效率
     *
     * @param monitorUnitIds 监控单元ID列表（逗号分隔）
     * @param port 端口号
     * @param username 用户名
     * @param password 密码
     * @param protocol 协议（ftp或sftp）
     * @param uniqueId 任务唯一标识
     */
    void sendMonitorUnitConfigXMLAsyncParallel(String monitorUnitIds, Integer port, String username, String password, String protocol, String uniqueId);

    /**
     * 将工作站的监控单元配置文件打包成ZIP
     * @param workStations 工作站列表
     * @return ZIP文件字节数组
     */
    byte[] zipMonitorUnitConfigXML(List<WorkStation> workStations);

    /**
     * 获取多个监控单元的配置文件并打包
     * @param monitorUnitIds 监控单元ID列表
     * @return ZIP文件字节数组
     */
    byte[] zipMultipleMonitorUnitConfigXML(List<Integer> monitorUnitIds);

    /**
     * 下载远程采集器配置文件包
     * 从远程采集器下载xmlCfg目录下的所有XML文件和cmbcfg目录下的cmb_dictionary.xml文件
     *
     * @param monitorUnitId 监控单元ID
     * @param protocol 协议类型 (ftp/sftp/ssh/scp)
     * @param port 端口号
     * @param username 用户名
     * @param password 密码
     * @param uniqueId 任务唯一ID
     * @return 下载的配置文件包
     */
    File downloadRemoteConfigPackage(Integer monitorUnitId, String protocol,
                                     Integer port, String username, String password, String uniqueId);

    /**
     * 异步创建监控单元备份包
     * 生成监控单元配置、全字典表、SO库等文件并打包成ZIP
     *
     * @param monitorUnitId 监控单元ID
     * @param uniqueId 任务唯一ID
     */
    void createBackupPackageAsync(Integer monitorUnitId, String uniqueId);
}
