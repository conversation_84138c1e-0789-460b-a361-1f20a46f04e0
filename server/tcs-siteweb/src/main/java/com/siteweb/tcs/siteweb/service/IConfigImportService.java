package com.siteweb.tcs.siteweb.service;

import com.siteweb.tcs.siteweb.dto.ConfigDiffResult;
import com.siteweb.tcs.siteweb.dto.ConfigImportReport;
import com.siteweb.tcs.siteweb.dto.ConfigImportResult;
import com.siteweb.tcs.siteweb.dto.ConfigImportTaskRequest;
import com.siteweb.tcs.siteweb.dto.ConfigUploadAndDiffResult;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;

/**
 * 配置导入服务接口
 */
public interface IConfigImportService {

    /**
     * 上传配置文件并进行差异对比（推荐使用的新流程）
     * @param file 上传的配置文件
     * @return 上传和差异对比结果
     */
    ConfigUploadAndDiffResult uploadAndCompareConfig(MultipartFile file);

    /**
     * 确认导入配置（基于之前上传的文件）
     * @param uploadTaskId 上传任务ID
     * @param request 导入请求参数
     * @return 导入报告
     */
    ConfigImportReport confirmImportFromUpload(String uploadTaskId, ConfigImportTaskRequest request);

    /**
     * 取消上传任务并清理相关文件
     * @param uploadTaskId 上传任务ID
     * @return 是否成功清理
     */
    boolean cancelUploadTask(String uploadTaskId);


    /**
     * 从文件执行配置导入
     * @param taskId 任务ID
     * @param zipFilePath zip文件
     * @param request 导入请求参数
     * @return 导入报告
     */
    @Transactional(rollbackFor = Exception.class)
    ConfigImportReport executeImportFromFile(String taskId, String zipFilePath, ConfigImportTaskRequest request);

    /**
     * 清理临时文件
     * @param tempDir 临时目录
     */
    void cleanupTempFiles(File tempDir);

    // 以下方法保留用于向后兼容，但不推荐直接使用

    /**
     * 比较配置文件与数据库中的配置差异
     * @param file 上传的配置文件
     * @param monitorUnitId 监控单元ID
     * @return 配置差异结果
     * @deprecated 推荐使用 uploadAndCompareConfig 方法
     */
    @Deprecated
    ConfigDiffResult compareConfigWithDatabase(MultipartFile file, Integer monitorUnitId);

    /**
     * 从文件比较配置差异
     * @param zipFilePath zip文件路径
     * @param monitorUnitId 监控单元ID
     * @return 配置差异结果
     * @deprecated 推荐使用 uploadAndCompareConfig 方法
     */
    @Deprecated
    ConfigDiffResult compareConfigWithDatabaseFromFile(String zipFilePath, Integer monitorUnitId);
}
