package com.siteweb.tcs.siteweb.dto;

import lombok.Data;
import org.dom4j.Element;

import java.util.List;

/**
 * 配置导入解析结果
 */
@Data
public class ConfigImportResult {
    
    /**
     * 监控单元信息
     */
    private MonitorUnitImportData monitorUnit;
    
    /**
     * 设备模板列表
     */
    private List<EquipmentTemplateImportData> equipmentTemplates;
    
    /**
     * 应用配置信息
     */
    private Element appConfiguration;
    
    /**
     * 原始XML文件路径
     */
    private String xmlFilePath;
    
    /**
     * 监控单元导入数据
     */
    @Data
    public static class MonitorUnitImportData {
        private Integer monitorUnitId;
        private String monitorUnitName;
        private Integer monitorUnitCategory;
        private String monitorUnitCode;
        private Integer stationId;
        private String ipAddress;
        private Integer runMode;
        private String description;
        
        // 关联的设备列表
        private List<EquipmentImportData> equipments;
        
        // 关联的端口列表
        private List<PortImportData> ports;
        
        // 关联的采集单元列表
        private List<SamplerUnitImportData> samplerUnits;
    }
    
    /**
     * 设备模板导入数据
     */
    @Data
    public static class EquipmentTemplateImportData {
        private Integer equipmentTemplateId;
        private String equipmentTemplateName;
        private String protocolCode;
        private Integer equipmentCategory;
        private Integer equipmentType;
        private String memo;
        private String property;
        private String description;
        private Integer equipmentBaseType;
        
        // 关联的信号列表
        private List<SignalImportData> signals;
        
        // 关联的事件列表
        private List<EventImportData> events;
        
        // 关联的控制列表
        private List<ControlImportData> controls;
    }
    
    /**
     * 信号导入数据
     */
    @Data
    public static class SignalImportData {
        private Integer signalId;
        private String signalName;
        private Integer signalCategory;
        private Integer signalType;
        private Integer channelNo;
        private Integer channelType;
        private String expression;
        private String dataType;
        private String showPrecision;
        private String unit;
        private String storeInterval;
        private String absValueThreshold;
        private String percentThreshold;
        private String staticsPeriod;
        private Boolean enable;
        private Boolean visible;
        private String description;
        private String baseTypeId;
        private String chargeStoreInterVal;
        private String chargeAbsValue;
        private Integer displayIndex;
        private String signalProperty;
        private String signalMeaning;
        private Integer moduleNo;
    }
    
    /**
     * 事件导入数据
     */
    @Data
    public static class EventImportData {
        private Integer eventId;
        private String eventName;
        private Integer eventCategory;
        private Integer startType;
        private Integer endType;
        private String startExpression;
        private String suppressExpression;
        private Integer signalId;
        private Boolean enable;
        private Boolean visible;
        private String description;
        private String turnover;
        private Integer displayIndex;
        private Integer moduleNo;
        
        // 事件条件列表
        private List<EventConditionImportData> conditions;
    }
    
    /**
     * 事件条件导入数据
     */
    @Data
    public static class EventConditionImportData {
        private Integer eventConditionId;
        private Integer eventSeverity;
        private String startOperation;
        private String startCompareValue;
        private String startDelay;
        private String endOperation;
        private String endCompareValue;
        private String endDelay;
        private String frequency;
        private String frequencyThreshold;
        private String meanings;
        private String equipmentState;
        private String baseTypeId;
        private String standardName;
    }
    
    /**
     * 控制导入数据
     */
    @Data
    public static class ControlImportData {
        private Integer controlId;
        private String controlName;
        private Integer controlCategory;
        private String cmdToken;
        private Long baseTypeId;
        private Integer controlSeverity;
        private Integer signalId;
        private Double timeOut;
        private Integer retry;
        private String description;
        private Boolean enable;
        private Boolean visible;
        private Integer displayIndex;
        private Integer commandType;
        private Short controlType;
        private Short dataType;
        private Double maxValue;
        private Double minValue;
        private Double defaultValue;
        private Integer moduleNo;
        
        // 控制含义列表
        private List<ControlMeaningImportData> meanings;
    }
    
    /**
     * 控制含义导入数据
     */
    @Data
    public static class ControlMeaningImportData {
        private String parameterValue;
        private String meaning;
    }
    
    /**
     * 设备导入数据
     */
    @Data
    public static class EquipmentImportData {
        private Integer equipmentId;
        private String equipmentName;
        private Integer equipmentTemplateId;
        private Integer equipmentCategory;
        private Integer equipmentType;
        private String equipmentCode;
        private String description;
        private Integer houseId;
        private String houseName;
    }
    
    /**
     * 端口导入数据
     */
    @Data
    public static class PortImportData {
        private Integer portId;
        private Integer portNo;
        private Integer portType;
        private String setting;
        private String description;
    }
    
    /**
     * 采集单元导入数据
     */
    @Data
    public static class SamplerUnitImportData {
        private Integer samplerUnitId;
        private String samplerUnitName;
        private Integer portNo;
        private Integer address;
        private Integer spUnitInterval;
        private String dllPath;
        private String description;
    }
}
