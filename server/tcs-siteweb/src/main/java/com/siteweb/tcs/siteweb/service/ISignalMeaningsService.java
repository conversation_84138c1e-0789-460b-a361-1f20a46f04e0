package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.entity.SignalMeanings;

import java.util.List;

/**
 * Signal Meanings Service Interface
 */
public interface ISignalMeaningsService extends IService<SignalMeanings> {

    /**
     * 解析信号含义字符串
     * @param equipmentTemplateId 设备模板ID
     * @param signalId 信号ID
     * @param meaningsArr 含义数组
     * @return 信号含义列表
     */
    List<SignalMeanings> parseSignalMeaningStr(Integer equipmentTemplateId, Integer signalId, String[] meaningsArr);

    /**
     * 批量创建信号含义
     * @param signalMeaningList 信号含义列表
     */
    void batchCreateSignalMeanings(List<SignalMeanings> signalMeaningList);

    /**
     * 创建通信状态信号含义
     * @param equipmentTemplateId 设备模板ID
     */
    void communicationStateSignalMeaning(Integer equipmentTemplateId);

    /**
     * 根据设备模板ID和信号ID查找信号含义
     * @param equipmentTemplateId 设备模板ID
     * @param signalId 信号ID
     * @return 信号含义列表
     */
    List<SignalMeanings> findByEquipmentTemplateIdAndSignalId(Integer equipmentTemplateId, Integer signalId);

    /**
     * 根据设备模板ID删除信号含义
     * @param equipmentTemplateId 设备模板ID
     */
    void deleteByEquipmentTemplateId(Integer equipmentTemplateId);

    /**
     * 根据设备模板ID查找信号含义
     * @param equipmentTemplateId 设备模板ID
     * @return 信号含义列表
     */
    List<SignalMeanings> findByEquipmentTemplateId(Integer equipmentTemplateId);

    /**
     * 复制信号含义
     * @param originEquipmentTemplateId 源设备模板ID
     * @param destEquipmentTemplateId 目标设备模板ID
     */
    void copySignalMeanings(Integer originEquipmentTemplateId, Integer destEquipmentTemplateId);

    /**
     * 更新信号含义
     * @param equipmentTemplateId 设备模板ID
     * @param signalId 信号ID
     * @param signalMeaningsList 信号含义列表
     */
    void updateSignalMeanings(Integer equipmentTemplateId, Integer signalId, List<SignalMeanings> signalMeaningsList);

    /**
     * 批量保存联通信号含义
     * @return 是否保存成功
     */
    boolean batchsaveLianTongSignalMeanings();

    /**
     * 根据设备模板ID和信号ID删除信号含义
     * @param equipmentTemplateId 设备模板ID
     * @param signalId 信号ID
     */
    void deleteByEquipmentTemplateIdAndSignalId(Integer equipmentTemplateId, Integer signalId);
}
