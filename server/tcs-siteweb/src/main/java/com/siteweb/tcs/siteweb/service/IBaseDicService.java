package com.siteweb.tcs.siteweb.service;

import java.util.List;
import java.util.function.IntFunction;

/**
 * Base Dictionary Service Interface
 */
public interface IBaseDicService {
    
    /**
     * 更新模板中的基类信息
     * PCT_GenerateBaseDic存储过程实现
     * @param equipmentTemplateId 设备模板ID
     */
    void updateBaseClassStandardDictionary(Integer equipmentTemplateId);

    /**
     * 检查指定基类ID是否存在
     * @param baseTypeId 基类ID
     * @return 是否存在
     */
    boolean existsByBaseTypeId(Long baseTypeId);

    /**
     * 生成基类字典
     * @param baseTypeId 目标基类ID
     * @param sourceId 源基类ID
     */
    void generateBaseDic(Long baseTypeId, Long sourceId);

    /**
     * 处理基类ID列表
     * @param findBaseTypeIdsFunction 查找基类ID的函数
     * @param baseDicService 基类字典服务
     * @param equipmentTemplateId 设备模板ID
     */
    void processBaseTypeIdList(IntFunction<List<Long>> findBaseTypeIdsFunction, IBaseDicService baseDicService, Integer equipmentTemplateId);
} 