package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.entity.DoorCard;
import com.siteweb.tcs.siteweb.entity.DoorCardBackup;
import com.siteweb.tcs.siteweb.mapper.DoorCardBackupMapper;
import com.siteweb.tcs.siteweb.service.IDoorCardBackupService;
import com.siteweb.tcs.siteweb.service.IDoorCardService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Door Card Backup Service Implementation
 */
@Slf4j
@Service
public class DoorCardBackupServiceImpl extends ServiceImpl<DoorCardBackupMapper, DoorCardBackup> implements IDoorCardBackupService {

    @Autowired
    private IDoorCardService doorCardService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void backupCard(Integer equipmentId, String equipmentName) {
        // 获取需要备份的门禁卡
        List<Integer> cardIds = doorCardService.findCardIdByEquipmentId(equipmentId);
        if (cardIds.isEmpty()) {
            log.info("设备没有绑定门禁卡，无需备份，equipmentId: {}", equipmentId);
            return;
        }
        
        // 备份门禁卡
        log.info("开始备份门禁卡，设备ID: {}, 设备名称: {}", equipmentId, equipmentName);
        
        for (Integer cardId : cardIds) {
            DoorCardBackup backup = new DoorCardBackup();
            backup.setCardId(cardId);
            backup.setEquipmentId(equipmentId);
            backup.setEquipmentName(equipmentName);
            backup.setBackupTime(LocalDateTime.now());
            this.save(backup);
        }
        
        log.info("备份门禁卡完成，共备份{}张卡, 设备ID: {}", cardIds.size(), equipmentId);
    }
} 