package com.siteweb.tcs.siteweb.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.dto.TypeItemDTO;
import com.siteweb.tcs.siteweb.entity.DataEntry;
import com.siteweb.tcs.siteweb.entity.DataItem;
import com.siteweb.tcs.siteweb.entity.PrimaryKeyValue;
import com.siteweb.tcs.siteweb.enums.DataEntryEnum;
import com.siteweb.tcs.siteweb.exception.BusinessException;
import com.siteweb.tcs.siteweb.mapper.DataEntryMapper;
import com.siteweb.tcs.siteweb.mapper.DataItemMapper;
import com.siteweb.tcs.siteweb.mapper.PrimaryKeyValueMapper;
import com.siteweb.tcs.siteweb.service.IChangeEventService;
import com.siteweb.tcs.siteweb.service.IDataItemService;
import com.siteweb.tcs.siteweb.util.I18n;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Data Item Service Implementation
 */
@Service
public class DataItemServiceImpl extends ServiceImpl<DataItemMapper, DataItem> implements IDataItemService {

    @Autowired
    private PrimaryKeyValueMapper primaryKeyValueMapper;

    @Autowired
    private I18n i18n;

    @Autowired
    private IChangeEventService changeEventService;

    @Autowired
    private DataItemMapper dataItemMapper;

    @Autowired
    private DataEntryMapper dataEntryMapper;
    @Override
    public List<DataItem> findByEntryId(DataEntryEnum dataEntryEnum) {
        LambdaQueryWrapper<DataItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DataItem::getEntryId, dataEntryEnum.getValue());
        return dataItemMapper.selectList(wrapper);
    }

    @Override
    public DataItem findByEntryIdAndItemId(DataEntryEnum dataEntryEnum, Integer itemId) {
        LambdaQueryWrapper<DataItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DataItem::getEntryId, dataEntryEnum.getValue())
               .eq(DataItem::getItemId, itemId);
        return dataItemMapper.selectOne(wrapper);
    }

    @Override
    public int createDataItem(DataItem dataItem) {
        if (Objects.isNull(dataItem.getEntryItemId())) {
            dataItem.setEntryItemId(getIncrementMaxEntryItemId());
        }
        if (Objects.isNull(dataItem.getItemId())) {
            dataItem.setItemId(getIncrementMaxItemId(dataItem.getEntryId()));
        }
        if (existsByEntryIdAndItemId(null,dataItem.getEntryId(), dataItem.getItemId())) {
            throw new BusinessException(i18n.T("dictionary.id.exists"));
        }
        int result = dataItemMapper.insert(dataItem);
        dataItem =  dataItemMapper.selectById(dataItem.getEntryItemId());
//        dataItemCache.putItem(dataItem);
        changeEventService.sendUpdate(dataItem);
        return result;
    }

    @Override
    public int getIncrementMaxItemId(Integer entryId) {
        DataItem entity = new LambdaQueryWrapper<DataItem>()
                .eq(DataItem::getEntryId, entryId)
                .orderByDesc(DataItem::getItemId)
                .last("LIMIT 1")
                .getEntity();
        if (Objects.isNull(entity)) {
            return 0;
        }
        return entity.getItemId() + 1;
    }

    @Override
    public int getIncrementMaxEntryItemId() {
        // 实现获取最大EntryItemId的逻辑
        Integer entryItemId = selectMaxItemId();
        if (Objects.isNull(entryItemId)) {
            return 1;
        }
        return entryItemId + 1;
    }

    private Integer selectMaxItemId(){
        LambdaQueryWrapper<DataItem> queryWrapper = new LambdaQueryWrapper<DataItem>()
                .orderByDesc(DataItem::getEntryItemId)
                .last("LIMIT 1");
        DataItem dataItem = dataItemMapper.selectOne(queryWrapper);
        return dataItem.getEntryItemId();
    }

    private boolean existsByEntryIdAndItemId(Integer entryItemId,Integer entryId, Integer itemId) {
        return dataItemMapper.exists(Wrappers.lambdaQuery(DataItem.class)
                .ne(Objects.nonNull(entryItemId), DataItem::getEntryItemId, entryItemId)
                .eq(DataItem::getEntryId, entryId)
                .eq(DataItem::getItemId, itemId));
    }


    @Override
    public void initDataEntryItem(DataItem dataItem) {
        // 判断tbl_primarykeyvalue是否存在记录
        PrimaryKeyValue primaryKeyValue = primaryKeyValueMapper.selectOne(Wrappers.lambdaQuery(PrimaryKeyValue.class)
                .eq(PrimaryKeyValue::getTableId, 61)
                .eq(PrimaryKeyValue::getPostalCode, 0));
        // 不存在则初始化
        if (Objects.isNull(primaryKeyValue)) {
            primaryKeyValue = new PrimaryKeyValue();
            primaryKeyValue.setTableId(61);
            primaryKeyValue.setPostalCode(0);
            primaryKeyValue.setMinValue(1);
            // 查询dataentryitem表中最大的itemid
            Integer maxItemId = selectMaxItemId();
            primaryKeyValue.setCurrentValue(maxItemId + 1);
            primaryKeyValueMapper.insert(primaryKeyValue);
        }
        // 新增dataentryitem
        dataItem.setEntryItemId(primaryKeyValue.getCurrentValue());
        createDataItem(dataItem);
    }

    @Override
    public void deleteByEntryId(DataEntryEnum dataEntryEnum) {
        LambdaQueryWrapper<DataItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DataItem::getEntryId, dataEntryEnum.getValue());
        dataItemMapper.delete(wrapper);
    }

    @Override
    public int saveDictionaryItemByEntry(DataItem dataItem) {
        int ret = 0;
        // 参数检验
        if (dataItem.getEntryId() == 0 || dataItem.getItemValue() == null) {
            return -1;
        }

        // 检查是否存在EntryId
        if (!checkEntryIdExists(dataItem.getEntryId())) {
            return -2;
        }

        dataItem.setParentEntryId(dataItem.getParentEntryId() == 0 ? null : dataItem.getParentEntryId());
        dataItem.setParentItemId(dataItem.getParentItemId() == 0 ? null : dataItem.getParentItemId());
        dataItem.setItemId(dataItem.getItemId() == 0 ? null : dataItem.getItemId());
        dataItem.setIsSystem(dataItem.getIsSystem() != null && dataItem.getIsSystem().equals(false) ? null : dataItem.getIsSystem());

        // 获取最大EntryItemId
        int maxEntryItemId = getIncrementMaxEntryItemId();

        // 更新主键值
        updatePrimaryKeyValue(maxEntryItemId);

        // 获取最大ItemId
        if (dataItem.getItemId() == null) {
            dataItem.setItemId(getIncrementMaxItemId(dataItem.getEntryId()));
        }
        dataItem.setEntryItemId(maxEntryItemId);
        createDataItem(dataItem);
        return ret;
    }

    /**
     * 检查EntryId是否存在
     *
     * @param entryId 数据项类别ID
     * @return 是否存在
     */
    private boolean checkEntryIdExists(int entryId) {
        return dataEntryMapper.exists(Wrappers.lambdaQuery(DataEntry.class)
                .eq(DataEntry::getEntryId, entryId));
    }

    /**
     * 更新主键值
     *
     * @param maxEntryItemId 最大EntryItemId
     */
    private void updatePrimaryKeyValue(int maxEntryItemId) {
        primaryKeyValueMapper.update(Wrappers.lambdaUpdate(PrimaryKeyValue.class)
                .set(PrimaryKeyValue::getCurrentValue, maxEntryItemId)
                .eq(PrimaryKeyValue::getTableId, 61));
    }

    @Override
    public Map<Integer, String> findMapByEntryId(DataEntryEnum dataEntryEnum) {
        List<DataItem> items = findByEntryId(dataEntryEnum);
        return items.stream()
                   .collect(Collectors.toMap(
                       DataItem::getItemId,
                       DataItem::getItemValue,
                       (existing, replacement) -> existing
                   ));
    }

    @Override
    public int deleteByEntryIdAndItemId(Integer entryId, Integer itemId) {
        LambdaQueryWrapper<DataItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DataItem::getEntryId, entryId)
               .eq(DataItem::getItemId, itemId);
        return dataItemMapper.delete(wrapper) > 0 ? 1 : 0;
    }

    @Override
    public int updatePortExtendField2() {
        dataItemMapper.update(
                new LambdaUpdateWrapper<DataItem>()
                        .eq(DataItem::getEntryId, 39)
                        .in(DataItem::getItemId, Arrays.asList(3, 34, 35))
                        .set(DataItem::getExtendField2, "2,4,5,6,7,8,9,10,11,12,14,16,17,18,19,20,21,22,23,25")
        );

        // 第2条 SQL
        dataItemMapper.update(
                new LambdaUpdateWrapper<DataItem>()
                        .eq(DataItem::getEntryId, 39)
                        .in(DataItem::getItemId, Arrays.asList(33, 31, 30, 32))
                        .set(DataItem::getExtendField2, "1,24")
        );

        // 第3条 SQL
        dataItemMapper.update(
                new LambdaUpdateWrapper<DataItem>()
                        .eq(DataItem::getEntryId, 39)
                        .in(DataItem::getItemId, Arrays.asList(1, 6, 5, 19))
                        .set(DataItem::getExtendField2, "2,4,5,6,7,8,9,10,11,12,14,16,17,18,19,20,21,22,23,1,24,25"));
        return 1;
    }

    @Override
    public int deleteByEntryItemIds(List<Integer> entryItemIds) {
        if (entryItemIds == null || entryItemIds.isEmpty()) {
            return 0;
        }
        
        List<DataItem> dataItems = dataItemMapper.selectBatchIds(entryItemIds);
        if (dataItems.isEmpty()) {
            return 0;
        }
        
        int result = dataItemMapper.deleteBatchIds(entryItemIds);
        
        // 发送删除事件
        dataItems.forEach(dataItem -> changeEventService.sendDelete(dataItem));
        
        return dataItems.size();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(DataItem dataItem) {
        if (existsByEntryIdAndItemId(dataItem.getEntryItemId(),dataItem.getEntryId(), dataItem.getItemId())) {
            throw new BusinessException(i18n.T("dictionary.id.exists"));
        }
        dataItemMapper.updateById(dataItem);
        dataItem = baseMapper.selectById(dataItem.getEntryItemId());
        changeEventService.sendUpdate(dataItem);
        return 1;
    }


    @Override
    public Map<Integer, String> findMapByEntryIdAndItemId(DataEntryEnum dataEntryEnum, Integer itemId) {
        DataItem dataItem = findByEntryIdAndItemId(dataEntryEnum, itemId);
        if (Objects.isNull(dataItem)) {
            return Collections.emptyMap();
        }
        return Map.of(itemId, dataItem.getItemValue());
    }

    @Override
    public List<TypeItemDTO> findTypes(DataEntryEnum dataEntryEnum) {
        return findByEntryId(dataEntryEnum)
                .stream()
                .sorted(Comparator.comparingInt(DataItem::getEntryItemId))
                .map(TypeItemDTO::new)
                .toList();
    }

    /**
     * -- 采集类型端口的排序：标准串口、终端服务器口、虚拟端口、SNMP端口（3 需要 生成 为19）、简单逻辑控制口（19）、自诊断端口、板载IO端口
     * -- 非采集器类型（RMU）：端口排序：标准串口、终端服务器口，虚拟端口、SNMP口（33-》5）、简单逻辑控制口、移动B接口门禁透传端口、MDU端口、BACNet端口
     * @param monitorUnitCategory
     * @return
     */
    @Override
    public List<TypeItemDTO> findPortTypesByMonitorUnitCategory(Integer monitorUnitCategory) {
        return findByEntryId(DataEntryEnum.PORT_TYPE)
                .stream()
                .filter(dataItem -> {
                    String extendField2 = dataItem.getExtendField2();
                    if (StrUtil.isBlank(extendField2)) {
                        return true;
                    }
                    String[] split = extendField2.split(",");
                    return Arrays.stream(split).anyMatch(s -> Integer.parseInt(s) == monitorUnitCategory);
                })
                .sorted(Comparator.comparingInt(DataItem::getEntryItemId))
                .map(TypeItemDTO::new)
                .toList();
    }

    @Override
    public List<DataItem> findEquipmentCategory() {
        return dataItemMapper.findEquipmentCategory();
    }
}
