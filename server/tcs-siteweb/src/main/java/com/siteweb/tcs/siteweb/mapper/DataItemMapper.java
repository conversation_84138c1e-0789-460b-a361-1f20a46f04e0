package com.siteweb.tcs.siteweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.siteweb.entity.DataItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Data Item Mapper
 */
@Mapper
@Repository
public interface DataItemMapper extends BaseMapper<DataItem> {

    Integer findItemIdByEntryId(@Param("entryId") String entryId);

    List<DataItem> findEquipmentCategory();
}
