package com.siteweb.tcs.siteweb.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.dto.IdValueDTO;
import com.siteweb.tcs.siteweb.dto.OperationDetailDTO;
import com.siteweb.tcs.siteweb.entity.Equipment;
import com.siteweb.tcs.siteweb.entity.OperationDetail;
import com.siteweb.tcs.siteweb.enums.OperationObjectTypeEnum;
import com.siteweb.tcs.siteweb.mapper.EquipmentMapper;
import com.siteweb.tcs.siteweb.mapper.OperationDetailMapper;
import com.siteweb.tcs.siteweb.service.IOperationDetailService;
import com.siteweb.tcs.siteweb.vo.OperationDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Operation Detail Service Implementation
 */
@Slf4j
@Service
public class OperationDetailServiceImpl extends ServiceImpl<OperationDetailMapper, OperationDetail> implements IOperationDetailService {

    @Autowired
    private OperationDetailMapper operationDetailMapper;
    
    @Autowired
    private EquipmentMapper equipmentMapper;

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.NESTED)
    public void recordOperationLog(Integer userId, String objectId, OperationObjectTypeEnum operationObjectTypeEnum, String propertyName, String operationType, String oldValue, String newValue) {
        if ("添加".equals(operationType) && Objects.equals(oldValue, newValue)) {
            return;
        }
        
        OperationDetail operationDetail = new OperationDetail();
        operationDetail.setUserId(userId);
        operationDetail.setObjectId(objectId);
        operationDetail.setObjectType(operationObjectTypeEnum.getValue());
        operationDetail.setPropertyName(propertyName);
        operationDetail.setOperationTime(LocalDateTime.now());
        operationDetail.setOperationType(operationType);
        operationDetail.setOldValue(oldValue);
        operationDetail.setNewValue(newValue);
        
        operationDetailMapper.insert(operationDetail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.NESTED)
    public <T> void compareEntitiesRecordLog(Integer userId, T oldEntity, T newEntity) {
        if (Objects.isNull(oldEntity) || Objects.isNull(newEntity)) {
            log.warn("oldEntity or newEntity is null");
            return;
        }
        
        // 这里简化了实现，没有使用注解方式，而是使用简单的反射比较
        LocalDateTime currentTime = LocalDateTime.now();
        String objectId = getObjectId(oldEntity);
        List<OperationDetail> operationDetailList = new ArrayList<>();
        String operationType = "修改";
        
        // 定义忽略的字段
        Set<String> ignoreFieldSet = Set.of("serialVersionUID", "id");
        
        Field[] fields = ReflectUtil.getFields(oldEntity.getClass());
        for (Field field : fields) {
            // 忽略字段跳过
            if (ignoreFieldSet.contains(field.getName())) {
                continue;
            }
            
            Object oldFieldValue = ReflectUtil.getFieldValue(oldEntity, field);
            String oldValue = Convert.toStr(oldFieldValue, "");
            Object newFieldValue = ReflectUtil.getFieldValue(newEntity, field);
            String newValue = Convert.toStr(newFieldValue, "");
            
            // 相同则无需记录日志
            if (Objects.equals(oldValue, newValue)) {
                continue;
            }
            
            // mybatisPlus默认不会修改空值，所以空值无需记录日志
            if (Objects.isNull(newFieldValue)) {
                continue;
            }
            
            OperationDetail operationDetail = new OperationDetail();
            operationDetail.setUserId(userId);
            operationDetail.setObjectId(objectId);
            
            // 尝试获取对象类型
            Integer objectType;
            try {
                Field typeField = oldEntity.getClass().getDeclaredField("type");
                typeField.setAccessible(true);
                objectType = (Integer) typeField.get(oldEntity);
            } catch (Exception e) {
                // 默认设为设备类型
                objectType = OperationObjectTypeEnum.EQUIPMENT.getValue();
            }
            
            operationDetail.setObjectType(objectType);
            operationDetail.setPropertyName(field.getName());
            operationDetail.setOperationTime(currentTime);
            operationDetail.setOperationType(operationType);
            operationDetail.setOldValue(oldValue);
            operationDetail.setNewValue(newValue);
            
            operationDetailList.add(operationDetail);
        }
        
        if (CollUtil.isEmpty(operationDetailList)) {
            return;
        }
        
        operationDetailMapper.insertBatchSomeColumn(operationDetailList);
    }

    @Override
    public Page<OperationDetailVO> findPage(Page<OperationDetail> page, OperationDetailDTO operationDetailDTO) {
        return operationDetailMapper.findPage(page, operationDetailDTO);
    }

    @Override
    public Page<OperationDetail> findPageByObjectTypeAndObjectId(Page<OperationDetail> page, String objectType, String objectId) {
        return this.page(page, Wrappers.lambdaQuery(OperationDetail.class)
                .eq(OperationDetail::getObjectType, objectType)
                .eq(OperationDetail::getObjectId, objectId)
                .orderByDesc(OperationDetail::getOperationTime));
    }

    @Override
    public Page<OperationDetailVO> findEquipmentTemplateLogPage(Page<OperationDetail> page, OperationDetailDTO operationDetailDTO) {
        return operationDetailMapper.findEquipmentTemplateLogPage(page, operationDetailDTO);
    }

    @Override
    public Page<OperationDetailVO> findEquipmentLogPage(Page<OperationDetail> page, OperationDetailDTO operationDetailDTO) {
        Integer equipmentId = Integer.valueOf(operationDetailDTO.getObjectId());
        Equipment equipment = equipmentMapper.selectOne(
                new LambdaQueryWrapper<Equipment>()
                        .select(Equipment::getEquipmentTemplateId)
                        .eq(Equipment::getEquipmentId, equipmentId)
        );
        
        if (equipment == null) {
            return new Page<>();
        }
        
        return operationDetailMapper.findEquipmentLogPage(page, operationDetailDTO, equipment.getEquipmentTemplateId());
    }
    
    /**
     * 简单获取对象ID
     * 根据反射获取id或者主键字段
     */
    private <T> String getObjectId(T entity) {
        try {
            // 尝试获取对象的ID字段
            for (String fieldName : List.of("id", "equipmentId", "stationId", "portId", "samplerUnitId")) {
                try {
                    Field field = ReflectUtil.getField(entity.getClass(), fieldName);
                    if (field != null) {
                        Object value = ReflectUtil.getFieldValue(entity, field);
                        if (value != null) {
                            return value.toString();
                        }
                    }
                } catch (Exception ignored) {
                    // 忽略异常，继续尝试其他字段
                }
            }

            // 如果没有找到主键字段，使用对象的哈希码
            return String.valueOf(entity.hashCode());
        } catch (Exception e) {
            log.error("Failed to get object ID", e);
            return String.valueOf(entity.hashCode());
        }
    }

    // ==================== 设备管理相关方法实现 ====================

    @Override
    public List<IdValueDTO<Integer, String>> findOperationTypes() {
        try {
            List<IdValueDTO<Integer, String>> result = new ArrayList<>();
            Arrays.stream(OperationObjectTypeEnum.values()).forEach(type -> result.add(new IdValueDTO<>(type.getValue(), type.getDescribe())));
            return result;
        } catch (Exception e) {
            log.error("Failed to find operation types", e);
            return List.of();
        }
    }

    @Override
    public Page<OperationDetailVO> findEquipmentTemplateLogPage(Map<String, Object> params) {
        try {
            // 构建分页参数
            int current = (int) params.getOrDefault("current", 1);
            int size = (int) params.getOrDefault("size", 10);
            Page<OperationDetail> page = new Page<>(current, size);

            // 构建查询条件
            OperationDetailDTO dto = new OperationDetailDTO();
            if (params.containsKey("objectId")) {
                dto.setObjectId((String) params.get("objectId"));
            }
            if (params.containsKey("operationType")) {
                dto.setOperationType((String) params.get("operationType"));
            }

            return findEquipmentTemplateLogPage(page, dto);
        } catch (Exception e) {
            log.error("Failed to find equipment template log page", e);
            return new Page<>();
        }
    }

    @Override
    public Page<OperationDetailVO> findEquipmentLogPage(Map<String, Object> params) {
        try {
            // 构建分页参数
            int current = (int) params.getOrDefault("current", 1);
            int size = (int) params.getOrDefault("size", 10);
            Page<OperationDetail> page = new Page<>(current, size);

            // 构建查询条件
            OperationDetailDTO dto = new OperationDetailDTO();
            if (params.containsKey("objectId")) {
                dto.setObjectId((String) params.get("objectId"));
            }
            if (params.containsKey("operationType")) {
                dto.setOperationType((String) params.get("operationType"));
            }

            return findEquipmentLogPage(page, dto);
        } catch (Exception e) {
            log.error("Failed to find equipment log page", e);
            return new Page<>();
        }
    }

    @Override
    public Page<OperationDetailVO> findPage(Map<String, Object> params) {
        try {
            // 构建分页参数
            int current = (int) params.getOrDefault("current", 1);
            int size = (int) params.getOrDefault("size", 10);
            Page<OperationDetail> page = new Page<>(current, size);

            // 构建查询条件
            OperationDetailDTO dto = new OperationDetailDTO();
            if (params.containsKey("objectId")) {
                dto.setObjectId((String) params.get("objectId"));
            }
            if (params.containsKey("operationType")) {
                dto.setOperationType((String) params.get("operationType"));
            }

            return findPage(page, dto);
        } catch (Exception e) {
            log.error("Failed to find operation detail page", e);
            return new Page<>();
        }
    }

}
