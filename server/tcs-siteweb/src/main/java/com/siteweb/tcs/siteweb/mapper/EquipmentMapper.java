package com.siteweb.tcs.siteweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.siteweb.dto.EquipmentDetailDTO;
import com.siteweb.tcs.siteweb.entity.Equipment;
import com.siteweb.tcs.siteweb.vo.EquipmentReferenceVO;
import com.siteweb.tcs.siteweb.vo.EquipmentTreeVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Equipment Mapper
 */
@Mapper
@Repository
public interface EquipmentMapper extends BaseMapper<Equipment> {

    /**
     * 根据分类映射更新设备分类
     *
     * @param equipmentId 设备ID
     * @param businessId 业务ID
     * @param categoryTypeId 分类类型ID
     * @return 是否更新成功
     */
    boolean updateEquipmentCategoryByCategoryIdMap(@Param("equipmentId") Integer equipmentId,
                                                 @Param("businessId") Integer businessId,
                                                 @Param("categoryTypeId") Integer categoryTypeId);

    // ==================== 设备管理相关方法 ====================

    /**
     * 获取设备树结构
     *
     * @return 设备树VO列表
     */
    List<EquipmentTreeVO> getEquipmentTree();

    /**
     * 根据资源结构ID查询设备列表
     *
     * @param structureId 资源结构ID
     * @return 设备列表
     */
    List<Equipment> findByStructureId(@Param("structureId") Integer structureId);


    /**
     * 获取简化设备列表（上一级设备）
     *
     * @return 简化设备列表
     */
    List<Equipment> findSimplifyEquipments();


    /**
     * 根据设备模板ID查询设备引用信息
     *
     * @param equipmentTemplateId 设备模板ID
     * @return 设备引用信息列表
     */
    List<EquipmentReferenceVO> findReferenceByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    /**
     * 批量更新设备模板ID
     *
     * @param equipmentIds 设备ID列表
     * @param destTemplateId 目标模板ID
     * @return 更新数量
     */
    int batchUpdateEquipmentTemplate(@Param("equipmentIds") List<Integer> equipmentIds, @Param("destTemplateId") Integer destTemplateId);

    Equipment findSimplifyEquipmentById(@Param("equipmentId") Integer equipmentId);

    /**
     * 批量更新设备
     *
     * @param equipments 设备列表
     * @return 更新数量
     */
    int batchUpdateEquipment(@Param("equipments") List<Equipment> equipments);

    EquipmentDetailDTO findEquipmentDetail(Integer equipmentId);

}
