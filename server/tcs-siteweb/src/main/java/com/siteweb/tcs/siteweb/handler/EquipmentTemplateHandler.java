package com.siteweb.tcs.siteweb.handler;

import com.siteweb.tcs.siteweb.change.ChangeRecord;
import com.siteweb.tcs.siteweb.change.ObjectChangeHandlerAdapter;
import com.siteweb.tcs.siteweb.entity.EquipmentTemplate;
import com.siteweb.tcs.siteweb.service.IEquipmentService;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2025/3/18
 */
@Slf4j
@Component
public class EquipmentTemplateHandler extends ObjectChangeHandlerAdapter {

    @Autowired
    private IEquipmentService equipmentService;

    @Override
    protected List<Class<?>> doRegisterHandler() {
        return List.of(EquipmentTemplate.class);
    }


    @Override
    public void onCreate(ChangeRecord changeRecord) {

    }


    @Override
    public void onDelete(ChangeRecord changeRecord) {

    }


    @Override
    public void onUpdate(ChangeRecord changeRecord) {
        // 如果修改了设备模板类型需要同步修改已有设备的类型
        EquipmentTemplate equipmentTemplate = changeRecord.readMessageBody(EquipmentTemplate.class);
        if (equipmentTemplate == null) {
            return;
        }
        equipmentService.checkEquipmentSyncField(equipmentTemplate.getEquipmentTemplateId());
    }
}
