package com.siteweb.tcs.siteweb.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 配置上传和差异对比结果
 */
@Data
public class ConfigUploadAndDiffResult {
    
    /**
     * 上传任务ID，用于后续的确认导入操作
     */
    private String uploadTaskId;
    
    /**
     * 上传的文件路径（服务器端）
     */
    private String uploadedFilePath;
    
    /**
     * 上传时间
     */
    private LocalDateTime uploadTime;
    
    /**
     * 原始文件名
     */
    private String originalFileName;
    
    /**
     * 文件大小（字节）
     */
    private Long fileSize;
    
    /**
     * 解析出的监控单元ID（从XML文件中获取）
     */
    private Integer detectedMonitorUnitId;
    
    /**
     * 解析出的监控单元名称
     */
    private String detectedMonitorUnitName;
    
    /**
     * 是否为新建监控单元（数据库中不存在）
     */
    private boolean isNewMonitorUnit;
    
    /**
     * 配置差异对比结果
     */
    private ConfigDiffResult diffResult;
    
    /**
     * 差异统计摘要
     */
    private DiffSummary diffSummary;
    
    /**
     * 上传和解析是否成功
     */
    private boolean success;
    
    /**
     * 错误信息（如果失败）
     */
    private String errorMessage;
    
    /**
     * 差异统计摘要
     */
    @Data
    public static class DiffSummary {
        private int monitorUnitChanges = 0;
        private int equipmentTemplateChanges = 0;
        private int newEquipmentTemplates = 0;
        private int deletedEquipmentTemplates = 0;
        private int modifiedEquipmentTemplates = 0;
        private int signalChanges = 0;
        private int eventChanges = 0;
        private int controlChanges = 0;
        private int portChanges = 0;
        private int samplerUnitChanges = 0;
        private int equipmentChanges = 0;
        
        /**
         * 计算总变更数
         */
        public int getTotalChanges() {
            return monitorUnitChanges + equipmentTemplateChanges + signalChanges + 
                   eventChanges + controlChanges + portChanges + samplerUnitChanges + equipmentChanges;
        }
        
        /**
         * 是否有变更
         */
        public boolean hasChanges() {
            return getTotalChanges() > 0;
        }
    }
}
