package com.siteweb.tcs.siteweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.siteweb.entity.EquipmentTemplate;
import com.siteweb.tcs.siteweb.vo.EquipmentTemplateBaseClassVO;
import com.siteweb.tcs.siteweb.vo.EquipmentTemplateVO;
import com.siteweb.tcs.siteweb.vo.SamplerVO;
import com.siteweb.tcs.siteweb.dto.EquipmentTemplateTreeDTO;
import com.siteweb.tcs.siteweb.dto.IdValueDTO;
import com.siteweb.tcs.siteweb.dto.EquipTemplateChangeDTO;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * Equipment Template Mapper
 */
@Mapper
@Repository
public interface EquipmentTemplateMapper extends BaseMapper<EquipmentTemplate> {

    /**
     * 根据名称模糊查询设备模板
     * @param equipmentTemplateName 设备模板名称
     * @return 设备模板列表
     */
    List<EquipmentTemplate> findByNameLike(@Param("equipmentTemplateName") String equipmentTemplateName);

    /**
     * 查询B接口设备根模板ID
     * B接口设备模板通常具有特定的protocolCode，例如 "BInterfaceProtocol"
     * 并且是顶级模板（ParentTemplateId 为 null 或 0，取决于数据库设计）
     * @return B接口设备根模板ID
     */
    Integer getBInterfaceDeviceTemplateRootId();

    /**
     * 查找指定父模板ID的子模板数量
     * @param parentTemplateId 父模板ID
     * @return 子模板数量
     */
    int countByParentTemplateId(@Param("parentTemplateId") Integer parentTemplateId);

    /**
     * 根据动态条件查询设备模板
     * @param equipmentTemplateVO 查询条件
     * @return 设备模板列表
     */
    List<EquipmentTemplate> queryTemplateByVO(@Param("vo") EquipmentTemplateVO equipmentTemplateVO);

    /**
     * 更新设备模板的基础类型为空
     * @return 更新的记录数
     */
    int updateEquipmentBaseTypeToNull();

    /**
     * 根据设备模板分类查询设备模板ID
     * @param equipmentTemplateIdDiv 设备模板分类
     * @return 设备模板ID列表
     */
    List<Integer> findEquipmentTemplateIdByEquipmentTemplateIdDiv(@Param("equipmentTemplateIdDiv") Integer equipmentTemplateIdDiv);

    List<SamplerVO> findEquipmentTemplateByProtocolCodes(@Param("protocolCodeList") List<String> protocolCodeList);

    Set<String> findReferenceEquipmentNameByProtocolCodes(@Param("protocolCodeList") List<String> protocolCodeList);

    /**
     * 查询所有设备模板VO（用于协议管理页面显示）
     * @return 设备模板VO列表
     */
    List<EquipmentTemplateVO> findVoAll();

    // ==================== 设备管理相关方法 ====================


    /**
     * 查询基类设备模板
     *
     * @return 基类设备模板列表
     */
    List<EquipmentTemplateBaseClassVO> findBaseClassAll();

    // ==================== 新增设备模板迁移相关方法 ====================

    /**
     * 获取设备模板树结构数据
     * @return 设备模板树DTO列表
     */
    List<EquipmentTemplateTreeDTO> findTree();

    /**
     * 根据设备类别获取设备模板树
     * @param equipmentCategory 设备类别
     * @return 设备模板树DTO列表
     */
    List<EquipmentTemplateTreeDTO> findTreeByEquipmentCategory(@Param("equipmentCategory") Integer equipmentCategory);

    /**
     * 根据设备模板ID查询VO
     * @param equipmentTemplateId 设备模板ID
     * @return 设备模板VO
     */
    EquipmentTemplateVO findVoByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    /**
     * 根据设备模板名称查找设备模板
     * @param equipmentTemplateName 设备模板名称
     * @return 设备模板实体
     */
    EquipmentTemplate findByName(@Param("equipmentTemplateName") String equipmentTemplateName);

    /**
     * 查找指定父模板的所有子模板ID（递归）
     * @param parentTemplateId 父模板ID
     * @return 子模板ID列表
     */
    List<Integer> findAllChildId(@Param("parentTemplateId") Integer parentTemplateId);

    /**
     * 根据设备类别、协议代码和模板名称查询设备模板
     * @param equipmentCategory 设备类别
     * @param protocolCode 协议代码
     * @param equipmentTemplateName 设备模板名称
     * @return 设备模板列表
     */
    List<EquipmentTemplate> findByEquipmentCategoryAndProtocolCode(
            @Param("equipmentCategory") Integer equipmentCategory,
            @Param("protocolCode") String protocolCode,
            @Param("equipmentTemplateName") String equipmentTemplateName);

    /**
     * 根据模板ID列表获取模板名称
     * @param equipmentTemplateIds 模板ID列表
     * @return ID-名称映射列表
     */
    List<IdValueDTO> findNameByIds(@Param("equipmentTemplateIds") List<Integer> equipmentTemplateIds);

    /**
     * 根据协议代码列表查找DLL路径
     * @param equipmentTemplateIds 设备模板id
     * @return DLL路径列表
     */
    List<String> findDLlPathByEquipmentTemplateIds(@Param("equipmentTemplateIds") List<Integer> equipmentTemplateIds);

    /**
     * 更新子模板的设备类别
     * @param parentTemplateId 父模板ID
     * @param equipmentCategory 设备类别
     * @return 影响行数
     */
    int updateChildrenEquipmentCategory(@Param("parentTemplateId") Integer parentTemplateId,
                                       @Param("equipmentCategory") Integer equipmentCategory);

    /**
     * 更新模板的设备类别
     * @param equipmentTemplateId 模板ID
     * @param equipmentCategory 设备类别
     * @return 影响行数
     */
    int updateEquipmentCategory(@Param("equipmentTemplateId") Integer equipmentTemplateId,
                               @Param("equipmentCategory") Integer equipmentCategory);

    /**
     * 清空设备基类类型
     * @param equipmentTemplateId 设备模板ID
     * @return 影响行数
     */
    int clearEquipmentBaseType(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    /**
     * 升级为根模板
     * @param equipmentTemplateId 设备模板ID
     * @return 影响行数
     */
    int upgradeToRootTemplate(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    /**
     * 比较模板变更影响
     *
     * @param originTemplateId 原模板ID
     * @param destTemplateId 目标模板ID
     * @param equipmentIds 设备ID列表
     * @return 模板变更影响列表
     */
    List<EquipTemplateChangeDTO> compareTemplateChanges(@Param("originTemplateId") Integer originTemplateId,
                                                        @Param("destTemplateId") Integer destTemplateId,
                                                        @Param("equipmentIds") List<Integer> equipmentIds);

    /**
     * 检查信号引用冲突
     *
     * @param originTemplateId 原模板ID
     * @param destTemplateId 目标模板ID
     * @return 冲突数量
     */
    int checkSignalReferenceConflict(@Param("originTemplateId") Integer originTemplateId,
                                   @Param("destTemplateId") Integer destTemplateId);

    List<EquipmentTemplate> findDynamicConfigTemplate(@Param("hideDynamicConfigTemplate") Boolean hideDynamicConfigTemplate);


}
