package com.siteweb.tcs.siteweb.service.impl;

import cn.hutool.core.util.StrUtil;
import com.siteweb.tcs.common.util.PathUtil;
import com.siteweb.tcs.siteweb.dto.MonitorUnitDTO;
import com.siteweb.tcs.siteweb.entity.Port;
import com.siteweb.tcs.siteweb.entity.Sampler;
import com.siteweb.tcs.siteweb.entity.SamplerUnit;
import com.siteweb.tcs.siteweb.service.IConfigFilePreparationService;
import com.siteweb.tcs.siteweb.service.IPortService;
import com.siteweb.tcs.siteweb.service.ISamplerService;
import com.siteweb.tcs.siteweb.service.ISamplerUnitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.file.Paths;
import java.util.*;

/**
 * 配置文件准备服务实现
 * 提供统一的文件查找和准备逻辑，供下发和备份共同使用
 */
@Slf4j
@Service
public class ConfigFilePreparationServiceImpl implements IConfigFilePreparationService {

    @Autowired
    private ISamplerUnitService samplerUnitService;

    @Autowired
    private ISamplerService samplerService;

    @Autowired
    private IPortService portService;

    @Override
    public ConfigFilePreparationResult prepareAllFiles(MonitorUnitDTO monitorUnit) {
        try {
            ConfigFilePreparationResult result = new ConfigFilePreparationResult(true, "文件准备成功");
            
            // 1. 获取配置XML文件
            File configXmlFile = getConfigXmlFile(monitorUnit);
            result.setConfigXmlFile(configXmlFile);
            
            // 2. 获取CMB字典文件
            File cmbDictionaryFile = getCmbDictionaryFile();
            result.setCmbDictionaryFile(cmbDictionaryFile);
            
            // 3. 获取SO文件列表
            SoFilePreparationResult soFileResult = getSoFiles(monitorUnit);
            result.setSoFileResult(soFileResult);
            
            return result;
        } catch (Exception e) {
            log.error("准备文件失败: 监控单元ID={}", monitorUnit.getMonitorUnitId(), e);
            return new ConfigFilePreparationResult(false, "文件准备失败: " + e.getMessage());
        }
    }

    @Override
    public File getConfigXmlFile(MonitorUnitDTO monitorUnit) {
        try {
            // 构建配置文件路径
            String configPath = String.format("plugins/south-omc-siteweb/workspace/monitorUnitConfig/%d/%s/MonitorUnits%s.xml",
                    monitorUnit.getMonitorUnitId(), 
                    monitorUnit.getMonitorUnitName(), 
                    monitorUnit.getMonitorUnitName());
            
            File configFile = new File(configPath);
            if (configFile.exists()) {
                log.debug("找到配置文件: {}", configPath);
                return configFile;
            } else {
                log.warn("配置文件不存在: {}", configPath);
                return null;
            }
        } catch (Exception e) {
            log.error("获取配置XML文件失败: 监控单元ID={}", monitorUnit.getMonitorUnitId(), e);
            return null;
        }
    }

    @Override
    public File getCmbDictionaryFile() {
        try {
            // 使用指定的CMB字典文件路径
            String cmbDictionaryPath = Paths.get("plugins", "south-cmcc-plugin", "workspace", "standard-dic", "cmb_dictionary.xml")
                    .toString();
            
            File cmbDictionaryFile = new File(cmbDictionaryPath);
            if (cmbDictionaryFile.exists()) {
                log.debug("找到CMB字典文件: {}", cmbDictionaryPath);
                return cmbDictionaryFile;
            } else {
                log.debug("CMB字典文件不存在: {}", cmbDictionaryPath);
                return null;
            }
        } catch (Exception e) {
            log.error("获取CMB字典文件失败", e);
            return null;
        }
    }

    @Override
    public SoFilePreparationResult getSoFiles(MonitorUnitDTO monitorUnit) {
        try {
            log.debug("开始获取监控单元[{}]的SO文件列表", monitorUnit.getMonitorUnitName());
            
            // 获取监控单元下的采样单元
            List<SamplerUnit> samplerUnits = samplerUnitService.findByMonitorUnitId(monitorUnit.getMonitorUnitId());
            if (samplerUnits.isEmpty()) {
                log.debug("监控单元[{}]没有采样单元", monitorUnit.getMonitorUnitName());
                SoFilePreparationResult result = new SoFilePreparationResult(true);
                result.setAvailableFiles(new ArrayList<>());
                result.setNotFoundFiles(new HashSet<>());
                return result;
            }

            List<SoFileInfo> availableFiles = new ArrayList<>();
            Set<String> notFoundFiles = new HashSet<>();
            Set<String> processedFiles = new HashSet<>();

            for (SamplerUnit samplerUnit : samplerUnits) {
                Sampler sampler = samplerService.findById(samplerUnit.getSamplerId());
                Port port = portService.findByPortId(samplerUnit.getPortId());

                if (sampler == null || port == null) {
                    continue;
                }

                // 跳过自诊断设备和板载IO设备
                if ("comm_host_dev.so".equals(port.getSetting()) || "comm_io_dev.so".equals(port.getSetting())) {
                    continue;
                }

                // 处理单个SO文件
                processSingleSoFile(samplerUnit, sampler, availableFiles, notFoundFiles, processedFiles);
            }
            
            SoFilePreparationResult result = new SoFilePreparationResult(true);
            result.setAvailableFiles(availableFiles);
            result.setNotFoundFiles(notFoundFiles);
            
            log.debug("监控单元[{}]SO文件准备完成: 找到{}个文件, {}个文件未找到", 
                    monitorUnit.getMonitorUnitName(), availableFiles.size(), notFoundFiles.size());
            
            return result;
        } catch (Exception e) {
            log.error("获取SO文件列表失败: 监控单元ID={}", monitorUnit.getMonitorUnitId(), e);
            SoFilePreparationResult result = new SoFilePreparationResult(false);
            result.setAvailableFiles(new ArrayList<>());
            result.setNotFoundFiles(new HashSet<>());
            return result;
        }
    }

    /**
     * 处理单个SO文件
     */
    private void processSingleSoFile(SamplerUnit samplerUnit, Sampler sampler,
                                   List<SoFileInfo> availableFiles, Set<String> notFoundFiles,
                                   Set<String> processedFiles) {
        try {
            String soFileName = sampler.getDllPath();
            if (StrUtil.isBlank(soFileName)) {
                return;
            }

            // 确保文件名以.so结尾
            if (!soFileName.endsWith(".so")) {
                soFileName = soFileName.substring(0, soFileName.lastIndexOf(".")) + ".so";
            }

            // 避免重复处理相同的SO文件
            if (processedFiles.contains(soFileName)) {
                return;
            }
            processedFiles.add(soFileName);

            // 查找本地SO文件
            File soFile = new File(PathUtil.pathJoin("upload-dir", "protocol", sampler.getProtocolCode(), soFileName));
            
            if (soFile.exists()) {
                SoFileInfo soFileInfo = new SoFileInfo(soFileName, soFile, sampler.getProtocolCode(), true);
                availableFiles.add(soFileInfo);
                log.debug("找到SO文件: {}", soFile.getAbsolutePath());
            } else {
                notFoundFiles.add(soFileName);
                log.debug("SO文件不存在: {}", soFile.getAbsolutePath());
            }

        } catch (Exception e) {
            log.warn("处理SO文件失败: {}", e.getMessage());
        }
    }
}
