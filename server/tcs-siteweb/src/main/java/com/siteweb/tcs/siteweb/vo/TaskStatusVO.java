package com.siteweb.tcs.siteweb.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 任务状态VO
 * 用于前端展示任务状态信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TaskStatusVO {

    /**
     * 任务唯一标识符
     */
    private String taskId;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 关联的监控单元ID
     */
    private Integer monitorUnitId;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 当前步骤描述
     */
    private String currentStep;

    /**
     * 进度百分比
     */
    private Integer progress;

    /**
     * 状态消息
     */
    private String message;

    /**
     * 是否是最终状态
     */
    private Boolean isFinal;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建便捷的构造方法
     */
    public static TaskStatusVO of(String taskId, String taskType, String status, String message, Boolean isFinal) {
        TaskStatusVO vo = new TaskStatusVO();
        vo.setTaskId(taskId);
        vo.setTaskType(taskType);
        vo.setStatus(status);
        vo.setMessage(message);
        vo.setIsFinal(isFinal);
        vo.setUpdateTime(LocalDateTime.now());
        return vo;
    }

    /**
     * 创建带进度的状态
     */
    public static TaskStatusVO withProgress(String taskId, String taskType, String status, String message, Integer progress, Boolean isFinal) {
        TaskStatusVO vo = of(taskId, taskType, status, message, isFinal);
        vo.setProgress(progress);
        return vo;
    }
}
