package com.siteweb.tcs.siteweb.runner;

import com.siteweb.tcs.siteweb.dto.CenterDTO;
import com.siteweb.tcs.siteweb.service.ICenterService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-05-26 14:31
 **/
@Component
public class DevelopModeRunner {
    @Value("${spring.plugins.runtime-mode}")
    private String runtimeMode;

    private static final String RUNTIME_MODE_DEVELOPMENT = "development";

    @Autowired
    private ICenterService centerService;

    @PostConstruct
    private void initCenter(){
        if(StringUtils.equals(RUNTIME_MODE_DEVELOPMENT, runtimeMode)){
            CenterDTO centerDTO = new CenterDTO();
            centerDTO.setCenterId(201);
            centerDTO.setCenterName("测试中心");
            centerDTO.setCenterIp("*************");
            centerDTO.setStandardType(0);
            centerService.create(centerDTO);
        }
    }
}
