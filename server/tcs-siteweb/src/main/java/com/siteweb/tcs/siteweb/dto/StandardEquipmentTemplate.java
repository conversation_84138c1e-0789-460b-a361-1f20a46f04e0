package com.siteweb.tcs.siteweb.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 标准设备模板DTO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class StandardEquipmentTemplate {
    
    /**
     * 设备模板ID
     */
    private Integer equipmentTemplateId;
    
    /**
     * 设备模板名称
     */
    private String equipmentTemplateName;
    
    /**
     * 父模板ID
     */
    private Integer parentTemplateId;
    
    /**
     * 协议代码
     */
    private String protocolCode;
    
    /**
     * 设备类别
     */
    private Integer equipmentCategory;
    
    /**
     * 设备类型
     */
    private Integer equipmentType;
    
    /**
     * 是否为标准模板
     */
    private Boolean isStandard;
    
    /**
     * 备注
     */
    private String memo;
}
