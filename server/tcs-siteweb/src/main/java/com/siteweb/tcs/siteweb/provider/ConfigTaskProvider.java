package com.siteweb.tcs.siteweb.provider;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.tcs.siteweb.entity.TaskStatus;
import com.siteweb.tcs.siteweb.enums.TaskStatusEnum;
import com.siteweb.tcs.siteweb.enums.TaskTypeEnum;
import com.siteweb.tcs.siteweb.service.IMonitorUnitXmlService;
import com.siteweb.tcs.siteweb.service.ITaskStatusService;
import com.siteweb.tcs.siteweb.vo.TaskStatusVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 配置任务Provider
 * 负责配置生成和下发任务的业务逻辑协调
 * 职责：
 * 1. 创建任务记录
 * 2. 调用具体的业务服务执行任务
 * 3. 提供任务状态查询和管理功能
 */
@Slf4j
@Component
public class ConfigTaskProvider {

    @Autowired
    private ITaskStatusService taskStatusService;

    @Autowired
    private IMonitorUnitXmlService monitorUnitXmlService;

    /**
     * 创建配置生成任务
     * 流程：
     * 1. 创建任务记录（设置为PENDING状态）
     * 2. 调用MonitorUnitXmlService异步执行
     * 3. MonitorUnitXmlService负责更新任务状态和进度
     * @param taskId 任务ID
     * @param monitorUnitIds 监控单元ID列表
     * @param userId 用户ID
     * @return 任务状态
     */
    public TaskStatus createConfigGenerationTask(String taskId, List<Integer> monitorUnitIds, String userId) {
        try {
            log.info("Creating config generation task: {} for monitor units: {}", taskId, monitorUnitIds);
            String monitorUnitIdsStr = monitorUnitIds.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));

            // 1. 创建任务记录（只在这里创建一次）
            TaskStatus task = taskStatusService.createTask(taskId, TaskTypeEnum.CONFIGURATION_GENERATION, monitorUnitIdsStr);

            // 2. 调用具体服务异步执行任务

            // 注意：MonitorUnitXmlService不会再创建任务，只负责执行和更新状态
            monitorUnitXmlService.createMonitorUnitConfigXMLAsync(monitorUnitIdsStr, taskId);

            log.info("Config generation task created successfully: {}", taskId);
            return task;
        } catch (Exception e) {
            log.error("Failed to create config generation task: {}", taskId, e);
            // 如果任务创建失败，设置错误状态
            try {
                taskStatusService.setTaskError(taskId, "创建配置生成任务失败: " + e.getMessage());
            } catch (Exception ex) {
                log.error("Failed to set task error status: {}", taskId, ex);
            }
            throw new RuntimeException("创建配置生成任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建配置下发任务
     *
     * 流程：
     * 1. 创建任务记录（设置为PENDING状态）
     * 2. 调用MonitorUnitXmlService异步执行
     * 3. MonitorUnitXmlService负责更新任务状态和进度
     *
     * @param taskId 任务ID
     * @param monitorUnitIds 监控单元ID列表
     * @param username FTP用户名
     * @param password FTP密码
     * @param port FTP端口
     * @param protocol 协议类型(ftp/sftp)
     * @param userId 用户ID
     * @return 任务状态
     */
    public TaskStatus createConfigDistributionTask(String taskId, List<Integer> monitorUnitIds,
                                                   String username, String password, Integer port,
                                                   String protocol, String userId) {
        try {
            log.info("Creating config distribution task: {} for monitor units: {}", taskId, monitorUnitIds);

            // 1. 创建任务记录（只在这里创建一次）
            TaskStatus task = taskStatusService.createTask(taskId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, null);

            // 2. 调用具体服务异步执行任务
            String monitorUnitIdsStr = monitorUnitIds.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));

            // 注意：MonitorUnitXmlService不会再创建任务，只负责执行和更新状态
            monitorUnitXmlService.sendMonitorUnitConfigXMLAsyncParallel(monitorUnitIdsStr, port, username, password, protocol, taskId);

            log.info("Config distribution task created successfully: {}", taskId);
            return task;
        } catch (Exception e) {
            log.error("Failed to create config distribution task: {}", taskId, e);
            // 如果任务创建失败，设置错误状态
            try {
                taskStatusService.setTaskError(taskId, "创建配置下发任务失败: " + e.getMessage());
            } catch (Exception ex) {
                log.error("Failed to set task error status: {}", taskId, ex);
            }
            throw new RuntimeException("创建配置下发任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取任务状态
     * @param taskId 任务ID
     * @return 任务状态VO
     */
    public TaskStatusVO getTaskStatus(String taskId) {
        try {
            return taskStatusService.getTaskStatus(taskId);
        } catch (Exception e) {
            log.error("Failed to get task status: {}", taskId, e);
            return null;
        }
    }

    /**
     * 分页获取任务历史
     * @param page 页码（从0开始）
     * @param size 页大小
     * @param taskType 任务类型(可选)
     * @return 分页结果
     */
    public Page<TaskStatusVO> getTaskHistory(int page, int size, String taskType) {
        try {
            // 创建分页对象（MyBatis-Plus页码从1开始）
            Page<TaskStatus> pageRequest = new Page<>(page + 1, size);

            // 使用TaskStatusService的分页查询方法
            Page<TaskStatusVO> voPage = taskStatusService.pageTaskHistory(pageRequest, taskType);

            // 转换为前端的页码（从0开始）
            voPage.setCurrent(voPage.getCurrent() - 1);

            return voPage;
        } catch (Exception e) {
            log.error("Failed to get task history, page: {}, size: {}, taskType: {}", page, size, taskType, e);
            // 返回空页面
            Page<TaskStatusVO> emptyPage = new Page<>();
            emptyPage.setCurrent(page);
            emptyPage.setSize(size);
            emptyPage.setTotal(0);
            emptyPage.setPages(0);
            emptyPage.setRecords(new java.util.ArrayList<>());
            return emptyPage;
        }
    }

    /**
     * 创建远程配置下载任务
     *
     * 流程：
     * 1. 创建任务记录（设置为PENDING状态）
     * 2. 异步执行下载任务
     * 3. 立即返回任务状态给前端
     *
     * @param taskId 任务ID
     * @param monitorUnitId 监控单元ID
     * @param protocol 协议类型
     * @param port 端口号
     * @param username 用户名
     * @param password 密码
     * @param userId 用户ID
     * @return 任务状态
     */
    public TaskStatus createRemoteConfigDownloadTask(String taskId, Integer monitorUnitId,
                                                     String protocol, Integer port,
                                                     String username, String password, String userId) {
        try {
            log.info("Creating remote config download task: {} for monitor unit: {}", taskId, monitorUnitId);

            // 1. 创建任务记录
            TaskStatus task = taskStatusService.createTask(taskId, TaskTypeEnum.CONFIGURATION_PULL, null);

            // 2. 异步执行下载任务
            executeRemoteConfigDownloadAsync(taskId, monitorUnitId, protocol, port, username, password);

            // 3. 立即返回任务状态
            return task;
        } catch (Exception e) {
            log.error("Failed to create remote config download task: {}", taskId, e);
            // 如果任务创建失败，设置错误状态
            try {
                taskStatusService.setTaskError(taskId, "创建远程配置下载任务失败: " + e.getMessage());
            } catch (Exception ex) {
                log.error("Failed to set task error status: {}", taskId, ex);
            }
            throw new RuntimeException("创建远程配置下载任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 异步执行远程配置下载任务
     */
    @Async("remoteTaskExecutor")
    public CompletableFuture<Void> executeRemoteConfigDownloadAsync(String taskId, Integer monitorUnitId,
                                                                    String protocol, Integer port,
                                                                    String username, String password) {
        try {
            log.info("Starting async remote config download for task: {}", taskId);

            // 更新任务状态为运行中
            taskStatusService.updateTaskStatus(taskId, TaskStatusEnum.RUNNING, "正在下载远程配置文件...", false);

            // 执行下载
            File configPackage = monitorUnitXmlService.downloadRemoteConfigPackage(
                    monitorUnitId, protocol, port, username, password, taskId);

            if (configPackage != null && configPackage.exists()) {
                // 任务完成
                taskStatusService.completeTaskWithCounts(taskId, 1, 1, 0, "远程配置文件下载完成");
                log.info("Remote config download task completed successfully: {}", taskId);
            } else {
                taskStatusService.completeTaskWithCounts(taskId, 1, 0, 1, "远程配置文件下载失败");
                log.error("Remote config download failed for task: {}", taskId);
            }

        } catch (Exception e) {
            taskStatusService.setTaskErrorSimplified(taskId, "远程配置文件下载异常: " + e.getMessage());
            log.error("Remote config download task failed: {}", taskId, e);
        }

        return CompletableFuture.completedFuture(null);
    }

    /**
     * 创建配置备份任务
     *
     * 流程：
     * 1. 创建任务记录（设置为PENDING状态）
     * 2. 调用MonitorUnitXmlService异步执行备份
     * 3. MonitorUnitXmlService负责更新任务状态和进度
     *
     * @param taskId 任务ID
     * @param monitorUnitId 监控单元ID
     * @param userId 用户ID
     * @return 任务状态
     */
    public TaskStatus createConfigBackupTask(String taskId, Integer monitorUnitId, String userId) {
        try {
            log.info("Creating config backup task: {} for monitor unit: {}", taskId, monitorUnitId);

            // 1. 创建任务记录（只在这里创建一次）
            TaskStatus task = taskStatusService.createTask(taskId, TaskTypeEnum.CONFIGURATION_BACKUP, String.valueOf(monitorUnitId));

            // 2. 调用具体服务异步执行任务
            // 注意：MonitorUnitXmlService不会再创建任务，只负责执行和更新状态
            monitorUnitXmlService.createBackupPackageAsync(monitorUnitId, taskId);

            log.info("Config backup task created successfully: {}", taskId);
            return task;
        } catch (Exception e) {
            log.error("Failed to create config backup task: {}", taskId, e);
            // 如果任务创建失败，设置错误状态
            try {
                taskStatusService.setTaskError(taskId, "创建配置备份任务失败: " + e.getMessage());
            } catch (Exception ex) {
                log.error("Failed to set task error status: {}", taskId, ex);
            }
            throw new RuntimeException("创建配置备份任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取监控单元的最新备份文件
     * @param monitorUnitId 监控单元ID
     * @return 最新的备份文件，如果不存在返回null
     */
    public File getLatestBackupFile(Integer monitorUnitId) {
        try {
            String backupDir = "plugins/south-omc-siteweb/workspace/backup/" + monitorUnitId;
            Path backupPath = Paths.get(backupDir);

            if (!Files.exists(backupPath)) {
                log.debug("备份目录不存在: {}", backupDir);
                return null;
            }
            try(Stream<Path> stream = Files.list(backupPath)) {
                return stream
                        .filter(path -> path.toString().endsWith(".zip"))
                        .map(Path::toFile)
                        .max(Comparator.comparing(File::lastModified))
                        .orElse(null);
            }
        } catch (Exception e) {
            log.error("Failed to get latest backup file for monitor unit: {}", monitorUnitId, e);
            return null;
        }
    }

    /**
     * 检查监控单元是否在指定时间内备份过
     * @param monitorUnitId 监控单元ID
     * @param minutes 时间范围（分钟）
     * @return 是否近期备份过
     */
    public boolean hasRecentBackup(Integer monitorUnitId, int minutes) {
        try {
            File latestBackup = getLatestBackupFile(monitorUnitId);
            if (latestBackup == null) {
                return false;
            }

            long currentTime = System.currentTimeMillis();
            long backupTime = latestBackup.lastModified();
            long timeDiff = currentTime - backupTime;
            long minutesDiff = timeDiff / (1000 * 60);

            boolean hasRecent = minutesDiff <= minutes;
            log.debug("Monitor unit {} backup check: latest backup {} minutes ago, threshold {} minutes, hasRecent: {}",
                    monitorUnitId, minutesDiff, minutes, hasRecent);

            return hasRecent;
        } catch (Exception e) {
            log.error("Failed to check recent backup for monitor unit: {}", monitorUnitId, e);
            return false;
        }
    }

    /**
     * 取消任务
     * @param taskId 任务ID
     */
    public void cancelTask(String taskId) {
        try {
            taskStatusService.updateTaskStatus(taskId, TaskStatusEnum.CANCELLED, "任务已取消", true);
            log.info("Cancelled task: {}", taskId);
        } catch (Exception e) {
            log.error("Failed to cancel task: {}", taskId, e);
            throw new RuntimeException("取消任务失败: " + e.getMessage(), e);
        }
    }
}
