package com.siteweb.tcs.siteweb.handler;

import com.siteweb.tcs.siteweb.change.ChangeRecord;
import com.siteweb.tcs.siteweb.change.ObjectChangeHandlerAdapter;
import com.siteweb.tcs.siteweb.entity.House;
import com.siteweb.tcs.siteweb.service.IEquipmentService;
import com.siteweb.tcs.siteweb.service.IHouseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 房间数据变更处理器
 *
 * <AUTHOR> (2024-03-14)
 **/
@Slf4j
@Component
public class HouseChangeHandler extends ObjectChangeHandlerAdapter {

    @Autowired
    private IHouseService houseService;

    @Autowired
    private IEquipmentService equipmentService;

    @Override
    protected List<Class<?>> doRegisterHandler() {
        return List.of(House.class);
    }

    @Override
    public void onCreate(ChangeRecord changeRecord) {
        House house = changeRecord.readMessageBody(House.class);
        log.info("房间创建成功,houseId:{},houseName:{},stationId:{}",
                house.getHouseId(), house.getHouseName(), house.getStationId());
    }

    @Override
    public void onDelete(ChangeRecord changeRecord) {
        House house = changeRecord.readMessageBody(House.class);
        log.info("房间删除成功,houseId:{},houseName:{},stationId:{}",
                house.getHouseId(), house.getHouseName(), house.getStationId());

        // 删除房间下的所有设备
        equipmentService.deleteByHouseId(house.getHouseId());
    }

    @Override
    public void onUpdate(ChangeRecord changeRecord) {
        House house = changeRecord.readMessageBody(House.class);
        log.trace("房间修改成功,houseId:{},houseName:{},stationId:{}",
                house.getHouseId(), house.getHouseName(), house.getStationId());
    }
}
