package com.siteweb.tcs.siteweb.manager;

import com.siteweb.tcs.siteweb.dto.StructureTreeNodeDTO;
import com.siteweb.tcs.siteweb.entity.Equipment; // Using Equipment entity
import com.siteweb.tcs.siteweb.entity.ResourceStructure;
import com.siteweb.tcs.siteweb.enums.StructureTypeEnum;
import com.siteweb.tcs.siteweb.service.IEquipmentService;
import com.siteweb.tcs.siteweb.service.IResourceStructureService;
import com.siteweb.tcs.siteweb.vo.EquipmentVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class StructureTreeManager {

    private final IResourceStructureService resourceStructureService;
    private final IEquipmentService equipmentService; // To fetch equipment

    public StructureTreeNodeDTO root() {
        ResourceStructure rootStructure = resourceStructureService.getRootStructure();
        if (rootStructure == null) {
            log.warn("Root structure not found.");
            return null;
        }
        List<ResourceStructure> allStructures = resourceStructureService.getAllStructures();
        Map<Integer, List<ResourceStructure>> parentChildrenMap = allStructures.stream()
                .filter(s -> s.getParentResourceStructureId() != null)
                .collect(Collectors.groupingBy(ResourceStructure::getParentResourceStructureId));
        
        return buildTreeFromMap(rootStructure, parentChildrenMap, true, 64); // Default full depth, with equipment
    }

    /**
     * 枚举对象树的分支，可使用 maxDepth 参数指定递归深度
     * 返回结果包含了层级中的设备列表
     *
     * @param resourceStructureID 对象树节点ID  （默认为根节点）
     * @param eqs                 是否包含设备列表 （默认不包含设备列表）
     * @param maxDepth            递归深度   （默认为最大深度）
     */
    public StructureTreeNodeDTO enumTreeBranch(Integer resourceStructureID, Boolean eqs, Integer maxDepth) {
        // Set default values if null
        boolean includeEquipment = (eqs != null) && eqs;
        int currentMaxDepth = (maxDepth == null || maxDepth < 0) ? 64 : maxDepth;

        ResourceStructure startStructure;
        if (resourceStructureID == null) {
            startStructure = resourceStructureService.getRootStructure();
        } else {
            startStructure = resourceStructureService.getStructureByID(resourceStructureID);
        }

        if (startStructure == null) {
            log.warn("Start structure not found for ID: {}", resourceStructureID);
            return null;
        }
        
        return buildTreeWithDepth(startStructure, includeEquipment, currentMaxDepth);
    }

    private StructureTreeNodeDTO buildTreeFromMap(
            ResourceStructure currentStructure,
            Map<Integer, List<ResourceStructure>> parentChildrenMap,
            boolean includeEquipment,
            int maxDepth) {
        
        return buildTreeRecursiveWithMap(currentStructure, parentChildrenMap, includeEquipment, maxDepth, 0);
    }
    
    private StructureTreeNodeDTO buildTreeRecursiveWithMap(
        ResourceStructure currentStructure,
        Map<Integer, List<ResourceStructure>> parentChildrenMap,
        boolean includeEquipment,
        int maxDepth,
        int currentDepth) {

        if (currentDepth > maxDepth) {
            return null; 
        }

        StructureTreeNodeDTO node = convertToTreeNode(currentStructure);

        if (includeEquipment) {
            List<Equipment> equipments = equipmentService.getEquipmentsByResourceStructureId(currentStructure.getResourceStructureId());
            if (equipments != null && !equipments.isEmpty()) {
                node.setEquipments(equipments.stream().map(EquipmentVO::fromEntity).collect(Collectors.toList())); // Assuming EquipmentVO has fromEntity
            }
        }

        List<ResourceStructure> childrenEntities = parentChildrenMap.getOrDefault(currentStructure.getResourceStructureId(), Collections.emptyList());
        if (!childrenEntities.isEmpty() && currentDepth < maxDepth) {
            for (ResourceStructure childEntity : childrenEntities) {
                StructureTreeNodeDTO childNode = buildTreeRecursiveWithMap(childEntity, parentChildrenMap, includeEquipment, maxDepth, currentDepth + 1);
                if(childNode != null) node.getChildren().add(childNode);
            }
            node.sortChildren();
        }
        return node;
    }

    /**
     * 构建指定深度的树结构
     */
    private StructureTreeNodeDTO buildTreeWithDepth(ResourceStructure structure, boolean includeEquipment, int maxDepth) {
        if (maxDepth <= 0) {
            return convertToTreeNode(structure);
        }

        StructureTreeNodeDTO node = convertToTreeNode(structure);

        if (includeEquipment) {
            List<Equipment> equipments = equipmentService.getEquipmentsByResourceStructureId(structure.getResourceStructureId());
            if (equipments != null && !equipments.isEmpty()) {
                node.setEquipments(equipments.stream().map(EquipmentVO::fromEntity).collect(Collectors.toList()));
            }
        }

        if (maxDepth > 0) {
            List<ResourceStructure> children = resourceStructureService.getChildrenByParentId(structure.getResourceStructureId());
            if (children != null && !children.isEmpty()) {
                for (ResourceStructure child : children) {
                    StructureTreeNodeDTO childNode = buildTreeWithDepth(child, includeEquipment, maxDepth - 1);
                    if (childNode != null) {
                        node.getChildren().add(childNode);
                    }
                }
                node.sortChildren();
            }
        }

        return node;
    }

    private StructureTreeNodeDTO convertToTreeNode(ResourceStructure structure) {
        StructureTreeNodeDTO treeNode = StructureTreeNodeDTO.build(structure);
        setNodeIds(treeNode, structure);
        return treeNode;
    }
    
    private void setNodeIds(StructureTreeNodeDTO treeNode, ResourceStructure structure) {
        if (structure.getStructureTypeId() != null) {
            if (structure.getStructureTypeId() == 104) {
                // 局站类型，设置 originId
                treeNode.setOriginId(structure.getOriginId());
            } else if (structure.getStructureTypeId() == 103 ||
                    structure.getStructureTypeId() == 102 ||
                    structure.getStructureTypeId() == 101) {
                // 片区、区域、位置类型，设置 structureId 清空 originId
                treeNode.setStructureId(structure.getOriginId());
                treeNode.setOriginId(null);
            } else if (structure.getStructureTypeId() == StructureTypeEnum.STATION_HOUSE.getValue()) {
                // 局房类型，设置 houseId 清空 originId
                treeNode.setHouseId(structure.getOriginId());
                treeNode.setOriginId(null);
            } else {
                // 其他类型，保留 originId
                treeNode.setOriginId(structure.getOriginId());
            }
        }
    }

    /**
     * 缓存加载完成后的处理
     * This method is called after the cache has been reloaded
     */
    public void cacheLoadCompleteAfter() {
        // This manager itself doesn't hold a cache of the tree, it builds it on demand.
        // The actual cache to reload would be ResourceStructureCache.
        log.info("StructureTreeManager cache load completed. Tree will be built on demand.");
    }
    
    public void reload() {
        // Forward to the method that matches the original implementation
        cacheLoadCompleteAfter();
    }
} 