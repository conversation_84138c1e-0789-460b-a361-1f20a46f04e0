package com.siteweb.tcs.siteweb.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.siteweb.tcs.siteweb.entity.EquipmentTemplate;
import com.siteweb.tcs.siteweb.entity.DataItem;

import java.util.List;

/**
 * 设备模板树DTO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class EquipmentTemplateTreeDTO {
    
    public EquipmentTemplateTreeDTO(EquipmentTemplate equipmentTemplate) {
        this.id = equipmentTemplate.getEquipmentTemplateId();
        this.parentId = equipmentTemplate.getParentTemplateId();
        this.name = equipmentTemplate.getEquipmentTemplateName();
        this.template = true;
        this.equipmentCategory = equipmentTemplate.getEquipmentCategory();
    }

    public EquipmentTemplateTreeDTO(DataItem dataItem) {
        this.parentId = -999;
        this.name = dataItem.getItemValue();
        this.id = dataItem.getItemId();
        this.template = false;
        this.equipmentCategory = dataItem.getItemId();
    }

    /**
     * 父id
     */
    private Integer parentId;
    
    /**
     * id
     */
    private Integer id;
    
    /**
     * 名称
     */
    private String name;
    
    /**
     * 是否是模板
     */
    private boolean template;
    
    /**
     * 设备种类
     */
    private Integer equipmentCategory;
    
    /**
     * 子节点
     */
    private List<EquipmentTemplateTreeDTO> children;
}
