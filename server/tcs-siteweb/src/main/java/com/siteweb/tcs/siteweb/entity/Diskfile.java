package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 磁盘文件实体类
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("diskfile")
@Builder
public class Diskfile implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件ID，主键，自增
     */
    @TableId(value = "fileid", type = IdType.AUTO)
    private Long fileid;

    /**
     * 文件路径
     */
    @TableField("filepath")
    private String filepath;

    /**
     * 文件名
     */
    @TableField("filename")
    private String filename;

    /**
     * 状态
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField("createtime")
    private LocalDateTime createtime;
}