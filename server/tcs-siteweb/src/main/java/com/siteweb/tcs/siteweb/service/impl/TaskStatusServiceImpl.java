package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.siteweb.tcs.siteweb.entity.TaskStatus;
import com.siteweb.tcs.siteweb.enums.TaskStatusEnum;
import com.siteweb.tcs.siteweb.enums.TaskTypeEnum;
import com.siteweb.tcs.siteweb.mapper.TaskStatusMapper;
import com.siteweb.tcs.siteweb.service.ITaskStatusService;
import com.siteweb.tcs.siteweb.vo.TaskStatusVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 任务状态服务实现类
 * 使用Redis缓存 + 数据库持久化的方式管理任务状态
 */
@Slf4j
@Service
public class TaskStatusServiceImpl implements ITaskStatusService {

    @Autowired
    private TaskStatusMapper taskStatusMapper;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String TASK_STATUS_KEY_PREFIX = "task:status:";
    private static final String TASK_HISTORY_KEY_PREFIX = "task:history:";
    private static final long CACHE_EXPIRE_HOURS = 24; // Redis缓存24小时
    private static final long DB_EXPIRE_DAYS = 7; // 数据库保存7天

    @Override
    public TaskStatus createTask(String taskId, TaskTypeEnum taskType, Integer monitorUnitId) {
        TaskStatus taskStatus = new TaskStatus();
        taskStatus.setTaskId(taskId);
        taskStatus.setTaskType(taskType.getCode());
        taskStatus.setMonitorUnitId(monitorUnitId);
        taskStatus.setStatus(TaskStatusEnum.PENDING.getCode());
        taskStatus.setProgress(0);
        taskStatus.setIsFinal(false);
        taskStatus.setCreateTime(LocalDateTime.now());
        taskStatus.setUpdateTime(LocalDateTime.now());
        taskStatus.setExpireTime(LocalDateTime.now().plusDays(DB_EXPIRE_DAYS));

        // 保存到数据库
        taskStatusMapper.insert(taskStatus);

        // 缓存到Redis
        cacheTaskStatus(taskStatus);

        log.info("Created task: {} of type: {}", taskId, taskType.getCode());
        return taskStatus;
    }

    @Override
    public void updateTaskStatus(String taskId, TaskStatusEnum status, String message, Boolean isFinal) {
        TaskStatus taskStatus = getTaskFromCache(taskId);
        if (taskStatus == null) {
            taskStatus = getTaskFromDatabase(taskId);
            if (taskStatus == null) {
                log.warn("Task not found: {}", taskId);
                return;
            }
        }

        taskStatus.setStatus(status.getCode());
        taskStatus.setMessage(message);
        taskStatus.setIsFinal(isFinal);
        taskStatus.setUpdateTime(LocalDateTime.now());

        // 更新数据库
        updateTaskInDatabase(taskStatus);

        // 更新缓存
        cacheTaskStatus(taskStatus);

        // 添加到历史记录
        addToHistory(taskStatus);

        log.info("Updated task: {} to status: {} with message: {}", taskId, status.getCode(), message);
    }

    @Override
    public void updateTaskProgress(String taskId, Integer progress, String currentStep, String message) {
        TaskStatus taskStatus = getTaskFromCache(taskId);
        if (taskStatus == null) {
            taskStatus = getTaskFromDatabase(taskId);
            if (taskStatus == null) {
                log.warn("Task not found: {}", taskId);
                return;
            }
        }

        taskStatus.setProgress(progress);
        taskStatus.setCurrentStep(currentStep);
        taskStatus.setMessage(message);
        taskStatus.setUpdateTime(LocalDateTime.now());

        // 更新数据库
        updateTaskInDatabase(taskStatus);

        // 更新缓存
        cacheTaskStatus(taskStatus);

        // 添加到历史记录
        addToHistory(taskStatus);

        log.info("Updated task progress: {} to {}% - {}", taskId, progress, currentStep);
    }

    @Override
    public void setTaskError(String taskId, String errorMessage) {
        updateTaskStatus(taskId, TaskStatusEnum.FAILED, errorMessage, true);
        
        TaskStatus taskStatus = getTaskFromCache(taskId);
        if (taskStatus != null) {
            taskStatus.setErrorMessage(errorMessage);
            updateTaskInDatabase(taskStatus);
            cacheTaskStatus(taskStatus);
        }
    }

    @Override
    public void completeTask(String taskId, String message) {
        updateTaskStatus(taskId, TaskStatusEnum.COMPLETED, message, true);
    }

    @Override
    public TaskStatusVO getTaskStatus(String taskId) {
        TaskStatus taskStatus = getTaskFromCache(taskId);
        if (taskStatus == null) {
            taskStatus = getTaskFromDatabase(taskId);
            if (taskStatus != null) {
                cacheTaskStatus(taskStatus);
            }
        }

        if (taskStatus == null) {
            return null;
        }

        return convertToVO(taskStatus);
    }

    @Override
    public List<TaskStatusVO> getTaskHistory(String taskId) {
        String historyKey = TASK_HISTORY_KEY_PREFIX + taskId;
        List<Object> history = redisTemplate.opsForList().range(historyKey, 0, -1);
        
        if (history == null || history.isEmpty()) {
            // 如果Redis中没有历史记录，从数据库查询
            List<TaskStatus> dbHistory = taskStatusMapper.selectList(
                new LambdaQueryWrapper<TaskStatus>()
                    .eq(TaskStatus::getTaskId, taskId)
                    .orderByDesc(TaskStatus::getUpdateTime)
            );
            return dbHistory.stream().map(this::convertToVO).collect(Collectors.toList());
        }

        return history.stream()
            .filter(obj -> obj instanceof TaskStatus)
            .map(obj -> convertToVO((TaskStatus) obj))
            .collect(Collectors.toList());
    }

    @Override
    public List<TaskStatusVO> getActiveTasksByType(TaskTypeEnum taskType) {
        List<TaskStatus> tasks = taskStatusMapper.findActiveTasksByType(taskType.getCode());
        return tasks.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public List<TaskStatusVO> getTasksByMonitorUnitId(Integer monitorUnitId) {
        List<TaskStatus> tasks = taskStatusMapper.findTasksByMonitorUnitId(monitorUnitId);
        return tasks.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public void deleteTask(String taskId) {
        // 从数据库删除
        taskStatusMapper.delete(new LambdaQueryWrapper<TaskStatus>().eq(TaskStatus::getTaskId, taskId));
        
        // 从Redis删除
        redisTemplate.delete(TASK_STATUS_KEY_PREFIX + taskId);
        redisTemplate.delete(TASK_HISTORY_KEY_PREFIX + taskId);
        
        log.info("Deleted task: {}", taskId);
    }

    @Override
    @Scheduled(fixedRate = 3600000) // 每小时执行一次
    public void cleanupExpiredTasks() {
        LocalDateTime expireTime = LocalDateTime.now().minusDays(DB_EXPIRE_DAYS);
        int deletedCount = taskStatusMapper.deleteExpiredTasks(expireTime);
        if (deletedCount > 0) {
            log.info("Cleaned up {} expired tasks", deletedCount);
        }
    }

    @Override
    public void sendMessage(String taskId, TaskTypeEnum taskType, String message, Boolean isFinal) {
        TaskStatusEnum status = isFinal ? TaskStatusEnum.COMPLETED : TaskStatusEnum.RUNNING;
        updateTaskStatus(taskId, status, message, isFinal);
    }

    @Override
    public void sendMessageWithProgress(String taskId, TaskTypeEnum taskType, String message, Integer progress, Boolean isFinal) {
        TaskStatus taskStatus = getTaskFromCache(taskId);
        if (taskStatus == null) {
            // 如果任务不存在，创建新任务
            createTask(taskId, taskType, null);
        }
        
        TaskStatusEnum status = isFinal ? TaskStatusEnum.COMPLETED : TaskStatusEnum.RUNNING;
        updateTaskStatus(taskId, status, message, isFinal);
        updateTaskProgress(taskId, progress, message, message);
    }

    // 私有辅助方法
    private TaskStatus getTaskFromCache(String taskId) {
        String key = TASK_STATUS_KEY_PREFIX + taskId;
        Object cached = redisTemplate.opsForValue().get(key);
        return cached instanceof TaskStatus ? (TaskStatus) cached : null;
    }

    private TaskStatus getTaskFromDatabase(String taskId) {
        return taskStatusMapper.selectOne(
            new LambdaQueryWrapper<TaskStatus>().eq(TaskStatus::getTaskId, taskId)
        );
    }

    private void cacheTaskStatus(TaskStatus taskStatus) {
        String key = TASK_STATUS_KEY_PREFIX + taskStatus.getTaskId();
        redisTemplate.opsForValue().set(key, taskStatus, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
    }

    private void updateTaskInDatabase(TaskStatus taskStatus) {
        taskStatusMapper.update(taskStatus, 
            new LambdaUpdateWrapper<TaskStatus>().eq(TaskStatus::getTaskId, taskStatus.getTaskId())
        );
    }

    private void addToHistory(TaskStatus taskStatus) {
        String historyKey = TASK_HISTORY_KEY_PREFIX + taskStatus.getTaskId();
        redisTemplate.opsForList().leftPush(historyKey, taskStatus);
        redisTemplate.expire(historyKey, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
        
        // 限制历史记录数量
        redisTemplate.opsForList().trim(historyKey, 0, 99); // 保留最近100条记录
    }

    private TaskStatusVO convertToVO(TaskStatus taskStatus) {
        TaskStatusVO vo = new TaskStatusVO();
        BeanUtils.copyProperties(taskStatus, vo);
        return vo;
    }
}
