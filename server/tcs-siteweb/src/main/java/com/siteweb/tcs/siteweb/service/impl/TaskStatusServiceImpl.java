package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.tcs.siteweb.entity.TaskStatus;
import com.siteweb.tcs.siteweb.enums.TaskStatusEnum;
import com.siteweb.tcs.siteweb.enums.TaskTypeEnum;
import com.siteweb.tcs.siteweb.mapper.TaskStatusMapper;
import com.siteweb.tcs.siteweb.service.ITaskStatusService;
import com.siteweb.tcs.siteweb.vo.TaskStatusVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 任务状态服务实现类
 * 使用数据库持久化的方式管理任务状态
 */
@Slf4j
@Service
public class TaskStatusServiceImpl implements ITaskStatusService {

    @Autowired
    private TaskStatusMapper taskStatusMapper;

    private static final long DB_EXPIRE_DAYS = 7; // 数据库保存7天

    @Override
    public TaskStatus createTask(String taskId, TaskTypeEnum taskType, String monitorUnitId) {
        TaskStatus taskStatus = new TaskStatus();
        taskStatus.setTaskId(taskId);
        taskStatus.setTaskType(taskType.getCode());
        taskStatus.setMonitorUnitId(monitorUnitId);
        taskStatus.setStatus(TaskStatusEnum.PENDING.getCode());
        taskStatus.setProgress(0);
        taskStatus.setIsFinal(false);
        taskStatus.setCreateTime(LocalDateTime.now());
        taskStatus.setUpdateTime(LocalDateTime.now());
        taskStatus.setExpireTime(LocalDateTime.now().plusDays(DB_EXPIRE_DAYS));

        // 保存到数据库
        taskStatusMapper.insert(taskStatus);

        log.info("Created task: {} of type: {}", taskId, taskType.getCode());
        return taskStatus;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateTaskStatus(String taskId, TaskStatusEnum status, String message, Boolean isFinal) {
        TaskStatus taskStatus = getTaskFromDatabase(taskId);
        if (taskStatus == null) {
            log.warn("Task not found: {}", taskId);
            return;
        }

        taskStatus.setStatus(status.getCode());
        taskStatus.setMessage(message);
        taskStatus.setIsFinal(isFinal);
        taskStatus.setUpdateTime(LocalDateTime.now());

        updateTaskInDatabase(taskStatus);

        log.info("Updated task: {} to status: {} with message: {}", taskId, status.getCode(), message);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateTaskProgress(String taskId, Integer progress, String currentStep, String message) {
        TaskStatus taskStatus = getTaskFromDatabase(taskId);
        if (taskStatus == null) {
            log.warn("Task not found: {}", taskId);
            return;
        }

        taskStatus.setProgress(progress);
        taskStatus.setCurrentStep(currentStep);
        taskStatus.setMessage(message);
        taskStatus.setUpdateTime(LocalDateTime.now());

        updateTaskInDatabase(taskStatus);

        log.info("Updated task progress: {} to {}% - {}", taskId, progress, currentStep);
    }

    @Override
    public void setTaskError(String taskId, String errorMessage) {
        updateTaskStatus(taskId, TaskStatusEnum.FAILED, errorMessage, true);

        TaskStatus taskStatus = getTaskFromDatabase(taskId);
        if (taskStatus != null) {
            taskStatus.setErrorMessage(errorMessage);
            taskStatus.setIsFinal(Boolean.TRUE);
            updateTaskInDatabase(taskStatus);
        }
    }

    @Override
    public void completeTask(String taskId, String message) {
        updateTaskStatus(taskId, TaskStatusEnum.COMPLETED, message, true);
    }

    @Override
    public TaskStatusVO getTaskStatus(String taskId) {
        TaskStatus taskStatus = getTaskFromDatabase(taskId);

        if (taskStatus == null) {
            return null;
        }

        return convertToVO(taskStatus);
    }

    @Override
    public List<TaskStatusVO> getTaskHistory(String taskId) {
        List<TaskStatus> dbHistory = taskStatusMapper.selectList(
                new LambdaQueryWrapper<TaskStatus>()
                        .eq(TaskStatus::getTaskId, taskId)
                        .orderByDesc(TaskStatus::getUpdateTime)
        );
        return dbHistory.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public List<TaskStatusVO> getActiveTasksByType(TaskTypeEnum taskType) {
        List<TaskStatus> tasks = taskStatusMapper.selectList(new LambdaQueryWrapper<TaskStatus>().eq(TaskStatus::getTaskType, taskType.getCode()));
        return tasks.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public List<TaskStatusVO> getTasksByMonitorUnitId(Integer monitorUnitId) {
        List<TaskStatus> tasks = taskStatusMapper.selectList(new LambdaQueryWrapper<TaskStatus>().like(TaskStatus::getMonitorUnitId, String.valueOf(monitorUnitId)));
        return tasks.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public void deleteTask(String taskId) {
        taskStatusMapper.delete(new LambdaQueryWrapper<TaskStatus>().eq(TaskStatus::getTaskId, taskId));
        log.info("Deleted task: {}", taskId);
    }

    @Override
    public void cleanupExpiredTasks() {
        LocalDateTime expireTime = LocalDateTime.now().minusDays(DB_EXPIRE_DAYS);
        int deletedCount = taskStatusMapper.delete(new LambdaQueryWrapper<TaskStatus>().lt(TaskStatus::getExpireTime, expireTime));
        if (deletedCount > 0) {
            log.info("Cleaned up {} expired tasks", deletedCount);
        }
    }

    @Override
    public void sendMessage(String taskId, TaskTypeEnum taskType, String message, Boolean isFinal) {
        TaskStatusEnum status = isFinal ? TaskStatusEnum.COMPLETED : TaskStatusEnum.RUNNING;
        updateTaskStatus(taskId, status, message, isFinal);
    }

    @Override
    public void sendMessageWithProgress(String taskId, TaskTypeEnum taskType, String message, Integer progress, Boolean isFinal) {
        TaskStatus taskStatus = getTaskFromDatabase(taskId);
        if (taskStatus == null) {
            // 如果任务不存在，创建新任务
            createTask(taskId, taskType, null);
        }

        TaskStatusEnum status = isFinal ? TaskStatusEnum.COMPLETED : TaskStatusEnum.RUNNING;
        updateTaskStatus(taskId, status, message, isFinal);
        updateTaskProgress(taskId, progress, message, message);
    }

    @Override
    public Page<TaskStatusVO> pageTaskHistory(Page<TaskStatus> page, String taskType) {
        // 构建查询条件
        LambdaQueryWrapper<TaskStatus> queryWrapper = new LambdaQueryWrapper<>();

        if (StringUtils.hasText(taskType)) {
            queryWrapper.eq(TaskStatus::getTaskType, taskType);
        }

        queryWrapper.orderByDesc(TaskStatus::getCreateTime);

        // 查询数据库
        Page<TaskStatus> taskPage = taskStatusMapper.selectPage(page, queryWrapper);

        // 转换为VO
        Page<TaskStatusVO> voPage = new Page<>();
        voPage.setCurrent(taskPage.getCurrent());
        voPage.setSize(taskPage.getSize());
        voPage.setTotal(taskPage.getTotal());
        voPage.setPages(taskPage.getPages());

        List<TaskStatusVO> voList = taskPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        voPage.setRecords(voList);

        return voPage;
    }

    @Override
    public TaskStatus getTaskFromDatabase(String taskId) {
        return taskStatusMapper.selectOne(
                new LambdaQueryWrapper<TaskStatus>().eq(TaskStatus::getTaskId, taskId)
        );
    }

    @Override
    public void updateTaskInDatabase(TaskStatus taskStatus) {
        taskStatusMapper.update(taskStatus,
                new LambdaUpdateWrapper<TaskStatus>().eq(TaskStatus::getTaskId, taskStatus.getTaskId())
        );
    }

    private TaskStatusVO convertToVO(TaskStatus taskStatus) {
        TaskStatusVO vo = new TaskStatusVO();
        BeanUtils.copyProperties(taskStatus, vo);
        return vo;
    }

    // ==================== 新增的简化增强方法实现 ====================

    @Override
    public void setTaskErrorSimplified(String taskId, String errorMessage) {
        // 简化错误消息，避免过长的错误信息
        String simplifiedMessage = simplifyErrorMessage(errorMessage);

        TaskStatus task = getTaskFromDatabase(taskId);
        if (task != null) {
            task.setStatus(TaskStatusEnum.FAILED.getCode());
            task.setMessage(simplifiedMessage);
            task.setErrorMessage(simplifiedMessage);
            task.setIsFinal(true);
            task.setUpdateTime(LocalDateTime.now());

            updateTaskInDatabase(task);
            log.info("Set simplified task error: taskId={}, message={}", taskId, simplifiedMessage);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void completeTaskWithCounts(String taskId, int totalCount, int successCount, int failureCount, String message) {
        TaskStatus task = getTaskFromDatabase(taskId);
        if (task == null) {
            log.warn("Task not found: {}", taskId);
            return;
        }

        // 根据成功失败情况确定最终状态
        TaskStatusEnum finalStatus;
        if (totalCount == 0) {
            finalStatus = TaskStatusEnum.FAILED;
        } else if (failureCount == 0) {
            finalStatus = TaskStatusEnum.COMPLETED;  // 全部成功
        } else if (successCount > 0) {
            finalStatus = TaskStatusEnum.COMPLETED_PARTIAL;  // 部分成功
        } else {
            finalStatus = TaskStatusEnum.COMPLETED_FAILED;   // 全部失败
        }

        task.setStatus(finalStatus.getCode());
        task.setMessage(message);
        task.setIsFinal(true);
        task.setUpdateTime(LocalDateTime.now());

        updateTaskInDatabase(task);

        log.info("Task completed: taskId={}, status={}, total={}, success={}, failed={}",
                taskId, finalStatus.getCode(), totalCount, successCount, failureCount);
    }

    /**
     * 简化错误消息，避免过长的错误信息
     */
    private String simplifyErrorMessage(String originalMessage) {
        if (originalMessage == null || originalMessage.length() <= 100) {
            return originalMessage;
        }

        // 提取关键错误信息
        if (originalMessage.contains("Connection refused")) {
            return "连接被拒绝";
        } else if (originalMessage.contains("timeout") || originalMessage.contains("Timeout")) {
            return "连接超时";
        } else if (originalMessage.contains("Authentication failed") || originalMessage.contains("login")) {
            return "认证失败";
        } else if (originalMessage.contains("No such file") || originalMessage.contains("FileNotFoundException")) {
            return "文件不存在";
        } else if (originalMessage.contains("Permission denied")) {
            return "权限不足";
        } else if (originalMessage.contains("Network is unreachable")) {
            return "网络不可达";
        } else {
            // 截取前100个字符
            return originalMessage.substring(0, 100) + "...";
        }
    }
}
