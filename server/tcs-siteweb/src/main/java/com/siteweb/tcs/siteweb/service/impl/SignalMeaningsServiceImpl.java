package com.siteweb.tcs.siteweb.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.constants.SignalConstant;
import com.siteweb.tcs.siteweb.entity.SignalMeanings;
import com.siteweb.tcs.siteweb.mapper.SignalMeaningsMapper;
import com.siteweb.tcs.siteweb.service.IChangeEventService;
import com.siteweb.tcs.siteweb.service.ISignalMeaningsService;
import com.siteweb.tcs.siteweb.util.I18n;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Service
public class SignalMeaningsServiceImpl extends ServiceImpl<SignalMeaningsMapper, SignalMeanings> implements ISignalMeaningsService {

    @Autowired
    private IChangeEventService changeEventService;

    @Autowired
    private I18n i18n;

    @Override
    public List<SignalMeanings> parseSignalMeaningStr(Integer equipmentTemplateId, Integer signalId, String[] meaningsArr) {
        List<SignalMeanings> signalMeaningsList = new ArrayList<>(meaningsArr.length);
        for (String meaning : meaningsArr) {
            String[] meaningArr = meaning.split(":");
            if (!NumberUtil.isNumber(meaningArr[0])){
                continue;
            }
            SignalMeanings signalMeanings = new SignalMeanings();
            signalMeanings.setEquipmentTemplateId(equipmentTemplateId);
            signalMeanings.setSignalId(signalId);
            signalMeanings.setStateValue(Integer.valueOf(meaningArr[0]));
            signalMeanings.setMeanings(meaningArr[1]);
            //注意：预制的十个模板的XML文件中可能没有信号含义的基类信息
            if (meaningArr.length == 3 && CharSequenceUtil.isNotBlank(meaningArr[2])) {
                signalMeanings.setBaseCondId(new BigDecimal(meaningArr[2]));
            }
            signalMeaningsList.add(signalMeanings);
        }
        return signalMeaningsList;
    }

    @Override
    public void batchCreateSignalMeanings(List<SignalMeanings> signalMeaningList) {
        if (CollUtil.isEmpty(signalMeaningList)) {
            return;
        }
        saveBatch(signalMeaningList);
    }

    @Override
    public void communicationStateSignalMeaning(Integer equipmentTemplateId) {
        //0:通讯异常:0", "1:通讯正常:1
        String[] meaningsArr = {"0:" + i18n.T("monitor.equipment.communicationFail") + ":0", "1:" + i18n.T("monitor.equipment.communicationOk") + ":1"};
        List<SignalMeanings> signalMeaningsList = parseSignalMeaningStr(equipmentTemplateId, SignalConstant.COMMUNICATION_STATE_SIGNAL, meaningsArr);
        batchCreateSignalMeanings(signalMeaningsList);
    }

    @Override
    public List<SignalMeanings> findByEquipmentTemplateIdAndSignalId(Integer equipmentTemplateId, Integer signalId) {
        return list(new QueryWrapper<SignalMeanings>()
                .eq("EquipmentTemplateId", equipmentTemplateId)
                .eq("SignalId", signalId));
    }

    @Override
    public void deleteByEquipmentTemplateId(Integer equipmentTemplateId) {
        remove(new QueryWrapper<SignalMeanings>()
                .eq("EquipmentTemplateId", equipmentTemplateId));
    }

    @Override
    public List<SignalMeanings> findByEquipmentTemplateId(Integer equipmentTemplateId) {
        return list(new QueryWrapper<SignalMeanings>()
                .eq("EquipmentTemplateId", equipmentTemplateId));
    }

    @Override
    public void copySignalMeanings(Integer originEquipmentTemplateId, Integer destEquipmentTemplateId) {
        List<SignalMeanings> signalMeaningsList = findByEquipmentTemplateId(originEquipmentTemplateId);
        signalMeaningsList.forEach(meanings -> {
            meanings.setId(null); // 清空ID以便插入新记录
            meanings.setEquipmentTemplateId(destEquipmentTemplateId);
        });
        batchCreateSignalMeanings(signalMeaningsList);
    }

    @Override
    public void updateSignalMeanings(Integer equipmentTemplateId, Integer signalId, List<SignalMeanings> signalMeaningsList) {
        deleteByEquipmentIdAndSignalId(equipmentTemplateId, signalId);
        if (CollUtil.isEmpty(signalMeaningsList)) {
            return;
        }
        batchCreateSignalMeanings(signalMeaningsList);
    }

    private void deleteByEquipmentIdAndSignalId(Integer equipmentTemplateId, Integer signalId) {
        remove(new QueryWrapper<SignalMeanings>()
                .eq("EquipmentTemplateId", equipmentTemplateId)
                .eq("SignalId", signalId));
    }

    @Override
    public boolean batchsaveLianTongSignalMeanings() {
        try {
            // 这里实现批量保存联通信号含义的逻辑
            // 由于没有具体的业务需求，这里提供一个空实现
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public void deleteByEquipmentTemplateIdAndSignalId(Integer equipmentTemplateId, Integer signalId) {
        deleteByEquipmentIdAndSignalId(equipmentTemplateId, signalId);
    }
}