package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.dto.MonitorUnitDTO;
import com.siteweb.tcs.siteweb.entity.*;
import com.siteweb.tcs.siteweb.enums.MonitorUnitCategoryEnum;
import com.siteweb.tcs.siteweb.enums.MonitorUnitStateEnum;
import com.siteweb.tcs.siteweb.enums.OperationObjectTypeEnum;
import com.siteweb.tcs.siteweb.enums.TableIdentityEnum;
import com.siteweb.tcs.siteweb.exception.InvalidParameterException;
import com.siteweb.tcs.siteweb.mapper.MonitorUnitMapper;
import com.siteweb.tcs.siteweb.service.*;
import com.siteweb.tcs.siteweb.template.MonitorUnitTemplate;
import com.siteweb.tcs.siteweb.template.dto.monitor.unit.EquipmentTemplateDTO;
import com.siteweb.tcs.siteweb.template.dto.monitor.unit.MonitorUnitTempItemDTO;
import com.siteweb.tcs.siteweb.template.dto.monitor.unit.MonitorUnitTemplateDTO;
import com.siteweb.tcs.siteweb.template.dto.monitor.unit.SamplerUnitTemplateDTO;
import com.siteweb.tcs.siteweb.util.I18n;
import com.siteweb.tcs.siteweb.util.TokenUserSiteWebUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Monitor Unit Service Implementation
 */
@Slf4j
@Service
public class MonitorUnitServiceImpl extends ServiceImpl<MonitorUnitMapper, MonitorUnit> implements IMonitorUnitService {

    @Autowired
    private MonitorUnitMapper monitorUnitMapper;
    
    @Autowired(required = false)
    private IOperationDetailService operationDetailService;
    
    @Autowired
    private IEquipmentService equipmentService;

    @Autowired
    private IPrimaryKeyValueService primaryKeyValueService;

    @Autowired
    private IChangeEventService changeEventService;

    @Autowired
    private MonitorUnitTemplate monitorUnitTemplate;

    @Autowired
    private IEquipmentTemplateService equipmentTemplateService;

    @Autowired
    private ISamplerService samplerService;

    @Autowired
    private ISamplerUnitService samplerUnitService;

    @Autowired
    private IPortService portService;

    @Autowired
    private IHouseService houseService;

    @Autowired
    private I18n i18n;
    
    @Override
    public boolean isCrossSite(Integer monitorUnitId) {
        return monitorUnitMapper.exists(Wrappers.lambdaQuery(MonitorUnit.class)
                .eq(MonitorUnit::getMonitorUnitId, monitorUnitId)
                .eq(MonitorUnit::getMonitorUnitCategory, MonitorUnitCategoryEnum.ACROSS_MU_OF_RMU.getValue()));
    }
    
    @Override
    public boolean isCrossSiteMonitoringUnit(Integer monitorUnitId) {
        // In this implementation, we reuse the isCrossSite method
        // In a real-world scenario, there might be additional checks specific to monitoring units
        return isCrossSite(monitorUnitId);
    }
    
    @Override
    public MonitorUnitDTO findById(Integer monitorUnitId) {
        MonitorUnitDTO monitorUnit = monitorUnitMapper.selectByMonitorUnitId(monitorUnitId);

        return monitorUnit;
    }

    @Override
    public MonitorUnitDTO findByIdWithoutStation(Integer monitorUnitId) {
        return monitorUnitMapper.selectByMonitorUnitIdWithoutStation(monitorUnitId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMonitorUnit(MonitorUnitDTO monitorUnit) {
        verification(monitorUnit);
        
        MonitorUnit entity = getById(monitorUnit.getMonitorUnitId());
        if (entity == null) {
            log.error("Monitor unit not found: {}", monitorUnit.getMonitorUnitId());
            return false;
        }
        int update = monitorUnitMapper.updateDto(monitorUnit);

        if (update == 1 && operationDetailService != null) {
            try {
                operationDetailService.compareEntitiesRecordLog(
                    -1, // User ID, null for system operation
                        entity,
                        monitorUnit
                );
            } catch (Exception e) {
                log.warn("Failed to record operation log for monitor unit update", e);
            }
        }
        
        return update == 1;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMonitorUnit(Integer monitorUnitId) {
        MonitorUnit monitorUnit = getById(monitorUnitId);
        if (monitorUnit == null) {
            log.error("Monitor unit not found: {}", monitorUnitId);
            return false;
        }
        
        boolean deleted = removeById(monitorUnitId);
        
        if (deleted && operationDetailService != null) {
            try {
                operationDetailService.recordOperationLog(
                    null, // User ID, null for system operation
                    monitorUnitId.toString(),
                    OperationObjectTypeEnum.MONITOR_UNIT,
                    "monitor.unit.name",
                    "删除",
                    "",
                    monitorUnit.getMonitorUnitName()
                );
            } catch (Exception e) {
                log.warn("Failed to record operation log for monitor unit deletion", e);
            }
        }
        
        return deleted;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMonitorUnitAndEqs(Integer monitorUnitId) {
        // First delete associated equipment
        try {
            equipmentService.deleteByMonitorUnitId(monitorUnitId);
        } catch (Exception e) {
            log.error("Failed to delete equipment for monitor unit: {}", monitorUnitId, e);
            throw new RuntimeException("Failed to delete equipment", e);
        }
        
        // Then delete the monitor unit
        return deleteMonitorUnit(monitorUnitId);
    }
    
    @Override
    public void verification(MonitorUnitDTO monitorUnit) {
        if (monitorUnit.getMonitorUnitId() == null && monitorUnit.getStationId() == null) {
            throw new InvalidParameterException("Station ID cannot be null");
        }
        
        if (monitorUnit.getMonitorUnitName() == null || monitorUnit.getMonitorUnitName().trim().isEmpty()) {
            throw new InvalidParameterException("Monitor unit name cannot be empty");
        }
        
        // Check IP address format if provided
        if (monitorUnit.getIpAddress() != null && !monitorUnit.getIpAddress().trim().isEmpty()) {
            String ipAddress = monitorUnit.getIpAddress().trim();
            if (!isValidIpAddress(ipAddress)) {
                throw new InvalidParameterException("Invalid IP address format: " + ipAddress);
            }
        }
        
        // Additional validations can be added based on business requirements
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createMonitorUnit(MonitorUnitDTO monitorUnit, Integer resourceStructureId) {
        if (monitorUnit.getMonitorUnitId() == null || monitorUnit.getMonitorUnitId().equals(0)) {
            Integer monitorUnitId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TSL_MONITOR_UNIT, 0);
            monitorUnit.setMonitorUnitId(monitorUnitId);
            monitorUnit.setMonitorUnitCode(monitorUnitId.toString());
        }
        monitorUnit.setUpdateTime(LocalDateTime.now());
        int rows = monitorUnitMapper.insertDto(monitorUnit);
        if (rows > 0) {
            changeEventService.sendCreate(monitorUnit);
            parseTemplate(monitorUnit, resourceStructureId);
            operationDetailService.recordOperationLog(TokenUserSiteWebUtil.getLoginUserId(), monitorUnit.getMonitorUnitId().toString(), OperationObjectTypeEnum.MONITOR_UNIT, i18n.T("monitor.unit.name"), i18n.T("add"), "", monitorUnit.getMonitorUnitName());
        }
        return rows > 0;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createMonitorUnitV3(MonitorUnitDTO monitorUnit) {
        // For V3 API, we're basically reusing the same method but handling resource structure differently
        return createMonitorUnit(monitorUnit, null);
    }
    
    @Override
    public List<MonitorUnitDTO> findByIds(List<Integer> monitorUnitIds) {
        if (monitorUnitIds == null || monitorUnitIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<MonitorUnitDTO> monitorUnits = monitorUnitMapper.selectByMonitorIds(monitorUnitIds);
        
        return monitorUnits;
    }
    
    @Override
    public List<MonitorUnitDTO> findAllMonitorUnit() {
        // monitorUnitCategory == 0 的是中心自诊断监控单元,需要忽略,monitorUnitStateManager获取监控单元状态
        return monitorUnitMapper.selectAll().stream()
                .filter(e -> !Objects.equals(e.getMonitorUnitCategory(), 0))
                .peek(e -> {
                    //todo 后续确认状态值 xsx
                    e.setState(MonitorUnitStateEnum.PENDING.getValue());
                })
                .collect(Collectors.toList());
    }
    @Override
    public List<MonitorUnitDTO> findAllMonitorUnitWithoutStation() {
        // monitorUnitCategory == 0 的是中心自诊断监控单元,需要忽略,monitorUnitStateManager获取监控单元状态
        return monitorUnitMapper.selectAllWithoutStation().stream()
                .filter(e -> !Objects.equals(e.getMonitorUnitCategory(), 0))
                .peek(e -> {
                    //todo 后续确认状态值 xsx
                    e.setState(MonitorUnitStateEnum.PENDING.getValue());
                })
                .collect(Collectors.toList());
    }
    
    /**
     * Validate IP address format
     *
     * @param ip IP address string
     * @return true if valid, false otherwise
     */
    private boolean isValidIpAddress(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }
        
        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }
        
        for (String part : parts) {
            try {
                int value = Integer.parseInt(part);
                if (value < 0 || value > 255) {
                    return false;
                }
            } catch (NumberFormatException e) {
                return false;
            }
        }
        
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    private void parseTemplate(MonitorUnitDTO monitorUnit, Integer resourceStructureId) {
        // 按照模板创建相关配套
        MonitorUnitTemplateDTO template = monitorUnitTemplate.getTemplate(monitorUnit.getMonitorUnitCategory());
        if (template == null) return;
        
        // 判断如果是rmu下mu，则需创建house和houst设备，无需创建端口和采集单元
        if (monitorUnit.getMonitorUnitCategory() == MonitorUnitCategoryEnum.MU_OF_RMU.getValue() || 
            monitorUnit.getMonitorUnitCategory() == MonitorUnitCategoryEnum.ACROSS_MU_OF_RMU.getValue()) {
            Equipment equipment = new Equipment();
            equipment.setStationId(monitorUnit.getStationId());
            equipment.setMonitorUnitId(monitorUnit.getMonitorUnitId());
            equipment.setEquipmentName(template.getItems().get(0).getEquipment().getEquipmentName() + "-" + monitorUnit.getMonitorUnitName());
            equipment.setEquipmentNo("");
            EquipmentTemplate equipmentTemplate = equipmentTemplateService.findByName(template.getItems().get(0).getEquipment().getEquipmentTemplateName());
            equipment.setEquipmentTemplateId(equipmentTemplate.getEquipmentTemplateId());
            equipment.setEquipmentType(template.getItems().get(0).getEquipment().getEquipmentType());
            equipment.setEquipmentCategory(template.getItems().get(0).getEquipment().getEquipmentCategory());
            equipment.setEquipmentClass(-1);
            equipment.setEquipmentState(1);
            equipment.setDisplayIndex(0);
            equipment.setInstalledModule("");
            equipment.setResourceStructureId(resourceStructureId);
            House house = houseService.findStationDefaultHouse(monitorUnit.getStationId());
            if (house == null) {
                equipment.setHouseId(-1);
            } else {
                equipment.setHouseId(house.getHouseId());
            }
            equipment.setConnectState(2);
            equipment.setWorkStationId(null);
            equipment.setSamplerUnitId(50000);
            equipment.setUpdateTime(LocalDateTime.now());
            equipmentService.createEquipment(equipment);
            return;
        }
        
        // 一次性创建标准接口
        if (template.getStandardPortNum() == null) {
            template.setStandardPortNum(0);
        }
        
        // 创建自诊断设备和IO设备
        for (MonitorUnitTempItemDTO item : template.getItems()) {
            Port port = null;
            SamplerUnit samplerUnit = null;

            if (item.getPort() != null) {
                port = new Port();
                port.setMonitorUnitId(monitorUnit.getMonitorUnitId());
                port.setPortNo(item.getPort().getPortNo());
                port.setPortType(item.getPort().getPortType());
                port.setSetting(item.getPort().getSetting());
                port.setPortName("COM" + item.getPort().getPortNo());
                port.setLinkSamplerUnitId(0);
                port = portService.createPort(port);
            }

            SamplerUnitTemplateDTO samplerUnitDTO = item.getSamplerUnit();
            if (samplerUnitDTO != null && port != null) {
                samplerUnit = new SamplerUnit();
                samplerUnit.setMonitorUnitId(monitorUnit.getMonitorUnitId());
                samplerUnit.setPortId(port.getPortId());
                samplerUnit.setDllPath(samplerUnitDTO.getDllPath());
                if (samplerUnitDTO.getDllPath().equals("WorkStationHOST.so")) {
                    samplerUnit.setDllPath("SPARKHOST.so");
                }
                Sampler sampler = samplerService.findByNameAndDllPath(samplerUnitDTO.getSamplerUnitName(), samplerUnitDTO.getDllPath());
                if (sampler == null) {
                    continue;
                }
                samplerUnit.setSamplerType(sampler.getSamplerType());
                samplerUnit.setSamplerId(sampler.getSamplerId());
                samplerUnit.setParentSamplerUnitId(0);
                samplerUnit.setSamplerUnitName(item.getSamplerUnit().getSamplerUnitName());
                samplerUnit.setAddress(item.getSamplerUnit().getAddress());
                samplerUnit.setSpUnitInterval(item.getSamplerUnit().getSpUnitInterval());
                samplerUnit.setConnectState(0);
                samplerUnit.setUpdateTime(LocalDateTime.now());
                samplerUnit.setPhoneNumber("");
                samplerUnit.setDescription("");
                samplerUnit = samplerUnitService.createSamplerUnit(samplerUnit);
            }
            
            EquipmentTemplateDTO equipmentDTO = item.getEquipment();
            if (equipmentDTO != null) {
                Integer samplerUnitId = null;
                if (equipmentDTO.getEquipmentTemplateName().equals("WorkStation-Host设备")) {
                    equipmentDTO.setEquipmentTemplateName("SPARK-HOST设备");
                }
                if (equipmentDTO.getEquipmentTemplateName().equals("RMU-MUHOST设备")) {
                    samplerUnitId = 50000;
                } else if (samplerUnit != null) {
                    samplerUnitId = samplerUnit.getSamplerUnitId();
                }
                EquipmentTemplate eqTemplate = equipmentTemplateService.findByName(equipmentDTO.getEquipmentTemplateName());
                if (eqTemplate != null) {
                    Equipment equipment = new Equipment();
                    equipment.setStationId(monitorUnit.getStationId());
                    equipment.setMonitorUnitId(monitorUnit.getMonitorUnitId());
                    equipment.setSamplerUnitId(samplerUnitId);
                    equipment.setEquipmentName(equipmentDTO.getEquipmentName() + "-" + monitorUnit.getMonitorUnitName());
                    equipment.setEquipmentNo("");
                    equipment.setEquipmentTemplateId(eqTemplate.getEquipmentTemplateId());
                    equipment.setEquipmentType(eqTemplate.getEquipmentType());
                    equipment.setEquipmentCategory(eqTemplate.getEquipmentCategory());
                    equipment.setEquipmentState(1);
                    equipment.setDisplayIndex(0);
                    equipment.setInstalledModule("");
                    equipment.setResourceStructureId(resourceStructureId);
                    House house = houseService.findStationDefaultHouse(monitorUnit.getStationId());
                    if (house != null) {
                        equipment.setHouseId(house.getHouseId());
                    } else {
                        equipment.setHouseId(-1);
                    }
                    equipment.setConnectState(2);
                    equipment.setWorkStationId(null);
                    equipment.setUpdateTime(LocalDateTime.now());
                    equipmentService.createEquipment(equipment);
                }
            }
        }
    }

    @Override
    public void updateMonitorUnitConfigFileCode(Integer monitorUnitId, String configFileCode) {
        update(new LambdaUpdateWrapper<>(MonitorUnit.class)
                .set(MonitorUnit::getConfigFileCode, configFileCode)
                .set(MonitorUnit::getIsConfigOK, true)
                .eq(MonitorUnit::getMonitorUnitId, monitorUnitId));
    }

    @Override
    public List<MonitorUnitDTO> findByWorkStationId(Integer workStationId) {
        if (workStationId == null) {
            log.warn("Cannot find monitor units by work station ID: work station ID is null");
            return new ArrayList<>();
        }

        return monitorUnitMapper.selectList(new QueryWrapper<MonitorUnit>()
                .eq("WorkStationId", workStationId))
                .stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    private MonitorUnitDTO convertToDTO(MonitorUnit monitorUnit) {
        // 简单的转换逻辑，实际项目中可能需要更复杂的映射
        MonitorUnitDTO dto = new MonitorUnitDTO();
        dto.setMonitorUnitId(monitorUnit.getMonitorUnitId());
        dto.setMonitorUnitName(monitorUnit.getMonitorUnitName());
        dto.setMonitorUnitCode(monitorUnit.getMonitorUnitCode());
        dto.setMonitorUnitCategory(monitorUnit.getMonitorUnitCategory());
        dto.setWorkStationId(monitorUnit.getWorkStationId());
        dto.setStationId(monitorUnit.getStationId());
        dto.setIpAddress(monitorUnit.getIpAddress());
        dto.setRunMode(monitorUnit.getRunMode());
        dto.setConfigFileCode(monitorUnit.getConfigFileCode());
        dto.setSampleConfigCode(monitorUnit.getSampleConfigCode());
        dto.setSoftwareVersion(monitorUnit.getSoftwareVersion());
        dto.setDescription(monitorUnit.getDescription());
        dto.setDataServer(monitorUnit.getDataServer());
        dto.setRdsServer(monitorUnit.getRdsServer());
        dto.setAppConfigId(monitorUnit.getAppConfigId());
        return dto;
    }

}
