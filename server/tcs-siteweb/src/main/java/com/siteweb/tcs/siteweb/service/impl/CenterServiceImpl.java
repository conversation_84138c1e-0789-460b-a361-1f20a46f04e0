package com.siteweb.tcs.siteweb.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.siteweb.tcs.siteweb.dto.*;
import com.siteweb.tcs.siteweb.entity.*;
import com.siteweb.tcs.siteweb.enums.*;
import com.siteweb.tcs.siteweb.exception.BusinessException;
import com.siteweb.tcs.siteweb.service.*;
import com.siteweb.tcs.siteweb.util.I18n;
import com.siteweb.tcs.siteweb.util.TokenUserSiteWebUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.RandomUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 监控中心服务实现类
 */
@Service
@Slf4j
public class CenterServiceImpl implements ICenterService {

    @Autowired
    private I18n i18n;

    @Autowired
    private IDataItemService dataItemService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private IStandardTypeService standardTypeService;

    @Autowired
    private IStationBaseMapService stationBaseMapService;

    @Autowired
    private IBaseEquipmentCategoryMapService baseEquipmentCategoryMapService;

    @Autowired
    private IStationBaseTypeService stationBaseTypeService;

    @Autowired
    private IEquipmentService equipmentService;

    @Autowired
    private IEquipmentTemplateService equipmentTemplateService;

    @Autowired
    private ISamplerUnitService samplerUnitService;

    @Autowired
    private ISignalService  signalService;

    @Autowired
    private IEventService eventService;

    @Autowired
    private IControlService controlService;

    @Autowired
    private ISamplerService samplerService;

    @Autowired
    private ICategoryIdMapService categoryIdMapService;

    @Autowired
    private IEventBaseDicService eventBaseDicService;

    @Autowired
    private ILogicClassEntryService logicClassEntryService;

    @Autowired
    private ISignalMeaningsService signalMeaningsService;

    @Autowired
    private ISignalPropertyService signalPropertyService;

    @Autowired
    private IEventConditionService eventConditionService;

    @Autowired
    private IControlMeaningsService controlMeaningsService;

    @Autowired
    private IDbVersionRecordService dbVersionRecordService;

    // 新增的依赖注入
    @Autowired
    private IPrimaryKeyValueService primaryKeyValueService;

    @Autowired
    private IStationStructureService stationStructureService;

    @Autowired
    private IStationService stationService;

    @Autowired
    private IChangeEventService changeEventService;

    @Autowired
    private IStationStructureMapService stationStructureMapService;

    @Autowired
    private IWorkStationService workStationService;

    @Autowired
    private IResourceStructureService resourceStructureService;

    @Autowired
    private IHouseService houseService;

    @Autowired
    private Environment environment;

    @Autowired
    private IEquipmentTemplateXmlService equipmentTemplateXmlService;

    @Autowired
    private IMonitorUnitService monitorUnitService;
    
    @Autowired
    private IPortService portService;
    
    @Autowired
    private IOperationDetailService operationDetailService;

    @Autowired
    private TokenUserSiteWebUtil tokenUserSiteWebUtil;

    @Qualifier(value = "SiteWebAccountService")
    @Autowired
    private IAccountService accountService;

    @Autowired
    private IMonitorUnitConfigService monitorUnitConfigService;

    @Override
    public void create(CenterDTO centerDTO) {
        // 验证参数
        validateCenterDTO(centerDTO);

        // 处理标准类型
        handleStandardType(centerDTO);

        // 创建二级中心
        createSS(centerDTO);

        // 更新主键值
        updatePrimaryIdentityValue(centerDTO);

        // 从环境中获取语言
        String language = getLanguageFromEnvironment();

        // 创建资源结构
        createResourceStructure(centerDTO);

        // 导入工作站自诊断模板
        importWorkStationSelfDiagnosisTemplate(centerDTO, language);

        // 导入预制模板
        importPredefinedTemplates(language);

        // 更新监控单元配置
        updateMonitorUnitConfig(centerDTO);

        // 更新账户中心ID
        updateAccountCenterId(centerDTO);

        // 记录操作日志
        recordOperationLog(centerDTO);
    }

    @Override
    public void createOMCCenter() {
        //如果字典项里面已经有中心的话，就不需要再创建了
        List<TypeItemDTO> center = dataItemService.findTypes(DataEntryEnum.DATA_ENTRY);
        if(!CollectionUtils.isEmpty(center)) return;

        //自己构建中心DTO
        CenterDTO centerDTO = new CenterDTO();
        int centerId = 10 + RandomUtils.nextInt(999); // 0~999
        centerDTO.setCenterId(centerId);
        centerDTO.setCenterName("OMC测试中心");
        centerDTO.setStandardType(0);
        centerDTO.setCenterIp("***********");

        validateCenterDTO(centerDTO);
        // 处理标准类型
        handleStandardType(centerDTO);
        // 主键以及端口
        createSSOMC(centerDTO);
        // 更新主键值
        updatePrimaryIdentityValue(centerDTO);
        // 从环境中获取语言
        String language = getLanguageFromEnvironment();
        // 导入预制模板
        importPredefinedTemplates(language);
        // 记录操作日志
        recordOperationLog(centerDTO);
    }

    /**
     * 创建二级中心
     *
     * @param centerDTO 中心DTO
     */
    private void createSS(CenterDTO centerDTO) {
        createDataEntryItem(centerDTO);
        // 创建中心根节点
        createCenterRootNode(centerDTO);
        createStationStructure(centerDTO);
        createManagementStation(centerDTO);
        // 更新主键值
        updatePrimaryIdentityValue(centerDTO);
        // 创建默认服务器
        workStationService.createDefaultWorkStation(centerDTO);
        // 新增监控单元类别和端口类型
        addMonitorUnitAndPortTypes();
    }


    private void createSSOMC(CenterDTO centerDTO) {
        createDataEntryItem(centerDTO);
        // 更新主键值
        updatePrimaryIdentityValue(centerDTO);
        // 新增监控单元类别和端口类型
        addMonitorUnitAndPortTypes();
    }

    /**
     * 更新主键值
     *
     * @param centerDTO 中心DTO
     */
    private void updatePrimaryIdentityValue(CenterDTO centerDTO) {
        primaryKeyValueService.initPrimaryIdentityValue(centerDTO.getCenterId(), 10000);
    }

    /**
     * 创建资源结构
     *
     * @param centerDTO 中心DTO
     */
    private void createResourceStructure(CenterDTO centerDTO) {
        ResourceStructure rootResourceStructure = new ResourceStructure();
        rootResourceStructure.setStructureTypeId(StructureTypeEnum.PARK.getValue());
        rootResourceStructure.setResourceStructureName(centerDTO.getCenterName());
        rootResourceStructure.setParentResourceStructureId(0);
        rootResourceStructure.setDisplay(Boolean.TRUE);
        rootResourceStructure.setSortValue(1);
        // OriginId设置为局站分组
        rootResourceStructure.setOriginId(centerDTO.getCenterId());
        rootResourceStructure.setOriginParentId(0);
        resourceStructureService.create(rootResourceStructure);

        // 创建一个对应的管理局站
        ResourceStructure manageResourceStructure = new ResourceStructure();
        manageResourceStructure.setStructureTypeId(104);
        manageResourceStructure.setResourceStructureName(centerDTO.getCenterName() + "-" + "管理局站");
        manageResourceStructure.setParentResourceStructureId(rootResourceStructure.getResourceStructureId());
        manageResourceStructure.setDisplay(Boolean.TRUE);
        manageResourceStructure.setSortValue(1);
        // OriginId设置为自诊断局站ID
        manageResourceStructure.setOriginId(-1 * centerDTO.getCenterId());
        manageResourceStructure.setOriginParentId(centerDTO.getCenterId());
        manageResourceStructure.setLevelOfPath(rootResourceStructure.getResourceStructureId().toString());
        resourceStructureService.create(manageResourceStructure);

        // 管理局站下创建一个对应的局房
        ResourceStructure manageHouseResourceStructure = new ResourceStructure();
        manageHouseResourceStructure.setStructureTypeId(StructureTypeEnum.STATION_HOUSE.getValue());
        manageHouseResourceStructure.setResourceStructureName("自诊断设备房");
        manageHouseResourceStructure.setParentResourceStructureId(manageResourceStructure.getResourceStructureId());
        manageHouseResourceStructure.setDisplay(Boolean.TRUE);
        manageHouseResourceStructure.setSortValue(1);
        manageHouseResourceStructure.setLevelOfPath(manageResourceStructure.getLevelOfPath());
        // OriginId设置为自诊断局站ID
        List<House> house = houseService.findHouseByStationId(-1 * centerDTO.getCenterId());
        if (!CollectionUtils.isEmpty(house)) {
            manageHouseResourceStructure.setOriginId(house.get(0).getHouseId());
            manageHouseResourceStructure.setOriginParentId(-1 * centerDTO.getCenterId());
        } else {
            log.warn("House not found for stationId: {}", -1 * centerDTO.getCenterId());
        }
        resourceStructureService.create(manageHouseResourceStructure);
    }

    /**
     * 从环境中获取语言
     *
     * @return
     */
    private String getLanguageFromEnvironment() {
        String property = environment.getProperty("spring.web.locale");
        String language = "";
        if ("zh_CN".equals(property)) {
            language = "ZH";
        } else if ("en_US".equals(property)) {
            language = "EN";
        }
        return language;
    }

    /**
     * 导入工作站自诊断模板
     *
     * @param centerDTO 中心DTO
     * @param language 语言
     */
    public void importWorkStationSelfDiagnosisTemplate(CenterDTO centerDTO, String language) {
        // 构建文件路径
        String fileName = "upload-dir/SampleManage/EquipmentTemplate/" + String.format("EquipmentTemplate%s.xml", language);
        File selftFile = new File(fileName);
        if (!selftFile.exists()) {
            throw new BusinessException(i18n.T("file.not.exist"));
        }

        try {
            // 使用 dom4j 读取 XML
            SAXReader reader = new SAXReader();
            Document document = reader.read(selftFile);

            // 获取根元素
            Element rootElement = document.getRootElement();

            // 导入模板
            EquipmentTemplate equipmentTemplate = equipmentTemplateXmlService.importTemplate(rootElement);

            // 查询数据库服务器工作站
            List<WorkStation> dbWorkStations = workStationService.findByWorkStationType(WorkStationTypeEnum.DATABASE_SERVER);

            // 更新工作站信号名称
            signalService.updateWorkStationSignalName(centerDTO.getCenterName().trim() + centerDTO.getCenterId(), equipmentTemplate.getEquipmentTemplateId());
            signalService.updateSelfDiagnosisSignal(equipmentTemplate.getEquipmentTemplateId(), centerDTO.getCenterId());
            signalService.updateDBWorkStationSignalName(dbWorkStations.get(0).getWorkStationName() + i18n.T("space.alarm.state"), equipmentTemplate.getEquipmentTemplateId(), dbWorkStations.get(0).getWorkStationId());

            // 更新工作站事件名称
            eventService.updateWorkStationEventName(centerDTO.getCenterName().trim() + centerDTO.getCenterId(), equipmentTemplate.getEquipmentTemplateId());
            eventService.updateSelfDiagnosisEvent(equipmentTemplate.getEquipmentTemplateId(), centerDTO.getCenterId());
            eventService.updateDBWorkStationEventName(dbWorkStations.get(0).getWorkStationName() + i18n.T("space.alarm.state"), equipmentTemplate.getEquipmentTemplateId(), dbWorkStations.get(0).getWorkStationId());

            // 根据后台应用服务器的数量添加信号和事件
            initServerEventConfig(equipmentTemplate, WorkStationTypeEnum.BACKGROUD_SERVER);

            // 发送变更通知
            changeEventService.sendUpdate(equipmentTemplate);

            // 创建工作站自诊断虚拟设备
             createWSVirtualEquipment(centerDTO, equipmentTemplate.getEquipmentTemplateId());
        } catch (Exception e) {
            log.error("导入工作站自诊断模板错误：{}", e.getMessage(), e);
            throw new BusinessException(i18n.T("import.template.error"));
        }
    }

    /**
     * 初始化服务器事件配置
     *
     * @param equipmentTemplate 设备模板
     * @param workStationType 工作站类型
     */
    private void initServerEventConfig(EquipmentTemplate equipmentTemplate, WorkStationTypeEnum workStationType) {
        List<WorkStation> siteWebWorkStations = workStationService.findByWorkStationType(workStationType);
        if (CollUtil.isEmpty(siteWebWorkStations)) {
            return;
        }

        // 查询最大信号和事件
        SignalConfigItem signalConfigItem = signalService.findMaxSignalByEquipmentTemplateId(equipmentTemplate.getEquipmentTemplateId());
        EventConfigItem eventConfigItem = eventService.findMaxEventByEquipmentTemplateId(equipmentTemplate.getEquipmentTemplateId());
        EventCondition eventCondition = eventConditionService.findMaxEventConditionByEquipmentTemplateId(equipmentTemplate.getEquipmentTemplateId());

        // 创建信号含义列表
        List<SignalMeanings> signalMeanings = new ArrayList<>();
        List<EventCondition> eventConditions = new ArrayList<>();

        // 遍历工作站创建信号和事件
        for (WorkStation siteWebWorkStation : siteWebWorkStations) {
            // 创建信号
            signalConfigItem.setId(null);
            signalConfigItem.setSignalId(signalConfigItem.getSignalId() + 1);
            signalConfigItem.setSignalName(siteWebWorkStation.getWorkStationName() + i18n.T("working.state"));

            // 创建信号含义
            SignalMeanings runningMeaning = new SignalMeanings();
            runningMeaning.setEquipmentTemplateId(equipmentTemplate.getEquipmentTemplateId());
            runningMeaning.setMeanings(i18n.T("running"));
            runningMeaning.setStateValue(1);
            signalMeanings.add(runningMeaning);

            SignalMeanings shutdownMeaning = new SignalMeanings();
            shutdownMeaning.setEquipmentTemplateId(equipmentTemplate.getEquipmentTemplateId());
            shutdownMeaning.setMeanings(i18n.T("shutdown"));
            shutdownMeaning.setStateValue(2);
            signalMeanings.add(shutdownMeaning);

            signalConfigItem.setSignalMeaningsList(signalMeanings);
            signalService.createSignal(signalConfigItem);

            // 创建事件
            eventConfigItem.setId(null);
            eventConfigItem.setEventId(eventConfigItem.getEventId() + 1);
            eventConfigItem.setEventName(siteWebWorkStation.getWorkStationName() + i18n.T("working.state"));
            eventConfigItem.setStartExpression("[" + "-1" + "," + eventConfigItem.getEventId() + "]");
            eventConfigItem.setSignalId(signalConfigItem.getSignalId());

            // 创建事件条件
            eventCondition.setId(null);
            eventCondition.setBaseTypeId(null);
            eventConditions.add(eventCondition);
            List<EventConditionDTO> eventConditionList = eventConfigItem.getEventConditionList(eventConditions);
            eventConfigItem.setEventConditionList(eventConditionList);
            eventService.createEventByEventItem(eventConfigItem);
        }
    }

    /**
     * 创建工作站自诊断虚拟设备
     *
     * @param centerDTO 中心DTO
     * @param equipmentTemplateId 设备模板ID
     */
    public void createWSVirtualEquipment(CenterDTO centerDTO, int equipmentTemplateId) {
        MonitorUnitDTO tslMonitorUnit = new MonitorUnitDTO();
        int globalIdentity = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TSL_MONITOR_UNIT, 0);
        tslMonitorUnit.setEnable(true);
        tslMonitorUnit.setMonitorUnitCategory(MonitorUnitCategoryEnum.VMU.getValue());
        tslMonitorUnit.setMonitorUnitId(globalIdentity);
        tslMonitorUnit.setMonitorUnitCode(String.valueOf(globalIdentity));
        tslMonitorUnit.setMonitorUnitName(String.format(i18n.T("workstation.selfdiagnostic.monitor"), centerDTO.getCenterName(), centerDTO.getCenterId()));
        tslMonitorUnit.setRunMode(RunModeEnum.NETWORK_MODE.getValue());
        tslMonitorUnit.setStationId(-1 * centerDTO.getCenterId());
        tslMonitorUnit.setAppConfigId(-1);
        tslMonitorUnit.setConnectState(StationStateEnum.ONLINE.getValue());
        tslMonitorUnit.setIsConfigOK(true);
        tslMonitorUnit.setIsSync(false);
        tslMonitorUnit.setUpdateTime(LocalDateTime.now());
        tslMonitorUnit.setCanDistribute(true);
        monitorUnitService.createMonitorUnit(tslMonitorUnit, 0);
        operationDetailService.recordOperationLog(TokenUserSiteWebUtil.getLoginUserId(), tslMonitorUnit.getMonitorUnitId().toString(), OperationObjectTypeEnum.MONITOR_UNIT, i18n.T("monitor.unit.name"), i18n.T("add"), "", tslMonitorUnit.getMonitorUnitName());

        Port Port = new Port();
        int portId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TSL_PORT, 0);
        Port.setPortId(portId);
        Port.setMonitorUnitId(tslMonitorUnit.getMonitorUnitId());
        Port.setPortNo(1);
        Port.setPortName("COM1");
        Port.setPortType(PortTypeEnum.VIRTUAL_PORT.getValue());
        Port.setLinkSamplerUnitId(0);
        Port.setSetting("");
        portService.createPort(Port);
        operationDetailService.recordOperationLog(TokenUserSiteWebUtil.getLoginUserId(), Port.getId().toString(), OperationObjectTypeEnum.PORT, i18n.T("port.name"), i18n.T("add"), "", Port.getPortName());

        SamplerUnit SamplerUnit = new SamplerUnit();
        int samplerUnitId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TSL_SAMPLER_UNIT, 0);
        SamplerUnit.setSamplerUnitId(samplerUnitId);
        SamplerUnit.setPortId(portId);
        SamplerUnit.setMonitorUnitId(globalIdentity);
        Sampler sampler = samplerService.findBySamplerType(SamplerTypeEnum.SELF_DIAGNOSTIC_SAMPLER.getValue());
        SamplerUnit.setSamplerId(sampler.getSamplerId());
        SamplerUnit.setParentSamplerUnitId(0);
        SamplerUnit.setSamplerType(sampler.getSamplerType());
        SamplerUnit.setSamplerUnitName(i18n.T("site.self.Diagnosis.Equip"));
        SamplerUnit.setAddress(1);
        SamplerUnit.setSpUnitInterval(2D);
        SamplerUnit.setDllPath("KoloBusinessServer.exe");
        SamplerUnit.setConnectState(StationStateEnum.ONLINE.getValue());
        SamplerUnit.setUpdateTime(LocalDateTime.now());
        samplerUnitService.createSamplerUnit(SamplerUnit);
        operationDetailService.recordOperationLog(TokenUserSiteWebUtil.getLoginUserId(), SamplerUnit.getSamplerUnitId().toString(), OperationObjectTypeEnum.SAMPLER_UNIT, i18n.T("sampler.unit.name"), i18n.T("add"), "", SamplerUnit.getSamplerUnitName());

        List<House> house = houseService.findHouseByStationId(-1 * centerDTO.getCenterId());

        Equipment Equipment = new Equipment();
        int equipmentId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_EQUIPMENT, 0);
        Equipment.setStationId(-1 * centerDTO.getCenterId());
        Equipment.setEquipmentId(equipmentId);
        Station station = stationService.findByStationId(centerDTO.getCenterId() * -1);
        Equipment.setEquipmentName(station.getStationName() + "-" + SamplerUnit.getSamplerUnitName());
        Equipment.setEquipmentCategory(EquipmentCategoryEnum.SELF_DIAGNOSTICS.getValue());
        Equipment.setEquipmentType(EquipmentTypeEnum.SELF_DISGNOSTIC_EQUIPMENT.getValue());
        Equipment.setEquipmentState(StationStateEnum.ONLINE.getValue());
        Equipment.setEquipmentTemplateId(equipmentTemplateId);
        Equipment.setHouseId(house.get(0).getHouseId());
        Equipment.setMonitorUnitId(tslMonitorUnit.getMonitorUnitId());
        Equipment.setWorkStationId(centerDTO.getCenterId());
        Equipment.setSamplerUnitId(SamplerUnit.getSamplerUnitId());
        Equipment.setConnectState(StationStateEnum.ONLINE.getValue());
        Equipment.setUpdateTime(LocalDateTime.now());
        Equipment.setEquipmentNo("");
        Equipment.setDisplayIndex(0);
        Equipment.setInstalledModule("");
        Equipment.setEquipmentClass(-1);
        // 将自诊断设备挂到中心下
        ResourceStructure structureByName = resourceStructureService.findResourceStructureByOriginIdAndParentIdAndStructureTypeId(house.get(0).getHouseId(), house.get(0).getStationId(), StructureTypeEnum.STATION_HOUSE.getValue());
//        ResourceStructure structureByName = resourceStructureService.getStructureByName(centerDTO.getCenterName());
        Equipment.setResourceStructureId(structureByName.getResourceStructureId());
        equipmentService.createEquipment(Equipment);
        operationDetailService.recordOperationLog(TokenUserSiteWebUtil.getLoginUserId(), Equipment.getEquipmentId().toString(), OperationObjectTypeEnum.EQUIPMENT, i18n.T("equipment.name"), i18n.T("add"), "", Equipment.getEquipmentName());
    }

    /**
     * 新增监控单元类别和端口类型
     */
    private void addMonitorUnitAndPortTypes() {
        // 新增监控单元类别
        dataItemService.deleteByEntryIdAndItemId(34, 18);
        dataItemService.saveDictionaryItemByEntry(new DataItem(34, 18, 0, 0, true, "GFSU3", "", "GFSU3", null, "2", null));

        dataItemService.deleteByEntryIdAndItemId(34, 19);
        dataItemService.saveDictionaryItemByEntry(new DataItem(34, 19, 0, 0, true, "IPLU-0602", "", "IPLU-0602", null, "7", null));

        dataItemService.deleteByEntryIdAndItemId(34, 20);
        dataItemService.saveDictionaryItemByEntry(new DataItem(34, 20, 0, 0, true, "IPLU-1202", "", "IPLU-1202", null, "8", null));

        dataItemService.deleteByEntryIdAndItemId(34, 21);
        dataItemService.saveDictionaryItemByEntry(new DataItem(34, 21, 0, 0, true, "IPLU-0006", "", "IPLU-0006", null, "9", null));

        dataItemService.deleteByEntryIdAndItemId(34, 22);
        dataItemService.saveDictionaryItemByEntry(new DataItem(34, 22, 0, 0, true, "IPLU-0008", "", "IPLU-0008", null, "10", null));

        dataItemService.deleteByEntryIdAndItemId(34, 23);
        dataItemService.saveDictionaryItemByEntry(new DataItem(34, 23, 0, 0, true, "IFSU", "", "IFSU", null, "12", null));

        dataItemService.deleteByEntryIdAndItemId(34, 24);
        dataItemService.saveDictionaryItemByEntry(new DataItem(34, 24, 0, 0, true, "RMU跨站监控单元", "", "RMU跨站监控单元", null, "4", null));

        dataItemService.saveDictionaryItemByEntry(new DataItem(34, 25, 0, 0, true, "GFSU2", "", "GFSU2", null, "4", null));

        // 新增端口类型
        dataItemService.deleteByEntryIdAndItemId(39, 34);
        dataItemService.saveDictionaryItemByEntry(new DataItem(39, 34, 0, 0, true, "自诊断端口", "", "Self-diagnosis Port", "7", null, null));

        dataItemService.deleteByEntryIdAndItemId(39, 35);
        dataItemService.saveDictionaryItemByEntry(new DataItem(39, 35, 0, 0, true, "板载IO端口", "", "IO Port", "8", null, null));

        // 修改端口字典表ExtendField2
        dataItemService.updatePortExtendField2();

        // 删除IPLU
        dataItemService.deleteByEntryIdAndItemId(34, 4);
    }

    /**
     * 创建管理局站
     *
     * @param centerDTO 中心DTO
     */
    private void createManagementStation(CenterDTO centerDTO) {
        // 创建管理局站
        Station managementStation = new Station();
        managementStation.setCenterId(centerDTO.getCenterId());
        managementStation.setEnable(Boolean.TRUE);
        managementStation.setStationCategory(StationCategoryEnum.VIRTUAL_STATION.getValue());
        managementStation.setStationGrade(StationGradeEnum.GENERAL_STATION.getValue());
        managementStation.setStationId(centerDTO.getCenterId() * -1);
        managementStation.setStationName(centerDTO.getCenterName().trim() + i18n.T("managed.site"));
        managementStation.setStationState(StationStateEnum.ONLINE.getValue());
        managementStation.setConnectState(StationStateEnum.ONLINE.getValue());
        managementStation.setUpdateTime(LocalDateTime.now());
        managementStation.setContainNode(false);
        stationService.create(managementStation);
        changeEventService.sendCreate(managementStation);

        // 管理局站挂载中心下
        StationStructureMap stationStructureMap = new StationStructureMap();
        stationStructureMap.setStationId(managementStation.getStationId());
        stationStructureMap.setStructureId(centerDTO.getCenterId());
        stationStructureMapService.create(stationStructureMap);
        changeEventService.sendCreate(stationStructureMap);
    }

    /**
     * 创建分组局站
     *
     * @param centerDTO 中心DTO
     */
    private void createStationStructure(CenterDTO centerDTO) {
        List<DataItem> dataItems = dataItemService.findByEntryId(DataEntryEnum.STATION_GROUP_TYPE);
        // 每种类型创建为分组局站
        for (DataItem item : dataItems) {
            int structureId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_STATION_STRUCTURE, 0);
            StationStructure stationStructure = new StationStructure();
            stationStructure.setStructureId(structureId);
            stationStructure.setEnable(Boolean.TRUE);
            stationStructure.setIsUngroup(Boolean.TRUE);
            stationStructure.setParentStructureId(centerDTO.getCenterId());
            stationStructure.setLevelPath(centerDTO.getCenterId().toString() + "." + structureId);
            stationStructure.setStructureGroupId(item.getItemId());
            stationStructure.setStructureName(i18n.T("ungrouped.site"));
            stationStructure.setStructureType(StationStructureTypeEnum.SG.getValue());
            stationStructureService.create(stationStructure);
            changeEventService.sendCreate(stationStructure);
        }
    }

    /**
     * 创建中心根节点
     *
     * @param centerDTO 中心DTO
     */
    private void createCenterRootNode(CenterDTO centerDTO) {
        StationStructure centerStationStructure = new StationStructure();
        centerStationStructure.setStructureId(centerDTO.getCenterId());
        centerStationStructure.setStructureGroupId(0);
        centerStationStructure.setParentStructureId(0);
        centerStationStructure.setStructureName(centerDTO.getCenterName());
        centerStationStructure.setIsUngroup(Boolean.FALSE);
        centerStationStructure.setStructureType(StationStructureTypeEnum.SS.getValue());
        centerStationStructure.setEnable(Boolean.TRUE);
        centerStationStructure.setLevelPath(centerDTO.getCenterId().toString());
        stationStructureService.create(centerStationStructure);
    }

    /**
     * 创建数据字典
     *
     * @param centerDTO 中心DTO
     */
    private void createDataEntryItem(CenterDTO centerDTO) {
        // 创建数据字典
        DataItem dataItem = new DataItem();
        dataItem.setEntryId(DataEntryEnum.DATA_ENTRY.getValue());
        dataItem.setItemId(centerDTO.getCenterId());
        dataItem.setParentEntryId(0);
        dataItem.setParentItemId(0);
        dataItem.setIsSystem(Boolean.TRUE);
        dataItem.setItemValue(centerDTO.getCenterName().trim());
        dataItem.setItemAlias(centerDTO.getCenterName().trim());
        dataItemService.initDataEntryItem(dataItem);
    }

    /**
     * 处理标准类型
     *
     * @param centerDTO 监控中心DTO
     */
    private void handleStandardType(CenterDTO centerDTO) {
        // 插入当前客户类型选择
        SysConfig standardVer = sysConfigService.findByKey(SysConfigEnum.STANDARD_VER);
        StandardType standardType = standardTypeService.findByStandardId(centerDTO.getStandardType());
        if (standardVer == null) {
            SysConfig sysConfig = new SysConfig();
            sysConfig.setConfigKey(SysConfigEnum.STANDARD_VER.getConfigKey());
            sysConfig.setConfigValue(standardType.getStandardAlias());
            sysConfigService.createSysConfig(sysConfig);
        } else {
            standardVer.setConfigValue(standardType.getStandardAlias());
            sysConfigService.updateSysConfig(standardVer);
        }
        // 向sys_config中插入记录
        SysConfig standardCategory = sysConfigService.findByKey(SysConfigEnum.STANDAR_CATEGORY);
        if (standardCategory == null) {
            SysConfig sysConfig = new SysConfig();
            sysConfig.setConfigKey(SysConfigEnum.STANDAR_CATEGORY.getConfigKey());
            sysConfig.setConfigValue(standardType.getStandardId().toString());
            sysConfigService.createSysConfig(sysConfig);
        } else {
            standardCategory.setConfigValue(standardType.getStandardId().toString());
            sysConfigService.updateSysConfig(standardCategory);
        }

        // 移动标准，对应存储过程PNL_UpdateToYiDongCategoryDictionary
        if (centerDTO.getStandardType() == 1) {
            handleYiDongStandard();
        } else if (centerDTO.getStandardType() == 2) { // 电信标准，对应的存储过程PNL_UpdateToDianXinCategoryDictionary
            handleDianXinStandard();
        } else if (centerDTO.getStandardType() == 3) { // 联通标准
            handleLianTongStandard();
        }
    }

    /**
     * 处理移动标准
     */
    private void handleYiDongStandard() {
        dataItemService.deleteByEntryId(DataEntryEnum.STATION_CATEGORY);
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 1, 0, 0, true, "基站", "", "Site", "BigStation.png", "1", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 2, 0, 0, true, "机房", "", "Station", "SmallStation.png", "1", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 3, 0, 0, true, "虚拟局站", "", "Virtual Station", "", "1", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 4, 0, 0, true, "服务厅", "", "Service Hall", "", "1", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 5, 0, 0, true, "传输汇接层", "", "Transfer Aggregation Layer", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 6, 0, 0, true, "工程站", "", "Engineering Station", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 7, 0, 0, true, "备用站", "", "Standby Station", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 8, 0, 0, true, "微蜂窝", "", "Microcell", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 9, 0, 0, true, "村通局站", "", "Village Station", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 10, 71, 1, true, "VIP基站", "", "VIP Station", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 11, 71, 1, true, "VVIP", "", "VVIP", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 12, 71, 1, true, "一级VIP", "", "一级VIP", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 13, 71, 1, true, "二级VIP", "", "二级VIP", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 14, 71, 1, true, "三级VIP", "", "三级VIP", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 15, 0, 0, true, "其他类型", "", "其他类型", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 16, 0, 0, true, "干结点", "", "干结点", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 17, 71, 5, true, "一类节点", "", "区域性骨干节点", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 18, 71, 5, true, "二类节点", "", "汇聚节点", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 19, 71, 5, true, "三类节点", "", "综合业务接入节点", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 20, 71, 2, true, "一类机房", "", "一类机房", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 21, 71, 2, true, "二类机房", "", "二类机房", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 22, 71, 2, true, "三类机房", "", "三类机房", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 23, 0, 0, true, "微站", "", "Micro Station", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 24, 0, 0, true, "高铁专网", "", "High Railway", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 25, 0, 0, true, "超级VIP基站", "", "", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 26, 0, 0, true, "数据中心", "", "Data Center", null, null, null));
        dataItemService.deleteByEntryId(DataEntryEnum.EQUIPMENT_CATEGORY);
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 11, 0, 0, true, "高压配电", "", "High Voltage Distribution", "Equipment.png", "1", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 12, 0, 0, true, "低压交流配电", "", "Low Voltage Distribution", "Equipment.png", "2", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 13, 0, 0, true, "发电机组", "", "Diesel Generator", "Generator.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 17, 0, 0, true, "高压直流电源", "", "High Voltage DC Power Supply", "Equipment.png", "87", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 18, 0, 0, true, "变压器", "", "Transformer", "Equipment.png", "3", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 20, 0, 0, true, "锂电池", "", "Lithium battery", "Battery.png", "68", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 22, 0, 0, true, "开关电源", "", "Power Supply", "Equipment.png", "6", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 23, 0, 0, true, "低压直流配电", "", "Low Voltage DC Power Distribution Cabinet", "Equipment.png", "4", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 24, 0, 0, true, "铅酸电池", "", "Plumbum phosphate battery", "Battery.png", "7", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 31, 0, 0, true, "UPS设备", "", "UPS", "Ups.png", "8", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 34, 0, 0, true, "变换设备", "", "Converter Equipment", "Equipment.png", "14", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 35, 0, 0, true, "UPS配电屏", "", "UPS Distribution Board", null, "9", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 40, 0, 0, true, "机房专用空调", "", "Special Air-Condition", "AirCondition.png", "11", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 41, 0, 0, true, "中央空调末端", "", "Central Air-Condition extremity", "AirCondition.png", "12", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 42, 0, 0, true, "中央空调主机", "", "Central Air-Condition cardinal", "AirCondition.png", "13", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 45, 0, 0, true, "普通空调", "", "Split Air-Condition", "AirCondition.png", "15", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 51, 0, 0, true, "机房环境", "", "Station Environment", "Equipment.png", "17", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 55, 0, 0, true, "极早期烟感", "", "Very Early Smoke Detection", "Equipment.png", "16", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 62, 0, 0, true, "风光设备", "", "Wind, Light, Electric Complementary Power Supply", "Equipment.png", "78", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 67, 0, 0, true, "电池恒温箱", "", "Battery Thermotank", "Equipment.png", "18", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 82, 0, 0, true, "智能门禁", "", "Door", "Door.png", "93", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 85, 0, 0, true, "智能电表", "", "Ammeter", "ATMKey.png", "92", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 86, 0, 0, true, "智能通风换热", "", "Ventilation/Heat Exchanger ", "ATMKey.png", "77", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 88, 0, 0, true, "高压直流电源配电", "", "High Voltage DC Power Distribution Cabinet", "Equipment.png", "88", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 97, 0, 0, true, "人脸读头", "", "Face Reader", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 98, 0, 0, true, "指纹录入仪", "", "Finger Reader", "Equipment.png", null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 99, 0, 0, true, "监控设备", "", "Monitor System", "Equipment.png", "76", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 101, 0, 0, true, "240V组合电源", "", "240V Power Supply", "Equipment.png", "41A", null));

        baseEquipmentCategoryMapService.deleteAll();
        baseEquipmentCategoryMapService.batchsaveYiDongEquipmentCategoryMap();

        stationBaseTypeService.deleteByStandardId(1);
        stationBaseTypeService.save(new StationBaseType(0, "适应所有类型", 1));
        stationBaseTypeService.save(new StationBaseType(1, "机房", 1));
        stationBaseTypeService.save(new StationBaseType(2, "基站", 1));
        stationBaseTypeService.save(new StationBaseType(3, "节点", 1));
        stationBaseTypeService.save(new StationBaseType(4, "数据中心", 1));

        stationBaseMapService.deleteByStandardType(1);
        stationBaseMapService.save(new StationBaseMap(2, 1, 1));
        stationBaseMapService.save(new StationBaseMap(1, 2, 1));
        stationBaseMapService.save(new StationBaseMap(1, 3, 1));
        stationBaseMapService.save(new StationBaseMap(2, 4, 1));
        stationBaseMapService.save(new StationBaseMap(3, 5, 1));
        stationBaseMapService.save(new StationBaseMap(2, 6, 1));
        stationBaseMapService.save(new StationBaseMap(2, 7, 1));
        stationBaseMapService.save(new StationBaseMap(2, 8, 1));
        stationBaseMapService.save(new StationBaseMap(2, 9, 1));
        stationBaseMapService.save(new StationBaseMap(2, 10, 1));
        stationBaseMapService.save(new StationBaseMap(2, 11, 1));
        stationBaseMapService.save(new StationBaseMap(2, 12, 1));
        stationBaseMapService.save(new StationBaseMap(2, 13, 1));
        stationBaseMapService.save(new StationBaseMap(2, 14, 1));
        stationBaseMapService.save(new StationBaseMap(2, 15, 1));
        stationBaseMapService.save(new StationBaseMap(2, 16, 1));
        stationBaseMapService.save(new StationBaseMap(3, 17, 1));
        stationBaseMapService.save(new StationBaseMap(3, 18, 1));
        stationBaseMapService.save(new StationBaseMap(3, 19, 1));
        stationBaseMapService.save(new StationBaseMap(1, 20, 1));
        stationBaseMapService.save(new StationBaseMap(1, 21, 1));
        stationBaseMapService.save(new StationBaseMap(1, 22, 1));
        stationBaseMapService.save(new StationBaseMap(2, 23, 1));
        stationBaseMapService.save(new StationBaseMap(4, 26, 1));


        equipmentService.updateEquipmentCategoryByCategoryIdMap(1, 2);
        equipmentTemplateService.updateEquipmentTemplateCategoryByCategoryIdMap(1, 2);
    }

    /**
     * 处理电信标准
     */
    private void handleDianXinStandard() {
        dataItemService.deleteByEntryId(DataEntryEnum.EQUIPMENT_CATEGORY);
        dataItemService.deleteByEntryId(DataEntryEnum.STATION_CATEGORY);
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 101, 0, 0, true, "A级局站", "", "A Station", "SmallStation.png", "1", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 102, 0, 0, true, "B级局站", "", "B Station", null, "2", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 103, 0, 0, true, "C级局站", "", "C Station", null, "3", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 104, 0, 0, true, "D级局站", "", "D Station", null, "4", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 105, 0, 0, true, "J级基站", "", "CTCC", null, "5", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 106, 0, 0, true, "L级基站", "", "WITH CNCC", null, "6", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 107, 0, 0, true, "Y级基站", "", "WITH CMCC", null, "7", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 108, 0, 0, true, "S级基站", "", "With CNCC&CMCC", null, "8", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 3, 0, 0, true, "虚拟局站", "", "Virtual Station", null, "9", null));


        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1000, 0, 0, true, "高压变配电系统", "", "High Voltage Power Transformation And Distribution System", "Equipment.png", "010", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 18, 0, 0, true, "变压器", "", "Transformer", "Equipment.png", "011", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 17, 0, 0, true, "高压操作电源", "", "High Voltage Operating Power Supply", "Equipment.png", "012", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 31, 0, 0, true, "UPS主机", "", "UPS Host", "Equipment.png", "040", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1013, 0, 0, true, "UPS配电屏", "", "UPS Power Distribution Panel", "Equipment.png", "043", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1011, 0, 0, true, "UPS蓄电池组", "", "UPS Battery Pack", "Equipment.png", "041", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1010, 0, 0, true, "UPS系统", "", "UPS System", "Equipment.png", "040", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1016, 0, 0, true, "240V直流系统", "", "240V DC System", "Equipment.png", "050", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1017, 0, 0, true, "240V整流设备", "", "240V Rectifier Equipment", "Equipment.png", "050", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1021, 0, 0, true, "240V交流屏", "", "240V AC Screen", "Equipment.png", "054", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1018, 0, 0, true, "240V蓄电池组", "", "240V Battery Pack", "Equipment.png", "051", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1023, 0, 0, true, "48V直流系统", "", "48V DC System", "Equipment.png", "060", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 22, 0, 0, true, "48V整流设备", "", "48V Rectifier Equipment", "Equipment.png", "060", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 24, 0, 0, true, "48V蓄电池组", "", "48V Battery Pack", "Equipment.png", "061", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 51, 0, 0, true, "机房系统", "", "Computer Room System", "Equipment.png", "120", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1001, 0, 0, true, "低压配电系统", "", "Low Voltage Power Distribution System", "Equipment.png", "020", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 27, 0, 0, true, "电容补偿柜", "", "Capacitor Compensation Cabinet", "Equipment.png", "022", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 28, 0, 0, true, "谐波滤波器", "", "Harmonic Filter", "Equipment.png", "023", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1029, 0, 0, true, "配电屏", "", "Distribution Panel", "Equipment.png", "073", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 42, 0, 0, true, "中央空调主机", "", "Central Air Conditioning Host", "Equipment.png", "070", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1028, 0, 0, true, "冷却塔", "", "Cooling Tower", "Equipment.png", "072", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1027, 0, 0, true, "水泵", "", "Water Pump", "Equipment.png", "071", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1030, 0, 0, true, "水箱", "", "Water Tank", "Equipment.png", "074", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1031, 0, 0, true, "储冷罐", "", "Cold Storage Tank", "Equipment.png", "075", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1032, 0, 0, true, "蝶阀", "", "Butterfly Valve", "Equipment.png", "076", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1026, 0, 0, true, "中央空调系统", "", "Central Aircon System", "Equipment.png", "070", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1042, 0, 0, true, "交流列柜", "", "Exchange Cabinet", "Equipment.png", "110", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1043, 0, 0, true, "直流列柜", "", "DC Cabinet", "Equipment.png", "111", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 99, 0, 0, true, "采集设备", "", "Collection Equipment", "Equipment.png", "090", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1038, 0, 0, true, "被控设备", "", "Controlled Device", "Equipment.png", "091", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1039, 0, 0, true, "网络设备", "", "Internet Equipment", "Equipment.png", "092", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1037, 0, 0, true, "动力监控系统", "", "Power Monitoring System", "Equipment.png", "090", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 13, 0, 0, true, "固定式发电机组", "", "Stationary Generator Set", "Equipment.png", "030", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 83, 0, 0, true, "环境监测设备", "", "Environmental Monitoring Equipment", "Equipment.png", "120", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1044, 0, 0, true, "消防安防监测设备", "", "Fire Security Monitoring Equipment", "Equipment.png", "121", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1005, 0, 0, true, "发电机组系统", "", "Generator System", "Equipment.png", "030", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1008, 0, 0, true, "油库/箱", "", "Oil Depot/Tank", "Equipment.png", "033", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1009, 0, 0, true, "交流屏", "", "Communication Screen", "Equipment.png", "034", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1006, 0, 0, true, "移动式发电机组", "", "Mobile Generator Set", "Equipment.png", "031", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 45, 0, 0, true, "普通空调", "", "Ordinary Air Conditioner", "Equipment.png", "082", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 11, 0, 0, true, "高压配电屏", "", "High Voltage Distribution Panel", "Equipment.png", "010", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 12, 0, 0, true, "低压配电屏", "", "Low Voltage Power Distribution Panel", "Equipment.png", "020", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1012, 0, 0, true, "UPS设备", "", "UPS Equipment", "Equipment.png", "042", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 43, 0, 0, true, "精密空调", "", "Precision Air Conditioner", "Equipment.png", "080", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1049, 0, 0, true, "微模块空调设备", "", "Micro Module Air Conditioning Equipment", "Equipment.png", "150", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1050, 0, 0, true, "微模块主配电设备", "", "Micro Module Main Power Distribution Equipment", "Equipment.png", "151", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1051, 0, 0, true, "微模块HVDC设备", "", "Micro Module HVDC Equipment", "Equipment.png", "152", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1052, 0, 0, true, "微模块电池组", "", "Micro Module Battery Pack", "Equipment.png", "153", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1053, 0, 0, true, "微模块交流支路设备", "", "Micro Module AC Branch Equipment", "Equipment.png", "154", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1054, 0, 0, true, "微模块直流支路设备", "", "Micro Module DC Branch Equipment", "Equipment.png", "155", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1055, 0, 0, true, "微模块监控设备", "", "Micro Module Monitoring Equipment", "Equipment.png", "156", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1034, 0, 0, true, "机房空调系统", "", "Computer Room Air Conditioning System", "Equipment.png", "080", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1041, 0, 0, true, "机房配电系统", "", "Computer Room Power Distribution System", "Equipment.png", "110", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1048, 0, 0, true, "微模块系统", "", "Micro Module System", "Equipment.png", "150", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1040, 0, 0, true, "防雷接地系统系统", "", "Lightning Protection Grounding System", "Equipment.png", "100", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1047, 0, 0, true, "其他系统", "", "Other Systems", "Equipment.png", "130", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1045, 0, 0, true, "机柜设备", "", "Cabinet Equipment", "Equipment.png", "122", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1015, 0, 0, true, "模块化UPS", "", "Modular UPS", "Equipment.png", "045", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1046, 0, 0, true, "ATS设备", "", "ATS Equipment", "Equipment.png", "124", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 85, 0, 0, true, "智能电表", "", "Smart Meter", "Equipment.png", "123", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 44, 0, 0, true, "专用空调", "", "Dedicated Air Conditioner", "Equipment.png", "083", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1014, 0, 0, true, "DPS", "", "DPS", "Equipment.png", "044", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1036, 0, 0, true, "节能空调", "", "Energy Saving Air Conditioner", "Equipment.png", "084", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1003, 0, 0, true, "交流稳压器", "", "AC Voltage Regulator", "Equipment.png", "021", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1007, 0, 0, true, "发电机辅助设备", "", "Generator Auxiliary Equipment", "Equipment.png", "032", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1019, 0, 0, true, "240V整流模块", "", "240V Rectifier Module", "Equipment.png", "052", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1020, 0, 0, true, "240V监控模块", "", "240V Monitoring Module", "Equipment.png", "053", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1022, 0, 0, true, "240V直流屏", "", "240V DC Screen", "Equipment.png", "055", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1024, 0, 0, true, "48V整流模块", "", "48V Rectifier Module", "Equipment.png", "062", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1025, 0, 0, true, "48V监控模块", "", "48V Monitoring Module", "Equipment.png", "063", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 21, 0, 0, true, "48V交流屏", "", "48V AC Screen", "Equipment.png", "064", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 23, 0, 0, true, "48V直流屏", "", "48V DC Screen", "Equipment.png", "065", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1035, 0, 0, true, "湿度控制设备", "", "Humidity Control Equipment", "Equipment.png", "081", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 72, 0, 0, true, "防雷设备", "", "Lightning Protection Equipment", "Equipment.png", "100", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1033, 0, 0, true, "热交换器", "", "Heat Exchanger", "Equipment.png", "077", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 71, 0, 0, true, "接地设备", "", "Grounding Equipment", "Equipment.png", "101", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1004, 0, 0, true, "电力电缆", "", "Power Cable", "Equipment.png", "024", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 82, 0, 0, true, "智能门禁", "", "Smart Access Control", "Equipment.png", "160", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1056, 0, 0, true, "门禁系统", "", "Access Control System", "Equipment.png", "160", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 102, 0, 0, true, "B接口设备", "", "B Interface Device", "Equipment.png", "102", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1057, 0, 0, true, "安全锂电储能系统", "", "Safe lithium battery energy storage system", "Equipment.png", "170", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1058, 0, 0, true, "储能柜监控模块", "", "Energy storage cabinet monitoring module", "Equipment.png", "170", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1059, 0, 0, true, "储能柜锂电池模块", "", "Energy storage cabinet lithium battery module", "Equipment.png", "171", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1060, 0, 0, true, "储能柜温控模块", "", "Energy storage cabinet temperature control module", "Equipment.png", "172", "CTCC_DEVICE"));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 1061, 0, 0, true, "组合开关电源", "", "Combined switching power supply", "Equipment.png", "066", "CTCC_DEVICE"));

        // 存储过程PNL_UpgradeToDianXinCategory
        baseEquipmentCategoryMapService.batchsaveDianXinEquipmentCategoryMap();
        equipmentTemplateService.updateEquipmentBaseTypeToNull();

        stationBaseMapService.deleteByStandardType(2);
        stationBaseMapService.save(new StationBaseMap(1, 101, 2));
        stationBaseMapService.save(new StationBaseMap(2, 102, 2));
        stationBaseMapService.save(new StationBaseMap(3, 103, 2));
        stationBaseMapService.save(new StationBaseMap(4, 104, 2));
        stationBaseMapService.save(new StationBaseMap(4, 105, 2));
        stationBaseMapService.save(new StationBaseMap(4, 106, 2));
        stationBaseMapService.save(new StationBaseMap(4, 107, 2));
        stationBaseMapService.save(new StationBaseMap(4, 108, 2));
        stationBaseMapService.save(new StationBaseMap(1, 3, 2));


        // 执行
        categoryIdMapService.dianXinBusinessCategoryFromOriginCategory();
        equipmentService.updateEquipmentCategoryByCategoryIdMap(2, 2);
        equipmentTemplateService.updateEquipmentTemplateCategoryByCategoryIdMap(2, 2);
    }

    /**
     * 处理联通标准
     */
    private void handleLianTongStandard() {
        dataItemService.deleteByEntryId(DataEntryEnum.STATION_CATEGORY);
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 1, 0, 0, true, "基站", "", "Site", "BigStation.png", "1", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 2, 0, 0, true, "机房", "", "Station", "SmallStation.png", "2", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 3, 0, 0, true, "虚拟局站", "", "Virtual Station", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 4, 0, 0, true, "服务厅", "", "Service Hall", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 5, 0, 0, true, "传输汇接层", "", "Transfer Aggregation Layer", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 6, 0, 0, true, "工程站", "", "Engineering Station", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 7, 0, 0, true, "备用站", "", "Standby Station", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 8, 0, 0, true, "微蜂窝", "", "Microcell", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 9, 0, 0, true, "村通局站", "", "Village Station", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 10, 71, 1, true, "VIP基站", "", "VIP Station", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 11, 71, 1, true, "VVIP", "", "VVIP", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 12, 71, 1, true, "一级VIP", "", "一级VIP", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 13, 71, 1, true, "二级VIP", "", "二级VIP", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 14, 71, 1, true, "三级VIP", "", "三级VIP", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 15, 0, 0, true, "其他类型", "", "其他类型", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 16, 0, 0, true, "干结点", "", "干结点", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 17, 71, 5, true, "一类节点", "", "区域性骨干节点", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 18, 71, 5, true, "二类节点", "", "汇聚节点", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 19, 71, 5, true, "三类节点", "", "综合业务接入节点", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 20, 71, 2, true, "一类机房", "", "一类机房", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 21, 71, 2, true, "二类机房", "", "二类机房", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 22, 71, 2, true, "三类机房", "", "三类机房", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(71, 23, 0, 0, true, "微站", "", "Micro Station", null, null, null));

        dataItemService.deleteByEntryId(DataEntryEnum.EQUIPMENT_CATEGORY);
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 11, 0, 0, true, "10KV配电设备", "", "10KV Distribution Equipment", "Equipment.png", "201", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 12, 0, 0, true, "低压配电柜", "", "Low Voltage Distribution", "Equipment.png", "241", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 13, 0, 0, true, "柴油发电机组", "", "Diesel Generator", "Generator.png", "281", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 14, 0, 0, true, "240V高压直流交流屏", "", "240V High Voltage DC-AC Board", "Equipment.png", "402", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 15, 0, 0, true, "240V高压直流整流屏", "", "240V High Voltage DC-Rectifier Board", "Equipment.png", "412", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 16, 0, 0, true, "240V高压直流直流屏", "", "240V High Voltage DC-DC Board", "Equipment.png", "422", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 17, 0, 0, true, "直流操作电源柜", "", "DC Operational Power Supply System", "Equipment.png", "231", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 18, 0, 0, true, "10KV/400V变压器", "", "10KV/400V Transformer", "Equipment.png", "271", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 20, 0, 0, true, "48V开关电源整流屏", "", "48V Power Supply Rectifier Board", "Equipment.png", "411", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 21, 0, 0, true, "48V开关电源交流屏", "", "48V Power Supply AC Board", "Equipment.png", "401", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 22, 0, 0, true, "48V组合开关电源", "", "48V Power Supply", "Equipment.png", "419", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 23, 0, 0, true, "48V开关电源直流屏", "", "48V Power Supply DC Board", "Equipment.png", "421", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 24, 0, 0, true, "48V铅酸阀控蓄电池组", "", "48V Battery", "Battery.png", "461", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 25, 0, 0, true, "240V直流系统", "", "240V DC System", "Equipment.png", "", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 26, 0, 0, true, "240V铅酸阀控蓄电池组", "", "240V Battery", "Battery.png", "463", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 27, 0, 0, true, "电容补偿柜", "", "Capacitance Compensation Cabinet", "Equipment.png", "2D1", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 28, 0, 0, true, "谐波抑制柜", "", "Hamonic Filter", "Equipment.png", "2E1", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 29, 0, 0, true, "低压联络柜", "", "Low Voltage Contact Cabinet", "Equipment.png", "2C1", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 30, 0, 0, true, "备用电源投入装置", "", "Backup Power Input Equipment", "Equipment.png", "311", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 31, 0, 0, true, "UPS", "", "UPS", "Ups.png", "2A1", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 32, 0, 0, true, "直流远供局端电源", "", "DC Remote Power Supply Infrastructure", "Equipment.png", "4B1", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 33, 0, 0, true, "太阳能供电", "", "Solar Power Equipment", "Equipment.png", "431", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 34, 0, 0, true, "直流直流变换器", "", "DC-DC Converter", "Equipment.png", "441", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 35, 0, 0, true, "模块化UPS", "", "Module UPS", "Ups.png", "2A2", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 36, 0, 0, true, "UPS铅酸阀控蓄电池组", "", "UPS Battery", "Battery.png", "462", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 37, 0, 0, true, "交流配电屏", "", "AC Distribution Board", "Equipment.png", "406", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 38, 0, 0, true, "交流配电箱", "", "AC Distribution Box", "Equipment.png", "40B", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 39, 0, 0, true, "48V配电屏", "", "48V Distribution Board", "Equipment.png", "426", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 40, 0, 0, true, "48V配电箱", "", "48V Distribution Box", "Equipment.png", "42B", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 41, 0, 0, true, "逆变器", "", "Inverter", "Equipment.png", "2B1", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 42, 0, 0, true, "冷冻系统", "", "Central Air-Condition (Water Cooling)", "AirCondition.png", "611", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 43, 0, 0, true, "专用空调（风冷）", "", "Special Air-Condition (Wind Cooling)", "AirCondition.png", "602", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 45, 0, 0, true, "舒适性空调", "", "Split Aircon", "AirCondition.png", "601", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 51, 0, 0, true, "环境", "", "Environmental Equipment", "Equipment.png", "911", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 62, 0, 0, true, "风力发电系统", "", "Wind Power Equipment", "Equipment.png", "451", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 65, 0, 0, true, "燃气轮发电机组", "", "Gas Genset", "Equipment.png", "291", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 67, 0, 0, true, "蓄电池分区温控", "", "Battery Temperature Control Cabinet", "Battery.png", "671", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 68, 0, 0, true, "热交换设备", "", "Heat Exchange Device", "Equipment.png", "652", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 69, 0, 0, true, "热管设备", "", "Heat Pipe", "Equipment.png", "651", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 72, 0, 0, true, "防雷箱", "", "Lightning Protection Box", "Equipment.png", "921", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 82, 0, 0, true, "门禁系统", "", "Door Access Control", "Equipment.png", "801", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 85, 0, 0, true, "智能电表", "", "Ammeter", "Equipment.png", "2C1", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 86, 0, 0, true, "新风系统", "", "Fresh Air Systems", "Equipment.png", "641", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 87, 0, 0, true, "燃料电池", "", "Fuel Cell", "Battery.png", "4C1", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 88, 0, 0, true, "湿膜新风系统", "", "Wet film Fresh Air Systems", "Equipment.png", "642", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 97, 0, 0, true, "人脸读头", "", "Face Reader", null, null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 98, 0, 0, true, "指纹录入仪", "", "Finger Reader", "Equipment.png", null, null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 99, 0, 0, true, "动环监控系统", "", "Power Environment Monitor System", "Equipment.png", "901", null));
        dataItemService.saveDictionaryItemByEntry(new DataItem(7, 101, 0, 0, true, "240V组合电源", "", "240V Power Supply", "Equipment.png", "41A", null));

        baseEquipmentCategoryMapService.deleteAll();
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(1001, 82));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(1004, 51));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(101, 11));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(102, 17));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(1101, 24));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(1103, 26));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(1601, 18));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(201, 12));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(201, 29));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(201, 37));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(201, 38));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(204, 27));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(205, 28));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(301, 13));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(302, 65));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(401, 20));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(401, 21));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(401, 22));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(401, 23));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(401, 32));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(402, 14));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(402, 15));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(402, 16));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(405, 39));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(405, 40));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(501, 31));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(503, 36));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(504, 35));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(602, 34));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(701, 45));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(702, 43));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(703, 44));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(705, 42));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(801, 86));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(805, 68));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(901, 62));
        baseEquipmentCategoryMapService.save(new BaseEquipmentCategoryMap(902, 33));

        stationBaseTypeService.deleteByStandardId(3);
        stationBaseTypeService.save(new StationBaseType(0, "适应所有类型", 3));
        stationBaseTypeService.save(new StationBaseType(1, "局站", 3));

        stationBaseMapService.deleteByStandardType(3);
        stationBaseMapService.save(new StationBaseMap(1, 1, 3));
        stationBaseMapService.save(new StationBaseMap(1, 2, 3));
        stationBaseMapService.save(new StationBaseMap(1, 3, 3));
        stationBaseMapService.save(new StationBaseMap(1, 4, 3));
        stationBaseMapService.save(new StationBaseMap(1, 5, 3));
        stationBaseMapService.save(new StationBaseMap(1, 6, 3));
        stationBaseMapService.save(new StationBaseMap(1, 7, 3));
        stationBaseMapService.save(new StationBaseMap(1, 8, 3));
        stationBaseMapService.save(new StationBaseMap(1, 9, 3));
        stationBaseMapService.save(new StationBaseMap(1, 10, 3));
        stationBaseMapService.save(new StationBaseMap(1, 11, 3));
        stationBaseMapService.save(new StationBaseMap(1, 12, 3));
        stationBaseMapService.save(new StationBaseMap(1, 13, 3));
        stationBaseMapService.save(new StationBaseMap(1, 14, 3));
        stationBaseMapService.save(new StationBaseMap(1, 15, 3));
        stationBaseMapService.save(new StationBaseMap(1, 16, 3));
        stationBaseMapService.save(new StationBaseMap(1, 17, 3));
        stationBaseMapService.save(new StationBaseMap(1, 18, 3));
        stationBaseMapService.save(new StationBaseMap(1, 19, 3));
        stationBaseMapService.save(new StationBaseMap(1, 20, 3));
        stationBaseMapService.save(new StationBaseMap(1, 21, 3));
        stationBaseMapService.save(new StationBaseMap(1, 22, 3));
        stationBaseMapService.save(new StationBaseMap(1, 23, 3));

        eventBaseDicService.deleteLianTongByBaseTypeId(7);
        eventBaseDicService.createLianTongEventBaseDic();
        equipmentService.updateEquipmentCategoryByCategoryIdMap(3, 2);
        equipmentTemplateService.updateEquipmentTemplateCategoryByCategoryIdMap(3, 2);
        logicClassEntryService.updateLogicClassByDataItem(3, 7);

        // 存储过程PNL_CUCCEquipmentTemplate
        List<Integer> equipmentTemplateIds = equipmentTemplateService.findEquipmentTemplateIdByEquipmentTemplateIdDiv(123);
        if (CollUtil.isEmpty(equipmentTemplateIds)) {
            equipmentTemplateService.batchsaveLianTongEquipmentTemplate();
            samplerService.batchInsertLianTongSampler();
            signalService.batchsaveLianTongSignal();
            signalMeaningsService.batchsaveLianTongSignalMeanings();
            signalPropertyService.batchsaveLianTongSignalPropertys();
            eventService.batchsaveLianTongEvents();
            eventConditionService.batchsaveLianTongEventConditions();
            controlService.batchsaveLianTongControls();
            controlMeaningsService.batchsaveControlMeanings();
            DbVersionRecord record = new DbVersionRecord();
            record.setUpdateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss")));
            record.setVersion("20190325");
            record.setModule("PNL_CUCCEquipmentTemplate.sql");
            record.setLastModifyTime("2019/3/25 15:51:53");
            record.setFeature("CREATE");
            dbVersionRecordService.saveDbVersionRecord(record);

        }
    }


    @Override
    public void validateCenterDTO(CenterDTO centerDTO) {
        // 中心名称不能为空
        if (StrUtil.isBlank(centerDTO.getCenterName())) {
            throw new BusinessException(i18n.T("center.name.empty"));
        }

        // 检查中心名称是否包含特殊字符
        if (checkSpecialString(centerDTO.getCenterName().trim())) {
            throw new BusinessException(i18n.T("center.name.special"));
        }

        // 检查中心名称长度是否超过128个字符
        if (centerDTO.getCenterName().length() > 128) {
            throw new BusinessException(i18n.T("center.name.length"));
        }

        // 验证IP
        if (!(isValidIpv4(centerDTO.getCenterIp()) || isValidIpv6(centerDTO.getCenterIp()))) {
            throw new BusinessException(i18n.T("ip.format.error"));
        }

        if (centerDTO.getCenterId() == null) {
            throw new BusinessException(i18n.T("center.id.empty"));
        }

        // 去掉centerId的前导0，并重新设置
        String centerIdStr = centerDTO.getCenterId().toString();
        // 使用正则表达式去除前导0
        centerIdStr = centerIdStr.replaceFirst("^0+", "");

        // 将处理后的centerId设置回DTO
        centerDTO.setCenterId(Integer.parseInt(centerIdStr));

        int centerIdLength = centerDTO.getCenterId().toString().length();
        // 检查中心ID长度是否在2到3个字符之间
        if (centerIdLength < 2 || centerIdLength > 3) {
            throw new BusinessException(i18n.T("center.id.length"));
        }

        // 判断中心是否已经创建过
        if (!CollectionUtils.isEmpty(dataItemService.findByEntryId(DataEntryEnum.DATA_ENTRY))) {
            throw new BusinessException(i18n.T("center.exist"));
        }
    }

    /**
     * 检查字符串是否包含特殊字符
     *
     * @param strSource 源字符串
     * @return 是否包含特殊字符
     */
    public static boolean checkSpecialString(String strSource) {
        // 使用相同的正则表达式检查字符串
        Pattern pattern = Pattern.compile("[\\\\'<>]+");
        Matcher matcher = pattern.matcher(strSource);
        return matcher.find(); // 如果找到匹配的特殊字符，返回true
    }

    /**
     * 验证IPv4地址
     */
    private boolean isValidIpv4(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return false;
        }
        String ipv4Pattern = "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$";
        return Pattern.matches(ipv4Pattern, ip);
    }

    /**
     * 验证IPv6地址
     */
    private boolean isValidIpv6(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return false;
        }
        String ipv6Pattern = "^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$";
        return Pattern.matches(ipv6Pattern, ip);
    }

    /**
     * 导入预制模板
     *
     * @param language 语言参数
     */
    private void importPredefinedTemplates(String language) {
        // 创建预制模板
        // 构建模板目录路径
        String equipmentTemplateDir = String.format("upload-dir/EquipmentTemplate%s", language);

        // 获取文件夹下所有文件
        File file1 = new File(equipmentTemplateDir);
        List<File> files = FileUtil.loopFiles(file1);

        for (File file : files) {
            String equipTemp = file.getName();
            String extension = equipTemp.substring(equipTemp.lastIndexOf('.') + 1);
            if ("xml".equals(extension)) {
                try {
                    // 使用 dom4j 读取 XML
                    SAXReader reader = new SAXReader();
                    Document equipTempDocument = reader.read(file);

                    // 获取根元素并导入模板
                    Element rootElement = equipTempDocument.getRootElement();
                    equipmentTemplateXmlService.importTemplate(rootElement);
                } catch (Exception e) {
                    log.error("导入预制模板错误：fileName:{},堆栈信息{}", file.getName(), ExceptionUtil.stacktraceToString(e));
                }
            }
        }
    }

    /**
     * 更新监控单元配置
     *
     * @param centerDTO 中心DTO
     */
    private void updateMonitorUnitConfig(CenterDTO centerDTO) {
        monitorUnitConfigService.updateAllIpAddressDS(centerDTO.getCenterIp());
    }

    /**
     * 更新账户中心ID
     *
     * @param centerDTO 中心DTO
     */
    private void updateAccountCenterId(CenterDTO centerDTO) {
        accountService.updateCenterIdForNegativeUserId(centerDTO.getCenterId());
    }

    /**
     * 记录操作日志
     *
     * @param centerDTO
     */
    private void recordOperationLog(CenterDTO centerDTO) {
        operationDetailService.recordOperationLog(TokenUserSiteWebUtil.getLoginUserId(), centerDTO.getCenterId().toString(), OperationObjectTypeEnum.MONITOR_CENTER, i18n.T("center.name"), i18n.T("add"), "", centerDTO.getCenterName());
    }
}