package com.siteweb.tcs.siteweb.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.constants.EventConstant;
import com.siteweb.tcs.siteweb.dto.BatchEventConfigItem;
import com.siteweb.tcs.siteweb.dto.EventConfigItem;
import com.siteweb.tcs.siteweb.dto.EventFieldCopyDTO;
import com.siteweb.tcs.siteweb.dto.excel.EventExcel;
import com.siteweb.tcs.siteweb.entity.*;
import com.siteweb.tcs.siteweb.enums.*;
import com.siteweb.tcs.siteweb.mapper.EquipmentTemplateMapper;
import com.siteweb.tcs.siteweb.mapper.EventMapper;
import com.siteweb.tcs.siteweb.service.*;
import com.siteweb.tcs.siteweb.util.I18n;
import com.siteweb.tcs.siteweb.util.TableExistenceChecker;
import com.siteweb.tcs.siteweb.util.TokenUserSiteWebUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Event Service Implementation
 */
@Slf4j
@Service
public class EventServiceImpl extends ServiceImpl<EventMapper, Event> implements IEventService {

    @Autowired
    private EventMapper eventMapper;

    @Autowired
    private IEventConditionService eventConditionService;

    @Autowired
    private IEventExService eventExService;

    @Autowired
    private I18n i18n;

    private static final int COMMUNICATION_STATE_EVENT = -3;

    @Autowired
    private IOperationDetailService operationDetailService;

    @Autowired
    private EquipmentTemplateMapper equipmentTemplateMapper;

    @Autowired
    private IChangeEventService changeEventService;

    @Autowired(required = false)
    private TableExistenceChecker tableExistenceChecker;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createEventByEventItem(EventConfigItem eventConfigItem) {
        Event event = BeanUtil.copyProperties(eventConfigItem, Event.class, "id");
        createEvent(event);
        List<EventCondition> EventCondition = eventConfigItem.getEventCondition();
        EventCondition.forEach(e -> {
            e.setEventId(event.getEventId());
            e.setEquipmentTemplateId(event.getEquipmentTemplateId());
        });
        eventConditionService.batchCreate(EventCondition);
        String objectId = event.getEquipmentTemplateId() + "." + event.getEventId();
        operationDetailService.recordOperationLog(TokenUserSiteWebUtil.getLoginUserId(), objectId, OperationObjectTypeEnum.EVENT, i18n.T("event.eventName"), i18n.T("add"), "", eventConfigItem.getEventName());
        sendUpdateMsgByEquipmentTemplateId(eventConfigItem.getEquipmentTemplateId());
        eventConfigItem.setId(event.getId());
        eventConfigItem.setEventId(event.getEventId());
        eventConfigItem.setEventConditionList(eventConfigItem.getEventConditionList(EventCondition));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByEventItem(EventConfigItem eventConfigItem) {
        // 检查事件名称是否重复
        List<EventConfigItem> eventConfigItems = findEventItemByEquipmentTemplateId(eventConfigItem.getEquipmentTemplateId());
        Event oldEvent = new Event();
        for (EventConfigItem configItem : eventConfigItems) {
            if (Objects.equals(configItem.getEventId(), eventConfigItem.getEventId())) {
                oldEvent = BeanUtil.copyProperties(configItem, Event.class);
                continue;
            }
            if (Objects.equals(configItem.getEventName(), eventConfigItem.getEventName())) {
                throw new RuntimeException(i18n.T("error.duplicate.name", i18n.T("monitor.event"), eventConfigItem.getEventName()));
            }
        }

        // 准备更新事件数据
        Event event = new Event();
        BeanUtils.copyProperties(eventConfigItem, event);
        event.setId(oldEvent.getId());

        // 更新事件
        update(event, Wrappers.lambdaUpdate(Event.class)
                .eq(Event::getEquipmentTemplateId, event.getEquipmentTemplateId())
                .eq(Event::getEventId, event.getEventId()));

        // 更新事件条件
        eventConditionService.updateEventCondition(eventConfigItem.getEquipmentTemplateId(), eventConfigItem.getEventId(), eventConfigItem.getEventCondition());

        // 更新事件扩展信息
        eventExService.updateEventx(eventConfigItem.getEquipmentTemplateId(), eventConfigItem.getEventId(), eventConfigItem.getTurnover());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateByEventItem(BatchEventConfigItem batchEventConfigItem) {
        List<EventConfigItem> eventConfigItemList = batchEventConfigItem.getEventConfigItemList();
        if (CollectionUtils.isEmpty(eventConfigItemList)) {
            return;
        }

        List<Event> eventList = new ArrayList<>(eventConfigItemList.size());
        List<EventEx> eventExList = new ArrayList<>(eventConfigItemList.size());
        List<EventCondition> eventConditionList = new ArrayList<>();

        for (EventConfigItem eventConfigItem : eventConfigItemList) {
            // 准备事件数据
            Event event = new Event();
            BeanUtils.copyProperties(eventConfigItem, event);
            eventList.add(event);

            // 准备事件扩展数据
            EventEx eventEx = new EventEx();
            eventEx.setEquipmentTemplateId(eventConfigItem.getEquipmentTemplateId());
            eventEx.setEventId(eventConfigItem.getEventId());
            eventEx.setTurnover(eventConfigItem.getTurnover());
            eventExList.add(eventEx);

            // 准备事件条件数据
            eventConditionList.addAll(eventConfigItem.getEventCondition());
        }

        // 批量更新事件
        eventMapper.batchUpdate(eventList);

        // 批量更新事件条件
        eventConditionService.batchUpdate(eventConditionList);

        // 批量更新事件扩展信息
        eventExService.batchUpdate(eventExList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteEvent(Integer equipmentTemplateId, Integer eventId) {
        // 设备通讯状态事件(ID为-3)不允许删除
        if (Objects.equals(eventId, COMMUNICATION_STATE_EVENT)) {
            return 0;
        }

        EventConfigItem eventConfigItem = findByEquipmentTemplateIdAndEventId(equipmentTemplateId, eventId);
        if (ObjectUtils.isEmpty(eventConfigItem)) {
            return 0;
        }

        // 删除事件
        int result = eventMapper.deleteEvent(equipmentTemplateId, eventId);

        // 删除关联的条件表和翻转时间
        eventConditionService.deleteByEvent(equipmentTemplateId, eventId);
        eventExService.deleteByEvent(equipmentTemplateId, eventId);

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchDeleteEvent(int equipmentTemplateId, List<Integer> eventIds) {
        try {
            if (ObjectUtils.isEmpty(equipmentTemplateId) || CollectionUtils.isEmpty(eventIds)) {
                return false;
            }

            // 过滤掉设备通讯状态事件(ID为-3)
            eventIds = eventIds.stream()
                    .filter(eventId -> !Objects.equals(eventId, COMMUNICATION_STATE_EVENT))
                    .toList();

            if (CollectionUtils.isEmpty(eventIds)) {
                return true;
            }

            // 批量删除事件、条件和扩展信息
            for (Integer eventId : eventIds) {
                eventMapper.deleteEvent(equipmentTemplateId, eventId);
                eventConditionService.deleteByEvent(equipmentTemplateId, eventId);
                eventExService.deleteByEvent(equipmentTemplateId, eventId);
            }

            return true;
        } catch (Exception e) {
            log.error("批量删除事件失败", e);
            throw e;
        }
    }

    @Override
    public List<EventConfigItem> findEventItemByEquipmentTemplateId(Integer equipmentTemplateId) {
        List<EventConfigItem> eventConfigItems = new ArrayList<>();
        if (isEventexTableExit()){
            eventConfigItems = eventMapper.findEventItemByEquipmentTemplateId(equipmentTemplateId);
        }
        else {
            eventConfigItems = eventMapper.findEventItemByEquipmentTemplateIdOMC(equipmentTemplateId);
        }
        return eventConfigItems;
    }

    @Override
    public void deleteByEquipmentTemplateId(Integer equipmentTemplateId) {
        eventMapper.delete(Wrappers.lambdaQuery(Event.class)
                .eq(Event::getEquipmentTemplateId, equipmentTemplateId));
        eventConditionService.deleteByEquipmentTemplateId(equipmentTemplateId);
        if (isEventexTableExit()){
            eventExService.deleteByEquipmentTemplateId(equipmentTemplateId);
        }
    }

    @Override
    public EventConfigItem getEventInfo(Integer equipmentTemplateId, Integer eventId) {
        return findByEquipmentTemplateIdAndEventId(equipmentTemplateId, eventId);
    }

    @Override
    public EventConfigItem findByEquipmentTemplateIdAndEventId(Integer equipmentTemplateId, Integer eventId) {
        if (isEventexTableExit()){
            return eventMapper.findByEquipmentTemplateIdAndEventId(equipmentTemplateId, eventId);
        }
        else {
            return eventMapper.findByEquipmentTemplateIdAndEventIdOMC(equipmentTemplateId, eventId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean linkEvent(Integer equipmentTemplateId, Integer signalId) {
        try {
            // 获取最大显示索引
            Integer displayIndex = getNextDisplayIndex(equipmentTemplateId);

            // 创建事件
            Event event = new Event();
            event.setEquipmentTemplateId(equipmentTemplateId);
            event.setStartType(1); // 默认开始类型
            event.setEndType(2);   // 默认结束类型
            event.setEventCategory(1); // 默认事件类别
            event.setSignalId(signalId);
            event.setEnable(true);
            event.setVisible(true);
            event.setDisplayIndex(displayIndex);

            // 查找最大事件ID
            Integer eventId = findMaxEventIdByEquipmentTemplateId(equipmentTemplateId);
            event.setEventId(eventId);

            // 设置事件名称（默认使用模板ID.事件ID）
            event.setEventName(equipmentTemplateId + "." + eventId);

            // 保存事件
            save(event);

            // 创建默认事件条件
            EventCondition condition = new EventCondition();
            condition.setEquipmentTemplateId(equipmentTemplateId);
            condition.setEventId(eventId);
            condition.setEventConditionId(1); // 默认条件ID
            condition.setStartOperation("="); // 默认开始操作
            condition.setStartCompareValue(1.0); // 默认比较值
            condition.setStartDelay(0); // 默认延迟
            condition.setEndOperation("="); // 默认结束操作
            condition.setEndCompareValue(0.0); // 默认结束比较值
            condition.setEndDelay(0); // 默认结束延迟
            condition.setEventSeverity(3); // 默认严重度

            // 保存条件
            eventConditionService.save(condition);

            return true;
        } catch (Exception e) {
            log.error("关联事件失败", e);
            throw e;
        }
    }

    /**
     * 获取下一个显示索引
     * @param equipmentTemplateId 设备模板ID
     * @return 下一个显示索引
     */
    private Integer getNextDisplayIndex(Integer equipmentTemplateId) {
        Integer maxIndex = eventMapper.findMaxDisplayIndexByEquipmentTemplateId(equipmentTemplateId);
        return maxIndex == null ? 1 : maxIndex + 1;
    }

    /**
     * 查找最大事件ID
     * @param equipmentTemplateId 设备模板ID
     * @return 最大事件ID
     */
    private Integer findMaxEventIdByEquipmentTemplateId(Integer equipmentTemplateId) {
        Integer maxId = eventMapper.findMaxEventIdByEquipmentTemplateId(equipmentTemplateId);
        return maxId == null ? 1 : maxId + 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchsaveLianTongEvents() {
        try {
            List<Event> events = new ArrayList<>();

            // 创建联通事件
            // 这里需要实现批量保存联通事件的逻辑
            // 由于没有具体的数据，这里只提供一个空实现
            log.warn("batchsaveLianTongEvents method is not fully implemented");
            return true;
        } catch (Exception e) {
            log.error("Failed to batch save LianTong events", e);
            return false;
        }
    }

    @Override
    public void updateWorkStationEventName(String workStationName, Integer equipmentTemplateId) {
        if (workStationName == null || equipmentTemplateId == null) {
            throw new IllegalArgumentException("WorkStationName and equipmentTemplateId cannot be null");
        }

        log.info("Updating work station event names with prefix: {} for equipmentTemplateId: {}",
                workStationName, equipmentTemplateId);

        eventMapper.updateWorkStationEventName(workStationName, equipmentTemplateId);
    }

    @Override
    public void updateDBWorkStationEventName(String eventName, Integer equipmentTemplateId, Integer workStationId) {
        if (eventName == null || equipmentTemplateId == null || workStationId == null) {
            throw new IllegalArgumentException("EventName, equipmentTemplateId and workStationId cannot be null");
        }

        log.info("Updating event name to: {} for equipmentTemplateId: {} and eventId: {}",
                eventName, equipmentTemplateId, workStationId);

        // Use UpdateWrapper to update specific event name
        update(Wrappers.lambdaUpdate(Event.class)
                .set(Event::getEventName, eventName)
                .eq(Event::getEquipmentTemplateId, equipmentTemplateId)
                .eq(Event::getEventId, workStationId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSelfDiagnosisEvent(Integer equipmentTemplateId, Integer centerId) {
        if (equipmentTemplateId == null || centerId == null) {
            throw new IllegalArgumentException("EquipmentTemplateId and centerId cannot be null");
        }

        log.info("Updating self diagnosis event IDs for equipmentTemplateId: {} with centerId: {}",
                equipmentTemplateId, centerId);

        eventMapper.updateEventIdAndStartExpressionAndSignalId(centerId, equipmentTemplateId);
    }

    @Override
    public EventConfigItem findMaxEventByEquipmentTemplateId(Integer equipmentTemplateId) {
        if (equipmentTemplateId == null) {
            throw new IllegalArgumentException("EquipmentTemplateId cannot be null");
        }

        log.debug("Finding maximum event by equipmentTemplateId: {}", equipmentTemplateId);
        if (isEventexTableExit()){
            return eventMapper.findMaxEventByEquipmentTemplateId(equipmentTemplateId);
        }
        else {
            return eventMapper.findMaxEventByEquipmentTemplateIdOMC(equipmentTemplateId);
        }
    }

    @Override
    public void createCommunicationStateEvent(Integer equipmentTemplateId) {
        if (communicationStateEventExists(equipmentTemplateId)) {
            return;
        }

        Event event = new Event();
        event.setEquipmentTemplateId(equipmentTemplateId);
        event.setEventId(EventConstant.COMMUNICATION_STATE_EVENT);
        event.setEventName(i18n.T("monitor.equipment.communicationState"));
        event.setSignalId(EventConstant.COMMUNICATION_STATE_EVENT);
        event.setStartExpression("[-1,-3]");
        event.setEventCategory(EventCategoryEnum.EQUIPMENT_COMMUNICATION_STATE_EVENT.getValue());
        event.setStartType(StartTypeEnum.CONDITIONAL_EVENT.getValue());
        event.setEndType(EndTypeEnum.CONTINUAL_EVENT.getValue());
        event.setEnable(true);
        event.setVisible(true);

        save(event);

        EventCondition eventCondition = new EventCondition();
        eventCondition.setEquipmentTemplateId(equipmentTemplateId);
        eventCondition.setEventId(event.getEventId());
        eventCondition.setEventConditionId(0);
        eventCondition.setEventSeverity(EventSeverityEnum.LEVEL_1.getValue());
        eventCondition.setStartOperation("=");
        eventCondition.setStartCompareValue((double) 0);
        eventCondition.setStartDelay(0);
        eventCondition.setMeanings(i18n.T("monitor.equipment.communicationFail"));

        eventConditionService.createEventCondition(eventCondition);
    }

    /**
     * 是否存在设备通信状态事件
     *
     * @param equipmentTemplateId 设备模板
     * @return true存在  false不存在
     */
    private boolean communicationStateEventExists(Integer equipmentTemplateId) {
        return count(Wrappers.lambdaQuery(Event.class)
                .eq(Event::getEquipmentTemplateId, equipmentTemplateId)
                .eq(Event::getEventId, EventConstant.COMMUNICATION_STATE_EVENT)) > 0;
    }

    @Override
    public List<Long> findBaseTypeIdsNotInEventBaseDicForEquipmentTemplate(Integer equipmentTemplateId) {
        return eventMapper.findBaseTypeIdsNotInEventBaseDicForEquipmentTemplate(equipmentTemplateId);
    }

    @Override
    public void createEvent(Event event) {
        save(event);
    }

    @Override
    public void batchInsertEvent(List<Event> batchEventList) {
        for (Event event : batchEventList) {
            save(event);
        }
    }

    @Override
    public Long applyStandard(Integer standardId, List<Integer> equipmentTemplateIds) {
        // TODO: 实现事件标准化应用逻辑
        // 这里应该根据标准ID和设备模板ID列表来应用事件标准化
        // 暂时返回0，实际实现需要根据业务逻辑来完成
        return 0L;
    }

    /**
     * 发送设备模板变更记录
     *
     * @param equipmentTemplateId 设备模板id
     */
    public void sendUpdateMsgByEquipmentTemplateId(Integer equipmentTemplateId) {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                EquipmentTemplate equipmentTemplate = equipmentTemplateMapper.selectById(equipmentTemplateId);
                if (Objects.isNull(equipmentTemplate)) {
                    return;
                }
                //告警的所有变更属于模板的更新
                changeEventService.sendUpdate(equipmentTemplate);
            }
        });
    }

    // ==================== 设备管理相关方法实现 ====================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Event createEvent(EventConfigItem eventConfigItem) {
        try {
            // 创建事件实体
            Event event = BeanUtil.copyProperties(eventConfigItem, Event.class, "id");

            // 如果事件ID为空或为生成标志，则生成新的事件ID
            if (event.getEventId() == null || Objects.equals(event.getEventId(), EventConstant.GENERATE_EVENT_ID_FLAG)) {
                Integer eventId = findMaxEventIdByEquipmentTemplateId(event.getEquipmentTemplateId());
                event.setEventId(eventId);
            }

            // 保存事件
            save(event);

            // 保存事件条件
            if (!CollectionUtils.isEmpty(eventConfigItem.getEventCondition())) {
                List<EventCondition> eventConditions = eventConfigItem.getEventCondition();
                eventConditions.forEach(condition -> {
                    condition.setEventId(event.getEventId());
                    condition.setEquipmentTemplateId(event.getEquipmentTemplateId());
                });
                eventConditionService.batchCreate(eventConditions);
            }

            // 记录操作日志
            String objectId = event.getEquipmentTemplateId() + "." + event.getEventId();
            operationDetailService.recordOperationLog(
                    TokenUserSiteWebUtil.getLoginUserId(),
                    objectId,
                    OperationObjectTypeEnum.EVENT,
                    i18n.T("event.eventName"),
                    i18n.T("add"),
                    "",
                    eventConfigItem.getEventName()
            );

            // 发送更新消息
            sendUpdateMsgByEquipmentTemplateId(eventConfigItem.getEquipmentTemplateId());

            return event;
        } catch (Exception e) {
            log.error("Failed to create event for device management", e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Event updateEvent(EventConfigItem eventConfigItem) {
        try {
            // 检查事件是否存在
            Event existingEvent = getOne(Wrappers.lambdaQuery(Event.class)
                    .eq(Event::getEquipmentTemplateId, eventConfigItem.getEquipmentTemplateId())
                    .eq(Event::getEventId, eventConfigItem.getEventId()));

            if (existingEvent == null) {
                throw new RuntimeException("Event not found: " + eventConfigItem.getEquipmentTemplateId() + "." + eventConfigItem.getEventId());
            }

            // 更新事件实体
            Event event = BeanUtil.copyProperties(eventConfigItem, Event.class);
            event.setId(existingEvent.getId()); // 保持原有ID

            // 更新事件
            updateById(event);

            // 更新事件条件
            if (!CollectionUtils.isEmpty(eventConfigItem.getEventCondition())) {
                eventConditionService.updateEventCondition(
                        eventConfigItem.getEquipmentTemplateId(),
                        eventConfigItem.getEventId(),
                        eventConfigItem.getEventCondition()
                );
            }

            // 记录操作日志
            String objectId = event.getEquipmentTemplateId() + "." + event.getEventId();
            operationDetailService.recordOperationLog(
                    TokenUserSiteWebUtil.getLoginUserId(),
                    objectId,
                    OperationObjectTypeEnum.EVENT,
                    i18n.T("event.eventName"),
                    i18n.T("update"),
                    existingEvent.getEventName(),
                    eventConfigItem.getEventName()
            );

            // 发送更新消息
            sendUpdateMsgByEquipmentTemplateId(eventConfigItem.getEquipmentTemplateId());

            return event;
        } catch (Exception e) {
            log.error("Failed to update event for device management", e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateEvent(BatchEventConfigItem batchEventConfigItem) {
        try {
            batchUpdateByEventItem(batchEventConfigItem);
            return true;
        } catch (Exception e) {
            log.error("Failed to batch update event for device management", e);
            throw e;
        }
    }

    @Override
    public void updateEventDescriptions(Integer childTemplateId, List<Event> parentEvents) {
        if (parentEvents.isEmpty()) {
            return;
        }

        // 转换为Map便于查找
        Map<Integer, String> eventDescMap = parentEvents.stream()
                .collect(Collectors.toMap(Event::getEventId, Event::getDescription));

        // 批量更新
        for (Map.Entry<Integer, String> entry : eventDescMap.entrySet()) {
            Integer eventId = entry.getKey();
            String description = entry.getValue();

            int updated = eventMapper.update(null,
                    new UpdateWrapper<Event>()
                            .eq("EquipmentTemplateId", childTemplateId)
                            .eq("EventId", eventId)
                            .set("Description", description)
            );

            if (updated > 0) {
                log.debug("子模版 {} 的事件 {} 描述已更新", childTemplateId, eventId);
            }
        }
    }

    @Override
    public List<EventExcel> findExcelDtoByEquipmentTemplateId(Integer equipmentTemplateId) {
        return eventMapper.findExcelDtoByEquipmentTemplateId(equipmentTemplateId);
    }

    @Override
    public void copyEvent(Integer originEquipmentTemplateId, Integer destEquipmentTemplateId) {
        List<EventConfigItem> eventConfigItemList = findEventItemByEquipmentTemplateId(originEquipmentTemplateId);
        List<Event> eventList = BeanUtil.copyToList(eventConfigItemList, Event.class);
        if (CollUtil.isEmpty(eventList)) {
            return;
        }
        eventList.forEach(event -> {
            event.setEquipmentTemplateId(destEquipmentTemplateId);
            event.setId(null);
        });
        batchInsertEvent(eventList);
        eventConditionService.copyEventCondition(originEquipmentTemplateId, destEquipmentTemplateId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean fieldCopyEvent(List<EventFieldCopyDTO> eventFieldCopyList) {
        if (eventFieldCopyList == null || eventFieldCopyList.isEmpty()) {
            return true;
        }

        try {
            List<Event> eventList = new ArrayList<>();
            for (EventFieldCopyDTO dto : eventFieldCopyList) {
                Integer equipmentTemplateId = dto.getEquipmentTemplateId();
                Integer eventId = dto.getEventId();
                String fieldName = dto.getFieldName();
                String fieldValue = dto.getFieldValue();

                if ("eventConditionList".equals(fieldName)) {
                    handleEventCondition(equipmentTemplateId, eventId, fieldValue);
                    continue;
                }

                Event event = new Event();
                event.setId(null); // 重要：设置为null，因为是自增主键
                event.setEquipmentTemplateId(equipmentTemplateId);
                event.setEventId(eventId);

                // 使用反射设置字段值
                try {
                    Field field = Event.class.getDeclaredField(fieldName);
                    field.setAccessible(true);
                    field.set(event, fieldValue);
                    eventList.add(event);
                } catch (NoSuchFieldException | IllegalAccessException e) {
                    log.warn("Failed to set field {} for event {}: {}", fieldName, eventId, e.getMessage());
                }
            }

            if (!eventList.isEmpty()) {
                batchUpdateField(eventList);
            }
            return true;
        } catch (Exception e) {
            log.error("Failed to copy event fields", e);
            throw e;
        }
    }

    /**
     * 处理事件条件复制
     * 按照原配置工具的逻辑实现
     */
    private void handleEventCondition(Integer srcTemplateId, Integer srcEventId, String fieldValue) {
        String[] dest = fieldValue.split("\\.");
        Integer destTemplateId = Integer.parseInt(dest[0]);
        Integer destEventId = Integer.parseInt(dest[1]);

        List<EventCondition> conditions = eventConditionService.findByEquipmentTemplateIdAndEventId(srcTemplateId, srcEventId);
        List<EventCondition> copied = conditions.stream()
                .map(c -> {
                    EventCondition copiedCondition = new EventCondition();
                    BeanUtils.copyProperties(c, copiedCondition);
                    copiedCondition.setId(null); // 重要：设置为null，因为是自增主键
                    copiedCondition.setEquipmentTemplateId(destTemplateId);
                    copiedCondition.setEventId(destEventId);
                    return copiedCondition;
                })
                .collect(Collectors.toList());

        eventConditionService.updateEventCondition(destTemplateId, destEventId, copied);
    }

    /**
     * 批量更新事件字段
     * 对应原配置工具的 eventMapper.batchUpdateField(eventList)
     */
    private void batchUpdateField(List<Event> eventList) {
        if (eventList == null || eventList.isEmpty()) {
            return;
        }

        // 使用 MyBatis-Plus 的批量更新功能
        // 注意：这里需要根据 equipmentTemplateId 和 eventId 进行更新，而不是根据主键 id
        for (Event event : eventList) {
            update(event, Wrappers.lambdaUpdate(Event.class)
                    .eq(Event::getEquipmentTemplateId, event.getEquipmentTemplateId())
                    .eq(Event::getEventId, event.getEventId()));
        }
    }

    boolean isEventexTableExit(){
        if (ObjectUtil.isNull(tableExistenceChecker)) {
            //上下文中没有找到表存在性检查器 ,跳过检查逻辑
            return true;
        }
        return tableExistenceChecker.isTableExists("tbl_eventex");
    }
}
