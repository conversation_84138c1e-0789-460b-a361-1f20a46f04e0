package com.siteweb.tcs.siteweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.siteweb.entity.TaskStatus;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务状态 Mapper
 */
@Mapper
@Repository
public interface TaskStatusMapper extends BaseMapper<TaskStatus> {

    /**
     * 删除过期的任务状态记录
     * @param expireTime 过期时间
     * @return 删除的记录数
     */
    int deleteExpiredTasks(@Param("expireTime") LocalDateTime expireTime);

    /**
     * 根据任务类型查询活跃的任务
     * @param taskType 任务类型
     * @return 任务列表
     */
    List<TaskStatus> findActiveTasksByType(@Param("taskType") String taskType);

    /**
     * 根据监控单元ID查询相关任务
     * @param monitorUnitId 监控单元ID
     * @return 任务列表
     */
    List<TaskStatus> findTasksByMonitorUnitId(@Param("monitorUnitId") Integer monitorUnitId);
}
