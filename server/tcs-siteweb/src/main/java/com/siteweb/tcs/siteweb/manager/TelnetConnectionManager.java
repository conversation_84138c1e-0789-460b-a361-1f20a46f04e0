package com.siteweb.tcs.siteweb.manager;

import com.siteweb.tcs.siteweb.dto.MonitorUnitDTO;
import com.siteweb.tcs.siteweb.service.IMonitorUnitService;
import com.siteweb.tcs.siteweb.service.ITaskStatusService;
import com.siteweb.tcs.siteweb.enums.TaskTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@Component
public class TelnetConnectionManager {
    private static final Logger log = LoggerFactory.getLogger(TelnetConnectionManager.class);
    private final Map<String, TelnetConnection> connections;
    private final ExecutorService executor;
    private static final long TIMEOUT = TimeUnit.MINUTES.toMillis(5);
    private final ITaskStatusService taskStatusService;
    private final IMonitorUnitService monitorUnitService;

    public TelnetConnectionManager(ITaskStatusService taskStatusService, IMonitorUnitService monitorUnitService) {
        this.taskStatusService = taskStatusService;
        this.monitorUnitService = monitorUnitService;
        connections = new ConcurrentHashMap<>();
        executor = Executors.newCachedThreadPool();
    }

    public void addConnection(Integer monitorUnitId, int port, String uniqueId) {
        // 创建Telnet连接任务
        taskStatusService.createTask(uniqueId, TaskTypeEnum.TELNET_CONNECTION, monitorUnitId);

        MonitorUnitDTO monitorUnit = monitorUnitService.findById(monitorUnitId);
        if (monitorUnit == null) {
            taskStatusService.setTaskError(uniqueId, "不存在此监控单元");
            return;
        }

        TelnetConnection connection = new TelnetConnection(monitorUnit.getIpAddress(), port, monitorUnitId);
        connection.setMessageListener(message -> {
            // 处理读取到的消息并通过任务状态服务发送
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.TELNET_CONNECTION, message, false);
        });
        connections.put(monitorUnitId + ":" + uniqueId, connection);

        // Start a thread to read the response
        executor.submit(connection::readResponse);

        // 更新任务状态为运行中
        taskStatusService.updateTaskStatus(uniqueId, com.siteweb.tcs.siteweb.enums.TaskStatusEnum.RUNNING,
            String.format("Telnet连接已建立到 %s:%d", monitorUnit.getIpAddress(), port), false);
    }

    public void sendCommandToAll(String command) {
        for (TelnetConnection connection : connections.values()) {
            executor.submit(() -> connection.sendCommand(command));
        }
    }

    public void sendCommandToMonitorUnit(String key, String command) {
        TelnetConnection connection = connections.get(key);
        if (connection != null) {
            executor.submit(() -> connection.sendCommand(command));
        } else {
            log.error("key ID {} not found.", key);
        }
    }

    @Scheduled(fixedRate = 60000)
    public void checkConnections() {
        long currentTime = System.currentTimeMillis();

        for (Iterator<Map.Entry<String, TelnetConnection>> it = connections.entrySet().iterator(); it.hasNext(); ) {
            Map.Entry<String, TelnetConnection> entry = it.next();
            TelnetConnection connection = entry.getValue();
            if (currentTime - connection.getLastActivityTime() > TIMEOUT) {
                connection.close();
                it.remove();
                log.info("Connection for key ID {} closed due to inactivity.", entry.getKey());
            }
        }
    }

    public void shutdown() {
        executor.shutdown();
        try {
            if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
        }

        for (TelnetConnection connection : connections.values()) {
            connection.close();
        }
    }

    // 判断连接是否可用
    public boolean isConnectionAvailable(String key) {
        TelnetConnection connection = connections.get(key);
        return connection != null && connection.getTelnet().isAvailable();
    }

    // 判断连接是否存在
    public boolean isConnectionExist(String uniqueId) {
        return connections.containsKey(uniqueId);
    }
}
