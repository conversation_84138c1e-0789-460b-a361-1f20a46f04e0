package com.siteweb.tcs.siteweb.vo;

import com.siteweb.tcs.siteweb.entity.SamplerUnit;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 采集单元树形结构 VO
 * 用于采样器树的采集单元节点展示
 */
@Data
public class SamplerUnitTreeVO {
    private Integer samplerUnitId;
    private String samplerUnitName;
    private Integer samplerId;
    private Short samplerType;
    private List<EquipmentTreeVO> equipments = new ArrayList<>();

    /**
     * 从 SamplerUnit 实体复制数据
     *
     * @param samplerUnit 采集单元实体
     * @return 当前 VO 对象
     */
    public SamplerUnitTreeVO copy(SamplerUnit samplerUnit) {
        this.setSamplerUnitId(samplerUnit.getSamplerUnitId());
        this.setSamplerUnitName(samplerUnit.getSamplerUnitName());
        this.setSamplerId(samplerUnit.getSamplerId());
        this.setSamplerType(samplerUnit.getSamplerType());
        return this;
    }
}
