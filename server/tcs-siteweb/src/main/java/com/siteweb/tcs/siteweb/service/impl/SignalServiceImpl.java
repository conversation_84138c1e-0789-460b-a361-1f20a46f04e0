package com.siteweb.tcs.siteweb.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.comparator.CompareUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.constants.SignalConstant;
import com.siteweb.tcs.siteweb.dto.SignalConfigItem;
import com.siteweb.tcs.siteweb.dto.SignalFieldCopyDTO;
import com.siteweb.tcs.siteweb.dto.SimplifySignalDTO;
import com.siteweb.tcs.siteweb.dto.excel.SignalExcel;
import com.siteweb.tcs.siteweb.entity.AcrossMonitorUnitSignal;
import com.siteweb.tcs.siteweb.entity.Signal;
import com.siteweb.tcs.siteweb.entity.SignalMeanings;
import com.siteweb.tcs.siteweb.entity.SignalProperty;
import com.siteweb.tcs.siteweb.enums.*;
import com.siteweb.tcs.siteweb.exception.BusinessException;
import com.siteweb.tcs.siteweb.mapper.AcrossMonitorUnitSignalMapper;
import com.siteweb.tcs.siteweb.mapper.SignalMapper;
import com.siteweb.tcs.siteweb.service.IOperationDetailService;
import com.siteweb.tcs.siteweb.service.IPrimaryKeyValueService;
import com.siteweb.tcs.siteweb.service.ISignalService;
import com.siteweb.tcs.siteweb.service.ISignalMeaningsService;
import com.siteweb.tcs.siteweb.service.ISignalPropertyService;
import com.siteweb.tcs.siteweb.util.I18n;
import com.siteweb.tcs.siteweb.util.TokenUserSiteWebUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Signal Service Implementation
 */
@Slf4j
@Service
public class SignalServiceImpl extends ServiceImpl<SignalMapper, Signal> implements ISignalService {

    @Autowired
    private SignalMapper signalMapper;
    @Autowired
    private AcrossMonitorUnitSignalMapper acrossMonitorUnitSignalMapper;

    @Autowired(required = false)
    private IPrimaryKeyValueService primaryKeyValueService;

    @Autowired(required = false)
    private IOperationDetailService operationDetailService;

    @Autowired(required = false)
    private ISignalMeaningsService signalMeaningsService;

    @Autowired(required = false)
    private ISignalPropertyService signalPropertyService;

    @Autowired
    private I18n i18n;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Signal createSignal(SignalConfigItem signalConfigItem) {
        // Create signal entity from DTO
        Signal signal = new Signal();
        BeanUtils.copyProperties(signalConfigItem, signal, "id", "signalId");

        // Generate signal ID if needed
        if (signal.getSignalId() == null || signal.getSignalId() == -99999) {
            // Find max signal ID for this equipment template
            Integer signalId = findMaxSignalIdByEquipmentTemplateId(signal.getEquipmentTemplateId());
            signal.setSignalId(signalId);
        }

        // Validate signal name - check for duplicates
        List<Signal> existingSignals = findByEquipmentTemplateId(signal.getEquipmentTemplateId());
        if (!CollectionUtils.isEmpty(existingSignals)) {
            for (Signal existingSignal : existingSignals) {
                // Check for duplicate signal name
                if (Objects.equals(existingSignal.getSignalName(), signal.getSignalName())) {
                    throw new RuntimeException("Signal name already exists: " + signal.getSignalName());
                }

                // Check for duplicate channel number
                // Channel numbers > 0 or equal to -3 must be unique
                if ((signal.getChannelNo() > 0 || signal.getChannelNo() == -3) &&
                        Objects.equals(existingSignal.getChannelNo(), signal.getChannelNo())) {
                    throw new RuntimeException("Channel number already exists: " + signal.getChannelNo());
                }
            }
        }

        // Set default module number if not provided
        if (signal.getModuleNo() == null) {
            signal.setModuleNo(0);
        }

        // Set default values for enable/visible if not provided
        if (signal.getEnable() == null) {
            signal.setEnable(true);
        }
        if (signal.getVisible() == null) {
            signal.setVisible(true);
        }

        // Save the signal
        boolean success = save(signal);
        if (!success) {
            throw new RuntimeException("Failed to save signal");
        }

        // Create signal meanings if provided
        if (signalMeaningsService != null && !CollectionUtils.isEmpty(signalConfigItem.getSignalMeaningsList())) {
            List<SignalMeanings> meaningsList = signalConfigItem.getSignalMeaningsList();
            // Set signal ID for each meaning
            meaningsList.forEach(meaning -> meaning.setSignalId(signal.getSignalId()));
            signalMeaningsService.batchCreateSignalMeanings(meaningsList);
        }

        // Create signal properties if provided
        if (signalPropertyService != null && !CollectionUtils.isEmpty(signalConfigItem.getSignalPropertyList())) {
            List<SignalProperty> propertyList = signalConfigItem.getSignalPropertyList();
            // Set signal ID for each property
            propertyList.forEach(property -> property.setSignalId(signal.getSignalId()));
            signalPropertyService.batchCreateSignalProperty(propertyList);
        }

        // Update the DTO with generated values
        signalConfigItem.setId(signal.getId());
        signalConfigItem.setSignalId(signal.getSignalId());

        // Record operation log
        if (operationDetailService != null) {
            try {
                operationDetailService.recordOperationLog(
                        TokenUserSiteWebUtil.getLoginUserId(), // User ID
                        signal.getSignalId().toString(),
                        OperationObjectTypeEnum.SIGNAL,
                        "signal.signalName",
                        "添加",
                        "",
                        signal.getSignalName()
                );
            } catch (Exception e) {
                log.warn("Failed to record operation log", e);
            }
        }

        return signal;
    }

    /**
     * Find the maximum signal ID for an equipment template and increment it
     */
    public Integer findMaxSignalIdByEquipmentTemplateId(Integer equipmentTemplateId) {
        Integer maxSignalId = signalMapper.findMaxSignalIdByEquipmentTemplateId(equipmentTemplateId);
        if (maxSignalId != null) {
            return ++maxSignalId;
        }
        // If no signals exist yet, get a global ID from the primary key service
        if (primaryKeyValueService != null) {
            return primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_SIGNAL, 0);
        }
        // Fallback to starting value if service is not available
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Signal updateSignal(SignalConfigItem signalConfigItem) {
        if (signalConfigItem == null || signalConfigItem.getSignalId() == null
                || signalConfigItem.getEquipmentTemplateId() == null) {
            throw new IllegalArgumentException("Signal ID and equipment template ID cannot be null");
        }

        // Check if signal exists
        SignalConfigItem oldSignal = findByEquipmentTemplateIdAndSignalId(
                signalConfigItem.getEquipmentTemplateId(), signalConfigItem.getSignalId());
        if (oldSignal == null) {
            throw new RuntimeException("Signal not found");
        }

        // Validate signal name
        if (existsByNameInTemplate(signalConfigItem.getEquipmentTemplateId(),
                signalConfigItem.getSignalName(), signalConfigItem.getSignalId())) {
            throw new RuntimeException("Signal name already exists in the equipment template");
        }

        // Create signal entity from DTO
        Signal signal = new Signal();
        BeanUtils.copyProperties(signalConfigItem, signal);

        // Record operation log
        if (operationDetailService != null) {
            try {
                operationDetailService.compareEntitiesRecordLog(
                        null, // User ID
                        oldSignal,
                        signal
                );
            } catch (Exception e) {
                log.warn("Failed to record operation log", e);
            }
        }

        // Update signal
        boolean success = updateById(signal);
        if (!success) {
            throw new RuntimeException("Failed to update signal");
        }

        return getById(signal.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteSignal(Integer equipmentTemplateId, Integer signalId) {
        if (equipmentTemplateId == null || signalId == null) {
            return 0;
        }

        // Check if signal exists
        SignalConfigItem signal = findByEquipmentTemplateIdAndSignalId(equipmentTemplateId, signalId);
        if (signal == null) {
            return 0;
        }

        // Delete signal using Lambda query wrapper (using standard MyBatis-Plus method)
        LambdaQueryWrapper<Signal> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Signal::getEquipmentTemplateId, equipmentTemplateId)
                .eq(Signal::getSignalId, signalId);

        boolean success = remove(queryWrapper);

        // Record operation log
        if (success && operationDetailService != null) {
            try {
                operationDetailService.recordOperationLog(
                        null, // User ID
                        signal.getSignalId().toString(),
                        OperationObjectTypeEnum.SIGNAL,
                        "signal.signalName",
                        "删除",
                        signal.getSignalName(),
                        ""
                );
            } catch (Exception e) {
                log.warn("Failed to record operation log", e);
            }
        }

        return success ? 1 : 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchDeleteSignal(Integer equipmentTemplateId, List<Integer> signalIds) {
        if (equipmentTemplateId == null || CollectionUtils.isEmpty(signalIds)) {
            return 0;
        }

        // Find signals first for logging
        List<Signal> signals = findSignalsByIdsForLogging(equipmentTemplateId, signalIds);

        if (CollectionUtils.isEmpty(signals)) {
            return 0;
        }

        // Use custom mapper method for batch delete
        int deletedCount = signalMapper.batchDelete(equipmentTemplateId, signalIds);

        // Record operation logs
        if (deletedCount > 0 && operationDetailService != null) {
            for (Signal signal : signals) {
                try {
                    operationDetailService.recordOperationLog(
                            null, // User ID
                            signal.getSignalId().toString(),
                            OperationObjectTypeEnum.SIGNAL,
                            "signal.signalName",
                            "删除",
                            signal.getSignalName(),
                            ""
                    );
                } catch (Exception e) {
                    log.warn("Failed to record operation log for signal ID {}", signal.getSignalId(), e);
                }
            }
        }

        return deletedCount;
    }

    /**
     * Helper method to find signals by IDs for logging
     */
    private List<Signal> findSignalsByIdsForLogging(Integer equipmentTemplateId, List<Integer> signalIds) {
        LambdaQueryWrapper<Signal> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Signal::getEquipmentTemplateId, equipmentTemplateId)
                .in(Signal::getSignalId, signalIds);
        return list(queryWrapper);
    }

    @Override
    public List<Signal> findByEquipmentTemplateId(Integer equipmentTemplateId) {
        if (equipmentTemplateId == null) {
            return new ArrayList<>();
        }

        // Use custom mapper method
        return signalMapper.findByTemplateId(equipmentTemplateId);
    }

    @Override
    public SignalConfigItem findByEquipmentTemplateIdAndSignalId(Integer equipmentTemplateId, Integer signalId) {
        if (equipmentTemplateId == null || signalId == null) {
            return null;
        }

        Signal signal = signalMapper.findSignalEntityByTemplateIdAndSignalId(equipmentTemplateId, signalId);
        if (signal == null) {
            return null;
        }

        SignalConfigItem signalConfigItem = new SignalConfigItem();
        BeanUtils.copyProperties(signal, signalConfigItem);

        if (signalMeaningsService != null) {
            List<SignalMeanings> meaningsList = signalMeaningsService.findByEquipmentTemplateIdAndSignalId(equipmentTemplateId, signalId);
            signalConfigItem.setSignalMeaningsList(meaningsList);
        }

        if (signalPropertyService != null) {
            List<SignalProperty> propertyList = signalPropertyService.findByEquipmentTemplateIdAndSignalId(equipmentTemplateId, signalId);
            signalConfigItem.setSignalPropertyList(propertyList);
        }

        // The acrossSignal flag might need to be set here if logic for it exists.
        // For now, it will default to null or whatever BeanUtils.copyProperties does.
        // Original siteweb6-config-server's SignalServiceImpl.findItemByEquipmentTemplateIdAndEquipmentId had logic for this.
        // If that's needed here, we'd need to replicate that, potentially fetching from TslAcrossMonitorUnitSignalMapper.

        return signalConfigItem;
    }

    @Override
    public boolean existsByNameInTemplate(Integer equipmentTemplateId, String signalName, Integer signalId) {
        if (equipmentTemplateId == null || signalName == null || signalName.isEmpty()) {
            return false;
        }

        LambdaQueryWrapper<Signal> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Signal::getEquipmentTemplateId, equipmentTemplateId)
                .eq(Signal::getSignalName, signalName);

        // If signalId is provided, exclude that signal from the check
        if (signalId != null) {
            queryWrapper.ne(Signal::getSignalId, signalId);
        }

        return count(queryWrapper) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchsaveLianTongSignal() {
        // TODO: Implement batch save lian tong signal logic
        log.warn("batchsaveLianTongSignal method not implemented yet");
        return true;
    }

    @Override
    public void updateWorkStationSignalName(String workStationName, Integer equipmentTemplateId) {
        if (workStationName == null || equipmentTemplateId == null) {
            throw new IllegalArgumentException("WorkStationName and equipmentTemplateId cannot be null");
        }

        log.info("Updating work station signal names with prefix: {} for equipmentTemplateId: {}",
                workStationName, equipmentTemplateId);

        signalMapper.updateWorkStationSignalName(workStationName, equipmentTemplateId);
    }

    @Override
    public void updateDBWorkStationSignalName(String signalName, Integer equipmentTemplateId, Integer workStationId) {
        if (signalName == null || equipmentTemplateId == null || workStationId == null) {
            throw new IllegalArgumentException("SignalName, equipmentTemplateId and workStationId cannot be null");
        }

        log.info("Updating signal name to: {} for equipmentTemplateId: {} and signalId: {}",
                signalName, equipmentTemplateId, workStationId);

        // Use UpdateWrapper to update specific signal name
        UpdateWrapper<Signal> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("SignalName", signalName)
                .eq("EquipmentTemplateId", equipmentTemplateId)
                .eq("SignalId", workStationId);

        boolean success = update(updateWrapper);
        if (!success) {
            throw new RuntimeException("Failed to update signal name");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSelfDiagnosisSignal(Integer equipmentTemplateId, Integer centerId) {
        if (equipmentTemplateId == null || centerId == null) {
            throw new IllegalArgumentException("EquipmentTemplateId and centerId cannot be null");
        }

        log.info("Updating self diagnosis signal IDs for equipmentTemplateId: {} with centerId: {}",
                equipmentTemplateId, centerId);

        signalMapper.updateSelfDiagnosisSignal(equipmentTemplateId, centerId);
    }

    @Override
    public SignalConfigItem findMaxSignalByEquipmentTemplateId(Integer equipmentTemplateId) {
        if (equipmentTemplateId == null) {
            throw new IllegalArgumentException("EquipmentTemplateId cannot be null");
        }

        log.debug("Finding maximum signal by equipmentTemplateId: {}", equipmentTemplateId);

        return signalMapper.findMaxSignalByEquipmentTemplateId(equipmentTemplateId);
    }

    @Override
    public void createCommunicationStateSignal(Integer equipmentTemplateId) {
        if (existCommunicationStateSignal(equipmentTemplateId)) {
            return;
        }
        Signal communicationStateSignal = new Signal();
        communicationStateSignal.setEquipmentTemplateId(equipmentTemplateId);
        communicationStateSignal.setSignalId(SignalConstant.COMMUNICATION_STATE_SIGNAL);
        communicationStateSignal.setSignalName(i18n.T("monitor.equipment.communicationState"));
        communicationStateSignal.setSignalCategory(SignalCategoryEnum.SWITCH_SIGNAL.getValue());
        communicationStateSignal.setSignalType(SignalTypeEnum.VIRTUAL_SIGNAL.getValue());
        communicationStateSignal.setChannelNo(SignalConstant.COMMUNICATION_STATE_SIGNAL);
        communicationStateSignal.setChannelType(ChannelTypeEnum.ANALOG.getValue());
        communicationStateSignal.setDataType(DataTypeEnum.FLOAT.getValue());
        communicationStateSignal.setShowPrecision("0");
        communicationStateSignal.setEnable(true);
        communicationStateSignal.setVisible(true);
        communicationStateSignal.setModuleNo(0);
        communicationStateSignal.setDisplayIndex(findCurrentDisplayIndexByEquipmentTemplateId(equipmentTemplateId));

        // Create signal using direct save instead of createSignal to avoid validation conflicts
        createSignal(communicationStateSignal);

        //设备通信状态的信号属性
        if (signalPropertyService != null) {
            SignalProperty signalProperty = new SignalProperty();
            signalProperty.setEquipmentTemplateId(equipmentTemplateId);
            signalProperty.setSignalId(SignalConstant.COMMUNICATION_STATE_SIGNAL);
            signalProperty.setSignalPropertyId(27);
            signalPropertyService.createSignalProperty(signalProperty);
        }

        //设备通信状态的信号含义
        if (signalMeaningsService != null) {
            signalMeaningsService.communicationStateSignalMeaning(equipmentTemplateId);
        }
    }

    /**
     * 是否存在设备通信状态信号
     * 注：设备通信状态信号id是固定的一直是 -3
     *
     * @return true是  false否
     */
    private boolean existCommunicationStateSignal(Integer equipmentTemplateId) {
        return count(Wrappers.lambdaQuery(Signal.class)
                .eq(Signal::getEquipmentTemplateId, equipmentTemplateId)
                .eq(Signal::getSignalId, SignalConstant.COMMUNICATION_STATE_SIGNAL)) > 0;
    }

    /**
     * 根据设备模板取得信号当前的DisplayIndex
     *
     * @param equipmentTemplateId 模板id
     * @return 该模板中最大的DisplayIndex
     */
    private Integer findCurrentDisplayIndexByEquipmentTemplateId(Integer equipmentTemplateId) {
        Integer maxDisplayIndex = signalMapper.findMaxDisplayIndexByEquipmentTemplateId(equipmentTemplateId);
        if (maxDisplayIndex == null) {
            return 1;
        }
        return ++maxDisplayIndex;
    }

    @Override
    public List<Long> findBaseTypeIdsNotInSignalBaseDicForEquipmentTemplate(Integer equipmentTemplateId) {
        return signalMapper.findBaseTypeIdsNotInSignalBaseDicForEquipmentTemplate(equipmentTemplateId);
    }

    @Override
    public void createSignal(Signal signal) {
        if (Objects.isNull(signal.getSignalId()) || Objects.equals(signal.getSignalId(), SignalConstant.GENERATE_SIGNAL_ID_FLAG)) {
            List<SignalConfigItem> signalConfigItems = signalMapper.findSignalItemByEquipmentTemplateId(signal.getEquipmentTemplateId());
            // 信号名称不能重复、通道号 channelNo 不可为空值 大于0 时、-3 不可重复，小于等于0 可重复
            if (CollUtil.isNotEmpty(signalConfigItems)) {
                for (SignalConfigItem signalConfigItem : signalConfigItems) {
                    if (Objects.equals(signalConfigItem.getSignalName(), signal.getSignalName())) {
                        throw new BusinessException(i18n.T("error.duplicate.name", i18n.T("monitor.signal"), signal.getSignalName()));
                    }
                    if ((signal.getChannelNo() > 0 || signal.getChannelNo() == -3) && Objects.equals(signalConfigItem.getChannelNo(), signal.getChannelNo())) {
                        throw new BusinessException(i18n.T("error.duplicate.name", i18n.T("signal.channelNo"), signal.getChannelNo()));
                    }
                }
            }
            Integer signalId = findMaxSignalIdByEquipmentTemplateId(signal.getEquipmentTemplateId());
            signal.setSignalId(signalId);
        }
        signalMapper.insert(signal);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInsertSignal(List<Signal> batchSignalList) {
        for (Signal signal : batchSignalList) {
            save(signal);
        }
    }

    @Override
    public Long applyStandard(Integer standardId, List<Integer> equipmentTemplateIds) {
        // TODO: 实现信号标准化应用逻辑
        // 这里应该根据标准ID和设备模板ID列表来应用信号标准化
        // 暂时返回0，实际实现需要根据业务逻辑来完成
        return 0L;
    }

    // ==================== 设备管理相关方法实现 ====================

    @Override
    public List<SimplifySignalDTO> findSimplifySignals(Integer equipmentTemplateId) {
        try {
            return Optional.ofNullable(signalMapper.findSignalItemByEquipmentTemplateId(equipmentTemplateId)).orElseGet(ArrayList::new)
                    .stream()
                    .map(signalConfigItem -> SimplifySignalDTO
                            .builder()
                            .signalId(signalConfigItem.getSignalId())
                            .signalName(signalConfigItem.getSignalName())
                            .build())
                    .sorted(CompareUtil.comparingPinyin(SimplifySignalDTO::getSignalName))
                    .toList();
        } catch (Exception e) {
            log.error("Failed to find simplify signals", e);
            return List.of();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void copySignal(Integer originEquipmentTemplateId, Integer destEquipmentTemplateId) {
        List<Signal> signalList = findByEquipmentTemplateId(originEquipmentTemplateId);
        if (CollUtil.isEmpty(signalList)) {
            return;
        }
        signalList.forEach(signal -> {
            signal.setEquipmentTemplateId(destEquipmentTemplateId);
            signal.setId(null);
        });
        batchInsertSignal(signalList);
        signalMeaningsService.copySignalMeanings(originEquipmentTemplateId, destEquipmentTemplateId);
        signalPropertyService.copySignalProperty(originEquipmentTemplateId, destEquipmentTemplateId);
    }
    @Override
    public List<SignalConfigItem> findItemByEquipmentTemplateIdAndEquipmentId(Integer equipmentTemplateId, Integer equipmentId) {
        if (ObjectUtil.isNull(equipmentId)){
            return Optional.ofNullable(signalMapper.findSignalItemByEquipmentTemplateId(equipmentTemplateId)).orElseGet(List::of)
                    .stream()
                    .sorted(Comparator.comparingInt(SignalConfigItem::getDisplayIndex))
                    .toList();
        }
        // 获取跨站信号配置
        List<AcrossMonitorUnitSignal> acrossMonitorUnitSignals = acrossMonitorUnitSignalMapper.selectList(Wrappers.lambdaQuery(AcrossMonitorUnitSignal.class)
                .eq(AcrossMonitorUnitSignal::getEquipmentId, equipmentId));
        // 获取信号配置项并排序
        List<SignalConfigItem> signalConfigItems = Optional.ofNullable(signalMapper.findSignalItemByEquipmentTemplateId(equipmentTemplateId))
                .orElseGet(List::of)
                .stream()
                .sorted(Comparator.comparingInt(SignalConfigItem::getDisplayIndex))
                .collect(Collectors.toList());

        // 创建一个 Map 来存储跨站信号的 signalId
        Map<Integer, AcrossMonitorUnitSignal> acrossSignalMap = acrossMonitorUnitSignals.stream()
                .collect(Collectors.toMap(AcrossMonitorUnitSignal::getSignalId, Function.identity()));

        // 设置 acrossSignal 字段
        for (SignalConfigItem item : signalConfigItems) {
            item.setAcrossSignal(acrossSignalMap.containsKey(item.getSignalId()));
        }

        return signalConfigItems;
    }

    @Override
    public List<SignalExcel> findExcelDtoByEquipmentTemplateId(Integer equipmentTemplateId) {
        return signalMapper.findExcelDtoByEquipmentTemplateId(equipmentTemplateId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean fieldCopySignal(List<SignalFieldCopyDTO> signalFieldCopyList) {
        if (signalFieldCopyList == null || signalFieldCopyList.isEmpty()) {
            return true;
        }

        try {
            List<Signal> signalList = new ArrayList<>(signalFieldCopyList.size());
            for (SignalFieldCopyDTO dto : signalFieldCopyList) {
                Integer equipmentTemplateId = dto.getEquipmentTemplateId();
                Integer signalId = dto.getSignalId();
                String fieldName = dto.getFieldName();
                String fieldValue = dto.getFieldValue();

                // 复制 signalPropertyList 字段
                if ("signalPropertyList".equals(fieldName)) {
                    handleSignalProperty(equipmentTemplateId, signalId, fieldValue);
                    continue;
                }
                // 复制 signalMeaningsList 字段
                if ("signalMeaningsList".equals(fieldName)) {
                    handleSignalMeanings(equipmentTemplateId, signalId, fieldValue);
                    continue;
                }

                // 普通字段处理：反射赋值
                Signal signal = new Signal();
                signal.setId(null); // 重要：设置为null，因为是自增主键
                signal.setEquipmentTemplateId(equipmentTemplateId);
                signal.setSignalId(signalId);

                // 使用反射设置字段值
                try {
                    Field field = Signal.class.getDeclaredField(fieldName);
                    field.setAccessible(true);
                    field.set(signal, fieldValue);
                    signalList.add(signal);
                } catch (NoSuchFieldException | IllegalAccessException e) {
                    log.warn("Failed to set field {} for signal {}: {}", fieldName, signalId, e.getMessage());
                }
            }

            // 批量更新主表字段
            if (!signalList.isEmpty()) {
                batchUpdateField(signalList);
            }
            return true;
        } catch (Exception e) {
            log.error("Failed to copy signal fields", e);
            throw e;
        }
    }

    /**
     * 处理 signalPropertyList 字段：复制信号属性
     * 按照原配置工具的逻辑实现
     */
    private void handleSignalProperty(Integer srcTemplateId, Integer srcSignalId, String fieldValue) {
        String[] dest = fieldValue.split("\\.");
        Integer destTemplateId = Integer.parseInt(dest[0]);
        Integer destSignalId = Integer.parseInt(dest[1]);

        List<SignalProperty> properties = signalPropertyService.findByEquipmentTemplateIdAndSignalId(srcTemplateId, srcSignalId);
        List<SignalProperty> copied = properties.stream()
                .map(p -> {
                    SignalProperty copiedProperty = new SignalProperty();
                    BeanUtils.copyProperties(p, copiedProperty);
                    copiedProperty.setId(null); // 重要：设置为null，因为是自增主键
                    copiedProperty.setEquipmentTemplateId(destTemplateId);
                    copiedProperty.setSignalId(destSignalId);
                    return copiedProperty;
                })
                .collect(Collectors.toList());

        signalPropertyService.updateSignalProperty(destTemplateId, destSignalId, copied);
    }

    /**
     * 处理 signalMeaningsList 字段：复制信号含义
     * 按照原配置工具的逻辑实现
     */
    private void handleSignalMeanings(Integer srcTemplateId, Integer srcSignalId, String fieldValue) {
        String[] dest = fieldValue.split("\\.");
        Integer destTemplateId = Integer.parseInt(dest[0]);
        Integer destSignalId = Integer.parseInt(dest[1]);

        List<SignalMeanings> meanings = signalMeaningsService.findByEquipmentTemplateIdAndSignalId(srcTemplateId, srcSignalId);
        List<SignalMeanings> copied = meanings.stream()
                .map(m -> {
                    SignalMeanings copiedMeaning = new SignalMeanings();
                    BeanUtils.copyProperties(m, copiedMeaning);
                    copiedMeaning.setId(null); // 重要：设置为null，因为是自增主键
                    copiedMeaning.setEquipmentTemplateId(destTemplateId);
                    copiedMeaning.setSignalId(destSignalId);
                    return copiedMeaning;
                })
                .collect(Collectors.toList());

        signalMeaningsService.updateSignalMeanings(destTemplateId, destSignalId, copied);
    }

    /**
     * 批量更新信号字段
     * 对应原配置工具的 signalMapper.batchUpdateField(signalList)
     */
    private void batchUpdateField(List<Signal> signalList) {
        if (signalList == null || signalList.isEmpty()) {
            return;
        }

        // 使用 MyBatis-Plus 的批量更新功能
        // 注意：这里需要根据 equipmentTemplateId 和 signalId 进行更新，而不是根据主键 id
        for (Signal signal : signalList) {
            update(signal, Wrappers.lambdaUpdate(Signal.class)
                    .eq(Signal::getEquipmentTemplateId, signal.getEquipmentTemplateId())
                    .eq(Signal::getSignalId, signal.getSignalId()));
        }
    }


    @Override
    public void updateSignalDescriptions(Integer childTemplateId, List<Signal> parentSignals) {
        if (parentSignals.isEmpty()) {
            return;
        }

        // 转换为Map便于查找
        Map<Integer, String> signalDescMap = parentSignals.stream()
                .collect(Collectors.toMap(Signal::getSignalId, Signal::getDescription));

        // 批量更新
        for (Map.Entry<Integer, String> entry : signalDescMap.entrySet()) {
            Integer signalId = entry.getKey();
            String description = entry.getValue();

            int updated = signalMapper.update(null,
                    new UpdateWrapper<Signal>()
                            .eq("EquipmentTemplateId", childTemplateId)
                            .eq("SignalId", signalId)
                            .set("Description", description)
            );

            if (updated > 0) {
                log.debug("子模版 {} 的信号 {} 描述已更新", childTemplateId, signalId);
            }
        }
    }
}
