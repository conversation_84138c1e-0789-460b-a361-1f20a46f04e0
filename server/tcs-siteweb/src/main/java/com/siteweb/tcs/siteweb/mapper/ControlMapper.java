package com.siteweb.tcs.siteweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.siteweb.dto.ControlConfigItem;
import com.siteweb.tcs.siteweb.dto.ControlPointDTO;
import com.siteweb.tcs.siteweb.dto.SimilarDataDTO;
import com.siteweb.tcs.siteweb.dto.excel.ControlExcel;
import com.siteweb.tcs.siteweb.entity.Control;
import com.siteweb.tcs.siteweb.vo.ControlVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Control Mapper
 */
@Mapper
public interface ControlMapper extends BaseMapper<Control> {

    Integer findMaxControlIdByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    Integer findMaxDisplayIndexByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    int deleteControl(@Param("equipmentTemplateId") int equipmentTemplateId, @Param("controlId") int controlId);
    
    List<ControlConfigItem> findControlItemByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    ControlConfigItem findByEquipmentTemplateIdAndControlId(@Param("equipmentTemplateId") Integer equipmentTemplateId, @Param("controlId") Integer controlId);
    
    int insertControl(Control control);
    
    int updateControl(Control control);

    /**
     * Find base type IDs not in control base dictionary for equipment template
     *
     * @param equipmentTemplateId Equipment template ID
     * @return List of base type IDs
     */
    List<Long> findBaseTypeIdsNotInControlBaseDicForEquipmentTemplate(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    int batchUpdateField(@Param("controlList") List<Control> controlList);

    List<ControlExcel> findExcelDtoByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    int batchInsertControl(@Param("controlList") List<Control> controlList);

    List<ControlVO> findVoByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);


}
