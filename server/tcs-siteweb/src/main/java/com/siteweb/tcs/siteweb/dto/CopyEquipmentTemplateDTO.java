package com.siteweb.tcs.siteweb.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;

/**
 * 复制设备模板DTO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CopyEquipmentTemplateDTO {
    
    /**
     * 源设备模板ID
     */
    private Integer originEquipmentTemplateId;
    
    /**
     * 新设备模板名称
     */
    @Size(max = 128, message = "设备模板名称长度不能超过128")
    private String newEquipmentTemplateName;

    /**
     * 复制原因
     */
    private String reason;
}
