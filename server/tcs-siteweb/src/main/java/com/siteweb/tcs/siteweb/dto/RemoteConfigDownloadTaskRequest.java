package com.siteweb.tcs.siteweb.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 远程配置下载任务请求DTO
 */
@Data
@Schema(description = "远程配置下载任务请求")
public class RemoteConfigDownloadTaskRequest {

    @Schema(description = "监控单元ID", example = "1")
    private Integer monitorUnitId;

    @Schema(description = "协议类型", example = "sftp", allowableValues = {"ftp", "sftp", "ssh", "scp"})
    private String protocol;

    @Schema(description = "端口号", example = "22")
    private Integer port;

    @Schema(description = "用户名", example = "root")
    private String username;

    @Schema(description = "密码", example = "password")
    private String password;

    @Schema(description = "用户ID", example = "admin")
    private String userId;
}
