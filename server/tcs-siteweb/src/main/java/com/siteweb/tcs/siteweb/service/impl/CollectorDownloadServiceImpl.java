package com.siteweb.tcs.siteweb.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.extra.ftp.Ftp;
import cn.hutool.extra.ftp.FtpConfig;
import cn.hutool.extra.ftp.FtpMode;
import cn.hutool.extra.ssh.JschUtil;
import cn.hutool.extra.ssh.Sftp;
import com.jcraft.jsch.Session;
import com.siteweb.tcs.siteweb.dto.MonitorUnitDTO;
import com.siteweb.tcs.siteweb.enums.TaskTypeEnum;
import com.siteweb.tcs.siteweb.service.ICollectorDownloadService;
import com.siteweb.tcs.siteweb.service.ITaskStatusService;
import com.siteweb.tcs.siteweb.util.*;
import com.siteweb.tcs.common.util.PathUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 采集器下载服务实现
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Slf4j
@Service
public class CollectorDownloadServiceImpl implements ICollectorDownloadService {

    @Autowired
    private ITaskStatusService taskStatusService;

    @Value("${collector.remote.base-path:/home/<USER>")
    private String remoteBasePath;

    @Value("${collector.connection.timeout:10000}")
    private int connectionTimeout;

    @Value("${collector.connection.sftp-timeout:10000}")
    private int sftpConnectionTimeout;

    @Value("${collector.connection.ftp-timeout:10000}")
    private int ftpConnectionTimeout;

    @Value("${collector.download.max-zip-files:5}")
    private int maxZipFiles;

    // 本地存储路径
    private final String localWorkspacePath = Paths.get("plugins", "south-omc-siteweb", "workspace").toString();

    @Override
    public File downloadLogFile(MonitorUnitDTO monitorUnit, String logPath, String protocol,
                               Integer port, String username, String password, String uniqueId) {
        try {
            if ("ftp".equalsIgnoreCase(protocol)) {
                return downloadViaFtp(monitorUnit, logPath, port, username, password, uniqueId);
            } else if ("sftp".equalsIgnoreCase(protocol)) {
                return downloadViaSftp(monitorUnit, logPath, port, username, password, uniqueId);
            } else if ("ssh".equalsIgnoreCase(protocol) || "scp".equalsIgnoreCase(protocol)) {
                return downloadViaScp(monitorUnit, logPath, port, username, password, uniqueId);
            } else {
                return downloadViaSftp(monitorUnit, logPath, port, username, password, uniqueId);
            }
        } catch (Exception e) {
            log.error("下载日志文件失败", e);
            String errorMsg = String.format("下载监控单元[%s]日志文件失败: %s",
                    monitorUnit.getMonitorUnitName(), e.getMessage());
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
            return null;
        }
    }

    @Override
    public File downloadConfigFile(MonitorUnitDTO monitorUnit, String configPath, String protocol,
                                  Integer port, String username, String password, String uniqueId) {
        try {
            if ("ftp".equalsIgnoreCase(protocol)) {
                return downloadViaFtp(monitorUnit, configPath, port, username, password, uniqueId);
            } else if ("sftp".equalsIgnoreCase(protocol)) {
                return downloadViaSftp(monitorUnit, configPath, port, username, password, uniqueId);
            } else if ("ssh".equalsIgnoreCase(protocol) || "scp".equalsIgnoreCase(protocol)) {
                return downloadViaScp(monitorUnit, configPath, port, username, password, uniqueId);
            } else {
                return downloadViaSftp(monitorUnit, configPath, port, username, password, uniqueId);
            }
        } catch (Exception e) {
            log.error("下载配置文件失败", e);
            String errorMsg = String.format("下载监控单元[%s]配置文件失败: %s",
                    monitorUnit.getMonitorUnitName(), e.getMessage());
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
            return null;
        }
    }

    @Override
    public File downloadAndPackageFiles(MonitorUnitDTO monitorUnit, List<String> filePaths,
                                       String protocol, Integer port, String username, String password,
                                       String uniqueId) {
        try {
            List<File> downloadedFiles = new ArrayList<>();

            for (String filePath : filePaths) {
                File file;
                if ("ftp".equalsIgnoreCase(protocol)) {
                    file = downloadViaFtp(monitorUnit, filePath, port, username, password, uniqueId);
                } else if ("sftp".equalsIgnoreCase(protocol)) {
                    file = downloadViaSftp(monitorUnit, filePath, port, username, password, uniqueId);
                } else if ("ssh".equalsIgnoreCase(protocol) || "scp".equalsIgnoreCase(protocol)) {
                    file = downloadViaScp(monitorUnit, filePath, port, username, password, uniqueId);
                } else {
                    file = downloadViaSftp(monitorUnit, filePath, port, username, password, uniqueId);
                }

                if (file != null && file.exists()) {
                    downloadedFiles.add(file);
                }
            }

            if (downloadedFiles.isEmpty()) {
                log.warn("没有成功下载任何文件");
                return null;
            }

            // 创建压缩包 - 使用monitorUnitId作为路径标识，避免监控单元名称中的非法字符
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String zipFileName = PathUtil.pathJoin(localWorkspacePath, "downloads",
                    String.valueOf(monitorUnit.getMonitorUnitId()),
                    String.format("collector_%s_%s.zip", monitorUnit.getMonitorUnitName(), timestamp));
            File zipFile = new File(zipFileName);

            // 确保目录存在
            FileUtil.mkdir(zipFile.getParent());

            // 压缩文件
            ZipUtil.zip(zipFile, false, downloadedFiles.toArray(new File[0]));

            String successMsg = String.format("成功打包下载%d个文件", downloadedFiles.size());
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, successMsg, false);
            log.info("文件打包完成: {}", zipFile.getAbsolutePath());

            return zipFile;

        } catch (Exception e) {
            log.error("下载并打包文件失败", e);
            String errorMsg = String.format("下载并打包监控单元[%s]文件失败: %s",
                    monitorUnit.getMonitorUnitName(), e.getMessage());
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
            return null;
        }
    }

    @Override
    public File downloadCollectorConfigPackage(MonitorUnitDTO monitorUnit, String protocol,
                                              Integer port, String username, String password, String uniqueId) {
        try {
            String successMsg = String.format("开始下载监控单元[%s]配置文件包...", monitorUnit.getMonitorUnitName());
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, successMsg, false);

            // 1. 清理旧的下载文件
            cleanupOldDownloadFiles(monitorUnit.getMonitorUnitId(), uniqueId);

            // 2. 在一个会话中完成所有下载操作，保留目录结构
            String downloadBaseDir = PathUtil.pathJoin(localWorkspacePath, "downloads", String.valueOf(monitorUnit.getMonitorUnitId()));

            if ("ftp".equalsIgnoreCase(protocol)) {
                downloadAllFilesWithStructureViaFtp(monitorUnit, port, username, password, uniqueId, downloadBaseDir);
            } else if ("sftp".equalsIgnoreCase(protocol)) {
                downloadAllFilesWithStructureViaSftp(monitorUnit, port, username, password, uniqueId, downloadBaseDir);
            } else if ("ssh".equalsIgnoreCase(protocol) || "scp".equalsIgnoreCase(protocol)) {
                downloadAllFilesWithStructureViaScp(monitorUnit, port, username, password, uniqueId, downloadBaseDir);
            } else {
                downloadAllFilesWithStructureViaSftp(monitorUnit, port, username, password, uniqueId, downloadBaseDir);
            }

            // 3. 检查下载的文件
            File downloadDir = new File(downloadBaseDir);
            if (!downloadDir.exists() || !hasAnyFiles(downloadDir)) {
                String warnMsg = String.format("监控单元[%s]没有找到任何配置文件", monitorUnit.getMonitorUnitName());
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, warnMsg, false);
                log.warn(warnMsg);
                return null;
            }

            // 4. 创建配置文件压缩包，保留目录结构
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String zipFileName = PathUtil.pathJoin(localWorkspacePath, "remote-configs",
                    String.valueOf(monitorUnit.getMonitorUnitId()),
                    String.format("config_%d_%s.zip", monitorUnit.getMonitorUnitId(), timestamp));
            File zipFile = new File(zipFileName);

            // 确保目录存在
            FileUtil.mkdir(zipFile.getParent());

            // 压缩整个下载目录，保留内部目录结构
            ZipUtil.zip(zipFile, false, downloadDir);

            // 5. 清理旧的zip文件，只保留最新的几个
            cleanupOldZipFiles(monitorUnit.getMonitorUnitId());

            int fileCount = countFiles(downloadDir);
            String completeMsg = String.format("监控单元[%s]配置文件包下载完成，共%d个文件",
                    monitorUnit.getMonitorUnitName(), fileCount);
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, completeMsg, false);
            log.info("配置文件包创建完成: {}", zipFile.getAbsolutePath());

            return zipFile;

        } catch (Exception e) {
            log.error("下载配置文件包失败", e);
            String errorMsg = String.format("下载监控单元[%s]配置文件包失败: %s",
                    monitorUnit.getMonitorUnitName(), e.getMessage());
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
            return null;
        }
    }

    @Override
    public String getCollectorStatus(MonitorUnitDTO monitorUnit, String protocol, Integer port,
                                    String username, String password, String uniqueId) {
        // TODO: 实现通过Telnet/SSH获取采集器状态
        log.info("获取采集器状态 - 监控单元: {}, 协议: {}", monitorUnit.getMonitorUnitName(), protocol);
        return "运行中";
    }

    @Override
    public boolean checkFileExists(MonitorUnitDTO monitorUnit, String filePath, String protocol,
                                  Integer port, String username, String password, String uniqueId) {
        try {
            if ("ftp".equalsIgnoreCase(protocol)) {
                return checkFileExistsViaFtp(monitorUnit, filePath, port, username, password);
            } else if ("sftp".equalsIgnoreCase(protocol)) {
                return checkFileExistsViaSftp(monitorUnit, filePath, port, username, password);
            } else if ("ssh".equalsIgnoreCase(protocol) || "scp".equalsIgnoreCase(protocol)) {
                return checkFileExistsViaScp(monitorUnit, filePath, port, username, password);
            } else {
                return checkFileExistsViaSftp(monitorUnit, filePath, port, username, password);
            }
        } catch (Exception e) {
            log.error("检查文件是否存在失败", e);
            return false;
        }
    }

    /**
     * 通过FTP下载文件
     */
    private File downloadViaFtp(MonitorUnitDTO monitorUnit, String remotePath, Integer port,
                               String username, String password, String uniqueId) throws Exception {
        FtpConfig ftpConfig = new FtpConfig();
        ftpConfig.setHost(monitorUnit.getIpAddress());
        ftpConfig.setPort(port);
        ftpConfig.setUser(username);
        ftpConfig.setPassword(password);
        ftpConfig.setCharset(CharsetUtil.CHARSET_GBK);
        ftpConfig.setConnectionTimeout(ftpConnectionTimeout);
        ftpConfig.setSoTimeout(ftpConnectionTimeout);

        try (Ftp ftp = new Ftp(ftpConfig, FtpMode.Passive)) {
            String fileName = remotePath.substring(remotePath.lastIndexOf("/") + 1);
            // 使用monitorUnitId作为路径标识，避免监控单元名称中的非法字符
            String localPath = PathUtil.pathJoin(localWorkspacePath, "downloads",
                    String.valueOf(monitorUnit.getMonitorUnitId()), fileName);
            File localFile = new File(localPath);

            // 确保目录存在
            FileUtil.mkdir(localFile.getParent());

            // 下载文件
            ftp.download(remotePath, localFile);

            String successMsg = String.format("文件下载成功: %s", fileName);
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, successMsg, false);
            log.info("FTP下载成功: {} -> {}", remotePath, localFile.getAbsolutePath());
            return localFile;
        }
        catch (Exception e) {
            log.error("FTP下载失败: {}", remotePath, e);
            throw e;
        }
    }

    /**
     * 通过SFTP下载文件
     */
    private File downloadViaSftp(MonitorUnitDTO monitorUnit, String remotePath, Integer port,
                                String username, String password, String uniqueId) throws Exception {
        try (Sftp sftp= JschUtil.createSftp(monitorUnit.getIpAddress(), port, username, password)) {
            String fileName = remotePath.substring(remotePath.lastIndexOf("/") + 1);
            String localPath = PathUtil.pathJoin(localWorkspacePath, "downloads",
                    monitorUnit.getMonitorUnitId().toString(), fileName);
            File localFile = new File(localPath);

            // 确保目录存在
            FileUtil.mkParentDirs(localFile);

            // 下载文件
            sftp.download(remotePath, localFile);

            if (localFile.exists()) {
                String successMsg = String.format("文件下载成功: %s", fileName);
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, successMsg, false);
                log.info("SFTP下载成功: {} -> {}", remotePath, localFile.getAbsolutePath());
                return localFile;
            } else {
                log.error("SFTP下载失败: {}", remotePath);
                return null;
            }
        }
    }

    /**
     * 通过FTP检查文件是否存在
     */
    private boolean checkFileExistsViaFtp(MonitorUnitDTO monitorUnit, String filePath, Integer port,
                                         String username, String password) throws Exception {
        FtpConfig ftpConfig = new FtpConfig();
        ftpConfig.setHost(monitorUnit.getIpAddress());
        ftpConfig.setPort(port);
        ftpConfig.setUser(username);
        ftpConfig.setPassword(password);
        ftpConfig.setCharset(CharsetUtil.CHARSET_GBK);

        try (Ftp ftp = new Ftp(ftpConfig, FtpMode.Passive)) {
            return ftp.exist(filePath);
        }
    }

    /**
     * 通过SFTP检查文件是否存在
     */
    private boolean checkFileExistsViaSftp(MonitorUnitDTO monitorUnit, String filePath, Integer port,
                                          String username, String password) throws Exception {
        try (Sftp sftp = JschUtil.createSftp(monitorUnit.getIpAddress(), port, username, password)) {
            return sftp.exist(filePath);
        }
    }

    /**
     * 通过FTP列出文件
     */
    private List<String> listFilesViaFtp(MonitorUnitDTO monitorUnit, String directoryPath, Integer port,
                                        String username, String password) throws Exception {
        FtpConfig ftpConfig = new FtpConfig();
        ftpConfig.setHost(monitorUnit.getIpAddress());
        ftpConfig.setPort(port);
        ftpConfig.setUser(username);
        ftpConfig.setPassword(password);
        ftpConfig.setCharset(CharsetUtil.CHARSET_GBK);

        try (Ftp ftp = new Ftp(ftpConfig, FtpMode.Passive)) {
            return ftp.ls(directoryPath);
        }
    }


    /**
     * 通过SSH+SCP下载文件
     */
    private File downloadViaScp(MonitorUnitDTO monitorUnit, String remotePath, Integer port,
                               String username, String password, String uniqueId) throws Exception {
        try (AutoCloseableSession session = createAutoCloseableSession(monitorUnit.getIpAddress(), port, username, password)) {
            String fileName = remotePath.substring(remotePath.lastIndexOf("/") + 1);
            String localPath = PathUtil.pathJoin(localWorkspacePath, "downloads",
                    monitorUnit.getMonitorUnitId().toString(), fileName);
            File localFile = new File(localPath);

            // 确保目录存在
            FileUtil.mkdir(localFile.getParent());

            // 使用SCP下载文件
            boolean success = downloadFileViaScp(session.getSession(), remotePath, localFile);

            if (success && localFile.exists()) {
                String successMsg = String.format("文件下载成功: %s", fileName);
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, successMsg, false);
                log.info("SCP下载成功: {} -> {}", remotePath, localFile.getAbsolutePath());
                return localFile;
            } else {
                log.error("SCP下载失败: {}", remotePath);
                return null;
            }
        }
    }

    /**
     * 通过SSH+SCP检查文件是否存在
     */
    private boolean checkFileExistsViaScp(MonitorUnitDTO monitorUnit, String filePath, Integer port,
                                         String username, String password) throws Exception {
        try (AutoCloseableSession session = createAutoCloseableSession(monitorUnit.getIpAddress(), port, username, password)) {
            return SshUtil.fileExists(session.getSession(), filePath);
        }
    }

    /**
     * 通过SSH+SCP列出文件
     */
    private List<String> listFilesViaScp(MonitorUnitDTO monitorUnit, String directoryPath, Integer port,
                                        String username, String password) throws Exception {
        try (AutoCloseableSession session = createAutoCloseableSession(monitorUnit.getIpAddress(), port, username, password)) {
            String result = SshUtil.listFiles(session.getSession(), directoryPath);
            List<String> files = new ArrayList<>();
            if (result != null && !result.trim().isEmpty()) {
                String[] lines = result.split("\n");
                for (String line : lines) {
                    String fileName = line.trim();
                    if (!fileName.isEmpty()) {
                        files.add(fileName);
                    }
                }
            }
            return files;
        }
    }
    /**
     * 使用SCP协议下载文件的底层实现
     */
    private boolean downloadFileViaScp(Session session, String remoteFilePath, File localFile) {
        try {
            // 使用ScpUtil进行真正的SCP协议下载，支持二进制文件
            boolean success = ScpUtil.downloadFile(session, remoteFilePath, localFile);

            if (success && localFile.exists()) {
                log.info("SCP下载文件成功: {} -> {}", remoteFilePath, localFile.getAbsolutePath());
                return true;
            } else {
                log.error("SCP下载文件失败: {}", remoteFilePath);
                return false;
            }
        } catch (Exception e) {
            log.error("SCP下载文件异常: {} -> {}", remoteFilePath, localFile.getAbsolutePath(), e);
            return false;
        }
    }

    /**
     * 创建自动关闭的SSH会话
     */
    private AutoCloseableSession createAutoCloseableSession(String host, Integer port, String username, String password) throws Exception {
        try {
            com.jcraft.jsch.JSch jsch = new com.jcraft.jsch.JSch();
            com.jcraft.jsch.Session session = jsch.getSession(username, host, port != null ? port : 22);
            session.setPassword(password);
            session.setConfig("StrictHostKeyChecking", "no");

            // 设置SSH会话支持UTF-8编码
            session.setConfig("compression.s2c", "none");
            session.setConfig("compression.c2s", "none");

            session.setTimeout(sftpConnectionTimeout);
            session.connect(sftpConnectionTimeout);
            log.info("SSH连接创建成功: {}:{}", host, port);
            return new AutoCloseableSession(session);
        } catch (Exception e) {
            log.error("SSH连接创建失败: {}:{}", host, port, e);
            throw e;
        }
    }

    @Override
    public File getLatestRemoteConfigFile(Integer monitorUnitId) {
        try {
            String configDir = PathUtil.pathJoin(localWorkspacePath, "remote-configs", String.valueOf(monitorUnitId));
            File configDirectory = new File(configDir);

            if (!configDirectory.exists() || !configDirectory.isDirectory()) {
                log.warn("远程配置目录不存在: {}", configDir);
                return null;
            }

            // 获取目录下所有的zip文件
            File[] zipFiles = configDirectory.listFiles((dir, name) -> name.toLowerCase().endsWith(".zip"));

            if (zipFiles == null || zipFiles.length == 0) {
                log.warn("监控单元[{}]没有找到远程配置文件", monitorUnitId);
                return null;
            }

            // 找到最新的文件（按修改时间排序）
            File latestFile = null;
            long latestTime = 0;

            for (File file : zipFiles) {
                if (file.lastModified() > latestTime) {
                    latestTime = file.lastModified();
                    latestFile = file;
                }
            }

            if (latestFile != null && latestFile.exists()) {
                log.info("找到监控单元[{}]的最新远程配置文件: {}", monitorUnitId, latestFile.getName());
                return latestFile;
            } else {
                log.warn("监控单元[{}]没有有效的远程配置文件", monitorUnitId);
                return null;
            }

        } catch (Exception e) {
            log.error("获取监控单元[{}]最新远程配置文件失败", monitorUnitId, e);
            return null;
        }
    }

    /**
     * 清理旧的下载文件
     */
    private void cleanupOldDownloadFiles(Integer monitorUnitId, String uniqueId) {
        try {
            String downloadDir = PathUtil.pathJoin(localWorkspacePath, "downloads", String.valueOf(monitorUnitId));
            File dir = new File(downloadDir);
            if (dir.exists()) {
                FileUtil.del(dir);
                log.info("清理旧的下载文件: {}", downloadDir);
                String msg = "清理旧的下载文件完成";
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, msg, false);
            }
        } catch (Exception e) {
            log.warn("清理旧下载文件失败: {}", e.getMessage());
        }
    }

    /**
     * 清理旧的zip文件，只保留最新的几个
     */
    private void cleanupOldZipFiles(Integer monitorUnitId) {
        try {
            String configDir = PathUtil.pathJoin(localWorkspacePath, "remote-configs", String.valueOf(monitorUnitId));
            File dir = new File(configDir);

            if (!dir.exists()) {
                return;
            }

            // 获取所有zip文件
            File[] zipFiles = dir.listFiles((file, name) -> name.toLowerCase().endsWith(".zip"));
            if (zipFiles == null || zipFiles.length <= maxZipFiles) {
                return;
            }

            // 按修改时间排序，最新的在前
            java.util.Arrays.sort(zipFiles, (a, b) -> Long.compare(b.lastModified(), a.lastModified()));

            // 删除多余的文件
            for (int i = maxZipFiles; i < zipFiles.length; i++) {
                if (zipFiles[i].delete()) {
                    log.info("删除旧的配置文件包: {}", zipFiles[i].getName());
                }
            }

        } catch (Exception e) {
            log.warn("清理旧zip文件失败: {}", e.getMessage());
        }
    }

    /**
     * 检查目录是否包含任何文件
     */
    private boolean hasAnyFiles(File dir) {
        if (!dir.exists() || !dir.isDirectory()) {
            return false;
        }

        File[] files = dir.listFiles();
        if (files == null) {
            return false;
        }

        for (File file : files) {
            if (file.isFile()) {
                return true;
            } else if (file.isDirectory() && hasAnyFiles(file)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 递归计算目录中的文件数量
     */
    private int countFiles(File dir) {
        if (!dir.exists() || !dir.isDirectory()) {
            return 0;
        }

        int count = 0;
        File[] files = dir.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isFile()) {
                    count++;
                } else if (file.isDirectory()) {
                    count += countFiles(file);
                }
            }
        }

        return count;
    }

    /**
     * 通过SFTP下载所有文件并保留目录结构
     */
    private void downloadAllFilesWithStructureViaSftp(MonitorUnitDTO monitorUnit, Integer port,
                                                     String username, String password, String uniqueId, String downloadBaseDir) {
        try (Sftp sftp =  JschUtil.createSftp(monitorUnit.getIpAddress(), port, username, password)) {
            log.info("SFTP连接建立成功，开始批量下载配置文件并保留目录结构");

            // 下载目标目录列表
            String[] targetDirs = {"XmlCfg", "cmbcfg", "SO"};

            for (String targetDir : targetDirs) {
                String remoteDirPath = remoteBasePath + "/" + targetDir;
                String localDirPath = downloadBaseDir+"/"+ targetDir;

                try {
                    // 检查远程目录是否存在
                    if (!sftp.exist(remoteDirPath) || !sftp.isDir(remoteDirPath)) {
                        log.warn("远程目录不存在或不是目录: {}", remoteDirPath);
                        String warnMsg = String.format("远程目录 %s 不存在，跳过下载", targetDir);
                        taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, warnMsg, false);
                        continue;
                    }


                    // 获取目录下的文件列表
                    List<String> fileList = sftp.ls(remoteDirPath);
                    if (fileList != null && !fileList.isEmpty()) {
                        int downloadedCount = 0;
                        for (String fileName : fileList) {
                            String remoteFilePath = remoteDirPath + "/" + fileName;
                            String localFilePath = PathUtil.pathJoin(localDirPath, fileName);
                            File localFile = new File(localFilePath);
                            // 确保本地目录存在
                            if (localFile.getParentFile() != null && !localFile.getParentFile().exists()) {
                                localFile.getParentFile().mkdirs();
                            }
                            try {
                                sftp.download(remoteFilePath, localFile);
                                if (localFile.exists()) {
                                    downloadedCount++;
                                    log.debug("SFTP下载文件成功: {} -> {}", remoteFilePath, localFilePath);
                                } else {
                                    log.warn("SFTP下载文件失败: {}", remoteFilePath);
                                }
                            } catch (Exception e) {
                                log.warn("SFTP下载文件失败: {} - {}", remoteFilePath, e.getMessage());
                            }
                        }

                        String successMsg = String.format("目录 %s 下载完成，共%d个文件", targetDir, downloadedCount);
                        taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, successMsg, false);
                        log.info("SFTP目录下载完成: {} - {}个文件", targetDir, downloadedCount);
                    }

                } catch (Exception e) {
                    log.warn("SFTP下载目录失败: {} - {}", targetDir, e.getMessage());
                    String errorMsg = String.format("目录 %s 下载失败: %s", targetDir, e.getMessage());
                    taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
                }
            }

            log.info("SFTP批量下载完成，保留目录结构");

        } catch (Exception e) {
            log.error("SFTP批量下载失败", e);
            String errorMsg = String.format("SFTP批量下载失败: %s", e.getMessage());
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
        }
    }

    /**
     * 通过FTP下载所有文件并保留目录结构
     */
    private void downloadAllFilesWithStructureViaFtp(MonitorUnitDTO monitorUnit, Integer port,
                                                    String username, String password, String uniqueId, String downloadBaseDir) {
        FtpConfig ftpConfig = new FtpConfig();
        ftpConfig.setHost(monitorUnit.getIpAddress());
        ftpConfig.setPort(port);
        ftpConfig.setUser(username);
        ftpConfig.setPassword(password);
        ftpConfig.setCharset(CharsetUtil.CHARSET_GBK);
        ftpConfig.setConnectionTimeout(ftpConnectionTimeout);
        ftpConfig.setSoTimeout(ftpConnectionTimeout);

        try (Ftp ftp = new Ftp(ftpConfig, FtpMode.Passive)) {
            log.info("FTP连接建立成功，开始批量下载配置文件并保留目录结构");

            // 下载目标目录列表
            String[] targetDirs = {"XmlCfg", "cmbcfg", "SO"};

            for (String targetDir : targetDirs) {
                String remoteDirPath = remoteBasePath + "/" + targetDir;
                String localDirPath = PathUtil.pathJoin(downloadBaseDir, targetDir);

                try {
                    // 检查远程目录是否存在
                    if (!ftp.exist(remoteDirPath)) {
                        log.warn("远程目录不存在: {}", remoteDirPath);
                        String warnMsg = String.format("远程目录 %s 不存在，跳过下载", targetDir);
                        taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, warnMsg, false);
                        continue;
                    }

                    // 获取目录下的文件列表
                    List<String> fileList = ftp.ls(remoteDirPath);
                    if (fileList != null && !fileList.isEmpty()) {
                        int downloadedCount = 0;
                        for (String fileName : fileList) {
                            String remoteFilePath = remoteDirPath + "/" + fileName;
                            String localFilePath = PathUtil.pathJoin(localDirPath, fileName);
                            File localFile = new File(localFilePath);
                            if (localFile.getParentFile() != null && !localFile.getParentFile().exists()) {
                                localFile.getParentFile().mkdirs();
                            }
                            try {
                                ftp.download(remoteFilePath, localFile);
                                if (localFile.exists()) {
                                    downloadedCount++;
                                    log.debug("FTP下载文件成功: {} -> {}", remoteFilePath, localFilePath);
                                }
                            } catch (Exception e) {
                                log.warn("FTP下载文件失败: {} - {}", remoteFilePath, e.getMessage());
                            }
                        }

                        String successMsg = String.format("目录 %s 下载完成，共%d个文件", targetDir, downloadedCount);
                        taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, successMsg, false);
                        log.info("FTP目录下载完成: {} - {}个文件", targetDir, downloadedCount);
                    }

                } catch (Exception e) {
                    log.warn("FTP下载目录失败: {} - {}", targetDir, e.getMessage());
                    String errorMsg = String.format("目录 %s 下载失败: %s", targetDir, e.getMessage());
                    taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
                }
            }

            log.info("FTP批量下载完成，保留目录结构");

        } catch (Exception e) {
            log.error("FTP批量下载失败", e);
            String errorMsg = String.format("FTP批量下载失败: %s", e.getMessage());
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
        }
    }

    /**
     * 通过SSH+SCP下载所有文件并保留目录结构
     */
    private void downloadAllFilesWithStructureViaScp(MonitorUnitDTO monitorUnit, Integer port,
                                                    String username, String password, String uniqueId, String downloadBaseDir) {
        try (AutoCloseableSession session = createAutoCloseableSession(monitorUnit.getIpAddress(), port, username, password)) {
            log.info("SSH连接建立成功，开始批量下载配置文件并保留目录结构");

            // 下载目标目录列表
            String[] targetDirs = {"XmlCfg", "cmbcfg", "SO"};

            for (String targetDir : targetDirs) {
                String remoteDirPath = remoteBasePath + "/" + targetDir;
                String localDirPath = PathUtil.pathJoin(downloadBaseDir, targetDir);

                try {
                    // 检查远程目录是否存在
                    if (!SshUtil.directoryExists(session.getSession(), remoteDirPath)) {
                        log.warn("远程目录不存在: {}", remoteDirPath);
                        String warnMsg = String.format("远程目录 %s 不存在，跳过下载", targetDir);
                        taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, warnMsg, false);
                        continue;
                    }

                    // 创建本地目录
                    FileUtil.mkdir(localDirPath);

                    // 获取目录下的文件列表
                    String result = SshUtil.listFiles(session.getSession(), remoteDirPath);
                    if (result != null && !result.trim().isEmpty()) {
                        String[] fileNames = result.split("\n");
                        int downloadedCount = 0;

                        for (String fileName : fileNames) {
                            fileName = fileName.trim();
                            if (!fileName.isEmpty()) {
                                String remoteFilePath = remoteDirPath + "/" + fileName;
                                String localFilePath = PathUtil.pathJoin(localDirPath, fileName);
                                File localFile = new File(localFilePath);

                                try {
                                    if (downloadFileViaScp(session.getSession(), remoteFilePath, localFile)) {
                                        downloadedCount++;
                                        log.debug("SCP下载文件成功: {} -> {}", remoteFilePath, localFilePath);
                                    }
                                } catch (Exception e) {
                                    log.warn("SCP下载文件失败: {} - {}", remoteFilePath, e.getMessage());
                                }
                            }
                        }

                        String successMsg = String.format("目录 %s 下载完成，共%d个文件", targetDir, downloadedCount);
                        taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, successMsg, false);
                        log.info("SCP目录下载完成: {} - {}个文件", targetDir, downloadedCount);
                    }

                } catch (Exception e) {
                    log.warn("SCP下载目录失败: {} - {}", targetDir, e.getMessage());
                    String errorMsg = String.format("目录 %s 下载失败: %s", targetDir, e.getMessage());
                    taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
                }
            }

            log.info("SCP批量下载完成，保留目录结构");

        } catch (Exception e) {
            log.error("SCP批量下载失败", e);
            String errorMsg = String.format("SCP批量下载失败: %s", e.getMessage());
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
        }
    }
}
