package com.siteweb.tcs.siteweb.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.extra.ftp.Ftp;
import cn.hutool.extra.ftp.FtpConfig;
import cn.hutool.extra.ftp.FtpMode;
import cn.hutool.extra.ssh.Sftp;
import com.siteweb.tcs.siteweb.dto.MonitorUnitDTO;
import com.siteweb.tcs.siteweb.entity.*;
import com.siteweb.tcs.siteweb.enums.MonitorUnitCategoryEnum;
import com.siteweb.tcs.siteweb.enums.MonitorUnitStateEnum;
import com.siteweb.tcs.siteweb.manager.MonitorUnitStateManager;
import com.siteweb.tcs.siteweb.service.*;
import com.siteweb.tcs.siteweb.util.AESUtil;
import com.siteweb.tcs.siteweb.util.I18n;
import com.siteweb.tcs.siteweb.enums.TaskTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.XMLWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Description:
 */
@Service
@Slf4j
public class MonitorUnitXmlServiceImpl implements IMonitorUnitXmlService {
    @Autowired
    private IMonitorUnitService monitorUnitService;

    @Autowired
    MonitorUnitStateManager monitorUnitStateManager;

    @Autowired
    private IMonitorUnitConfigService monitorUnitConfigService;

    @Autowired
    private IStationService stationService;

    @Autowired
    private IWorkStationService workStationService;

    @Autowired
    private IMonitorUnitExtendService monitorUnitExtendService;

    @Autowired
    private I18n i18n;

    @Autowired
    private IPortService portService;

    @Autowired
    private ISamplerUnitService samplerUnitService;

    @Autowired
    private IEquipmentService equipmentService;

    @Autowired
    private IEquipmentTemplateService equipmentTemplateService;

    @Autowired
    private ISamplerService samplerService;

    @Autowired
    private ITaskStatusService taskStatusService;

    @Value("${aes.key:defaultAESKey}")
    private String aesKey;
    /**
     * 异步生成监控单元配置文件
     * 请注意，虽然这个方法没有返回值，但是如果在方法执行过程中抛出了异常，
     * Spring将会抛出AsyncUncaughtExceptionHandler。
     * 你可以通过实现AsyncConfigurer接口并重写getAsyncUncaughtExceptionHandler()方法来自定义异常处理。
     *
     * @param msg
     * @param uniqueId
     */
    @Override
    @Async
    public void createMonitorUnitConfigXMLAsync(String msg, String uniqueId) {
        // 创建任务状态
        taskStatusService.createTask(uniqueId, TaskTypeEnum.CONFIGURATION_GENERATION, null);

        String[] strArray = msg.split(",");
        int[] intArray = new int[strArray.length];
        for (int i = 0; i < strArray.length; i++) {
            intArray[i] = Integer.parseInt(strArray[i]);
        }
        if (intArray.length == 0) {
            taskStatusService.setTaskError(uniqueId, "没有需要生成配置的监控单元");
            return;
        }

        int totalCount = intArray.length;
        int processedCount = 0;

        for (int monitorUnitId : intArray) {
            MonitorUnitDTO monitorUnit = monitorUnitService.findById(monitorUnitId);
            if (monitorUnit == null) {
                String errorMsg = String.format("监控单元[%s]不存在", monitorUnitId);
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_GENERATION, errorMsg, false);
                log.error("监控单元不存在，监控单元ID：{}", monitorUnitId);
                processedCount++;
                continue;
            }
            // 清除日志文件内容
            String path = "upload-dir/monitorUnitConfig/" + monitorUnit.getStationName() + monitorUnit.getStationId() + "/" + monitorUnit.getMonitorUnitName();
            String message = String.format("准备生成监控单元[%s]配置...", monitorUnit.getMonitorUnitName());

            // 更新进度
            int progress = (processedCount * 100) / totalCount;
            taskStatusService.sendMessageWithProgress(uniqueId, TaskTypeEnum.CONFIGURATION_GENERATION, message, progress, false);

            FileUtil.del(new File(path + "/" + monitorUnit.getMonitorUnitId() + ".log"));
            FileUtil.appendUtf8String(message + "\n", new File(path + "/" + monitorUnit.getMonitorUnitId() + ".log"));
            createMonitorUnitConfigXML(monitorUnit, path);
            writeMonitorUnitDetailsToFile(monitorUnit, path);
            message = String.format("生成监控单元[%s]配置成功!", monitorUnit.getMonitorUnitName());

            processedCount++;
            progress = (processedCount * 100) / totalCount;
            taskStatusService.sendMessageWithProgress(uniqueId, TaskTypeEnum.CONFIGURATION_GENERATION, message, progress, false);
            FileUtil.appendUtf8String(message + "\n", new File(path + "/" + monitorUnit.getMonitorUnitId() + ".log"));
        }
        taskStatusService.completeTask(uniqueId, "所有监控单元配置生成完成");
    }

    @Override
    public void createMonitorUnitConfigXML(MonitorUnitDTO tslMonitorUnit, String path) {
        // 判断是否存在非法字符
        if (containsInvalidChars(tslMonitorUnit)) return;

        // 获取监控单元配置
        List<MonitorUnitConfig> monitorUnitConfigs = monitorUnitConfigService.findByMonitorUnitId(tslMonitorUnit.getMonitorUnitId());
        if (monitorUnitConfigs.isEmpty()) {
            log.error("监控单元配置为空，监控单元ID：{}", tslMonitorUnit.getMonitorUnitId());
            return;
        }
        Station station = stationService.findByStationId(tslMonitorUnit.getStationId());
        if (station == null) {
            log.error("站点为空，站点ID：{}", tslMonitorUnit.getStationId());
            return;
        }
        List<WorkStation> dsWorkStations = workStationService.findDsWorkStations();
        List<WorkStation> rdsWorkStations = workStationService.findRDsWorkStations();

        String fileName = "MonitorUnits" + tslMonitorUnit.getMonitorUnitName() + ".xml";
        // 创建目录
        FileUtil.mkdir(new File(path));

        // 使用dom4j创建Document
        Document document = DocumentHelper.createDocument();

        // 创建根元素MainCfg
        Element mainCfg = createMainCfgElement(tslMonitorUnit, station);
        document.setRootElement(mainCfg);

        // appConfiguration
        Element appConfiguration = createAppConfigurationElement(tslMonitorUnit, monitorUnitConfigs, dsWorkStations, rdsWorkStations);
        mainCfg.add(appConfiguration);

        // 获取设备模板
        Element equipmentTemplates = createEquipmentTemplateElement(tslMonitorUnit);
        mainCfg.add(equipmentTemplates);

        // 获取监控单元
        Element monitorUnit = createMonitorUnitElement(tslMonitorUnit);
        mainCfg.add(monitorUnit);

        try {
            // 创建文件输出流
            File file = new File(path + "/" + fileName);
            FileOutputStream fos = new FileOutputStream(file);

            // 创建格式化输出
            OutputFormat format = OutputFormat.createPrettyPrint();
            format.setEncoding("UTF-8");

            // 创建XML写入器
            XMLWriter writer = new XMLWriter(fos, format);
            writer.write(document);
            writer.flush();
            writer.close();

            // 更新文件MD5
            String md5Hex = DigestUtil.md5Hex(file);
            monitorUnitService.updateMonitorUnitConfigFileCode(tslMonitorUnit.getMonitorUnitId(), md5Hex);
            // 更新监控单元状态为待下发
            monitorUnitStateManager.updateMonitorUnitStatus(tslMonitorUnit.getMonitorUnitId(), MonitorUnitStateEnum.PENDING);

        } catch (Exception e) {
            log.error("生成监控单元配置文件失败", e);
        }
    }

    private static void writeMonitorUnitDetailsToFile(MonitorUnitDTO tslMonitorUnit, String path) {
        // 写入监控单元名称和IP地址
        String muContext = String.format("%s,%s", tslMonitorUnit.getMonitorUnitName(), tslMonitorUnit.getIpAddress());
        FileUtil.writeUtf8String(muContext, new File(path + "/1.txt"));
    }


    @Override
    @Async("monitorUnitTaskExecutor")
    @Transactional(rollbackFor = Exception.class)
    public void sendMonitorUnitConfigXMLAsync(String monitorUnitIds, Integer port, String username, String password, String protocol, String uniqueId) {
        // 创建任务状态
        taskStatusService.createTask(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, null);

        String[] strArray = monitorUnitIds.split(",");
        // 使用Set去重
        Set<Integer> uniqueIds = new HashSet<>();
        for (String s : strArray) {
            uniqueIds.add(Integer.parseInt(s));
        }

        // 转换回数组
        int[] intArray = uniqueIds.stream().mapToInt(Integer::intValue).toArray();

        // 创建一个新的数组用于存储需要处理的监控单元ID
        List<Integer> processIds = new ArrayList<>();

        for (int monitorUnitId : intArray) {
            // 检查状态是否为SENDING
            Integer status = monitorUnitStateManager.getMonitorUnitStatus(monitorUnitId);
            if (status != null && status.equals(MonitorUnitStateEnum.SENDING.getValue())) {
                // 如果状态已经是SENDING，则跳过该ID
                log.info("监控单元[{}]状态已经是SENDING，跳过下发", monitorUnitId);
                continue;
            }
            // 更新状态为SENDING并添加到处理列表
            monitorUnitStateManager.updateMonitorUnitStatus(monitorUnitId, MonitorUnitStateEnum.SENDING);
            processIds.add(monitorUnitId);
        }

        // 并行处理监控单元配置下发
        int processorCount = Runtime.getRuntime().availableProcessors();
        // 根据处理器数量和待处理ID数量确定并发度
        int parallelism = Math.min(processIds.size(), processorCount * 2);

        // 使用CountDownLatch等待所有任务完成
        CountDownLatch latch = new CountDownLatch(processIds.size());
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (int monitorUnitId : processIds) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    // 在新的事务中处理单个监控单元
                    processSingleMonitorUnitWithTransaction(monitorUnitId, port, username, password, protocol, uniqueId);
                } finally {
                    latch.countDown();
                }
            });
            futures.add(future);
        }

        try {
            // 等待所有任务完成，最多等待30分钟
            latch.await(30, TimeUnit.MINUTES);

            // 检查是否有任务异常
            for (CompletableFuture<Void> future : futures) {
                try {
                    // 获取结果会重新抛出任务中的异常
                    future.get(1, TimeUnit.SECONDS);
                } catch (Exception e) {
                    log.error("监控单元下发任务执行异常", e);
                }
            }
        } catch (InterruptedException e) {
            log.error("等待监控单元下发任务完成时被中断", e);
            Thread.currentThread().interrupt();
        }

        taskStatusService.completeTask(uniqueId, "所有监控单元下发任务完成");
    }
    /**
     * 在新事务中处理单个监控单元的配置下发
     */
    @Transactional(rollbackFor = Exception.class)
    public void processSingleMonitorUnitWithTransaction(int monitorUnitId, Integer port, String username, String password, String protocol, String uniqueId) {
        processSingleMonitorUnit(monitorUnitId, port, username, password, protocol, uniqueId);
    }

    /**
     * 处理单个监控单元的配置下发
     *
     * @param monitorUnitId 监控单元ID
     * @param port          端口号
     * @param username      用户名
     * @param password      密码
     * @param protocol      协议（ftp或sftp）
     * @param uniqueId      唯一标识
     */
    private void processSingleMonitorUnit(int monitorUnitId, Integer port, String username, String password, String protocol, String uniqueId) {
        try {
            MonitorUnitDTO monitorUnit = monitorUnitService.findById(monitorUnitId);
            if (monitorUnit == null) {
                String errorMsg = String.format("监控单元[%s]不存在", monitorUnitId);
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
                return;
            }
            // 判断如果监控单元类型是rmu下mu，则无需下发。
            if (monitorUnit.getMonitorUnitCategory() == MonitorUnitCategoryEnum.MU_OF_RMU.getValue() || monitorUnit.getMonitorUnitCategory() == MonitorUnitCategoryEnum.ACROSS_MU_OF_RMU.getValue()) {
                String message = String.format("监控单元[%s]是RMU下的MU，无需下发", monitorUnit.getMonitorUnitName());
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, message, false);
                monitorUnitStateManager.updateMonitorUnitStatus(monitorUnit.getMonitorUnitId(), MonitorUnitStateEnum.NO_NEED_TO_SEND);
                return;
            }
            // 判断监控端元类型为GFSU3，protocol设置为sftp
            String currentProtocol = protocol;
            if (monitorUnit.getMonitorUnitCategory() == MonitorUnitCategoryEnum.GFSU3.getValue()) {
                currentProtocol = "sftp";
            }
            // 如果protocol是sftp，前端传入端口等于21，则将端口号设置为22
            Integer currentPort = port;
            if (currentProtocol.equalsIgnoreCase("sftp") && currentPort == 21) {
                currentPort = 22;
            }
            String prepareMessage = String.format("准备下发监控单元[%s]配置...", monitorUnit.getMonitorUnitName());
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, prepareMessage, false);
            String path = "upload-dir/monitorUnitConfig/" + monitorUnit.getStationName() + monitorUnit.getStationId() + "/" + monitorUnit.getMonitorUnitName();
            FileUtil.appendUtf8String(prepareMessage + "\n", new File(path + "/" + monitorUnit.getMonitorUnitId() + ".log"));
            boolean result = sendMonitorUnitConfigXML(monitorUnit, uniqueId, currentProtocol, currentPort, username, password);
            if (!result) {
                String failureMessage = String.format("下发监控单元[%s]配置文件失败", monitorUnit.getMonitorUnitName());
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, failureMessage, false);
                monitorUnitStateManager.updateMonitorUnitStatus(monitorUnit.getMonitorUnitId(), MonitorUnitStateEnum.FAILURE);
                FileUtil.appendUtf8String(failureMessage + "\n", new File(path + "/" + monitorUnit.getMonitorUnitId() + ".log"));
                monitorUnitExtendService.delete(monitorUnit.getMonitorUnitId());
                return;
            }
            String successMessage = String.format("下发监控单元[%s]配置文件成功", monitorUnit.getMonitorUnitName());
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, successMessage, false);
            monitorUnitStateManager.updateMonitorUnitStatus(monitorUnit.getMonitorUnitId(), MonitorUnitStateEnum.SUCCESS);
            FileUtil.appendUtf8String(successMessage + "\n", new File(path + "/" + monitorUnit.getMonitorUnitId() + ".log"));
            saveAccountPassword(monitorUnit.getMonitorUnitId(), username, password, String.valueOf(currentPort), currentProtocol);
        } catch (Exception ex) {
            log.error("发送监控单元[{}]配置文件失败:err:", monitorUnitId, ex);
            // 删除账号密码
            monitorUnitExtendService.delete(monitorUnitId);
        }
    }

    // 保存账号密码到数据库
    public void saveAccountPassword(Integer monitorUnitId, String username, String password, String Port, String protocol) {
        // 保存账号密码到数据库
        MonitorUnitExtend monitorUnitExtend = monitorUnitExtendService.findByMonitorUnitId(monitorUnitId);
        // 存在就修改否则新增
        if (monitorUnitExtend != null) {
            // 账号:密码:端口
            monitorUnitExtend.setExtendFiled1(AESUtil.encrypt(username + ":" + password + ":" + Port + ":" + protocol, aesKey));
            monitorUnitExtendService.update(monitorUnitExtend);
        } else {
            monitorUnitExtend = new MonitorUnitExtend();
            monitorUnitExtend.setMonitorUnitId(monitorUnitId);
            monitorUnitExtend.setExtendFiled1(AESUtil.encrypt(username + ":" + password + ":" + Port + ":" + protocol, aesKey));
            monitorUnitExtendService.insert(monitorUnitExtend);
        }
    }

    private static boolean containsInvalidChars(MonitorUnitDTO tslMonitorUnit) {
        if (tslMonitorUnit == null) {
            log.error("监控单元为空");
            return true;
        }
        // 判断是否存在非法字符
        if (containsInvalidChars(tslMonitorUnit.getMonitorUnitName())) {
            log.error("监控单元名称包含非法字符，监控单元名称：{}", tslMonitorUnit.getMonitorUnitName());
            return true;
        }
        return false;
    }

    private static boolean containsInvalidChars(String str) {
        if (StrUtil.isBlank(str)) {
            return false;
        }
        // 检查是否包含XML非法字符
        String invalidChars = "<>&\"'";
        for (char c : invalidChars.toCharArray()) {
            if (str.indexOf(c) != -1) {
                return true;
            }
        }
        return false;
    }

    private static Element createMainCfgElement(MonitorUnitDTO tslMonitorUnit, Station station) {
        Element mainCfg = DocumentHelper.createElement("MainCfg");
        mainCfg.addAttribute("StationId", String.valueOf(tslMonitorUnit.getStationId()));
        mainCfg.addAttribute("StationName", station.getStationName());
        return mainCfg;
    }

    private Element createAppConfigurationElement(MonitorUnitDTO tslMonitorUnit, List<MonitorUnitConfig> monitorUnitConfigs, List<WorkStation> dsWorkStations, List<WorkStation> rdsWorkStations) {
        Element appConfiguration = createAppConfigurationElementBase(tslMonitorUnit);

        // FTP Element with comment
        Element ftpElement = createFtpElement();
        appConfiguration.add(ftpElement);
        appConfiguration.addComment(i18n.T("ftp.username.and.password"));

        // SiteWebCommunication Element with comment
        Element siteWebCommunicationElement = createSiteWebCommunicationElement(monitorUnitConfigs);
        appConfiguration.add(siteWebCommunicationElement);
        appConfiguration.addComment(i18n.T("site.web.communication"));

        // Add other elements
        Element dscConfigurationElement = createDscConfigurationElement(tslMonitorUnit, dsWorkStations);
        appConfiguration.add(dscConfigurationElement);

        Element muNetworkPortConfig = createMuNetworkPortConfigElement();
        appConfiguration.add(muNetworkPortConfig);

        Element modelConfigurationElement = createModelConfigurationElement();
        appConfiguration.add(modelConfigurationElement);

        Element rdsConfigurationElement = createRdsConfigurationElement(tslMonitorUnit, rdsWorkStations);
        appConfiguration.add(rdsConfigurationElement);

        return appConfiguration;
    }

    private Element createFtpElement() {
        Element ftp = DocumentHelper.createElement("ftp");

        Element userId = DocumentHelper.createElement("userId");
        userId.setText("root");

        Element password = DocumentHelper.createElement("password");
        password.setText("hello");

        ftp.add(userId);
        ftp.add(password);

        return ftp;
    }

    private Element createAppConfigurationElementBase(MonitorUnitDTO tslMonitorUnit) {
        Element appConfiguration = DocumentHelper.createElement("AppConfiguration");
        appConfiguration.addAttribute("AppConfigId", String.valueOf(tslMonitorUnit.getAppConfigId()));
        appConfiguration.addAttribute("Name", i18n.T("monitor.xml.app.name"));
        return appConfiguration;
    }

    private Element createSiteWebCommunicationElement(List<MonitorUnitConfig> monitorUnitConfigs) {
        Element siteWebCommunication = DocumentHelper.createElement("siteWebCommunication");

        Element timeOut = DocumentHelper.createElement("timeOut");
        timeOut.setText(String.valueOf(monitorUnitConfigs.get(0).getSiteWebTimeOut()));

        Element retryTimes = DocumentHelper.createElement("retryTimes");
        retryTimes.setText(String.valueOf(monitorUnitConfigs.get(0).getRetryTimes()));

        Element heartBeat = DocumentHelper.createElement("heartBeat");
        heartBeat.setText(String.valueOf(monitorUnitConfigs.get(0).getHeartBeat()));

        siteWebCommunication.add(timeOut);
        siteWebCommunication.add(retryTimes);
        siteWebCommunication.add(heartBeat);

        return siteWebCommunication;
    }

    private Element createDscConfigurationElement(MonitorUnitDTO tslMonitorUnit, List<WorkStation> dsWorkStations) {
        Element dscConfiguration = DocumentHelper.createElement("dscConfiguration");

        // 默认端口9000
        String dscPort = "9000";

        // 判断是否指定了特定的数据服务器
        if (StrUtil.isNotBlank(tslMonitorUnit.getDataServer())) {
            // 指定ds
            String dsWorkstationIdStr = tslMonitorUnit.getDataServer();
            String[] workstationIds = dsWorkstationIdStr.split(",");

            for (String workstationId : workstationIds) {
                WorkStation workStation = workStationService.findByWorkStationId(Integer.parseInt(workstationId.trim()));
                if (workStation != null) {
                    Element dsIp = DocumentHelper.createElement("DscIp");

                    if (StrUtil.isEmpty(workStation.getIpAddress())) {
                        log.error("工作站IP地址为空，工作站ID：{}", workstationId);
                        continue;
                    }

                    String content;
                    if (StrUtil.contains(workStation.getIpAddress(), ":")) {
                        content = String.format("udp://[%s]:%s", workStation.getIpAddress(), dscPort);
                    } else {
                        content = String.format("udp://%s:%s", workStation.getIpAddress(), dscPort);
                    }

                    dsIp.setText(content);
                    dscConfiguration.add(dsIp);
                }
            }
        } else {
            // 全部ds
            for (WorkStation workStation : dsWorkStations) {
                Element dsIp = DocumentHelper.createElement("DscIp");

                if (StrUtil.isEmpty(workStation.getIpAddress())) {
                    log.error("工作站IP地址为空，工作站ID：{}", workStation.getWorkStationId());
                    continue;
                }

                String content;
                if (StrUtil.contains(workStation.getIpAddress(), ":")) {
                    content = String.format("udp://[%s]:%s", workStation.getIpAddress(), dscPort);
                } else {
                    content = String.format("udp://%s:%s", workStation.getIpAddress(), dscPort);
                }

                dsIp.setText(content);
                dscConfiguration.add(dsIp);
            }
        }

        return dscConfiguration;
    }

    private Element createMuNetworkPortConfigElement() {
        Element muNetworkPortConfig = DocumentHelper.createElement("MUNetworkPortConfig");
        muNetworkPortConfig.addAttribute("name", "MUNetworkPortConfig");
        muNetworkPortConfig.addAttribute("Enable", "false");
        muNetworkPortConfig.addAttribute("Port", "8848");
        return muNetworkPortConfig;
    }

    private static Element createModelConfigurationElement() {
        Element modelConfiguration = DocumentHelper.createElement("ModelConfiguration");
        modelConfiguration.addAttribute("name", "ModelConfiguration");

        Element model = DocumentHelper.createElement("Model");
        model.addAttribute("Enable", "false");
        model.addAttribute("ModelName", "Flex Web Model");
        model.addAttribute("SO_Path", "/data/WebService.so");
        model.addAttribute("CharSet", "2");
        model.addAttribute("Para", "Port=8846 ClientNumb=4 Timeout=60");

        modelConfiguration.add(model);
        return modelConfiguration;
    }

    private Element createRdsConfigurationElement(MonitorUnitDTO tslMonitorUnit, List<WorkStation> rdsWorkStations) {
        Element rdsConfiguration = DocumentHelper.createElement("RDSConfiguration");

        // 默认端口7000
        String rdsPort = "7000";

        if (StrUtil.isNotBlank(tslMonitorUnit.getRdsServer())) {
            // 指定rds
            String rdsWorkstationIdStr = tslMonitorUnit.getRdsServer();
            String[] workstationIds = rdsWorkstationIdStr.split(",");

            for (String workstationId : workstationIds) {
                WorkStation workStation = workStationService.findByWorkStationId(Integer.parseInt(workstationId.trim()));
                if (workStation != null) {
                    Element rdsIp = DocumentHelper.createElement("RDSIp");

                    if (StrUtil.isEmpty(workStation.getIpAddress())) {
                        log.error("工作站IP地址为空，工作站ID：{}", workstationId);
                        continue;
                    }

                    String content;
                    if (StrUtil.contains(workStation.getIpAddress(), ":")) {
                        content = String.format("udp://[%s]:%s", workStation.getIpAddress(), rdsPort);
                    } else {
                        content = String.format("udp://%s:%s", workStation.getIpAddress(), rdsPort);
                    }

                    rdsIp.setText(content);
                    rdsConfiguration.add(rdsIp);
                }
            }
        } else {
            for (WorkStation workStation : rdsWorkStations) {
                Element rdsIp = DocumentHelper.createElement("RDSIp");

                if (StrUtil.isEmpty(workStation.getIpAddress())) {
                    log.error("工作站IP地址为空，工作站ID：{}", workStation.getWorkStationId());
                    continue;
                }

                String content;
                if (StrUtil.contains(workStation.getIpAddress(), ":")) {
                    content = String.format("udp://[%s]:%s", workStation.getIpAddress(), rdsPort);
                } else {
                    content = String.format("udp://%s:%s", workStation.getIpAddress(), rdsPort);
                }

                rdsIp.setText(content);
                rdsConfiguration.add(rdsIp);
            }
        }

        // RDSHeartBeat
        Element rdsHeartBeat = DocumentHelper.createElement("RDSHeartBeat");
        rdsHeartBeat.setText("180");
        rdsConfiguration.add(rdsHeartBeat);

        return rdsConfiguration;
    }

    private Element createEquipmentTemplateElement(MonitorUnitDTO tslMonitorUnit) {
        Element equipmentTemplates = DocumentHelper.createElement("EquipmentTemplates");
        equipmentTemplates.addAttribute("name", "EquipmentTemplates");

        // 获取监控单元下的所有设备
        List<Equipment> equipmentList = equipmentService.findByMonitorUnitId(tslMonitorUnit.getMonitorUnitId());

        // 获取所有设备模板ID
        Set<Integer> templateIds = equipmentList.stream()
                .map(Equipment::getEquipmentTemplateId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 对于RMU类型，不下发Host设备模板
        Set<Integer> rmuCategories = Set.of(
                MonitorUnitCategoryEnum.MU_OF_RMU.getValue(),
                MonitorUnitCategoryEnum.ACROSS_MU_OF_RMU.getValue()
        );

        for (Integer templateId : templateIds) {
            EquipmentTemplate template = equipmentTemplateService.findById(templateId);
            if (template != null) {
                // 对于RMU，不下发其Host设备模板
                if (rmuCategories.contains(tslMonitorUnit.getMonitorUnitCategory()) &&
                    template.getEquipmentCategory() != null && template.getEquipmentCategory().equals(99)) {
                    continue;
                }

                // 这里可以添加设备模板的XML导出逻辑
                // 目前简化处理，只添加模板引用
                Element templateElement = DocumentHelper.createElement("EquipmentTemplate");
                templateElement.addAttribute("EquipmentTemplateId", String.valueOf(template.getEquipmentTemplateId()));
                templateElement.addAttribute("EquipmentTemplateName", template.getEquipmentTemplateName());
                equipmentTemplates.add(templateElement);
            }
        }

        return equipmentTemplates;
    }

    private Element createMonitorUnitElement(MonitorUnitDTO tslMonitorUnit) {
        Element monitorUnit = createMonitorUnitBase(tslMonitorUnit);

        appendPortsToMonitorUnit(tslMonitorUnit, monitorUnit);
        appendSamplerUnitsToMonitorUnit(tslMonitorUnit, monitorUnit);
        appendEquipmentsToMonitorUnit(tslMonitorUnit, monitorUnit);

        return monitorUnit;
    }

    private static Element createMonitorUnitBase(MonitorUnitDTO tslMonitorUnit) {
        Element monitorUnit = DocumentHelper.createElement("MonitorUnit");
        monitorUnit.addAttribute("MonitorUnitId", String.valueOf(tslMonitorUnit.getMonitorUnitId()));
        monitorUnit.addAttribute("MonitorUnitName", tslMonitorUnit.getMonitorUnitName());
        monitorUnit.addAttribute("MonitorUnitCategory", String.valueOf(tslMonitorUnit.getMonitorUnitCategory()));
        monitorUnit.addAttribute("MonitorUnitCode", String.valueOf(tslMonitorUnit.getMonitorUnitCode()));
        monitorUnit.addAttribute("WorkStationId", tslMonitorUnit.getWorkStationId() != null ? String.valueOf(tslMonitorUnit.getWorkStationId()) : "");
        monitorUnit.addAttribute("StationId", String.valueOf(tslMonitorUnit.getStationId()));
        monitorUnit.addAttribute("IpAddress", tslMonitorUnit.getIpAddress() != null ? tslMonitorUnit.getIpAddress() : "");
        monitorUnit.addAttribute("RunMode", String.valueOf(tslMonitorUnit.getRunMode() != null ? tslMonitorUnit.getRunMode() : 0));
        monitorUnit.addAttribute("ConfigFileCode", tslMonitorUnit.getConfigFileCode() != null ? tslMonitorUnit.getConfigFileCode() : "");
        monitorUnit.addAttribute("SampleConfigCode", tslMonitorUnit.getSampleConfigCode() != null ? tslMonitorUnit.getSampleConfigCode() : "");
        monitorUnit.addAttribute("SoftwareVersion", tslMonitorUnit.getSoftwareVersion() != null ? tslMonitorUnit.getSoftwareVersion() : "");
        monitorUnit.addAttribute("Description", tslMonitorUnit.getDescription() != null ? tslMonitorUnit.getDescription() : "");

        return monitorUnit;
    }

    private void appendPortsToMonitorUnit(MonitorUnitDTO tslMonitorUnit, Element monitorUnit) {
        Element ports = DocumentHelper.createElement("Ports");
        ports.addAttribute("Name", i18n.T("port"));

        List<Port> portList = portService.findByMonitorUnitId(tslMonitorUnit.getMonitorUnitId());
        for (Port port : portList) {
            Element portElement = DocumentHelper.createElement("Port");
            portElement.addAttribute("PortId", String.valueOf(port.getPortId()));
            portElement.addAttribute("PortNo", String.valueOf(port.getPortNo()));
            portElement.addAttribute("PortName", port.getPortName());

            // 端口类型转换逻辑
            switch (port.getPortType()) {
                case 3:  // 采集器SNMP端口需要设置为19
                    portElement.addAttribute("PortType", "19");
                    break;
                case 33: // RMU的snmp口
                case 32: // BACNet端口(Linux RMU)
                case 34: // 自诊断端口
                case 35: // IO端口
                    // 这些端口都需要设置为5
                    portElement.addAttribute("PortType", "5");
                    break;
                default:
                    portElement.addAttribute("PortType", String.valueOf(port.getPortType()));
                    break;
            }

            portElement.addAttribute("Setting", port.getSetting() != null ? port.getSetting() : "");
            portElement.addAttribute("PhoneNumber", port.getPhoneNumber() != null ? port.getPhoneNumber() : "");
            portElement.addAttribute("LinkSamplerUnitId", String.valueOf(port.getLinkSamplerUnitId() != null ? port.getLinkSamplerUnitId() : 0));
            portElement.addAttribute("Description", port.getDescription() != null ? port.getDescription() : "");

            ports.add(portElement);
        }

        monitorUnit.add(ports);
    }

    private void appendSamplerUnitsToMonitorUnit(MonitorUnitDTO tslMonitorUnit, Element monitorUnit) {
        Element samplerUnits = DocumentHelper.createElement("SamplerUnits");
        samplerUnits.addAttribute("Name", i18n.T("sampler.unit"));

        List<SamplerUnit> samplerUnitList = samplerUnitService.selectSamplerUnitWithPort(tslMonitorUnit.getMonitorUnitId());
        for (SamplerUnit samplerUnit : samplerUnitList) {
            Element samplerUnitElement = DocumentHelper.createElement("SamplerUnit");
            samplerUnitElement.addAttribute("SamplerUnitId", String.valueOf(samplerUnit.getSamplerUnitId()));
            samplerUnitElement.addAttribute("PortId", String.valueOf(samplerUnit.getPortId()));
            samplerUnitElement.addAttribute("ParentSamplerUnitId", String.valueOf(samplerUnit.getParentSamplerUnitId() != null ? samplerUnit.getParentSamplerUnitId() : 0));
            samplerUnitElement.addAttribute("SamplerUnitName", samplerUnit.getSamplerUnitName());
            samplerUnitElement.addAttribute("SamplerType", String.valueOf(samplerUnit.getSamplerType() != null ? samplerUnit.getSamplerType() : ""));
            samplerUnitElement.addAttribute("Address", String.valueOf(samplerUnit.getAddress() != null ? samplerUnit.getAddress() : ""));
            samplerUnitElement.addAttribute("SpUnitInterval", String.valueOf(samplerUnit.getSpUnitInterval() != null ? samplerUnit.getSpUnitInterval() : 0));
            samplerUnitElement.addAttribute("ConnectState", String.valueOf(samplerUnit.getConnectState() != null ? samplerUnit.getConnectState() : 0));
            samplerUnitElement.addAttribute("PhoneNumber", samplerUnit.getPhoneNumber() != null ? samplerUnit.getPhoneNumber() : "");
            samplerUnitElement.addAttribute("Description", samplerUnit.getDescription() != null ? samplerUnit.getDescription() : "");
            samplerUnitElement.addAttribute("DllPath", samplerUnit.getDllPath() != null ? samplerUnit.getDllPath() : "");

            samplerUnits.add(samplerUnitElement);
        }

        monitorUnit.add(samplerUnits);
    }

    private void appendEquipmentsToMonitorUnit(MonitorUnitDTO tslMonitorUnit, Element monitorUnit) {
        Element equipments = DocumentHelper.createElement("Equipments");
        equipments.addAttribute("Name", i18n.T("equipment"));

        List<Equipment> equipmentList = equipmentService.findByMonitorUnitId(tslMonitorUnit.getMonitorUnitId());

        // 对于RMU类型，不下发Host设备
        Set<Integer> rmuCategories = Set.of(
                MonitorUnitCategoryEnum.MU_OF_RMU.getValue(),
                MonitorUnitCategoryEnum.ACROSS_MU_OF_RMU.getValue()
        );

        for (Equipment equipment : equipmentList) {
            // 对于RMU，不下发其Host设备
            if (rmuCategories.contains(tslMonitorUnit.getMonitorUnitCategory()) &&
                equipment.getEquipmentCategory() != null && equipment.getEquipmentCategory().equals(99)) {
                continue;
            }

            Element equipmentElement = DocumentHelper.createElement("Equipment");
            equipmentElement.addAttribute("EquipmentId", String.valueOf(equipment.getEquipmentId()));
            equipmentElement.addAttribute("EquipmentName", equipment.getEquipmentName());
            equipmentElement.addAttribute("EquipmentCategory", String.valueOf(equipment.getEquipmentCategory() != null ? equipment.getEquipmentCategory() : 0));
            equipmentElement.addAttribute("EquipmentTemplateId", String.valueOf(equipment.getEquipmentTemplateId() != null ? equipment.getEquipmentTemplateId() : 0));
            equipmentElement.addAttribute("SamplerUnitId", String.valueOf(equipment.getSamplerUnitId() != null ? equipment.getSamplerUnitId() : 0));
            equipmentElement.addAttribute("ParentEquipmentId", String.valueOf(equipment.getParentEquipmentId() != null ? equipment.getParentEquipmentId() : 0));
            equipmentElement.addAttribute("ConnectState", String.valueOf(equipment.getConnectState() != null ? equipment.getConnectState() : 0));
            equipmentElement.addAttribute("Description", equipment.getDescription() != null ? equipment.getDescription() : "");

            equipments.add(equipmentElement);
        }

        monitorUnit.add(equipments);
    }

    @Override
    public void createMonitorUnitConfigXML(WorkStation workStation, String path) {
        log.info("Creating monitor unit config XML for workstation: {}", workStation.getWorkStationName());

        // 获取该工作站下的所有监控单元
        List<MonitorUnitDTO> monitorUnits = monitorUnitService.findByWorkStationId(workStation.getWorkStationId());

        for (MonitorUnitDTO monitorUnit : monitorUnits) {
            try {
                createMonitorUnitConfigXML(monitorUnit, path);
                log.info("Successfully created config XML for monitor unit: {}", monitorUnit.getMonitorUnitName());
            } catch (Exception e) {
                log.error("Failed to create config XML for monitor unit: {}", monitorUnit.getMonitorUnitName(), e);
            }
        }
    }

    @Override
    public boolean sendMonitorUnitConfigXML(MonitorUnitDTO tslMonitorUnit, String uniqueId, String protocol, Integer port, String username, String password) {
        if (tslMonitorUnit == null) {
            log.error("监控单元为空");
            return false;
        }

        String path = "upload-dir/monitorUnitConfig/" + tslMonitorUnit.getStationName() + tslMonitorUnit.getStationId() + "/" + tslMonitorUnit.getMonitorUnitName();

        // 准备监控单元配置文件
        File file = prepareFile(tslMonitorUnit, uniqueId);
        if (file == null) {
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, "监控单元配置文件不存在", false);
            FileUtil.appendUtf8String("监控单元配置文件不存在\n", new File(path + "/" + tslMonitorUnit.getMonitorUnitId() + ".log"));
            return false;
        }

        // 判断protocol是否为ftp，否则是使用sftp
        if (protocol.equalsIgnoreCase("ftp")) {
            if (!uploadFileToFtp(tslMonitorUnit, port, username, password, file, uniqueId)) {
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, "监控单元配置文件上传失败", false);
                FileUtil.appendUtf8String("监控单元配置文件上传失败\n", new File(path + "/" + tslMonitorUnit.getMonitorUnitId() + ".log"));
                return false;
            }
        } else {
            if (!uploadFileToSFtp(tslMonitorUnit, port, username, password, file, uniqueId)) {
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, "监控单元配置文件上传失败", false);
                FileUtil.appendUtf8String("监控单元配置文件上传失败\n", new File(path + "/" + tslMonitorUnit.getMonitorUnitId() + ".log"));
                return false;
            }
        }

        return true;
    }

    private File prepareFile(MonitorUnitDTO tslMonitorUnit, String uniqueId) {
        String path = "upload-dir/monitorUnitConfig/" + tslMonitorUnit.getStationName() + tslMonitorUnit.getStationId() + "/" + tslMonitorUnit.getMonitorUnitName();
        String fileName = "MonitorUnits" + tslMonitorUnit.getMonitorUnitName() + ".xml";
        File file = new File(path + "/" + fileName);

        if (!file.exists()) {
            log.error("监控单元配置文件不存在: {}", file.getAbsolutePath());
            return null;
        }

        return file;
    }

    /**
     * 使用ftp上传文件
     *
     * @param tslMonitorUnit 监控单元
     * @param ftpPort        ftp默认是21
     * @param ftpUsername    ftp用户名
     * @param ftpPassword    ftp密码
     * @param file           要上传的文件
     * @param uniqueId       唯一ID
     * @return 是否上传成功
     */
    private boolean uploadFileToFtp(MonitorUnitDTO tslMonitorUnit, Integer ftpPort, String ftpUsername, String ftpPassword, File file, String uniqueId) {
        log.info("使用ftp上传文件[monitorName:{}]", tslMonitorUnit.getMonitorUnitName());

        Ftp ftp = null;
        try {
            String connectingMsg = String.format("远程%s连接中...", tslMonitorUnit.getIpAddress());
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, connectingMsg, false);
            FileUtil.appendUtf8String(connectingMsg + "\n", new File(file.getParent() + "/" + tslMonitorUnit.getMonitorUnitId() + ".log"));

            FtpConfig ftpConfig = new FtpConfig();
            ftpConfig.setHost(tslMonitorUnit.getIpAddress());
            ftpConfig.setPort(ftpPort);
            ftpConfig.setUser(ftpUsername);
            ftpConfig.setPassword(ftpPassword);
            ftpConfig.setConnectionTimeout(10000);
            ftp = new Ftp(ftpConfig, FtpMode.Passive);

            String connectedMsg = String.format("远程%s连接成功", tslMonitorUnit.getIpAddress());
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, connectedMsg, false);
            FileUtil.appendUtf8String(connectedMsg + "\n", new File(file.getParent() + "/" + tslMonitorUnit.getMonitorUnitId() + ".log"));

            // 上传转码后的临时文件
            String encodedFileName = CharsetUtil.convert(file.getName(), CharsetUtil.CHARSET_GBK, CharsetUtil.CHARSET_ISO_8859_1);
            boolean upload = ftp.upload("/home/<USER>/XmlCfg/", encodedFileName, new FileInputStream(file));

            if (upload) {
                String successMsg = String.format("监控配置文件上传成功[文件名称:%s]", file.getName());
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, successMsg, false);
                log.info("监控配置文件上传成功[monitorUnitId={}, filepath={}]", tslMonitorUnit.getMonitorUnitId(), file.getAbsolutePath());
                FileUtil.appendUtf8String(successMsg + "\n", new File(file.getParent() + "/" + tslMonitorUnit.getMonitorUnitId() + ".log"));
            } else {
                String failureMsg = String.format("监控配置文件上传失败[文件名称:%s]", file.getName());
                log.error("监控配置文件上传失败[monitorUnitId={}, filepath={}]", tslMonitorUnit.getMonitorUnitId(), file.getAbsolutePath());
                FileUtil.appendUtf8String(failureMsg + "\n", new File(file.getParent() + "/" + tslMonitorUnit.getMonitorUnitId() + ".log"));
                return false;
            }

            ftp.close();

            // 更新配置文件MD5
            String md5Hex = DigestUtil.md5Hex(file);
            monitorUnitService.updateMonitorUnitConfigFileCode(tslMonitorUnit.getMonitorUnitId(), md5Hex);

            return true;

        } catch (Exception e) {
            log.error("FTP文件上传失败", e);
            String errorMsg = "FTP连接失败: " + e.getMessage();
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
            FileUtil.appendUtf8String(errorMsg + "\n", new File(file.getParent() + "/" + tslMonitorUnit.getMonitorUnitId() + ".log"));
            return false;
        } finally {
            if (ftp != null) {
                try {
                    ftp.close();
                } catch (Exception e) {
                    log.error("ftp关闭失败", e);
                }
            }
        }
    }

    /**
     * 使用SFTP上传文件
     *
     * @param tslMonitorUnit 监控单元
     * @param sftpPort       SFTP端口，默认是22
     * @param sftpUsername   SFTP用户名
     * @param sftpPassword   SFTP密码
     * @param file           要上传的文件
     * @param uniqueId       唯一ID
     * @return 是否上传成功
     */
    private boolean uploadFileToSFtp(MonitorUnitDTO tslMonitorUnit, Integer sftpPort, String sftpUsername, String sftpPassword, File file, String uniqueId) {
        log.info("使用sftp上传文件[monitorName:{}]", tslMonitorUnit.getMonitorUnitName());

        Sftp sftp = null;
        try {
            String connectingMsg = String.format("远程%s连接中...", tslMonitorUnit.getIpAddress());
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, connectingMsg, false);
            FileUtil.appendUtf8String(connectingMsg + "\n", new File(file.getParent() + "/" + tslMonitorUnit.getMonitorUnitId() + ".log"));

            sftp = new Sftp(tslMonitorUnit.getIpAddress(), sftpPort, sftpUsername, sftpPassword);

            String connectedMsg = String.format("远程%s连接成功", tslMonitorUnit.getIpAddress());
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, connectedMsg, false);
            FileUtil.appendUtf8String(connectedMsg + "\n", new File(file.getParent() + "/" + tslMonitorUnit.getMonitorUnitId() + ".log"));

            // 上传文件
            boolean upload = sftp.upload("/home/<USER>/XmlCfg/", file.getName(), new FileInputStream(file));

            if (upload) {
                String successMsg = String.format("监控配置文件上传成功[文件名称:%s]", file.getName());
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, successMsg, false);
                log.info("监控配置文件上传成功[monitorUnitId={}, filepath={}]", tslMonitorUnit.getMonitorUnitId(), file.getAbsolutePath());
                FileUtil.appendUtf8String(successMsg + "\n", new File(file.getParent() + "/" + tslMonitorUnit.getMonitorUnitId() + ".log"));
            } else {
                String failureMsg = String.format("监控配置文件上传失败[文件名称:%s]", file.getName());
                log.error("监控配置文件上传失败[monitorUnitId={}, filepath={}]", tslMonitorUnit.getMonitorUnitId(), file.getAbsolutePath());
                FileUtil.appendUtf8String(failureMsg + "\n", new File(file.getParent() + "/" + tslMonitorUnit.getMonitorUnitId() + ".log"));
                return false;
            }

            sftp.close();

            // 更新配置文件MD5
            String md5Hex = DigestUtil.md5Hex(file);
            monitorUnitService.updateMonitorUnitConfigFileCode(tslMonitorUnit.getMonitorUnitId(), md5Hex);

            return true;

        } catch (Exception e) {
            log.error("SFTP文件上传失败", e);
            String errorMsg = "SFTP连接失败: " + e.getMessage();
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
            FileUtil.appendUtf8String(errorMsg + "\n", new File(file.getParent() + "/" + tslMonitorUnit.getMonitorUnitId() + ".log"));
            return false;
        } finally {
            if (sftp != null) {
                try {
                    sftp.close();
                } catch (Exception e) {
                    log.error("sftp关闭失败", e);
                }
            }
        }
    }


}
