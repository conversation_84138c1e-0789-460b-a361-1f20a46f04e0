package com.siteweb.tcs.siteweb.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.siteweb.tcs.siteweb.dto.MonitorUnitDTO;
import com.siteweb.tcs.siteweb.entity.*;
import com.siteweb.tcs.siteweb.enums.MonitorUnitCategoryEnum;
import com.siteweb.tcs.siteweb.enums.MonitorUnitStateEnum;
import com.siteweb.tcs.siteweb.enums.OperationObjectTypeEnum;
import com.siteweb.tcs.siteweb.enums.TableIdentityEnum;
import com.siteweb.tcs.siteweb.enums.TaskStatusEnum;
import com.siteweb.tcs.siteweb.manager.MonitorUnitStateManager;
import com.siteweb.tcs.siteweb.util.TableExistenceChecker;
import com.siteweb.tcs.siteweb.service.*;
import com.siteweb.tcs.siteweb.util.AESUtil;
import com.siteweb.tcs.siteweb.util.I18n;
import com.siteweb.tcs.siteweb.enums.TaskTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.XMLWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.*;
import java.util.Collections;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;


/**
 * @Description:
 */
@Service
@Slf4j
public class MonitorUnitXmlServiceImpl implements IMonitorUnitXmlService {


    @Autowired
    private IMonitorUnitService monitorUnitService;

    @Autowired
    MonitorUnitStateManager monitorUnitStateManager;

    @Autowired
    private IMonitorUnitConfigService monitorUnitConfigService;

    @Autowired
    private IStationService stationService;

    @Autowired
    private IWorkStationService workStationService;

    @Autowired
    private IMonitorUnitExtendService monitorUnitExtendService;

    @Autowired
    private I18n i18n;

    @Autowired
    private IPortService portService;

    @Autowired
    private ISamplerUnitService samplerUnitService;

    @Autowired
    private IEquipmentService equipmentService;

    @Autowired
    private IEquipmentTemplateService equipmentTemplateService;

    @Autowired
    private ITaskStatusService taskStatusService;

    @Autowired
    private IConfigFilePreparationService configFilePreparationService;

    @Autowired
    private ICollectorUploadService collectorUploadService;

    @Autowired
    private ICollectorDownloadService collectorDownloadService;

    @Autowired
    private IEquipmentTemplateXmlService equipmentTemplateXmlService;

    @Autowired
    private TableExistenceChecker tableExistenceChecker;

    @Autowired(required = false)
    private IOperationDetailService operationDetailService;

    @Value("${aes.key:theDefaultAESKey}")
    private String aesKey;

    /**
     * 构建监控单元配置文件路径
     * 使用监控单元ID作为中间路径，避免依赖station信息
     */
    private String buildMonitorUnitConfigPath(MonitorUnitDTO monitorUnit) {
        return String.format("plugins/south-omc-siteweb/workspace/monitorUnitConfig/%d/%s",
                monitorUnit.getMonitorUnitId(), monitorUnit.getMonitorUnitName());
    }

    /**
     * 初始化日志文件
     */
    private void initializeLogFile(String path, Integer monitorUnitId, String message) {
        String logFilePath = path + "/" + monitorUnitId + ".log";
        FileUtil.del(new File(logFilePath));
        FileUtil.appendUtf8String(message + "\n", new File(logFilePath));
    }
    /**
     * 异步生成监控单元配置文件
     * 请注意，虽然这个方法没有返回值，但是如果在方法执行过程中抛出了异常，
     * Spring将会抛出AsyncUncaughtExceptionHandler。
     * 你可以通过实现AsyncConfigurer接口并重写getAsyncUncaughtExceptionHandler()方法来自定义异常处理。
     *
     * @param msg
     * @param uniqueId
     */
    @Override
    @Async("monitorUnitTaskExecutor")
    public void createMonitorUnitConfigXMLAsync(String msg, String uniqueId) {
        // 注意：任务已在Provider层创建，这里只负责执行和更新状态

        String[] strArray = msg.split(",");
        int[] intArray = Arrays.stream(strArray).mapToInt(Integer::parseInt).toArray();

        if (intArray.length == 0) {
            taskStatusService.setTaskErrorSimplified(uniqueId, "没有需要生成配置的监控单元");
            return;
        }

        int totalCount = intArray.length;
        int successCount = 0;
        int failureCount = 0;
        List<String> failureDetails = new ArrayList<>();

        // 更新任务状态为运行中
        taskStatusService.updateTaskStatus(uniqueId, TaskStatusEnum.RUNNING, "开始生成配置文件...", false);

        for (int i = 0; i < intArray.length; i++) {
            int monitorUnitId = intArray[i];

            try {
                MonitorUnitDTO monitorUnit = monitorUnitService.findByIdWithoutStation(monitorUnitId);
                if (monitorUnit == null) {
                    failureCount++;
                    failureDetails.add(String.format("监控单元[%d]: 不存在", monitorUnitId));
                    log.error("监控单元不存在，监控单元ID：{}", monitorUnitId);
                    continue;
                }

                // 更新进度
                int progress = ((i + 1) * 100) / intArray.length;
                String progressMsg = String.format("正在生成监控单元[%s]配置... (%d/%d)",
                        monitorUnit.getMonitorUnitName(), i + 1, intArray.length);
                taskStatusService.updateTaskProgress(uniqueId, progress, progressMsg, progressMsg);

                // 使用监控单元ID作为路径
                String path = buildMonitorUnitConfigPath(monitorUnit);

                // 清理并创建日志文件
                initializeLogFile(path, monitorUnit.getMonitorUnitId(), progressMsg);

                // 生成配置文件
                createMonitorUnitConfigXML(monitorUnit, path);
                writeMonitorUnitDetailsToFile(monitorUnit, path);

                successCount++;
                String successMsg = String.format("生成监控单元[%s]配置成功!", monitorUnit.getMonitorUnitName());
                FileUtil.appendUtf8String(successMsg + "\n", new File(path + "/" + monitorUnit.getMonitorUnitId() + ".log"));

                // 记录操作日志
                if (operationDetailService != null) {
                    try {
                        operationDetailService.recordOperationLog(
                                -1, // 系统操作
                                monitorUnit.getMonitorUnitId().toString(),
                                OperationObjectTypeEnum.MONITOR_UNIT,
                                "配置生成",
                                "生成",
                                "",
                                "生成监控单元配置成功"
                        );
                    } catch (Exception e) {
                        log.warn("记录配置生成操作日志失败", e);
                    }
                }

            } catch (Exception e) {
                failureCount++;
                String errorMsg = String.format("监控单元[%d]: %s", monitorUnitId, e.getMessage());
                failureDetails.add(errorMsg);
                log.error("Failed to generate config for monitor unit: {}", monitorUnitId, e);
            }
        }

        // 生成完成消息
        String completionMessage = String.format("配置生成完成: 总计%d个，成功%d个，失败%d个",
                totalCount, successCount, failureCount);

        if (failureCount > 0) {
            // 添加失败详情到错误消息中（限制长度）
            StringBuilder errorMsg = new StringBuilder(completionMessage);
            errorMsg.append("。失败详情: ");
            for (int i = 0; i < Math.min(failureDetails.size(), 3); i++) {
                errorMsg.append(failureDetails.get(i));
                if (i < Math.min(failureDetails.size(), 3) - 1) {
                    errorMsg.append("; ");
                }
            }
            if (failureDetails.size() > 3) {
                errorMsg.append("...");
            }

            // 如果有失败，将失败详情记录到errorMessage中
            TaskStatus task = taskStatusService.getTaskFromDatabase(uniqueId);
            if (task != null) {
                task.setErrorMessage(errorMsg.toString());
                taskStatusService.updateTaskInDatabase(task);
            }
        }

        // 完成任务
        taskStatusService.completeTaskWithCounts(uniqueId, totalCount, successCount, failureCount, completionMessage);
    }

    @Override
    public void createMonitorUnitConfigXML(MonitorUnitDTO tslMonitorUnit, String path) {
        // 判断是否存在非法字符
        if (containsInvalidChars(tslMonitorUnit)) {
            return;
        }

        // 获取监控单元配置
        List<MonitorUnitConfig> monitorUnitConfigs = monitorUnitConfigService.findByMonitorUnitId(tslMonitorUnit.getMonitorUnitId());
        if (monitorUnitConfigs.isEmpty()) {
            log.error("监控单元配置为空，监控单元ID：{}", tslMonitorUnit.getMonitorUnitId());
            return;
        }
        if (ObjectUtil.isNull(tableExistenceChecker) || tableExistenceChecker.isTableExists(TableIdentityEnum.TBL_STATION.getTableName())) {
            Station station = stationService.findByStationId(tslMonitorUnit.getStationId());
            if (station == null) {
                log.error("站点为空，站点ID：{}", tslMonitorUnit.getStationId());
                return;
            }
        }
        List<WorkStation> dsWorkStations = Collections.emptyList();
        List<WorkStation> rdsWorkStations = Collections.emptyList();
        if (ObjectUtil.isNull(tableExistenceChecker) || tableExistenceChecker.isTableExists(TableIdentityEnum.TBL_WORKSTATION.getTableName())) {
            dsWorkStations = workStationService.findDsWorkStations();
            rdsWorkStations = workStationService.findRDsWorkStations();
        }

        String fileName = "MonitorUnits" + tslMonitorUnit.getMonitorUnitName() + ".xml";
        // 创建目录
        FileUtil.mkdir(new File(path));

        // 使用dom4j创建Document
        Document document = DocumentHelper.createDocument();

        // 创建根元素MainCfg
        Element mainCfg = createMainCfgElement(tslMonitorUnit, new Station());
        document.setRootElement(mainCfg);

        // appConfiguration
        Element appConfiguration = createAppConfigurationElement(tslMonitorUnit, monitorUnitConfigs, dsWorkStations, rdsWorkStations);
        mainCfg.add(appConfiguration);

        // 获取设备模板
        Element equipmentTemplates = createEquipmentTemplateElement(tslMonitorUnit);
        mainCfg.add(equipmentTemplates);

        // 获取监控单元
        Element monitorUnit = createMonitorUnitElement(tslMonitorUnit);
        mainCfg.add(monitorUnit);

        try {
            // 创建文件输出流
            File file = new File(path + "/" + fileName);
            FileOutputStream fos = new FileOutputStream(file);

            // 创建格式化输出
            OutputFormat format = OutputFormat.createPrettyPrint();
            format.setEncoding("UTF-8");

            // 创建XML写入器
            XMLWriter writer = new XMLWriter(fos, format);
            writer.write(document);
            writer.flush();
            writer.close();

            // 更新文件MD5
            String md5Hex = DigestUtil.md5Hex(file);
            monitorUnitService.updateMonitorUnitConfigFileCode(tslMonitorUnit.getMonitorUnitId(), md5Hex);
            // 更新监控单元状态为待下发
            monitorUnitStateManager.updateMonitorUnitStatus(tslMonitorUnit.getMonitorUnitId(), MonitorUnitStateEnum.PENDING);

        } catch (Exception e) {
            log.error("生成监控单元配置文件失败", e);
        }
    }

    private static void writeMonitorUnitDetailsToFile(MonitorUnitDTO tslMonitorUnit, String path) {
        // 写入监控单元名称和IP地址
        String muContext = String.format("%s,%s", tslMonitorUnit.getMonitorUnitName(), tslMonitorUnit.getIpAddress());
        FileUtil.writeUtf8String(muContext, new File(path + "/1.txt"));
    }


    @Override
    @Async("monitorUnitTaskExecutor")
    public void sendMonitorUnitConfigXMLAsync(String monitorUnitIds, Integer port, String username, String password, String protocol, String uniqueId) {
        // 注意：任务已在Provider层创建，这里只负责执行和更新状态

        String[] strArray = monitorUnitIds.split(",");
        // 使用Set去重
        Set<Integer> uniqueIds = new HashSet<>();
        for (String s : strArray) {
            uniqueIds.add(Integer.parseInt(s));
        }

        // 转换回数组
        int[] intArray = uniqueIds.stream().mapToInt(Integer::intValue).toArray();

        if (intArray.length == 0) {
            taskStatusService.setTaskErrorSimplified(uniqueId, "没有需要下发配置的监控单元");
            return;
        }

        int totalCount = intArray.length;
        int successCount = 0;
        int failureCount = 0;
        int skippedCount = 0;
        List<String> failureDetails = new ArrayList<>();

        // 更新任务状态为运行中
        taskStatusService.updateTaskStatus(uniqueId, TaskStatusEnum.RUNNING, "开始下发配置文件...", false);

        for (int i = 0; i < intArray.length; i++) {
            int monitorUnitId = intArray[i];

            // 检查状态是否为SENDING
            Integer status = monitorUnitStateManager.getMonitorUnitStatus(monitorUnitId);
            if (status != null && status.equals(MonitorUnitStateEnum.SENDING.getValue())) {
                // 如果状态已经是SENDING，则跳过该ID
                String skipMessage = String.format("监控单元[%d]状态已经是SENDING，跳过下发", monitorUnitId);
                log.info(skipMessage);
                skippedCount++;

                // 更新进度
                int progress = ((i + 1) * 100) / intArray.length;
                taskStatusService.updateTaskProgress(uniqueId, progress, skipMessage, skipMessage);
                continue;
            }

            // 更新状态为SENDING
            monitorUnitStateManager.updateMonitorUnitStatus(monitorUnitId, MonitorUnitStateEnum.SENDING);

            // 更新进度
            int progress = ((i + 1) * 100) / intArray.length;
            String progressMessage = String.format("正在处理监控单元[%d] (%d/%d)", monitorUnitId, i + 1, intArray.length);
            taskStatusService.updateTaskProgress(uniqueId, progress, progressMessage, progressMessage);

            // 处理单个监控单元
            boolean success = processSingleMonitorUnit(monitorUnitId, port, username, password, protocol, uniqueId, failureDetails);
            if (success) {
                successCount++;
            } else {
                failureCount++;
            }
        }

        // 生成完成消息
        String completionMessage = String.format("配置下发完成: 总计%d个，成功%d个，失败%d个，跳过%d个",
                totalCount, successCount, failureCount, skippedCount);

        if (failureCount > 0) {
            // 添加失败详情到错误消息中（限制长度）
            StringBuilder errorMsg = new StringBuilder(completionMessage);
            errorMsg.append("。失败详情: ");
            for (int i = 0; i < Math.min(failureDetails.size(), 3); i++) {
                errorMsg.append(failureDetails.get(i));
                if (i < Math.min(failureDetails.size(), 3) - 1) {
                    errorMsg.append("; ");
                }
            }
            if (failureDetails.size() > 3) {
                errorMsg.append("...");
            }

            // 如果有失败，将失败详情记录到errorMessage中
            TaskStatus task = taskStatusService.getTaskFromDatabase(uniqueId);
            if (task != null) {
                task.setErrorMessage(errorMsg.toString());
                taskStatusService.updateTaskInDatabase(task);
            }
        }

        // 完成任务
        taskStatusService.completeTaskWithCounts(uniqueId, totalCount, successCount, failureCount, completionMessage);
    }

    /**
     * 多线程版本的配置下发方法
     * 使用线程池并行处理多个监控单元，提高处理效率
     */
    @Override
    @Async("monitorUnitTaskExecutor")
    public void sendMonitorUnitConfigXMLAsyncParallel(String monitorUnitIds, Integer port, String username, String password, String protocol, String uniqueId) {
        String[] strArray = monitorUnitIds.split(",");
        Set<Integer> uniqueIds = new HashSet<>();
        for (String s : strArray) {
            uniqueIds.add(Integer.parseInt(s));
        }

        int[] intArray = uniqueIds.stream().mapToInt(Integer::intValue).toArray();

        if (intArray.length == 0) {
            taskStatusService.setTaskErrorSimplified(uniqueId, "没有需要下发配置的监控单元");
            return;
        }

        int totalCount = intArray.length;
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);
        AtomicInteger skippedCount = new AtomicInteger(0);
        AtomicInteger processedCount = new AtomicInteger(0);

        // 线程安全的失败详情列表
        List<String> failureDetails = Collections.synchronizedList(new ArrayList<>());

        // 更新任务状态为运行中
        taskStatusService.updateTaskStatus(uniqueId, TaskStatusEnum.RUNNING, "开始并行下发配置文件...", false);

        // 创建线程池，根据监控单元数量动态调整线程数
        int threadPoolSize = Math.min(Math.max(2, totalCount / 5), 10); // 最少2个，最多10个线程
        ExecutorService executorService = Executors.newFixedThreadPool(threadPoolSize);

        // 创建CompletableFuture列表来管理异步任务
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        try {
            for (int i = 0; i < intArray.length; i++) {
                final int index = i;
                final int monitorUnitId = intArray[i];

                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        // 检查状态是否为SENDING
                        Integer status = monitorUnitStateManager.getMonitorUnitStatus(monitorUnitId);
                        if (status != null && status.equals(MonitorUnitStateEnum.SENDING.getValue())) {
                            String skipMessage = String.format("监控单元[%d]状态已经是SENDING，跳过下发", monitorUnitId);
                            log.info(skipMessage);
                            skippedCount.incrementAndGet();
                            updateProgressSafely(uniqueId, processedCount.incrementAndGet(), totalCount, skipMessage);
                            return;
                        }

                        // 更新状态为SENDING
                        monitorUnitStateManager.updateMonitorUnitStatus(monitorUnitId, MonitorUnitStateEnum.SENDING);

                        String progressMessage = String.format("正在处理监控单元[%d] (%d/%d)",
                                monitorUnitId, processedCount.get() + 1, totalCount);

                        // 处理单个监控单元
                        boolean success = processSingleMonitorUnitThreadSafe(monitorUnitId, port, username, password, protocol, uniqueId, failureDetails);

                        if (success) {
                            successCount.incrementAndGet();
                        } else {
                            failureCount.incrementAndGet();
                        }

                        // 更新进度
                        updateProgressSafely(uniqueId, processedCount.incrementAndGet(), totalCount, progressMessage);

                    } catch (Exception e) {
                        log.error("处理监控单元[{}]时发生异常", monitorUnitId, e);
                        failureCount.incrementAndGet();
                        failureDetails.add(String.format("监控单元[%d]: 处理异常 - %s", monitorUnitId, e.getMessage()));
                        updateProgressSafely(uniqueId, processedCount.incrementAndGet(), totalCount,
                                String.format("监控单元[%d]处理失败", monitorUnitId));
                    }
                }, executorService);

                futures.add(future);
            }

            // 等待所有任务完成
            CompletableFuture<Void> allTasks = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));

            // 设置超时时间，避免无限等待
            allTasks.get(30, TimeUnit.MINUTES); // 30分钟超时

        } catch (TimeoutException e) {
            log.error("配置下发任务超时: {}", uniqueId, e);
            taskStatusService.setTaskErrorSimplified(uniqueId, "任务执行超时，部分监控单元可能未完成处理");
            return;
        } catch (Exception e) {
            log.error("配置下发任务执行异常: {}", uniqueId, e);
            taskStatusService.setTaskErrorSimplified(uniqueId, "任务执行异常: " + e.getMessage());
            return;
        } finally {
            // 关闭线程池
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        // 生成完成消息
        int finalSuccessCount = successCount.get();
        int finalFailureCount = failureCount.get();
        int finalSkippedCount = skippedCount.get();

        String completionMessage = String.format("并行配置下发完成: 总计%d个，成功%d个，失败%d个，跳过%d个",
                totalCount, finalSuccessCount, finalFailureCount, finalSkippedCount);

        if (finalFailureCount > 0) {
            // 添加失败详情到错误消息中（限制长度）
            StringBuilder errorMsg = new StringBuilder(completionMessage);
            errorMsg.append("。失败详情: ");
            synchronized (failureDetails) {
                for (int i = 0; i < Math.min(failureDetails.size(), 3); i++) {
                    errorMsg.append(failureDetails.get(i));
                    if (i < Math.min(failureDetails.size(), 3) - 1) {
                        errorMsg.append("; ");
                    }
                }
                if (failureDetails.size() > 3) {
                    errorMsg.append("...");
                }
            }

            // 如果有失败，将失败详情记录到errorMessage中
            TaskStatus task = taskStatusService.getTaskFromDatabase(uniqueId);
            if (task != null) {
                task.setErrorMessage(errorMsg.toString());
                taskStatusService.updateTaskInDatabase(task);
            }
        }

        // 完成任务
        taskStatusService.completeTaskWithCounts(uniqueId, totalCount, finalSuccessCount, finalFailureCount, completionMessage);
    }

    /**
     * 线程安全的进度更新方法
     */
    private void updateProgressSafely(String uniqueId, int processedCount, int totalCount, String message) {
        try {
            int progress = (processedCount * 100) / totalCount;
            taskStatusService.updateTaskProgress(uniqueId, progress, message, message);
        } catch (Exception e) {
            log.warn("更新任务进度失败: {}", uniqueId, e);
        }
    }

    /**
     * 线程安全的单个监控单元处理方法
     */
    private boolean processSingleMonitorUnitThreadSafe(int monitorUnitId, Integer port, String username,
                                                       String password, String protocol, String uniqueId,
                                                       List<String> failureDetails) {
        try {
            MonitorUnitDTO monitorUnit = monitorUnitService.findByIdWithoutStation(monitorUnitId);
            if (monitorUnit == null) {
                String errorMsg = String.format("监控单元[%d]: 不存在", monitorUnitId);
                synchronized (failureDetails) {
                    failureDetails.add(errorMsg);
                }
                return false;
            }

            // 判断如果监控单元类型是rmu下mu，则无需下发。
            if (monitorUnit.getMonitorUnitCategory() == MonitorUnitCategoryEnum.MU_OF_RMU.getValue() ||
                    monitorUnit.getMonitorUnitCategory() == MonitorUnitCategoryEnum.ACROSS_MU_OF_RMU.getValue()) {
                monitorUnitStateManager.updateMonitorUnitStatus(monitorUnit.getMonitorUnitId(), MonitorUnitStateEnum.NO_NEED_TO_SEND);
                return true; // RMU下的MU无需下发，算作成功
            }

            // 判断监控端元类型为GFSU3，protocol设置为sftp
            String currentProtocol = protocol;
            if (monitorUnit.getMonitorUnitCategory() == MonitorUnitCategoryEnum.GFSU3.getValue()) {
                currentProtocol = "sftp";
            }
            // 如果protocol是sftp，前端传入端口等于21，则将端口号设置为22
            Integer currentPort = port;
            if ("sftp".equalsIgnoreCase(currentProtocol) && currentPort == 21) {
                currentPort = 22;
            }

            String path = buildMonitorUnitConfigPath(monitorUnit);
            boolean result = sendMonitorUnitConfigXML(monitorUnit, uniqueId, currentProtocol, currentPort, username, password);

            if (!result) {
                String failureMessage = String.format("监控单元[%s]: 下发失败", monitorUnit.getMonitorUnitName());
                synchronized (failureDetails) {
                    failureDetails.add(failureMessage);
                }
                monitorUnitStateManager.updateMonitorUnitStatus(monitorUnit.getMonitorUnitId(), MonitorUnitStateEnum.FAILURE);
                FileUtil.appendUtf8String(failureMessage + "\n", new File(path + "/" + monitorUnit.getMonitorUnitId() + ".log"));
                monitorUnitExtendService.delete(monitorUnit.getMonitorUnitId());
                return false;
            }

            String successMessage = String.format("下发监控单元[%s]配置文件成功", monitorUnit.getMonitorUnitName());
            monitorUnitStateManager.updateMonitorUnitStatus(monitorUnit.getMonitorUnitId(), MonitorUnitStateEnum.SUCCESS);
            FileUtil.appendUtf8String(successMessage + "\n", new File(path + "/" + monitorUnit.getMonitorUnitId() + ".log"));
            saveAccountPassword(monitorUnit.getMonitorUnitId(), username, password, String.valueOf(currentPort), currentProtocol);
            return true;

        } catch (Exception ex) {
            log.error("发送监控单元[{}]配置文件失败", monitorUnitId, ex);
            // 删除账号密码
            monitorUnitExtendService.delete(monitorUnitId);
            // 更新状态为失败
            monitorUnitStateManager.updateMonitorUnitStatus(monitorUnitId, MonitorUnitStateEnum.FAILURE);

            String errorMessage = String.format("监控单元[%d]: %s", monitorUnitId, ex.getMessage());
            synchronized (failureDetails) {
                failureDetails.add(errorMessage);
            }
            return false;
        }
    }


    /**
     * 处理单个监控单元的配置下发
     *
     * @param monitorUnitId 监控单元ID
     * @param port          端口号
     * @param username      用户名
     * @param password      密码
     * @param protocol      协议（ftp或sftp）
     * @param uniqueId      唯一标识
     * @param failureDetails 失败详情列表
     * @return 是否成功
     */
    private boolean processSingleMonitorUnit(int monitorUnitId, Integer port, String username, String password, String protocol, String uniqueId, List<String> failureDetails) {
        try {
            MonitorUnitDTO monitorUnit = monitorUnitService.findByIdWithoutStation(monitorUnitId);
            if (monitorUnit == null) {
                failureDetails.add(String.format("监控单元[%d]: 不存在", monitorUnitId));
                return false;
            }

            // 判断如果监控单元类型是rmu下mu，则无需下发。
            if (monitorUnit.getMonitorUnitCategory() == MonitorUnitCategoryEnum.MU_OF_RMU.getValue() || monitorUnit.getMonitorUnitCategory() == MonitorUnitCategoryEnum.ACROSS_MU_OF_RMU.getValue()) {
                monitorUnitStateManager.updateMonitorUnitStatus(monitorUnit.getMonitorUnitId(), MonitorUnitStateEnum.NO_NEED_TO_SEND);
                return true; // RMU下的MU无需下发，算作成功
            }

            // 判断监控端元类型为GFSU3，protocol设置为sftp
            String currentProtocol = protocol;
            if (monitorUnit.getMonitorUnitCategory() == MonitorUnitCategoryEnum.GFSU3.getValue()) {
                currentProtocol = "sftp";
            }
            // 如果protocol是sftp，前端传入端口等于21，则将端口号设置为22
            Integer currentPort = port;
            if ("sftp".equalsIgnoreCase(currentProtocol) && currentPort == 21) {
                currentPort = 22;
            }

            String path = buildMonitorUnitConfigPath(monitorUnit);
            boolean result = sendMonitorUnitConfigXML(monitorUnit, uniqueId, currentProtocol, currentPort, username, password);

            if (!result) {
                String failureMessage = String.format("监控单元[%s]: 下发失败", monitorUnit.getMonitorUnitName());
                failureDetails.add(failureMessage);
                monitorUnitStateManager.updateMonitorUnitStatus(monitorUnit.getMonitorUnitId(), MonitorUnitStateEnum.FAILURE);
                FileUtil.appendUtf8String(failureMessage + "\n", new File(path + "/" + monitorUnit.getMonitorUnitId() + ".log"));
                monitorUnitExtendService.delete(monitorUnit.getMonitorUnitId());
                return false;
            }

            String successMessage = String.format("下发监控单元[%s]配置文件成功", monitorUnit.getMonitorUnitName());
            monitorUnitStateManager.updateMonitorUnitStatus(monitorUnit.getMonitorUnitId(), MonitorUnitStateEnum.SUCCESS);
            FileUtil.appendUtf8String(successMessage + "\n", new File(path + "/" + monitorUnit.getMonitorUnitId() + ".log"));
            saveAccountPassword(monitorUnit.getMonitorUnitId(), username, password, String.valueOf(currentPort), currentProtocol);
            return true;

        } catch (Exception ex) {
            log.error("发送监控单元[{}]配置文件失败:err:", monitorUnitId, ex);
            // 删除账号密码
            monitorUnitExtendService.delete(monitorUnitId);
            // 更新状态为失败
            monitorUnitStateManager.updateMonitorUnitStatus(monitorUnitId, MonitorUnitStateEnum.FAILURE);

            String errorMessage = String.format("监控单元[%d]: %s", monitorUnitId, ex.getMessage());
            failureDetails.add(errorMessage);
            return false;
        }
    }



    // 保存账号密码到数据库
    public void saveAccountPassword(Integer monitorUnitId, String username, String password, String Port, String protocol) {
        // 保存账号密码到数据库
        MonitorUnitExtend monitorUnitExtend = monitorUnitExtendService.findByMonitorUnitId(monitorUnitId);
        // 存在就修改否则新增
        if (monitorUnitExtend != null) {
            // 账号:密码:端口
            monitorUnitExtend.setExtendFiled1(AESUtil.encrypt(username + ":" + password + ":" + Port + ":" + protocol, aesKey));
            monitorUnitExtendService.update(monitorUnitExtend);
        } else {
            monitorUnitExtend = new MonitorUnitExtend();
            monitorUnitExtend.setMonitorUnitId(monitorUnitId);
            monitorUnitExtend.setExtendFiled1(AESUtil.encrypt(username + ":" + password + ":" + Port + ":" + protocol, aesKey));
            monitorUnitExtendService.insert(monitorUnitExtend);
        }
    }

    private static boolean containsInvalidChars(MonitorUnitDTO tslMonitorUnit) {
        if (tslMonitorUnit == null) {
            log.error("监控单元为空");
            return true;
        }
        // 判断是否存在非法字符
        if (containsInvalidChars(tslMonitorUnit.getMonitorUnitName())) {
            log.error("监控单元名称包含非法字符，监控单元名称：{}", tslMonitorUnit.getMonitorUnitName());
            return true;
        }
        return false;
    }

    private static boolean containsInvalidChars(String str) {
        if (StrUtil.isBlank(str)) {
            return false;
        }
        // 检查是否包含XML非法字符
        String invalidChars = "<>&\"'";
        for (char c : invalidChars.toCharArray()) {
            if (str.indexOf(c) != -1) {
                return true;
            }
        }
        return false;
    }

    private static Element createMainCfgElement(MonitorUnitDTO tslMonitorUnit, Station station) {
        Element mainCfg = DocumentHelper.createElement("MainCfg");
        mainCfg.addAttribute("StationId", String.valueOf(tslMonitorUnit.getStationId()));
        mainCfg.addAttribute("StationName", station.getStationName());
        return mainCfg;
    }

    private Element createAppConfigurationElement(MonitorUnitDTO tslMonitorUnit, List<MonitorUnitConfig> monitorUnitConfigs, List<WorkStation> dsWorkStations, List<WorkStation> rdsWorkStations) {
        Element appConfiguration = createAppConfigurationElementBase(tslMonitorUnit);

        // FTP Element with comment
        Element ftpElement = createFtpElement();
        appConfiguration.add(ftpElement);
        appConfiguration.addComment(i18n.T("ftp.username.and.password"));

        // SiteWebCommunication Element with comment
        Element siteWebCommunicationElement = createSiteWebCommunicationElement(monitorUnitConfigs);
        appConfiguration.add(siteWebCommunicationElement);
        appConfiguration.addComment(i18n.T("site.web.communication"));

        // Add other elements
        Element dscConfigurationElement = createDscConfigurationElement(tslMonitorUnit, dsWorkStations);
        appConfiguration.add(dscConfigurationElement);

        Element muNetworkPortConfig = createMuNetworkPortConfigElement();
        appConfiguration.add(muNetworkPortConfig);

        Element modelConfigurationElement = createModelConfigurationElement();
        appConfiguration.add(modelConfigurationElement);

        Element rdsConfigurationElement = createRdsConfigurationElement(tslMonitorUnit, rdsWorkStations);
        appConfiguration.add(rdsConfigurationElement);

        return appConfiguration;
    }

    private Element createFtpElement() {
        Element ftp = DocumentHelper.createElement("ftp");

        Element userId = DocumentHelper.createElement("userId");
        userId.setText("root");

        Element password = DocumentHelper.createElement("password");
        password.setText("hello");

        ftp.add(userId);
        ftp.add(password);

        return ftp;
    }

    private Element createAppConfigurationElementBase(MonitorUnitDTO tslMonitorUnit) {
        Element appConfiguration = DocumentHelper.createElement("AppConfiguration");
        appConfiguration.addAttribute("AppConfigId", String.valueOf(tslMonitorUnit.getAppConfigId()));
        appConfiguration.addAttribute("Name", i18n.T("monitor.xml.app.name"));
        return appConfiguration;
    }

    private Element createSiteWebCommunicationElement(List<MonitorUnitConfig> monitorUnitConfigs) {
        Element siteWebCommunication = DocumentHelper.createElement("siteWebCommunication");

        Element timeOut = DocumentHelper.createElement("timeOut");
        timeOut.setText(String.valueOf(monitorUnitConfigs.get(0).getSiteWebTimeOut()));

        Element retryTimes = DocumentHelper.createElement("retryTimes");
        retryTimes.setText(String.valueOf(monitorUnitConfigs.get(0).getRetryTimes()));

        Element heartBeat = DocumentHelper.createElement("heartBeat");
        heartBeat.setText(String.valueOf(monitorUnitConfigs.get(0).getHeartBeat()));

        siteWebCommunication.add(timeOut);
        siteWebCommunication.add(retryTimes);
        siteWebCommunication.add(heartBeat);

        return siteWebCommunication;
    }

    private Element createDscConfigurationElement(MonitorUnitDTO tslMonitorUnit, List<WorkStation> dsWorkStations) {
        Element dscConfiguration = DocumentHelper.createElement("dscConfiguration");

        if ( !tableExistenceChecker.isTableExists(TableIdentityEnum.TBL_WORKSTATION.getTableName())){
            return dscConfiguration;
        }

        // 默认端口9000
        String dscPort = "9000";

        // 判断是否指定了特定的数据服务器
        if (StrUtil.isNotBlank(tslMonitorUnit.getDataServer())) {
            // 指定ds
            String dsWorkstationIdStr = tslMonitorUnit.getDataServer();
            String[] workstationIds = dsWorkstationIdStr.split(",");

            for (String workstationId : workstationIds) {
                WorkStation workStation = workStationService.findByWorkStationId(Integer.parseInt(workstationId.trim()));
                if (workStation != null) {
                    Element dsIp = DocumentHelper.createElement("DscIp");

                    if (StrUtil.isEmpty(workStation.getIpAddress())) {
                        log.error("工作站IP地址为空，工作站ID：{}", workstationId);
                        continue;
                    }

                    String content;
                    if (StrUtil.contains(workStation.getIpAddress(), ":")) {
                        content = String.format("udp://[%s]:%s", workStation.getIpAddress(), dscPort);
                    } else {
                        content = String.format("udp://%s:%s", workStation.getIpAddress(), dscPort);
                    }

                    dsIp.setText(content);
                    dscConfiguration.add(dsIp);
                }
            }
        } else {
            // 全部ds
            for (WorkStation workStation : dsWorkStations) {
                Element dsIp = DocumentHelper.createElement("DscIp");

                if (StrUtil.isEmpty(workStation.getIpAddress())) {
                    log.error("工作站IP地址为空，工作站ID：{}", workStation.getWorkStationId());
                    continue;
                }

                String content;
                if (StrUtil.contains(workStation.getIpAddress(), ":")) {
                    content = String.format("udp://[%s]:%s", workStation.getIpAddress(), dscPort);
                } else {
                    content = String.format("udp://%s:%s", workStation.getIpAddress(), dscPort);
                }

                dsIp.setText(content);
                dscConfiguration.add(dsIp);
            }
        }

        return dscConfiguration;
    }

    private Element createMuNetworkPortConfigElement() {
        Element muNetworkPortConfig = DocumentHelper.createElement("MUNetworkPortConfig");
        muNetworkPortConfig.addAttribute("name", "MUNetworkPortConfig");
        muNetworkPortConfig.addAttribute("Enable", "false");
        muNetworkPortConfig.addAttribute("Port", "8848");
        return muNetworkPortConfig;
    }

    private static Element createModelConfigurationElement() {
        Element modelConfiguration = DocumentHelper.createElement("ModelConfiguration");
        modelConfiguration.addAttribute("name", "ModelConfiguration");

        Element model = DocumentHelper.createElement("Model");
        model.addAttribute("Enable", "false");
        model.addAttribute("ModelName", "Flex Web Model");
        model.addAttribute("SO_Path", "/data/WebService.so");
        model.addAttribute("CharSet", "2");
        model.addAttribute("Para", "Port=8846 ClientNumb=4 Timeout=60");

        modelConfiguration.add(model);
        return modelConfiguration;
    }

    private Element createRdsConfigurationElement(MonitorUnitDTO tslMonitorUnit, List<WorkStation> rdsWorkStations) {
        Element rdsConfiguration = DocumentHelper.createElement("RDSConfiguration");

        if ( !tableExistenceChecker.isTableExists(TableIdentityEnum.TBL_WORKSTATION.getTableName())){
            return rdsConfiguration;
        }
        // 默认端口7000
        String rdsPort = "7000";

        if (StrUtil.isNotBlank(tslMonitorUnit.getRdsServer())) {
            // 指定rds
            String rdsWorkstationIdStr = tslMonitorUnit.getRdsServer();
            String[] workstationIds = rdsWorkstationIdStr.split(",");

            for (String workstationId : workstationIds) {
                WorkStation workStation = workStationService.findByWorkStationId(Integer.parseInt(workstationId.trim()));
                if (workStation != null) {
                    Element rdsIp = DocumentHelper.createElement("RDSIp");

                    if (StrUtil.isEmpty(workStation.getIpAddress())) {
                        log.error("工作站IP地址为空，工作站ID：{}", workstationId);
                        continue;
                    }

                    String content;
                    if (StrUtil.contains(workStation.getIpAddress(), ":")) {
                        content = String.format("udp://[%s]:%s", workStation.getIpAddress(), rdsPort);
                    } else {
                        content = String.format("udp://%s:%s", workStation.getIpAddress(), rdsPort);
                    }

                    rdsIp.setText(content);
                    rdsConfiguration.add(rdsIp);
                }
            }
        } else {
            for (WorkStation workStation : rdsWorkStations) {
                Element rdsIp = DocumentHelper.createElement("RDSIp");

                if (StrUtil.isEmpty(workStation.getIpAddress())) {
                    log.error("工作站IP地址为空，工作站ID：{}", workStation.getWorkStationId());
                    continue;
                }

                String content;
                if (StrUtil.contains(workStation.getIpAddress(), ":")) {
                    content = String.format("udp://[%s]:%s", workStation.getIpAddress(), rdsPort);
                } else {
                    content = String.format("udp://%s:%s", workStation.getIpAddress(), rdsPort);
                }

                rdsIp.setText(content);
                rdsConfiguration.add(rdsIp);
            }
        }

        // RDSHeartBeat
        Element rdsHeartBeat = DocumentHelper.createElement("RDSHeartBeat");
        rdsHeartBeat.setText("180");
        rdsConfiguration.add(rdsHeartBeat);

        return rdsConfiguration;
    }

    private Element createEquipmentTemplateElement(MonitorUnitDTO tslMonitorUnit) {
        List<EquipmentTemplate> equipmentTemplates = equipmentTemplateService.findByMonitorUnitId(tslMonitorUnit.getMonitorUnitId());
        Set<Integer> monitorUnitCategories = new HashSet<>(Arrays.asList(
                MonitorUnitCategoryEnum.MU_OF_RMU.getValue(),
                MonitorUnitCategoryEnum.ACROSS_MU_OF_RMU.getValue()
        ));
        List<Integer> equipmentTemplateIds = equipmentTemplates.stream()
                // 对于RMU，不下发其Host设备模板
                .filter(tblEquipmentTemplate -> !(monitorUnitCategories.contains(tslMonitorUnit.getMonitorUnitCategory()) && tblEquipmentTemplate.getEquipmentCategory().equals(99)))
                .map(EquipmentTemplate::getEquipmentTemplateId).toList();

        return equipmentTemplateXmlService.exportEquipmentTemplateElement(equipmentTemplateIds);
    }

    private Element createMonitorUnitElement(MonitorUnitDTO tslMonitorUnit) {
        Element monitorUnit = createMonitorUnitBase(tslMonitorUnit);

        appendPortsToMonitorUnit(tslMonitorUnit, monitorUnit);
        appendSamplerUnitsToMonitorUnit(tslMonitorUnit, monitorUnit);
        appendChannelMapsToMonitorUnit(tslMonitorUnit, monitorUnit);

        appendEquipmentsToMonitorUnit(tslMonitorUnit, monitorUnit);
        appendLogActionsToMonitorUnit(tslMonitorUnit, monitorUnit);
        appendBizExpSignalsToMonitorUnit(tslMonitorUnit, monitorUnit);

        return monitorUnit;
    }

    private static Element createMonitorUnitBase(MonitorUnitDTO tslMonitorUnit) {
        Element monitorUnit = DocumentHelper.createElement("MonitorUnit");
        monitorUnit.addAttribute("MonitorUnitId", String.valueOf(tslMonitorUnit.getMonitorUnitId()));
        monitorUnit.addAttribute("MonitorUnitName", tslMonitorUnit.getMonitorUnitName());
        monitorUnit.addAttribute("MonitorUnitCategory", String.valueOf(tslMonitorUnit.getMonitorUnitCategory()));
        monitorUnit.addAttribute("MonitorUnitCode", String.valueOf(tslMonitorUnit.getMonitorUnitCode()));
        monitorUnit.addAttribute("WorkStationId", tslMonitorUnit.getWorkStationId() != null ? String.valueOf(tslMonitorUnit.getWorkStationId()) : "");
        monitorUnit.addAttribute("StationId", String.valueOf(tslMonitorUnit.getStationId()));
        monitorUnit.addAttribute("IpAddress", tslMonitorUnit.getIpAddress() != null ? tslMonitorUnit.getIpAddress() : "");
        monitorUnit.addAttribute("RunMode", String.valueOf(tslMonitorUnit.getRunMode() != null ? tslMonitorUnit.getRunMode() : 0));
        monitorUnit.addAttribute("ConfigFileCode", tslMonitorUnit.getConfigFileCode() != null ? tslMonitorUnit.getConfigFileCode() : "");
        monitorUnit.addAttribute("SampleConfigCode", tslMonitorUnit.getSampleConfigCode() != null ? tslMonitorUnit.getSampleConfigCode() : "");
        monitorUnit.addAttribute("SoftwareVersion", tslMonitorUnit.getSoftwareVersion() != null ? tslMonitorUnit.getSoftwareVersion() : "");
        monitorUnit.addAttribute("Description", tslMonitorUnit.getDescription() != null ? tslMonitorUnit.getDescription() : "");

        return monitorUnit;
    }

    private void appendPortsToMonitorUnit(MonitorUnitDTO tslMonitorUnit, Element monitorUnit) {
        Element ports = DocumentHelper.createElement("Ports");
        ports.addAttribute("Name", i18n.T("port"));

        List<Port> portList = portService.findByMonitorUnitId(tslMonitorUnit.getMonitorUnitId());
        for (Port port : portList) {
            Element portElement = DocumentHelper.createElement("Port");
            portElement.addAttribute("PortId", String.valueOf(port.getPortId()));
            portElement.addAttribute("PortNo", String.valueOf(port.getPortNo()));
            portElement.addAttribute("PortName", port.getPortName());

            // 端口类型转换逻辑
            switch (port.getPortType()) {
                case 3:  // 采集器SNMP端口需要设置为19
                    portElement.addAttribute("PortType", "19");
                    break;
                case 33: // RMU的snmp口
                case 32: // BACNet端口(Linux RMU)
                case 34: // 自诊断端口
                case 35: // IO端口
                    // 这些端口都需要设置为5
                    portElement.addAttribute("PortType", "5");
                    break;
                default:
                    portElement.addAttribute("PortType", String.valueOf(port.getPortType()));
                    break;
            }

            portElement.addAttribute("Setting", port.getSetting() != null ? port.getSetting() : "");
            portElement.addAttribute("PhoneNumber", port.getPhoneNumber() != null ? port.getPhoneNumber() : "");
            portElement.addAttribute("LinkSamplerUnitId", String.valueOf(port.getLinkSamplerUnitId() != null ? port.getLinkSamplerUnitId() : 0));
            portElement.addAttribute("Description", port.getDescription() != null ? port.getDescription() : "");

            ports.add(portElement);
        }

        monitorUnit.add(ports);
    }
    private void appendChannelMapsToMonitorUnit(MonitorUnitDTO tslMonitorUnit, Element monitorUnit) {
        Element channelMaps = DocumentHelper.createElement("ChannelMaps");
        channelMaps.addAttribute("Name", i18n.T("monitor.xml.channel.maps"));
        monitorUnit.add(channelMaps);
    }

    private void appendBizExpSignalsToMonitorUnit(MonitorUnitDTO tslMonitorUnit, Element monitorUnit) {
        Element bizExpSignals = DocumentHelper.createElement("BizExpSignals");
        bizExpSignals.addAttribute("Name", i18n.T("business.expression.configuration"));
        monitorUnit.add(bizExpSignals);
    }

    private void appendLogActionsToMonitorUnit(MonitorUnitDTO tslMonitorUnit, Element monitorUnit) {
        Element logActions = DocumentHelper.createElement("LogActions");
        logActions.addAttribute("Name", i18n.T("alarm.linkage"));
        monitorUnit.add(logActions);
    }
    private void appendSamplerUnitsToMonitorUnit(MonitorUnitDTO tslMonitorUnit, Element monitorUnit) {
        Element samplerUnits = DocumentHelper.createElement("SamplerUnits");
        samplerUnits.addAttribute("Name", i18n.T("sampler.unit"));

        List<SamplerUnit> samplerUnitList = samplerUnitService.selectSamplerUnitWithPort(tslMonitorUnit.getMonitorUnitId());
        for (SamplerUnit samplerUnit : samplerUnitList) {
            Element samplerUnitElement = DocumentHelper.createElement("SamplerUnit");
            samplerUnitElement.addAttribute("SamplerUnitId", String.valueOf(samplerUnit.getSamplerUnitId()));
            samplerUnitElement.addAttribute("PortId", String.valueOf(samplerUnit.getPortId()));
            samplerUnitElement.addAttribute("ParentSamplerUnitId", String.valueOf(samplerUnit.getParentSamplerUnitId() != null ? samplerUnit.getParentSamplerUnitId() : 0));
            samplerUnitElement.addAttribute("SamplerUnitName", samplerUnit.getSamplerUnitName());
            samplerUnitElement.addAttribute("SamplerType", String.valueOf(samplerUnit.getSamplerType() != null ? samplerUnit.getSamplerType() : ""));
            samplerUnitElement.addAttribute("Address", String.valueOf(samplerUnit.getAddress() != null ? samplerUnit.getAddress() : ""));
            samplerUnitElement.addAttribute("SpUnitInterval", String.valueOf(samplerUnit.getSpUnitInterval() != null ? samplerUnit.getSpUnitInterval() : 0));
            samplerUnitElement.addAttribute("SamplerId", String.valueOf(samplerUnit.getSamplerId() != null ? samplerUnit.getSamplerId() : 0));
            samplerUnitElement.addAttribute("DllPath", samplerUnit.getDllPath() != null ? samplerUnit.getDllPath() : "");
            samplerUnitElement.addAttribute("PhoneNumber", samplerUnit.getPhoneNumber() != null ? samplerUnit.getPhoneNumber() : "");
            samplerUnitElement.addAttribute("Description", samplerUnit.getDescription() != null ? samplerUnit.getDescription() : "");

            samplerUnits.add(samplerUnitElement);
        }

        monitorUnit.add(samplerUnits);
    }

    private void appendEquipmentsToMonitorUnit(MonitorUnitDTO tslMonitorUnit, Element monitorUnit) {
        Element equipments = DocumentHelper.createElement("Equipments");
        equipments.addAttribute("Name", i18n.T("equipment"));

        List<Equipment> equipmentList = equipmentService.findByMonitorUnitId(tslMonitorUnit.getMonitorUnitId());

        // 对于RMU类型，不下发Host设备
        Set<Integer> rmuCategories = Set.of(
                MonitorUnitCategoryEnum.MU_OF_RMU.getValue(),
                MonitorUnitCategoryEnum.ACROSS_MU_OF_RMU.getValue()
        );

        for (Equipment equipment : equipmentList) {
            // 对于RMU，不下发其Host设备
            if (rmuCategories.contains(tslMonitorUnit.getMonitorUnitCategory()) &&
                    equipment.getEquipmentCategory() != null && equipment.getEquipmentCategory().equals(99)) {
                continue;
            }

            Element equipmentElement = DocumentHelper.createElement("Equipment");
            equipmentElement.addAttribute("EquipmentId", String.valueOf(equipment.getEquipmentId()));
            equipmentElement.addAttribute("EquipmentName", equipment.getEquipmentName());
            equipmentElement.addAttribute("EquipmentCategory", String.valueOf(equipment.getEquipmentCategory() != null ? equipment.getEquipmentCategory() : 0));
            equipmentElement.addAttribute("EquipmentType", String.valueOf(equipment.getEquipmentType() != null ? equipment.getEquipmentType() : 0));
            equipmentElement.addAttribute("EquipmentClass", String.valueOf(equipment.getEquipmentClass() != null ? equipment.getEquipmentClass() : 0));
            equipmentElement.addAttribute("EquipmentState", String.valueOf(equipment.getEquipmentState() != null ? equipment.getEquipmentState() : 0));
            equipmentElement.addAttribute("EquipmentExpression", equipment.getEventExpression() != null ? equipment.getEventExpression() : "");
            equipmentElement.addAttribute("StartDelay", String.valueOf(equipment.getStartDelay() != null ? equipment.getStartDelay() : 0));
            equipmentElement.addAttribute("EndDelay", String.valueOf(equipment.getEndDelay() != null ? equipment.getEndDelay() : 0));
            equipmentElement.addAttribute("Property", equipment.getProperty() != null ? equipment.getProperty() : "");
            equipmentElement.addAttribute("Description", equipment.getDescription() != null ? equipment.getDescription() : "");
            equipmentElement.addAttribute("EquipmentTemplateId", String.valueOf(equipment.getEquipmentTemplateId() != null ? equipment.getEquipmentTemplateId() : 0));
            equipmentElement.addAttribute("HouseId", String.valueOf(equipment.getHouseId() != null ? equipment.getHouseId() : 0));
            equipmentElement.addAttribute("StationId", String.valueOf(equipment.getStationId() != null ? equipment.getStationId() : 0));
            equipmentElement.addAttribute("MonitorUnitId", String.valueOf(equipment.getMonitorUnitId() != null ? equipment.getMonitorUnitId() : 0));
            equipmentElement.addAttribute("WorkStationId", String.valueOf(equipment.getWorkStationId() != null ? equipment.getWorkStationId() : 0));
            equipmentElement.addAttribute("SamplerUnitId", String.valueOf(equipment.getSamplerUnitId() != null ? equipment.getSamplerUnitId() : 0));
            equipmentElement.addAttribute("InstalledModule", equipment.getInstalledModule() != null ? equipment.getInstalledModule() : "");
            Element muSignals = DocumentHelper.createElement("MUSignals");
            muSignals.addAttribute("Name", i18n.T("equipment.signal"));

            Element muEvents = DocumentHelper.createElement("MUEvents");
            muEvents.addAttribute("Name", i18n.T("equipment.event"));

            Element muControls = DocumentHelper.createElement("MUControls");
            muControls.addAttribute("Name", i18n.T("equipment.control"));

            equipmentElement.add(muSignals);
            equipmentElement.add(muEvents);
            equipmentElement.add(muControls);

            equipments.add(equipmentElement);
        }

        monitorUnit.add(equipments);
    }

    @Override
    public void createMonitorUnitConfigXML(WorkStation workStation, String path) {
        log.info("Creating monitor unit config XML for workstation: {}", workStation.getWorkStationName());

        // 获取该工作站下的所有监控单元
        List<MonitorUnitDTO> monitorUnits = monitorUnitService.findByWorkStationId(workStation.getWorkStationId());

        for (MonitorUnitDTO monitorUnit : monitorUnits) {
            try {
                createMonitorUnitConfigXML(monitorUnit, path);
                log.info("Successfully created config XML for monitor unit: {}", monitorUnit.getMonitorUnitName());
            } catch (Exception e) {
                log.error("Failed to create config XML for monitor unit: {}", monitorUnit.getMonitorUnitName(), e);
            }
        }
    }

    @Override
    public boolean sendMonitorUnitConfigXML(MonitorUnitDTO tslMonitorUnit, String uniqueId, String protocol, Integer port, String username, String password) {
        if (tslMonitorUnit == null) {
            log.error("监控单元为空");
            return false;
        }

        String path = buildMonitorUnitConfigPath(tslMonitorUnit);

        // 准备监控单元配置文件
        File file = prepareFile(tslMonitorUnit, uniqueId);
        if (file == null) {
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, "监控单元配置文件不存在", false);
            FileUtil.appendUtf8String("监控单元配置文件不存在\n", new File(path + "/" + tslMonitorUnit.getMonitorUnitId() + ".log"));
            return false;
        }

        // 使用采集器上传服务进行上传（已集成所有文件上传：配置文件、SO文件、CMB字典）
        try {
            // 一次性上传所有文件（配置文件、SO库文件、cmb_dictionary.xml）
            boolean uploadResult = collectorUploadService.uploadMonitorUnitConfig(
                    tslMonitorUnit, file, protocol, port, username, password, uniqueId);

            // 记录日志
            String logPath = path + "/" + tslMonitorUnit.getMonitorUnitId() + ".log";
            if (uploadResult) {
                String successMsg = String.format("监控单元[%s]所有文件上传成功", tslMonitorUnit.getMonitorUnitName());
                FileUtil.appendUtf8String(successMsg + "\n", new File(logPath));

                // 更新配置文件MD5
                String md5Hex = DigestUtil.md5Hex(file);
                monitorUnitService.updateMonitorUnitConfigFileCode(tslMonitorUnit.getMonitorUnitId(), md5Hex);

                return true;
            } else {
                String failureMsg = String.format("监控单元[%s]文件上传失败", tslMonitorUnit.getMonitorUnitName());
                FileUtil.appendUtf8String(failureMsg + "\n", new File(logPath));
                return false;
            }
        } catch (Exception e) {
            log.error("上传文件失败", e);
            String errorMsg = "上传文件失败: " + e.getMessage();
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
            FileUtil.appendUtf8String(errorMsg + "\n", new File(path + "/" + tslMonitorUnit.getMonitorUnitId() + ".log"));
            return false;
        }
    }

    private File prepareFile(MonitorUnitDTO tslMonitorUnit, String uniqueId) {
        String path = buildMonitorUnitConfigPath(tslMonitorUnit);
        String fileName = "MonitorUnits" + tslMonitorUnit.getMonitorUnitName() + ".xml";
        File file = new File(path + "/" + fileName);

        if (!file.exists()) {
            log.error("监控单元配置文件不存在: {}", file.getAbsolutePath());
            return null;
        }

        return file;
    }





    @Override
    public byte[] zipMonitorUnitConfigXML(List<WorkStation> workStations) {
        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
            // 生成唯一的目录名：RMU_时间戳_随机数
            String uniqueId = String.format("RMU_%d_%d", System.currentTimeMillis(), new java.util.Random().nextInt(10000));
            String rmuRootPath = "upload-dir/monitorUnitConfig/" + uniqueId;

            try (java.util.zip.ZipOutputStream zipOutputStream = new java.util.zip.ZipOutputStream(byteArrayOutputStream)) {
                for (WorkStation workStation : workStations) {
                    String workStationPath = rmuRootPath + "/" + workStation.getWorkStationName();

                    // 为每个工作站创建配置文件
                    createMonitorUnitConfigXML(workStation, workStationPath);

                    // 将工作站目录下的文件添加到ZIP
                    addDirectoryToZip(zipOutputStream, workStationPath, workStation.getWorkStationName());
                }
            }

            // 清理临时文件
            cn.hutool.core.io.FileUtil.del(new java.io.File(rmuRootPath));

            return byteArrayOutputStream.toByteArray();
        } catch (Exception e) {
            log.error("Failed to create ZIP file for workstations", e);
            throw new RuntimeException("创建ZIP文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public byte[] zipMultipleMonitorUnitConfigXML(List<Integer> monitorUnitIds) {
        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
            Map<String, List<java.io.File>> fileMap = new HashMap<>();

            for (Integer monitorUnitId : monitorUnitIds) {
                MonitorUnitDTO monitorUnit = monitorUnitService.findByIdWithoutStation(monitorUnitId);
                if (monitorUnit != null) {
                    String path = buildMonitorUnitConfigPath(monitorUnit);
                    String fileName = "MonitorUnits" + monitorUnit.getMonitorUnitName() + ".xml";
                    java.io.File monitorUnitConfigFile = new java.io.File(path + "/" + fileName);

                    // 获取相关文件，添加进fileMap中
                    java.io.File bytedanceInitListFile = new java.io.File(path + "/" + "bytedance_init_list.ini");
                    if (bytedanceInitListFile.exists()) {
                        fileMap.computeIfAbsent(monitorUnit.getMonitorUnitName(), k -> new ArrayList<>()).add(bytedanceInitListFile);
                    }
                    if (monitorUnitConfigFile.exists()) {
                        fileMap.computeIfAbsent(monitorUnit.getMonitorUnitName(), k -> new ArrayList<>()).add(monitorUnitConfigFile);
                    }
                }
            }

            if (fileMap.isEmpty()) {
                log.error("没有找到任何配置文件");
                throw new RuntimeException("没有找到任何配置文件");
            }

            try (java.util.zip.ZipOutputStream zipOutputStream = new java.util.zip.ZipOutputStream(byteArrayOutputStream)) {
                for (Map.Entry<String, List<java.io.File>> entry : fileMap.entrySet()) {
                    String dirName = entry.getKey(); // 监控单元名称作为目录名
                    List<java.io.File> files = entry.getValue();

                    for (java.io.File file : files) {
                        // 创建带目录的ZIP条目路径
                        String zipEntryPath = dirName + "/" + file.getName();
                        java.util.zip.ZipEntry zipEntry = new java.util.zip.ZipEntry(zipEntryPath);
                        zipOutputStream.putNextEntry(zipEntry);

                        try (java.io.FileInputStream fis = new java.io.FileInputStream(file)) {
                            byte[] buffer = new byte[1024];
                            int length;
                            while ((length = fis.read(buffer)) > 0) {
                                zipOutputStream.write(buffer, 0, length);
                            }
                        }
                        zipOutputStream.closeEntry();
                    }
                }
            }

            return byteArrayOutputStream.toByteArray();
        } catch (Exception e) {
            log.error("Failed to create ZIP file for monitor units: {}", monitorUnitIds, e);
            throw new RuntimeException("创建ZIP文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将单个文件添加到ZIP文件中
     */
    private void addFileToZip(java.util.zip.ZipOutputStream zipOutputStream, java.io.File file, String zipEntryName) throws java.io.IOException {
        java.util.zip.ZipEntry zipEntry = new java.util.zip.ZipEntry(zipEntryName);
        zipOutputStream.putNextEntry(zipEntry);

        try (java.io.FileInputStream fis = new java.io.FileInputStream(file)) {
            byte[] buffer = new byte[1024];
            int length;
            while ((length = fis.read(buffer)) > 0) {
                zipOutputStream.write(buffer, 0, length);
            }
        }
        zipOutputStream.closeEntry();
    }

    /**
     * 将目录添加到ZIP文件中
     */
    private void addDirectoryToZip(java.util.zip.ZipOutputStream zipOutputStream, String sourceDirPath, String zipDirName) throws java.io.IOException {
        java.io.File sourceDir = new java.io.File(sourceDirPath);
        if (!sourceDir.exists() || !sourceDir.isDirectory()) {
            return;
        }

        java.io.File[] files = sourceDir.listFiles();
        if (files == null) {
            return;
        }

        for (java.io.File file : files) {
            if (file.isDirectory()) {
                // 递归处理子目录
                addDirectoryToZip(zipOutputStream, file.getAbsolutePath(), zipDirName + "/" + file.getName());
            } else {
                // 添加文件到ZIP
                String zipEntryPath = zipDirName + "/" + file.getName();
                java.util.zip.ZipEntry zipEntry = new java.util.zip.ZipEntry(zipEntryPath);
                zipOutputStream.putNextEntry(zipEntry);

                try (java.io.FileInputStream fis = new java.io.FileInputStream(file)) {
                    byte[] buffer = new byte[1024];
                    int length;
                    while ((length = fis.read(buffer)) > 0) {
                        zipOutputStream.write(buffer, 0, length);
                    }
                }
                zipOutputStream.closeEntry();
            }
        }
    }

    @Override
    public File downloadRemoteConfigPackage(Integer monitorUnitId, String protocol,
                                            Integer port, String username, String password, String uniqueId) {
        try {
            // 获取监控单元信息
            MonitorUnitDTO monitorUnit = monitorUnitService.findByIdWithoutStation(monitorUnitId);
            if (monitorUnit == null) {
                String errorMsg = String.format("监控单元[%s]不存在", monitorUnitId);
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
                log.error("监控单元不存在，监控单元ID：{}", monitorUnitId);
                return null;
            }

            String startMsg = String.format("开始下载监控单元[%s]的远程配置文件包...", monitorUnit.getMonitorUnitName());
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, startMsg, false);
            log.info("开始下载远程配置文件包: 监控单元ID={}, 协议={}, 端口={}", monitorUnitId, protocol, port);

            // 调用下载服务下载配置文件包
            File configPackage = collectorDownloadService.downloadCollectorConfigPackage(
                    monitorUnit, protocol, port, username, password, uniqueId);

            if (configPackage != null && configPackage.exists()) {
                String successMsg = String.format("监控单元[%s]远程配置文件包下载成功: %s",
                        monitorUnit.getMonitorUnitName(), configPackage.getName());
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, successMsg, false);
                log.info("远程配置文件包下载成功: {}", configPackage.getAbsolutePath());
                return configPackage;
            } else {
                String errorMsg = String.format("监控单元[%s]远程配置文件包下载失败", monitorUnit.getMonitorUnitName());
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
                log.error("远程配置文件包下载失败: 监控单元ID={}", monitorUnitId);
                return null;
            }

        } catch (Exception e) {
            String errorMsg = String.format("下载远程配置文件包异常: %s", e.getMessage());
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
            log.error("下载远程配置文件包异常: 监控单元ID={}", monitorUnitId, e);
            return null;
        }
    }

    @Override
    @Async("monitorUnitTaskExecutor")
    public void createBackupPackageAsync(Integer monitorUnitId, String uniqueId) {
        try {
            log.info("开始创建监控单元[{}]的备份包，任务ID: {}", monitorUnitId, uniqueId);

            // 更新任务状态为运行中
            taskStatusService.updateTaskStatus(uniqueId, TaskStatusEnum.RUNNING, "正在创建备份包...", false);

            // 获取监控单元信息
            MonitorUnitDTO monitorUnit = monitorUnitService.findByIdWithoutStation(monitorUnitId);
            if (monitorUnit == null) {
                String errorMsg = String.format("监控单元[%s]不存在", monitorUnitId);
                taskStatusService.setTaskError(uniqueId, errorMsg);
                log.error("监控单元不存在，监控单元ID：{}", monitorUnitId);
                return;
            }

            // 创建备份目录
            String backupBaseDir = "plugins/south-omc-siteweb/workspace/backup/" + monitorUnitId;
            FileUtil.mkdir(new File(backupBaseDir));

            // 清理旧的备份文件（每次备份覆盖上一次）
            FileUtil.clean(new File(backupBaseDir));

            String startMsg = String.format("开始为监控单元[%s]创建备份包...", monitorUnit.getMonitorUnitName());
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_BACKUP, startMsg, false);

            // 使用统一的文件准备服务
            taskStatusService.updateTaskStatus(uniqueId, TaskStatusEnum.RUNNING, "正在准备文件...", false);
            IConfigFilePreparationService.ConfigFilePreparationResult fileResult =
                    configFilePreparationService.prepareAllFiles(monitorUnit);

            if (!fileResult.isSuccess()) {
                String errorMsg = "文件准备失败: " + fileResult.getMessage();
                taskStatusService.setTaskError(uniqueId, errorMsg);
                log.error("监控单元[{}]文件准备失败: {}", monitorUnitId, fileResult.getMessage());
                return;
            }

            // 1. 处理监控单元配置文件 (XmlCfg目录)
            taskStatusService.updateTaskStatus(uniqueId, TaskStatusEnum.RUNNING, "正在复制监控单元配置文件...", false);
            String xmlCfgDir = backupBaseDir + "/XmlCfg";
            FileUtil.mkdir(new File(xmlCfgDir));

            if (fileResult.getConfigXmlFile() != null) {
                File targetConfigFile = new File(xmlCfgDir + "/" + fileResult.getConfigXmlFile().getName());
                FileUtil.copy(fileResult.getConfigXmlFile(), targetConfigFile, true);
                log.debug("配置文件复制成功: {}", targetConfigFile.getAbsolutePath());
            } else {
                // 如果没有现成的配置文件，则生成一个
                createMonitorUnitConfigXML(monitorUnit, xmlCfgDir);
            }

            // 2. 处理全字典表 (cmbcfg目录)
            taskStatusService.updateTaskStatus(uniqueId, TaskStatusEnum.RUNNING, "正在复制全字典表...", false);
            String cmbCfgDir = backupBaseDir + "/cmbcfg";
            FileUtil.mkdir(new File(cmbCfgDir));

            if (fileResult.getCmbDictionaryFile() != null) {
                File targetCmbFile = new File(cmbCfgDir + "/cmb_dictionary.xml");
                FileUtil.copy(fileResult.getCmbDictionaryFile(), targetCmbFile, true);
                log.debug("CMB字典文件复制成功: {}", targetCmbFile.getAbsolutePath());
            }

            // 3. 处理SO库文件 (SO目录)
            taskStatusService.updateTaskStatus(uniqueId, TaskStatusEnum.RUNNING, "正在复制SO库文件...", false);
            String soDir = backupBaseDir + "/SO";
            FileUtil.mkdir(new File(soDir));

            IConfigFilePreparationService.SoFilePreparationResult soResult = fileResult.getSoFileResult();
            if (soResult != null && soResult.getAvailableFiles() != null) {
                for (IConfigFilePreparationService.SoFileInfo soFileInfo : soResult.getAvailableFiles()) {
                    if (soFileInfo.isExists()) {
                        File targetSoFile = new File(soDir + "/" + soFileInfo.getFileName());
                        FileUtil.copy(soFileInfo.getLocalFile(), targetSoFile, true);
                        log.debug("SO文件复制成功: {}", targetSoFile.getAbsolutePath());
                    }
                }

                if (!soResult.getNotFoundFiles().isEmpty()) {
                    log.warn("监控单元[{}]有{}个SO文件未找到: {}",
                            monitorUnit.getMonitorUnitName(), soResult.getNotFoundFiles().size(), soResult.getNotFoundFiles());
                }
            }

            // 4. 创建ZIP压缩包
            taskStatusService.updateTaskStatus(uniqueId, TaskStatusEnum.RUNNING, "正在创建压缩包...", false);
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String zipFileName = String.format("backup_%d_%s.zip", monitorUnitId, timestamp);
            String zipFilePath = backupBaseDir + "/" + zipFileName;


            // 压缩XmlCfg、cmbcfg、SO目录
            File[] dirsToZip = {
                    new File(xmlCfgDir),
                    new File(cmbCfgDir),
                    new File(soDir)
            };

            // 只压缩存在且非空的目录
            List<File> validDirs = new ArrayList<>();
            for (File dir : dirsToZip) {
                if (dir.exists() && dir.isDirectory() && dir.listFiles() != null && dir.listFiles().length > 0) {
                    validDirs.add(dir);
                }
            }

            if (!validDirs.isEmpty()) {
                // 使用自定义方法压缩，保留目录结构但不包含backupBaseDir路径
                try (java.util.zip.ZipOutputStream zipOutputStream = new java.util.zip.ZipOutputStream(
                        new java.io.FileOutputStream(zipFilePath))) {

                    // 直接压缩XmlCfg、cmbcfg、SO目录，保持目录结构
                    for (File dir : validDirs) {
                        addDirectoryToZip(zipOutputStream, dir.getAbsolutePath(), dir.getName());
                    }
                } catch (java.io.IOException e) {
                    throw new RuntimeException("创建压缩包失败: " + e.getMessage(), e);
                }

                // 清理临时目录，只保留ZIP文件
                FileUtil.del(new File(xmlCfgDir));
                FileUtil.del(new File(cmbCfgDir));
                FileUtil.del(new File(soDir));

                String successMsg = String.format("监控单元[%s]备份包创建完成: %s",
                        monitorUnit.getMonitorUnitName(), zipFileName);
                taskStatusService.completeTask(uniqueId, successMsg);
                log.info("备份包创建完成: {}", zipFilePath);
            } else {
                String errorMsg = "没有找到任何可备份的文件";
                taskStatusService.setTaskError(uniqueId, errorMsg);
                log.warn("监控单元[{}]没有找到任何可备份的文件", monitorUnitId);
            }

        } catch (Exception e) {
            String errorMsg = String.format("创建备份包异常: %s", e.getMessage());
            taskStatusService.setTaskError(uniqueId, errorMsg);
            log.error("创建备份包异常: 监控单元ID={}", monitorUnitId, e);
        }
    }


}
