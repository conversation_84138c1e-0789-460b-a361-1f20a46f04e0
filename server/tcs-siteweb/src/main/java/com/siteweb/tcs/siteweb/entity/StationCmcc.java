package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;

/**
 * 站点实体类 (CMCC)
 */
@Data
@TableName("tbl_stationcmcc")
public class StationCmcc implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 站点ID
     */
    @TableId(value = "StationId", type = IdType.INPUT)
    private Integer stationId;

    @TableField("SiteID")
    private String siteId;

    @TableField("SiteName")
    private String siteName;

    @TableField("Description")
    private String description;
}