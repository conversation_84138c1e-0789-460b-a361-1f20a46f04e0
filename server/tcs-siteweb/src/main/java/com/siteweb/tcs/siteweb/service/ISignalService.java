package com.siteweb.tcs.siteweb.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.dto.SignalConfigItem;
import com.siteweb.tcs.siteweb.dto.SignalFieldCopyDTO;
import com.siteweb.tcs.siteweb.dto.SimplifySignalDTO;
import com.siteweb.tcs.siteweb.dto.excel.SignalExcel;
import com.siteweb.tcs.siteweb.entity.Signal;
import org.springframework.transaction.annotation.Transactional;

/**
 * Signal Service Interface
 */
public interface ISignalService extends IService<Signal> {

    /**
     * Create a new signal
     *
     * @param signalConfigItem Signal configuration item
     * @return Created signal
     */
    Signal createSignal(SignalConfigItem signalConfigItem);

    /**
     * Create communication state signal
     *
     * @param equipmentTemplateId Equipment template ID
     */
    void createCommunicationStateSignal(Integer equipmentTemplateId);

    /**
     * Update an existing signal
     *
     * @param signalConfigItem Signal configuration item
     * @return Updated signal
     */
    Signal updateSignal(SignalConfigItem signalConfigItem);

    /**
     * Delete a signal
     *
     * @param equipmentTemplateId Equipment template ID
     * @param signalId Signal ID
     * @return 1 if deletion was successful, 0 otherwise
     */
    int deleteSignal(Integer equipmentTemplateId, Integer signalId);

    /**
     * Batch delete signals
     *
     * @param equipmentTemplateId Equipment template ID
     * @param signalIds List of signal IDs to delete
     * @return Number of deleted signals
     */
    int batchDeleteSignal(Integer equipmentTemplateId, List<Integer> signalIds);

    /**
     * Find signals by equipment template ID
     *
     * @param equipmentTemplateId Equipment template ID
     * @return List of signals
     */
    List<Signal> findByEquipmentTemplateId(Integer equipmentTemplateId);

    @Transactional(rollbackFor = Exception.class)
    void deleteByEquipmentTemplateId(Integer equipmentTemplateId);

    /**
     * Find a signal by equipment template ID and signal ID
     *
     * @param equipmentTemplateId Equipment template ID
     * @param signalId Signal ID
     * @return SignalConfigItem or null if not found
     */
    SignalConfigItem findByEquipmentTemplateIdAndSignalId(Integer equipmentTemplateId, Integer signalId);

    /**
     * Check if a signal name exists in the equipment template
     *
     * @param equipmentTemplateId Equipment template ID
     * @param signalName Signal name
     * @param signalId Signal ID (null for new signal)
     * @return True if signal name exists, false otherwise
     */
    boolean existsByNameInTemplate(Integer equipmentTemplateId, String signalName, Integer signalId);

    /**
     * Batch save lian tong signals
     *
     * @return true if successful, false otherwise
     */
    boolean batchsaveLianTongSignal();

    /**
     * Update work station signal name with prefix
     *
     * @param workStationName Prefix to add to signal names
     * @param equipmentTemplateId Equipment template ID
     */
    void updateWorkStationSignalName(String workStationName, Integer equipmentTemplateId);

    /**
     * Update work station signal name directly
     *
     * @param signalName New signal name
     * @param equipmentTemplateId Equipment template ID
     * @param workStationId Signal ID (workStationId parameter maps to signal ID)
     */
    void updateDBWorkStationSignalName(String signalName, Integer equipmentTemplateId, Integer workStationId);

    /**
     * Update self diagnosis signal IDs
     *
     * @param equipmentTemplateId Equipment template ID
     * @param centerId Center ID
     */
    void updateSelfDiagnosisSignal(Integer equipmentTemplateId, Integer centerId);

    /**
     * Find maximum signal by equipment template ID
     *
     * @param equipmentTemplateId Equipment template ID
     * @return SignalConfigItem with maximum signal ID
     */
    SignalConfigItem findMaxSignalByEquipmentTemplateId(Integer equipmentTemplateId);

    /**
     * Find base type IDs not in signal base dictionary for equipment template
     *
     * @param equipmentTemplateId Equipment template ID
     * @return List of base type IDs
     */
    List<Long> findBaseTypeIdsNotInSignalBaseDicForEquipmentTemplate(Integer equipmentTemplateId);
    void createSignal(Signal signal);

    Integer findMaxSignalIdByEquipmentTemplateId(Integer equipmentTemplateId);

    void batchInsertSignal(List<Signal> batchSignalList);

    /**
     * 应用信号标准化
     *
     * @param standardId 标准ID
     * @param equipmentTemplateIds 设备模板ID列表
     * @return 应用的数量
     */
    Long applyStandard(Integer standardId, List<Integer> equipmentTemplateIds);

    // ==================== 设备管理相关方法 ====================

    /**
     * 获取简化信号列表
     *
     * @return 简化信号列表
     */
    List<SimplifySignalDTO> findSimplifySignals(Integer equipmentTemplateId);

    @Transactional(rollbackFor = Exception.class)
    void copySignal(Integer originEquipmentTemplateId, Integer destEquipmentTemplateId);

    /**
     * 根据设备模板ID和设备ID获取信号列表
     *
     * @param equipmentTemplateId 设备模板ID
     * @param equipmentId 设备ID
     * @return 信号列表
     */
    List<SignalConfigItem> findItemByEquipmentTemplateIdAndEquipmentId(Integer equipmentTemplateId, Integer equipmentId);

    List<SignalExcel> findExcelDtoByEquipmentTemplateId(Integer equipmentTemplateId);

    /**
     * 信号字段复制
     *
     * @param signalFieldCopyList 信号字段复制列表
     * @return 是否复制成功
     */
    boolean fieldCopySignal(List<SignalFieldCopyDTO> signalFieldCopyList);

    void updateSignalDescriptions(Integer childTemplateId, List<Signal> parentSignals);
}
