package com.siteweb.tcs.siteweb.mapper;

import java.util.List;

import com.siteweb.tcs.siteweb.dto.SignalConfigItem;
import com.siteweb.tcs.siteweb.dto.excel.SignalExcel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.siteweb.entity.Signal;

/**
 * Signal Mapper Interface
 */
@Mapper
@Repository
public interface SignalMapper extends BaseMapper<Signal> {

    /**
     * Find a signal entity by equipment template ID and signal ID
     *
     * @param equipmentTemplateId Equipment template ID
     * @param signalId Signal ID
     * @return Signal entity or null if not found
     */
    Signal findSignalEntityByTemplateIdAndSignalId(@Param("equipmentTemplateId") Integer equipmentTemplateId, 
                                       @Param("signalId") Integer signalId);

    /**
     * Find signals by equipment template ID
     *
     * @param equipmentTemplateId Equipment template ID
     * @return List of signals
     */
    List<Signal> findByTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    /**
     * Batch delete signals
     *
     * @param equipmentTemplateId Equipment template ID
     * @param signalIds List of signal IDs to delete
     * @return Number of deleted signals
     */
    int batchDelete(@Param("equipmentTemplateId") Integer equipmentTemplateId, 
                   @Param("signalIds") List<Integer> signalIds);

    /**
     * Check if a signal name exists in the equipment template
     *
     * @param equipmentTemplateId Equipment template ID
     * @param signalName Signal name
     * @param signalId Signal ID (null for new signal)
     * @return Count of signals with the same name
     */
    int checkSignalNameExists(@Param("equipmentTemplateId") Integer equipmentTemplateId, 
                             @Param("signalName") String signalName, 
                             @Param("signalId") Integer signalId);

    /**
     * Find the maximum signal ID for an equipment template
     *
     * @param equipmentTemplateId Equipment template ID
     * @return Maximum signal ID or null if no signals exist
     */
    Integer findMaxSignalIdByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    /**
     * Update work station signal names with prefix
     *
     * @param prefix Prefix to add to signal names
     * @param equipmentTemplateId Equipment template ID
     */
    void updateWorkStationSignalName(@Param("prefix") String prefix, @Param("equipmentTemplateId") Integer equipmentTemplateId);

    /**
     * Update self diagnosis signal IDs
     *
     * @param equipmentTemplateId Equipment template ID
     * @param centerId Center ID
     */
    void updateSelfDiagnosisSignal(@Param("equipmentTemplateId") Integer equipmentTemplateId, @Param("centerId") Integer centerId);

    /**
     * Find maximum signal configuration item by equipment template ID
     *
     * @param equipmentTemplateId Equipment template ID
     * @return SignalConfigItem with maximum signal ID
     */
    com.siteweb.tcs.siteweb.dto.SignalConfigItem findMaxSignalByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    /**
     * Find base type IDs not in signal base dictionary for equipment template
     *
     * @param equipmentTemplateId Equipment template ID
     * @return List of base type IDs
     */
    List<Long> findBaseTypeIdsNotInSignalBaseDicForEquipmentTemplate(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    /**
     * Find the maximum display index for an equipment template
     *
     * @param equipmentTemplateId Equipment template ID
     * @return Maximum display index or null if no signals exist
     */
    Integer findMaxDisplayIndexByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    List<SignalConfigItem> findSignalItemByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    List<SignalExcel> findExcelDtoByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);

}
