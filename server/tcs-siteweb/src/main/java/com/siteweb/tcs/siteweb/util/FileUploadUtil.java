package com.siteweb.tcs.siteweb.util;

import cn.hutool.core.util.CharsetUtil;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

/**
 * 文件上传工具类
 * 处理不同协议的文件名编码问题
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Slf4j
@UtilityClass
public class FileUploadUtil {

    /**
     * 获取FTP上传时的文件名
     * FTP协议需要将中文文件名从GBK编码转换为ISO-8859-1编码
     * 
     * @param originalFileName 原始文件名
     * @return 编码转换后的文件名
     */
    public static String getFtpFileName(String originalFileName) {
        if (originalFileName == null || originalFileName.isEmpty()) {
            return originalFileName;
        }
        
        try {
            String encodedFileName = CharsetUtil.convert(originalFileName, 
                    CharsetUtil.CHARSET_GBK, CharsetUtil.CHARSET_ISO_8859_1);
            log.debug("FTP文件名编码转换: {} -> {}", originalFileName, encodedFileName);
            return encodedFileName;
        } catch (Exception e) {
            log.warn("FTP文件名编码转换失败，使用原文件名: {}", originalFileName, e);
            return originalFileName;
        }
    }

    /**
     * 获取SFTP上传时的文件名
     * SFTP协议支持UTF-8编码，不需要进行编码转换
     * 
     * @param originalFileName 原始文件名
     * @return 原始文件名（不进行编码转换）
     */
    public static String getSftpFileName(String originalFileName) {
        log.debug("SFTP文件名（无需编码转换）: {}", originalFileName);
        return originalFileName;
    }

    /**
     * 检查文件名是否包含中文字符
     * 
     * @param fileName 文件名
     * @return 是否包含中文字符
     */
    public static boolean containsChinese(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return false;
        }
        
        return fileName.chars().anyMatch(ch -> 
            Character.UnicodeScript.of(ch) == Character.UnicodeScript.HAN);
    }

    /**
     * 获取安全的文件名（移除特殊字符）
     * 
     * @param fileName 原始文件名
     * @return 安全的文件名
     */
    public static String getSafeFileName(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return fileName;
        }
        
        // 移除或替换可能导致问题的特殊字符
        return fileName.replaceAll("[\\\\/:*?\"<>|]", "_");
    }

    /**
     * 根据协议类型获取适当的文件名
     * 
     * @param originalFileName 原始文件名
     * @param protocol 协议类型 (ftp/sftp)
     * @return 适当编码的文件名
     */
    public static String getProtocolFileName(String originalFileName, String protocol) {
        if (originalFileName == null || originalFileName.isEmpty()) {
            return originalFileName;
        }
        
        if ("ftp".equalsIgnoreCase(protocol)) {
            return getFtpFileName(originalFileName);
        } else if ("sftp".equalsIgnoreCase(protocol)) {
            return getSftpFileName(originalFileName);
        } else {
            log.warn("未知协议类型: {}，使用原文件名", protocol);
            return originalFileName;
        }
    }

    /**
     * 记录文件上传信息
     * 
     * @param protocol 协议类型
     * @param originalFileName 原始文件名
     * @param uploadFileName 上传文件名
     * @param remotePath 远程路径
     * @param success 是否成功
     */
    public static void logUploadInfo(String protocol, String originalFileName, 
                                   String uploadFileName, String remotePath, boolean success) {
        String status = success ? "成功" : "失败";
        if (originalFileName.equals(uploadFileName)) {
            log.info("{}文件上传{}: {} -> {}", protocol.toUpperCase(), status, originalFileName, remotePath);
        } else {
            log.info("{}文件上传{}: {} -> {} (编码转换: {})", 
                    protocol.toUpperCase(), status, originalFileName, remotePath, uploadFileName);
        }
    }
}
