package com.siteweb.tcs.siteweb.service;

import com.siteweb.tcs.siteweb.dto.MonitorUnitDTO;

import java.io.File;
import java.util.List;
import java.util.Set;

/**
 * 配置文件准备服务接口
 * 提供下发和备份共同使用的文件准备功能
 *
 * 核心思想：将文件查找/准备逻辑与具体的操作（下发/备份）分离
 * 这样下发和备份可以共享相同的文件查找逻辑，只是在找到文件后的处理方式不同
 */
public interface IConfigFilePreparationService {

    /**
     * 准备监控单元的所有需要的文件信息
     *
     * @param monitorUnit 监控单元信息
     * @return 文件准备结果
     */
    ConfigFilePreparationResult prepareAllFiles(MonitorUnitDTO monitorUnit);

    /**
     * 获取监控单元配置XML文件
     *
     * @param monitorUnit 监控单元信息
     * @return 配置文件，如果不存在返回null
     */
    File getConfigXmlFile(MonitorUnitDTO monitorUnit);

    /**
     * 获取CMB字典文件
     *
     * @return CMB字典文件，如果不存在返回null
     */
    File getCmbDictionaryFile();

    /**
     * 获取监控单元需要的SO文件列表
     *
     * @param monitorUnit 监控单元信息
     * @return SO文件准备结果
     */
    SoFilePreparationResult getSoFiles(MonitorUnitDTO monitorUnit);

    /**
     * 配置文件准备结果
     */
    class ConfigFilePreparationResult {
        private File configXmlFile;
        private File cmbDictionaryFile;
        private SoFilePreparationResult soFileResult;
        private boolean success;
        private String message;

        public ConfigFilePreparationResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        // Getters and Setters
        public File getConfigXmlFile() { return configXmlFile; }
        public void setConfigXmlFile(File configXmlFile) { this.configXmlFile = configXmlFile; }

        public File getCmbDictionaryFile() { return cmbDictionaryFile; }
        public void setCmbDictionaryFile(File cmbDictionaryFile) { this.cmbDictionaryFile = cmbDictionaryFile; }

        public SoFilePreparationResult getSoFileResult() { return soFileResult; }
        public void setSoFileResult(SoFilePreparationResult soFileResult) { this.soFileResult = soFileResult; }

        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }

        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }

    /**
     * SO文件准备结果
     */
    class SoFilePreparationResult {
        private List<SoFileInfo> availableFiles;
        private Set<String> notFoundFiles;
        private boolean success;

        public SoFilePreparationResult(boolean success) {
            this.success = success;
        }

        // Getters and Setters
        public List<SoFileInfo> getAvailableFiles() { return availableFiles; }
        public void setAvailableFiles(List<SoFileInfo> availableFiles) { this.availableFiles = availableFiles; }

        public Set<String> getNotFoundFiles() { return notFoundFiles; }
        public void setNotFoundFiles(Set<String> notFoundFiles) { this.notFoundFiles = notFoundFiles; }

        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
    }

    /**
     * SO文件信息
     */
    class SoFileInfo {
        private String fileName;
        private File localFile;
        private String protocolCode;
        private boolean exists;

        public SoFileInfo(String fileName, File localFile, String protocolCode, boolean exists) {
            this.fileName = fileName;
            this.localFile = localFile;
            this.protocolCode = protocolCode;
            this.exists = exists;
        }

        // Getters and Setters
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }

        public File getLocalFile() { return localFile; }
        public void setLocalFile(File localFile) { this.localFile = localFile; }

        public String getProtocolCode() { return protocolCode; }
        public void setProtocolCode(String protocolCode) { this.protocolCode = protocolCode; }

        public boolean isExists() { return exists; }
        public void setExists(boolean exists) { this.exists = exists; }
    }
}
