package com.siteweb.tcs.siteweb.util;

import com.jcraft.jsch.Channel;
import com.jcraft.jsch.ChannelExec;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.Session;
import lombok.extern.slf4j.Slf4j;

/**
 * 自动关闭的SSH会话包装器
 * 实现AutoCloseable接口，支持try-with-resources语法
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Slf4j
public class AutoCloseableSession implements AutoCloseable {
    
    private final Session session;
    
    public AutoCloseableSession(Session session) {
        this.session = session;
    }
    
    /**
     * 打开执行通道
     */
    public ChannelExec openExecChannel() throws Exception {
        return (ChannelExec) session.openChannel("exec");
    }
    
    /**
     * 打开SFTP通道
     */
    public ChannelSftp openSftpChannel() throws Exception {
        return (ChannelSftp) session.openChannel("sftp");
    }
    
    /**
     * 打开指定类型的通道
     */
    public Channel openChannel(String type) throws Exception {
        return session.openChannel(type);
    }
    
    /**
     * 获取原始Session对象（谨慎使用）
     */
    public Session getSession() {
        return session;
    }
    
    /**
     * 检查会话是否连接
     */
    public boolean isConnected() {
        return session != null && session.isConnected();
    }
    
    /**
     * 获取连接信息
     */
    public String getConnectionInfo() {
        if (session != null) {
            return String.format("%s@%s:%d", session.getUserName(), session.getHost(), session.getPort());
        }
        return "未知连接";
    }
    
    /**
     * 获取主机地址
     */
    public String getHost() {
        return session != null ? session.getHost() : null;
    }
    
    /**
     * 获取端口
     */
    public int getPort() {
        return session != null ? session.getPort() : -1;
    }
    
    /**
     * 获取用户名
     */
    public String getUserName() {
        return session != null ? session.getUserName() : null;
    }
    
    @Override
    public void close() {
        if (session != null && session.isConnected()) {
            try {
                session.disconnect();
                log.debug("SSH会话已断开: {}", getConnectionInfo());
            } catch (Exception e) {
                log.warn("断开SSH会话时发生异常: {}", getConnectionInfo(), e);
            }
        }
    }
}
