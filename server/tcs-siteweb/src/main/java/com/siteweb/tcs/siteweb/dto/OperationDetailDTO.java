package com.siteweb.tcs.siteweb.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.siteweb.tcs.siteweb.serializer.StringToListOfIntegerDeserializer;
import com.siteweb.tcs.siteweb.serializer.FlexibleLocalDateTimeDeserializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class OperationDetailDTO {
    /**
     * 开始时间
     */
    @JsonDeserialize(using = FlexibleLocalDateTimeDeserializer.class)
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @JsonDeserialize(using = FlexibleLocalDateTimeDeserializer.class)
    private LocalDateTime endTime;
    
    /**
     * 操作人id
     */
    private Integer userId;
    
    /**
     * 对象主键id
     */
    private String objectId;
    
    /**
     * 对象类型列表
     */
    @JsonDeserialize(using = StringToListOfIntegerDeserializer.class)
    private List<Integer> objectTypes;
    
    /**
     * 属性名称
     */
    private String propertyName;
    
    /**
     * 操作类型
     */
    private String operationType;
} 