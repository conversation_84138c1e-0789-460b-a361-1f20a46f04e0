package com.siteweb.tcs.siteweb.util;

import com.jcraft.jsch.ChannelExec;
import com.jcraft.jsch.Session;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;

/**
 * SSH命令执行工具类
 * 专门用于执行远程命令，最小化资源使用
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Slf4j
@UtilityClass
public class SshUtil {

    /**
     * 执行远程命令并返回输出
     * 
     * @param session SSH会话
     * @param command 要执行的命令
     * @return 命令输出，失败返回null
     */
    public static String executeCommand(Session session, String command) {
        return executeCommand(session, command, 30000); // 默认30秒超时
    }

    /**
     * 执行远程命令并返回输出
     * 
     * @param session SSH会话
     * @param command 要执行的命令
     * @param timeoutMs 超时时间（毫秒）
     * @return 命令输出，失败返回null
     */
    public static String executeCommand(Session session, String command, int timeoutMs) {
        ChannelExec channelExec = null;
        InputStream in = null;
        
        try {
            channelExec = (ChannelExec) session.openChannel("exec");
            channelExec.setCommand(command);

            // 设置环境变量支持UTF-8编码
            channelExec.setEnv("LC_ALL", "en_US.UTF-8");

            // 获取输出流
            in = channelExec.getInputStream();
            channelExec.setErrStream(System.err);

            channelExec.connect();
            
            // 读取命令输出
            StringBuilder result = new StringBuilder();
            byte[] buffer = new byte[1024];
            long startTime = System.currentTimeMillis();
            
            while (true) {
                // 检查超时
                if (System.currentTimeMillis() - startTime > timeoutMs) {
                    log.warn("SSH命令执行超时: {}", command);
                    break;
                }
                
                // 读取可用数据
                while (in.available() > 0) {
                    int bytesRead = in.read(buffer, 0, 1024);
                    if (bytesRead < 0) {
                        break;
                    }
                    result.append(new String(buffer, 0, bytesRead));
                }
                
                // 检查命令是否完成
                if (channelExec.isClosed()) {
                    // 读取剩余数据
                    if (in.available() > 0) {
                        continue;
                    }
                    break;
                }
                
                Thread.sleep(100);
            }
            
            int exitStatus = channelExec.getExitStatus();
            String output = result.toString().trim();
            
            if (exitStatus == 0) {
                log.debug("SSH命令执行成功: {} -> {}", command, output);
                return output;
            } else {
                log.error("SSH命令执行失败: {} (退出码: {})", command, exitStatus);
                return null;
            }
            
        } catch (Exception e) {
            log.error("SSH命令执行异常: {}", command, e);
            return null;
        } finally {
            if (in != null) {
                try { in.close(); } catch (Exception e) { /* ignore */ }
            }
            if (channelExec != null) {
                channelExec.disconnect();
            }
        }
    }

    /**
     * 获取远程文件的MD5值
     *
     * @param session SSH会话
     * @param remoteFilePath 远程文件路径
     * @return MD5值，失败返回null
     */
    public static String getFileMd5(Session session, String remoteFilePath) {
        String command = String.format("md5sum '%s' 2>/dev/null | cut -d' ' -f1", remoteFilePath);
        String result = executeCommand(session, command);

        if (result != null && result.matches("[a-fA-F0-9]{32}")) {
            log.debug("获取文件MD5成功: {} -> {}", remoteFilePath, result);
            return result.toLowerCase();
        } else {
            log.error("获取文件MD5失败: {} -> {}", remoteFilePath, result);
            return null;
        }
    }

    /**
     * 创建远程目录
     * 
     * @param session SSH会话
     * @param remotePath 远程目录路径
     * @return 创建是否成功
     */
    public static boolean createDirectory(Session session, String remotePath) {
        String command = String.format("mkdir -p '%s'", remotePath);
        String result = executeCommand(session, command);
        
        // mkdir -p 成功时通常没有输出
        boolean success = (result != null);
        if (success) {
            log.debug("创建远程目录成功: {}", remotePath);
        } else {
            log.error("创建远程目录失败: {}", remotePath);
        }
        
        return success;
    }

    /**
     * 检查远程文件是否存在
     * 
     * @param session SSH会话
     * @param remoteFilePath 远程文件路径
     * @return 文件是否存在
     */
    public static boolean fileExists(Session session, String remoteFilePath) {
        String command = String.format("test -f '%s' && echo 'EXISTS' || echo 'NOT_EXISTS'", remoteFilePath);
        String result = executeCommand(session, command);
        
        boolean exists = "EXISTS".equals(result);
        log.debug("检查文件存在性: {} -> {}", remoteFilePath, exists);
        return exists;
    }

    /**
     * 检查远程目录是否存在
     * 
     * @param session SSH会话
     * @param remotePath 远程目录路径
     * @return 目录是否存在
     */
    public static boolean directoryExists(Session session, String remotePath) {
        String command = String.format("test -d '%s' && echo 'EXISTS' || echo 'NOT_EXISTS'", remotePath);
        String result = executeCommand(session, command);
        
        boolean exists = "EXISTS".equals(result);
        log.debug("检查目录存在性: {} -> {}", remotePath, exists);
        return exists;
    }

    /**
     * 获取远程文件大小
     * 
     * @param session SSH会话
     * @param remoteFilePath 远程文件路径
     * @return 文件大小（字节），失败返回-1
     */
    public static long getFileSize(Session session, String remoteFilePath) {
        String command = String.format("stat -c%%s '%s' 2>/dev/null", remoteFilePath);
        String result = executeCommand(session, command);
        
        try {
            if (result != null && !result.isEmpty()) {
                long size = Long.parseLong(result);
                log.debug("获取文件大小: {} -> {}字节", remoteFilePath, size);
                return size;
            }
        } catch (NumberFormatException e) {
            log.error("解析文件大小失败: {} -> {}", remoteFilePath, result);
        }
        
        return -1;
    }

    /**
     * 删除远程文件
     * 
     * @param session SSH会话
     * @param remoteFilePath 远程文件路径
     * @return 删除是否成功
     */
    public static boolean deleteFile(Session session, String remoteFilePath) {
        String command = String.format("rm -f '%s'", remoteFilePath);
        String result = executeCommand(session, command);
        
        boolean success = (result != null);
        if (success) {
            log.debug("删除远程文件成功: {}", remoteFilePath);
        } else {
            log.error("删除远程文件失败: {}", remoteFilePath);
        }
        
        return success;
    }

    /**
     * 获取远程目录下的文件列表
     * 
     * @param session SSH会话
     * @param remotePath 远程目录路径
     * @return 文件列表，每行一个文件名
     */
    public static String listFiles(Session session, String remotePath) {
        String command = String.format("ls -1 '%s' 2>/dev/null", remotePath);
        String result = executeCommand(session, command);
        
        if (result != null) {
            log.debug("列出目录文件: {} -> {}个文件", remotePath, result.split("\n").length);
        } else {
            log.error("列出目录文件失败: {}", remotePath);
        }
        
        return result;
    }

    /**
     * 获取系统信息
     * 
     * @param session SSH会话
     * @return 系统信息
     */
    public static String getSystemInfo(Session session) {
        String command = "uname -a";
        String result = executeCommand(session, command);
        
        if (result != null) {
            log.debug("获取系统信息: {}", result);
        } else {
            log.error("获取系统信息失败");
        }
        
        return result;
    }

    /**
     * 测试SSH连接
     * 
     * @param session SSH会话
     * @return 连接是否正常
     */
    public static boolean testConnection(Session session) {
        String result = executeCommand(session, "echo 'SSH_TEST_OK'");
        boolean success = "SSH_TEST_OK".equals(result);
        
        if (success) {
            log.debug("SSH连接测试成功");
        } else {
            log.error("SSH连接测试失败");
        }
        
        return success;
    }
}
