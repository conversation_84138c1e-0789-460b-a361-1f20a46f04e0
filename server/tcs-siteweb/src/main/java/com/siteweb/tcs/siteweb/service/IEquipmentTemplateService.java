package com.siteweb.tcs.siteweb.service;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.dto.*;
import com.siteweb.tcs.siteweb.entity.EquipmentTemplate;
import com.siteweb.tcs.siteweb.vo.EquipmentTemplateBaseClassVO;
import com.siteweb.tcs.siteweb.vo.EquipmentTemplateVO;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * Equipment Template Service Interface
 */
public interface IEquipmentTemplateService extends IService<EquipmentTemplate> {

    /**
     * 获取B接口设备根模板ID
     * @return B接口设备根模板ID，如果找不到则返回null
     */
    Integer getBInterfaceDeviceTemplateRootId();

    /**
     * 根据设备模板ID删除模板及其关联数据
     * @param equipmentTemplateId 设备模板ID
     * @return 删除成功返回true，否则返回false
     */
    boolean deleteTemplateById(Integer equipmentTemplateId);

    /**
     * 创建设备模板
     * @param equipmentTemplate 设备模板实体
     * @return 创建后的设备模板实体（包含生成的ID）
     */
    EquipmentTemplate createEquipmentTemplate(EquipmentTemplate equipmentTemplate);

    /**
     * 根据查询条件查询设备模板
     * @param equipmentTemplateVO 查询条件VO
     * @return 符合条件的设备模板列表
     */
    List<EquipmentTemplate> queryTemplate(EquipmentTemplateVO equipmentTemplateVO);

    /**
     * 判断是否存在子模板
     * @param equipmentTemplateId 父模板ID
     * @return 存在子模板返回true，否则返回false
     */
    boolean existsChildTemplate(Integer equipmentTemplateId);

    EquipmentTemplate findById(Integer equipmentTemplateId);

    /**
     * 根据分类映射更新设备模板分类
     *
     * @param businessId 业务ID
     * @param categoryTypeId 分类类型ID
     * @return 是否更新成功
     */
    boolean updateEquipmentTemplateCategoryByCategoryIdMap(Integer businessId, Integer categoryTypeId);

    /**
     * 更新设备模板的基础类型为空
     *
     * @return 是否更新成功
     */
    boolean updateEquipmentBaseTypeToNull();

    /**
     * 根据设备模板分类查询设备模板ID
     *
     * @param equipmentTemplateIdDiv 设备模板分类
     * @return 设备模板ID列表
     */
    List<Integer> findEquipmentTemplateIdByEquipmentTemplateIdDiv(Integer equipmentTemplateIdDiv);

    /**
     * 批量保存联通标准的设备模板
     *
     * @return 是否保存成功
     */
    boolean batchsaveLianTongEquipmentTemplate();

    /**
     * 根据设备模板名称查找设备模板
     *
     * @param templateName 设备模板名称
     * @return 设备模板实体
     */
    EquipmentTemplate findByName(String templateName);

    /**
     * 查询所有设备模板VO（用于协议管理页面显示）
     * @return 设备模板VO列表
     */
    List<EquipmentTemplateVO> findVoAll();

    // ==================== 设备管理相关方法 ====================

    /**
     * 获取顶层设备模板信息
     *
     * @param equipmentTemplateId 设备模板ID
     * @return 顶层设备模板
     */
    EquipmentTemplate findTopParent(Integer equipmentTemplateId);

    /**
     * 查询基类设备模板
     *
     * @return 基类设备模板列表
     */
    List<EquipmentTemplateBaseClassVO> findBaseClassAll();

    /**
     * 修改子模板设备类型
     *
     * @param parentId 父模板ID
     * @param category 设备类型
     * @return 影响行数
     */
    int updateChildrenCategory(Integer parentId, Integer category);

    List<EquipmentTemplate> findByProtocolCode(String protocolCode);

    // ==================== 新增设备模板迁移相关方法 ====================

    /**
     * 获取设备模板树结构
     * @param hideDynamicConfigTemplate 是否隐藏动态配置模板
     * @return 设备模板树列表
     */
    List<Tree<Integer>> findTree(Boolean hideDynamicConfigTemplate);

    List<EquipmentTemplateTreeDTO> findTree(Integer equipmentCategory, String protocolCode, String equipmentTemplateName);

    /**
     * 根据设备模板ID查询VO
     * @param equipmentTemplateId 设备模板ID
     * @return 设备模板VO
     */
    EquipmentTemplateVO findVoById(Integer equipmentTemplateId);

    /**
     * 更新设备模板
     * @param equipmentTemplate 设备模板实体
     * @return 是否更新成功
     */
    boolean updateById(EquipmentTemplate equipmentTemplate);

    /**
     * 继承更新设备模板
     * @param equipmentTemplate 设备模板实体
     * @return 是否更新成功
     */
    boolean inheritUpdate(EquipmentTemplate equipmentTemplate);

    boolean doesTemplateNameExist(String templateName);

    /**
     * 复制设备模板
     * @param copyEquipmentTemplateDTO 复制设备模板DTO
     * @return 复制后的设备模板
     */
    int copyTemplate(CopyEquipmentTemplateDTO copyEquipmentTemplateDTO);

    /**
     * 删除设备模板
     * @param equipmentTemplateId 设备模板ID
     * @return 是否删除成功
     */
    boolean deleteById(Integer equipmentTemplateId);

    /**
     * 导出设备模板为XML
     * @param equipmentTemplateId 设备模板ID
     * @return XML字符串
     */
    String exportEquipmentTemplate(Integer equipmentTemplateId);

    /**
     * 导出设备模板为Excel
     * @param response HTTP响应
     * @param equipmentTemplateId 设备模板ID
     */
    void exportExcel(HttpServletResponse response, Integer equipmentTemplateId);

    /**
     * 根据模板ID列表获取DLL路径
     * @param equipmentTemplateIds 模板ID列表
     * @return DLL路径列表
     */
    List<String> findDLlPathByEquipmentTemplateIds(List<Integer> equipmentTemplateIds);

    /**
     * 更新设备类别
     * @param equipmentTemplateId 设备模板ID
     * @param equipmentCategory 设备类别
     * @return 影响行数
     */
    int updateEquipmentCategory(Integer equipmentTemplateId, Integer equipmentCategory);

    /**
     * 清空设备基类类型
     * @param equipmentTemplateId 设备模板ID
     * @return 影响行数
     */
    int clearEquipmentBaseType(Integer equipmentTemplateId);

    /**
     * 自动设置设备基类类型
     * @return 影响行数
     */
    int autoSetEquipmentBaseType();

    /**
     * 升级为根模板
     * @param equipmentTemplateId 设备模板ID
     * @return 是否升级成功
     */
    boolean upgradeToRootTemplate(Integer equipmentTemplateId);

    /**
     * 获取标准设备模板列表
     * @return 标准设备模板列表
     */
    List<StandardEquipmentTemplate> findStandardEquipmentTemplateList();

    /**
     * 检查模板切换影响
     *
     * @param originTemplateId 原模板ID
     * @param destTemplateId 目标模板ID
     * @param equipmentIds 设备ID列表
     * @return 模板变更影响列表
     */
    List<EquipTemplateChangeDTO> changeCompare(Integer originTemplateId, Integer destTemplateId, List<Integer> equipmentIds);

    /**
     * 检查模板切换信号引用
     *
     * @param switchTemplateDTO 切换模板DTO
     * @return 是否有信号引用冲突
     */
    boolean switchTemplateSignalCheck(SwitchTemplateDTO switchTemplateDTO);

    boolean applyStandardizationToChildrenForEquipmentTemplate(Integer parentTemplateId);
}
