package com.siteweb.tcs.siteweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.siteweb.entity.WorkStation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Work Station Mapper
 */
@Mapper
@Repository
public interface WorkStationMapper extends BaseMapper<WorkStation> {

    /**
     * 批量插入工作站
     *
     * @param workStations 工作站列表
     * @return 插入的记录数
     */
    int batchInsert(@Param("workStations") List<WorkStation> workStations);
}
