package com.siteweb.tcs.siteweb.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.siteweb.tcs.siteweb.enums.TableIdentityEnum;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 表存在性检查器
 * 在Spring容器初始化时检查数据库表是否存在，并缓存结果
 */
@Slf4j
@Component
public class TableExistenceChecker {
    
    @Autowired
    private DataSource dataSource;

    public void setDataSource(DataSource dataSource) {
        this.dataSource = dataSource;
        this.initialized = false;
    }
    
    /**
     * 表存在性缓存
     * key: 表名（小写）, value: 是否存在
     */
    private final Map<String, Boolean> tableExistenceCache = new ConcurrentHashMap<>();
    
    /**
     * 检查时间
     */
    private LocalDateTime lastCheckTime;
    
    /**
     * 是否已初始化
     */
    private volatile boolean initialized = false;
    
    /**
     * 检查并缓存表存在性
     */
    public void checkAndCacheTableExistence(DataSource dataSource) {
        log.info("开始检查数据库表存在性");
        this.dataSource = dataSource;
        try {
            // 清空旧缓存
            tableExistenceCache.clear();
            
            getAllExistingTables();

            lastCheckTime = LocalDateTime.now();
            initialized = true;
            log.info("表存在性检查完成，检查了 {} 个关键表", tableExistenceCache.size());
            
        } catch (Exception e) {
            log.error("检查表存在性时发生异常", e);
        }
    }
    
    /**
     * 检查单个表并缓存结果
     */
    private void checkTable(String tableName, Set<String> existingTables) {
        boolean exists = existingTables.contains(tableName.toLowerCase());
        tableExistenceCache.put(tableName.toLowerCase(), exists);
        
        if (exists) {
            log.debug("表 {} 存在", tableName);
        } else {
            log.info("表 {} 不存在", tableName);
        }
    }
    
    /**
     * 检查指定表是否存在
     * 
     * @param tableName 表名（不区分大小写）
     * @return true 如果表存在，false 如果表不存在
     */
    public boolean isTableExists(String tableName) {
        if (!initialized) {
            log.warn("表存在性检查器未初始化，返回false");
            return false;
        }
        
        if (tableName == null || tableName.trim().isEmpty()) {
            return false;
        }
        
        String normalizedTableName = tableName.toLowerCase().trim();
        Boolean exists = tableExistenceCache.get(normalizedTableName);
        
        return exists != null ? exists : false;
    }
    
    /**
     * 检查所有指定的表是否都存在
     * 
     * @param tableNames 表名列表
     * @return true 如果所有表都存在，false 如果任何一个表不存在
     */
    public boolean areAllTablesExist(String... tableNames) {
        if (!initialized) {
            log.warn("表存在性检查器未初始化");
            checkAndCacheTableExistence(this.dataSource);
//            return false;
        }
        
        for (String tableName : tableNames) {
            if (!isTableExists(tableName)) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 检查基类表是否存在
     * 
     * @return true 如果所有基类表都存在，false 如果任何一个基类表不存在
     */
    public boolean areBaseClassTablesExist() {
        return areAllTablesExist(
            TableIdentityEnum.TBL_SIGNAL_BASE_DIC.getTableName(),
                TableIdentityEnum.TBL_EVENT_BASE_DIC.getTableName(),
                TableIdentityEnum.TBL_COMMAND_BASE_DIC.getTableName()
        );
    }
    
    /**
     * 检查station相关表是否存在
     * 
     * @return true 如果station相关表都存在，false 如果任何一个表不存在
     */
    public boolean areStationTablesExist() {
        return areAllTablesExist(
                TableIdentityEnum.TBL_STATION.getTableName()
        );
    }
    
    /**
     * 获取数据库中所有存在的表名
     * 
     * @return 表名集合（小写）
     */
    private void getAllExistingTables() {
        Set<String> tableNames = new HashSet<>();

        try(Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            try (ResultSet tables = metaData.getTables(null, null, "%", new String[]{"TABLE"})) {
                while (tables.next()) {
                    String tableName = tables.getString("TABLE_NAME");
                    if (tableName != null) {
                        tableNames.add(tableName.toLowerCase());
                    }
                }
            }
        } catch (SQLException e) {
            log.error("获取数据库表列表时发生异常", e);
            throw new RuntimeException("Failed to get table list", e);
        }
        tableExistenceCache.putAll(tableNames.stream().collect(Collectors.toMap(tableName -> tableName, tableName -> true)));

    }



    
    /**
     * 获取检查时间
     * 
     * @return 最后检查时间
     */
    public LocalDateTime getLastCheckTime() {
        return lastCheckTime;
    }
    
    /**
     * 是否已初始化
     * 
     * @return true 如果已初始化，false 如果未初始化
     */
    public boolean isInitialized() {
        return initialized;
    }
}
