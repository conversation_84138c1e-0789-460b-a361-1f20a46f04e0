package com.siteweb.tcs.siteweb.service.impl;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.ftp.Ftp;
import cn.hutool.extra.ftp.FtpConfig;
import cn.hutool.extra.ftp.FtpMode;
import cn.hutool.extra.ssh.JschUtil;
import cn.hutool.extra.ssh.Sftp;
import com.jcraft.jsch.*;
import cn.hutool.crypto.digest.DigestUtil;
import com.siteweb.tcs.siteweb.dto.MonitorUnitDTO;
import com.siteweb.tcs.siteweb.entity.Port;
import com.siteweb.tcs.siteweb.entity.Sampler;
import com.siteweb.tcs.siteweb.entity.SamplerUnit;
import com.siteweb.tcs.siteweb.enums.OperationObjectTypeEnum;
import com.siteweb.tcs.siteweb.enums.TaskTypeEnum;
import com.siteweb.tcs.siteweb.service.*;
import com.siteweb.tcs.siteweb.util.*;
import com.siteweb.tcs.common.util.PathUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.net.ftp.FTP;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 采集器下发服务实现
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Slf4j
@Service
public class CollectorUploadServiceImpl implements ICollectorUploadService {

    @Autowired
    private ISamplerUnitService samplerUnitService;

    @Autowired
    private ISamplerService samplerService;

    @Autowired
    private IPortService portService;

    @Autowired
    private ITaskStatusService taskStatusService;

    @Autowired
    private IConfigFilePreparationService configFilePreparationService;

    @Autowired(required = false)
    private IOperationDetailService operationDetailService;

    @Value("${collector.remote.base-path:/home/<USER>")
    private String remoteBasePath;

    @Value("${collector.connection.timeout:10000}")
    private int connectionTimeout;

    @Value("${collector.connection.sftp-timeout:10000}")
    private int sftpConnectionTimeout;

    @Value("${collector.connection.ftp-timeout:10000}")
    private int ftpConnectionTimeout;

    String cmbDictionaryPath = Paths.get("plugins", "south-cmcc-plugin", "workspace", "standard-dic", "cmb_dictionary.xml")
            .toString();


    @Override
    public boolean uploadMonitorUnitConfig(MonitorUnitDTO monitorUnit, File configFile,
                                          String protocol, Integer port, String username, String password,
                                          String uniqueId) {
        try {
            if ("ftp".equalsIgnoreCase(protocol)) {
                return uploadViaFtp(monitorUnit, configFile, port, username, password, uniqueId);
            } else if ("sftp".equalsIgnoreCase(protocol)) {
                return uploadViaSftp(monitorUnit, configFile, port, username, password, uniqueId);
            } else if ("ssh".equalsIgnoreCase(protocol) || "scp".equalsIgnoreCase(protocol)) {
                return uploadMonitorUnitConfigViaScp(monitorUnit, configFile, port, username, password, uniqueId);
            } else {
                return uploadViaSftp(monitorUnit, configFile, port, username, password, uniqueId);
            }
        } catch (Exception e) {
            log.error("下发监控单元配置失败", e);
            String errorMsg = String.format("下发监控单元[%s]配置失败: %s",
                    monitorUnit.getMonitorUnitName(), e.getMessage());
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
            return false;
        }
    }
    

    @Override
    public boolean executeRestartCommand(MonitorUnitDTO monitorUnit, String protocol, Integer port, 
                                        String username, String password, String uniqueId) {
        // TODO: 实现Telnet/SSH重启命令
        log.info("执行采集器重启命令 - 监控单元: {}, 协议: {}", monitorUnit.getMonitorUnitName(), protocol);
        return true;
    }

    @Override
    public boolean batchUploadConfigs(List<MonitorUnitDTO> monitorUnits, String protocol, Integer port, 
                                     String username, String password, String uniqueId) {
        boolean allSuccess = true;
        for (MonitorUnitDTO monitorUnit : monitorUnits) {
            String configPath = String.format("upload-dir/monitorUnitConfig/%d/%s/MonitorUnits%s.xml", 
                    monitorUnit.getMonitorUnitId(), monitorUnit.getMonitorUnitName(), 
                    monitorUnit.getMonitorUnitName());
            File configFile = new File(configPath);
            
            if (!configFile.exists()) {
                log.warn("配置文件不存在: {}", configPath);
                allSuccess = false;
                continue;
            }
            
            boolean result = uploadMonitorUnitConfig(monitorUnit, configFile, protocol, port, 
                    username, password, uniqueId);
            if (!result) {
                allSuccess = false;
            }
        }
        return allSuccess;
    }

    /**
     * 通过FTP下发所有文件（配置文件、SO文件、CMB字典）
     * 优化版本：使用统一的文件准备服务
     */
    private boolean uploadViaFtp(MonitorUnitDTO monitorUnit, File configFile, Integer port,
                                String username, String password, String uniqueId) throws Exception {
        FtpConfig ftpConfig = new FtpConfig();
        ftpConfig.setHost(monitorUnit.getIpAddress());
        ftpConfig.setPort(port);
        ftpConfig.setUser(username);
        ftpConfig.setPassword(password);
        ftpConfig.setCharset(CharsetUtil.CHARSET_GBK);
        ftpConfig.setConnectionTimeout(ftpConnectionTimeout);
        ftpConfig.setSoTimeout(ftpConnectionTimeout);

        try (Ftp ftp = new Ftp(ftpConfig, FtpMode.Passive)) {
            ftp.getClient().setFileType(FTP.BINARY_FILE_TYPE);
            ftp.getClient().setControlEncoding("UTF-8");

            // 使用统一的文件准备服务获取所有需要的文件
            IConfigFilePreparationService.ConfigFilePreparationResult fileResult =
                    configFilePreparationService.prepareAllFiles(monitorUnit);

            if (!fileResult.isSuccess()) {
                String errorMsg = "文件准备失败: " + fileResult.getMessage();
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
                return false;
            }

            boolean allSuccess = true;

            // 1. 下发SO库文件（使用准备好的文件列表）
            boolean soUploadResult = uploadSoFilesViaFtpOptimized(fileResult.getSoFileResult(), ftp, uniqueId);
            if (!soUploadResult) {
                allSuccess = false;
                log.warn("SO文件下发失败，但继续下发其他文件");
            }

            // 2. 下发cmb_dictionary.xml文件（使用准备好的文件）
            boolean cmbUploadResult = uploadCmbDictionaryViaFtpOptimized(fileResult.getCmbDictionaryFile(), ftp, uniqueId);
            if (!cmbUploadResult) {
                allSuccess = false;
                log.warn("CMB字典下发失败，但继续下发配置文件");
            }

            // 3. 下发配置文件
            boolean configUploadResult = uploadConfigFileViaFtp(ftp, configFile, monitorUnit, uniqueId);
            if (!configUploadResult) {
                allSuccess = false;
            }

            return allSuccess;
        }
    }

    /**
     * 通过SFTP下发所有文件（配置文件、SO文件、CMB字典）
     * 优化版本：使用统一的文件准备服务
     */
    private boolean uploadViaSftp(MonitorUnitDTO monitorUnit, File configFile, Integer port,
                                 String username, String password, String uniqueId) throws Exception {
        try (Sftp sftp = JschUtil.createSftp(monitorUnit.getIpAddress(), port, username, password)) {
            // 一次性创建所有需要的目录，避免多次SSH连接
            ensureAllSftpDirectoriesExist(sftp, uniqueId);

            // 使用统一的文件准备服务获取所有需要的文件
            IConfigFilePreparationService.ConfigFilePreparationResult fileResult =
                    configFilePreparationService.prepareAllFiles(monitorUnit);

            if (!fileResult.isSuccess()) {
                String errorMsg = "文件准备失败: " + fileResult.getMessage();
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
                return false;
            }

            boolean allSuccess = true;

            // 1. 下发SO库文件（使用准备好的文件列表）
            boolean soUploadResult = uploadSoFilesViaSftpOptimized(fileResult.getSoFileResult(), sftp, uniqueId);
            if (!soUploadResult) {
                allSuccess = false;
                log.warn("SO文件下发失败，但继续下发其他文件");
            }

            // 2. 下发cmb_dictionary.xml文件（使用准备好的文件）
            boolean cmbUploadResult = uploadCmbDictionaryViaSftpOptimized(fileResult.getCmbDictionaryFile(), sftp, uniqueId);
            if (!cmbUploadResult) {
                allSuccess = false;
                log.warn("CMB字典下发失败，但继续下发配置文件");
            }

            // 3. 下发配置文件
            boolean configUploadResult = uploadConfigFileViaSftp(sftp, configFile, monitorUnit, uniqueId);
            if (!configUploadResult) {
                allSuccess = false;
            }

            return allSuccess;
        }
    }





    /**
     * 通过FTP下发SO库文件的具体实现
     */
    private boolean uploadSoFilesViaFtp(MonitorUnitDTO monitorUnit, Ftp ftp, String uniqueId) {
        try {
            List<SamplerUnit> samplerUnits = samplerUnitService.findByMonitorUnitId(monitorUnit.getMonitorUnitId());
            if (samplerUnits.isEmpty()) {
                return true;
            }

            Set<String> uploadedSoFiles = new HashSet<>();
            Set<String> notFoundSoFiles = new HashSet<>();

            for (SamplerUnit samplerUnit : samplerUnits) {
                Sampler sampler = samplerService.findById(samplerUnit.getSamplerId());
                Port port = portService.findByPortId(samplerUnit.getPortId());

                if (sampler == null || port == null) {
                    continue;
                }

                // 跳过自诊断设备和板载IO设备
                if ("comm_host_dev.so".equals(port.getSetting()) || "comm_io_dev.so".equals(port.getSetting())) {
                    continue;
                }

                uploadSingleSoFileViaFtp(samplerUnit, sampler, ftp, uniqueId, uploadedSoFiles, notFoundSoFiles);
            }
            return true;
        } catch (Exception e) {
            log.error("FTP下发SO文件失败", e);
            String errorMsg = "SO文件下发失败: " + e.getMessage();
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
            return false;
        }
    }

    /**
     * 通过SFTP下发SO库文件的具体实现
     */
    private boolean uploadSoFilesViaSftp(MonitorUnitDTO monitorUnit, Sftp sftp, String uniqueId) {
        try {
            List<SamplerUnit> samplerUnits = samplerUnitService.findByMonitorUnitId(monitorUnit.getMonitorUnitId());
            if (samplerUnits.isEmpty()) {
                return true;
            }

            Set<String> uploadedSoFiles = new HashSet<>();
            Set<String> notFoundSoFiles = new HashSet<>();

            for (SamplerUnit samplerUnit : samplerUnits) {
                Sampler sampler = samplerService.findById(samplerUnit.getSamplerId());
                Port port = portService.findByPortId(samplerUnit.getPortId());

                if (sampler == null || port == null) {
                    continue;
                }

                // 跳过自诊断设备和板载IO设备
                if ("comm_host_dev.so".equals(port.getSetting()) || "comm_io_dev.so".equals(port.getSetting())) {
                    continue;
                }

                uploadSingleSoFileViaSftp(samplerUnit, sampler, sftp, uniqueId, uploadedSoFiles, notFoundSoFiles);
            }
            return true;
        } catch (Exception e) {
            log.error("SFTP下发SO文件失败", e);
            String errorMsg = "SO文件下发失败: " + e.getMessage();
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
            return false;
        }
    }

    /**
     * 通过FTP下发单个SO文件
     */
    private void uploadSingleSoFileViaFtp(SamplerUnit samplerUnit, Sampler sampler, Ftp ftp,
                                         String uniqueId, Set<String> uploadedSoFiles, Set<String> notFoundSoFiles) {
        try {
            String soFileName = sampler.getDllPath();
            if (StrUtil.isBlank(samplerUnit.getDllPath())) {
                return;
            }

            // 确保文件名以.so结尾
            if (!soFileName.endsWith(".so")) {
                soFileName = soFileName.substring(0, soFileName.lastIndexOf(".")) + ".so";
            }

            File soFile = new File(PathUtil.pathJoin("upload-dir", "protocol", sampler.getProtocolCode(), soFileName));

            if (soFile.exists()) {
                // 处理文件名不匹配的情况
                if (!samplerUnit.getDllPath().equals(sampler.getDllPath())) {
                    String soFileNameNew = samplerUnit.getDllPath();
                    if (!soFileNameNew.endsWith(".so")) {
                        soFileNameNew = soFileNameNew.substring(0, soFileNameNew.lastIndexOf(".")) + ".so";
                    }
                    File soFileNew = new File(PathUtil.pathJoin("upload-dir", "protocol", sampler.getProtocolCode(), soFileNameNew));
                    try {
                        FileUtils.copyFile(soFile, soFileNew);
                        soFile = soFileNew;
                    } catch (IOException e) {
                        log.error("SO文件复制失败[filepath={}]", soFile.getAbsolutePath(), e);
                        return;
                    }
                }

                // 检查是否已经下发过相同的SO文件
                if (!uploadedSoFiles.contains(soFile.getName())) {
                    // 确保SO目录存在（远程Unix路径）
                    String soPath = remoteBasePath + "/SO";
                    ensureFtpDirectoryExists(ftp, soPath);

                    String encodedFileName = CharsetUtil.convert(soFile.getName(), CharsetUtil.CHARSET_GBK, CharsetUtil.CHARSET_ISO_8859_1);
                    boolean uploadSo = ftp.upload(soPath + "/", encodedFileName, soFile);

                    if (uploadSo) {
                        String successMsg = String.format("SO文件下发成功[文件名称:%s]", soFile.getName());
                        taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, successMsg, false);
                        log.info("SO文件下发成功[filepath={}]", soFile.getAbsolutePath());
                    } else {
                        String failureMsg = String.format("SO文件下发失败[文件名称:%s]", soFile.getName());
                        log.error("SO文件下发失败[filepath={}]", soFile.getAbsolutePath());
                    }
                    uploadedSoFiles.add(soFile.getName());
                }

                // 删除复制的SO文件
                if (!samplerUnit.getDllPath().equals(sampler.getDllPath())) {
                    boolean deleted = soFile.delete();
                    log.info("删除复制的SO文件[filepath={}, deleted={}]", soFile.getAbsolutePath(), deleted);
                }
            } else {
                // 检查是否已经报告过"未找到"的SO文件
                if (!notFoundSoFiles.contains(soFileName)) {
                    String notFoundMsg = String.format("未找到下发的.so文件[文件名称:%s]", soFileName);
                    taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, notFoundMsg, false);
                    log.error("SO文件不存在[filepath={}]", soFile.getAbsolutePath());
                    notFoundSoFiles.add(soFileName);
                }
            }
        } catch (Exception e) {
            log.error("下发SO文件异常", e);
        }
    }

    /**
     * 通过SFTP下发单个SO文件
     */
    private void uploadSingleSoFileViaSftp(SamplerUnit samplerUnit, Sampler sampler, Sftp sftp,
                                          String uniqueId, Set<String> uploadedSoFiles, Set<String> notFoundSoFiles) {
        try {
            String soFileName = sampler.getDllPath();
            if (StrUtil.isBlank(samplerUnit.getDllPath())) {
                return;
            }

            // 确保文件名以.so结尾
            if (!soFileName.endsWith(".so")) {
                soFileName = soFileName.substring(0, soFileName.lastIndexOf(".")) + ".so";
            }

            File soFile = new File(PathUtil.pathJoin("upload-dir", "protocol", sampler.getProtocolCode(), soFileName));

            if (soFile.exists()) {
                // 处理文件名不匹配的情况
                if (!samplerUnit.getDllPath().equals(sampler.getDllPath())) {
                    String soFileNameNew = samplerUnit.getDllPath();
                    if (!soFileNameNew.endsWith(".so")) {
                        soFileNameNew = soFileNameNew.substring(0, soFileNameNew.lastIndexOf(".")) + ".so";
                    }
                    File soFileNew = new File(PathUtil.pathJoin("upload-dir", "protocol", sampler.getProtocolCode(), soFileNameNew));
                    try {
                        FileUtils.copyFile(soFile, soFileNew);
                        soFile = soFileNew;
                    } catch (IOException e) {
                        log.error("SO文件复制失败[filepath={}]", soFile.getAbsolutePath(), e);
                        return;
                    }
                }

                // 检查是否已经下发过相同的SO文件
                if (!uploadedSoFiles.contains(soFile.getName())) {
                    // SO目录已在uploadViaSftp开始时创建
                    String soPath = remoteBasePath + "/SO";

                    // 下发SO文件 - 使用正确的SFTP upload方法
                    // upload(String destPath, String fileName, InputStream in)
                    boolean uploadSo;
                    try (FileInputStream fis = new FileInputStream(soFile)) {
                        uploadSo = sftp.upload(soPath, soFile.getName(), fis);
                    }

                    if (uploadSo) {
                        String successMsg = String.format("SO文件下发成功[文件名称:%s]", soFile.getName());
                        taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, successMsg, false);
                        log.info("SO文件下发成功[filepath={}]", soFile.getAbsolutePath());
                    } else {
                        String failureMsg = String.format("SO文件下发失败[文件名称:%s]", soFile.getName());
                        log.error("SO文件下发失败[filepath={}]", soFile.getAbsolutePath());
                    }
                    uploadedSoFiles.add(soFile.getName());
                }

                // 删除复制的SO文件
                if (!samplerUnit.getDllPath().equals(sampler.getDllPath())) {
                    boolean deleted = soFile.delete();
                    log.info("删除复制的SO文件[filepath={}, deleted={}]", soFile.getAbsolutePath(), deleted);
                }
            } else {
                // 检查是否已经报告过"未找到"的SO文件
                if (!notFoundSoFiles.contains(soFileName)) {
                    String notFoundMsg = String.format("未找到下发的.so文件[文件名称:%s]", soFileName);
                    taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, notFoundMsg, false);
                    log.error("SO文件不存在[filepath={}]", soFile.getAbsolutePath());
                    notFoundSoFiles.add(soFileName);
                }
            }
        } catch (Exception e) {
            log.error("下发SO文件异常", e);
        }
    }



    /**
     * 通过FTP下发cmb_dictionary.xml文件
     */
    private boolean uploadCmbDictionaryViaFtp(Ftp ftp, String uniqueId) {
        try {
            File cmbDictionaryFile = new File(cmbDictionaryPath);

            if (!cmbDictionaryFile.exists()) {
                log.debug("cmb_dictionary.xml文件不存在，跳过下发: {}", cmbDictionaryPath);
                return true;
            }

            // 确保cmbcfg目录存在（远程Unix路径）
            String cmbcfgPath = remoteBasePath + "/cmbcfg";
            ensureFtpDirectoryExists(ftp, cmbcfgPath);

            // 下发cmb_dictionary.xml文件
            String encodedFileName = CharsetUtil.convert(cmbDictionaryFile.getName(), CharsetUtil.CHARSET_GBK, CharsetUtil.CHARSET_ISO_8859_1);
            boolean uploadSuccess;
            try (FileInputStream fis = new FileInputStream(cmbDictionaryFile)) {
                uploadSuccess = ftp.upload(cmbcfgPath + "/", encodedFileName, fis);
            }

            if (uploadSuccess) {
                String successMsg = "cmb_dictionary.xml文件下发成功";
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, successMsg, false);
                log.info("cmb_dictionary.xml文件下发成功: {}", cmbDictionaryPath);
            } else {
                String failureMsg = "cmb_dictionary.xml文件下发失败";
                log.error("cmb_dictionary.xml文件下发失败: {}", cmbDictionaryPath);
            }

            return uploadSuccess;
        } catch (Exception e) {
            log.error("FTP下发cmb_dictionary.xml文件异常", e);
            String errorMsg = "cmb_dictionary.xml文件下发失败: " + e.getMessage();
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
            return false;
        }
    }



    /**
     * 通过SFTP下发cmb_dictionary.xml文件
     */
    private boolean uploadCmbDictionaryViaSftp(Sftp sftp, String uniqueId) {
        try {
            File cmbDictionaryFile = new File(cmbDictionaryPath);

            if (!cmbDictionaryFile.exists()) {
                log.debug("cmb_dictionary.xml文件不存在，跳过下发: {}", cmbDictionaryPath);
                return true;
            }

            // cmbcfg目录已在uploadViaSftp开始时创建
            String cmbcfgPath = remoteBasePath + "/cmbcfg";

            // 下发cmb_dictionary.xml文件
            boolean uploadSuccess;
            try (FileInputStream fis = new FileInputStream(cmbDictionaryFile)) {
                uploadSuccess = sftp.upload(cmbcfgPath, cmbDictionaryFile.getName(), fis);
            }
            catch (Exception e) {
                log.error("SFTP下发cmb_dictionary.xml文件异常", e);
                return false;
            }

            if (uploadSuccess) {
                String successMsg = "cmb_dictionary.xml文件下发成功";
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, successMsg, false);
                log.info("cmb_dictionary.xml文件下发成功: {}", cmbDictionaryPath);
            } else {
                String failureMsg = "cmb_dictionary.xml文件下发失败";
                log.error("cmb_dictionary.xml文件下发失败: {}", cmbDictionaryPath);
            }

            return uploadSuccess;
        } catch (Exception e) {
            log.error("SFTP下发cmb_dictionary.xml文件异常", e);
            String errorMsg = "cmb_dictionary.xml文件下发失败: " + e.getMessage();
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
            return false;
        }
    }

    /**
     * 确保FTP目录存在
     */
    private void ensureFtpDirectoryExists(Ftp ftp, String directoryPath) {
        try {
            if (!ftp.exist(directoryPath)) {
                ftp.mkDirs(directoryPath);
                log.info("FTP创建目录成功: {}", directoryPath);
            }
        } catch (Exception e) {
            log.warn("FTP检查或创建目录失败，继续尝试下发: {} - {}", directoryPath, e.getMessage());
        }
    }

    /**
     * 一次性确保SFTP所有需要的目录存在
     * 使用SSH命令批量创建目录，避免多次SSH连接，解决SFTP权限问题
     */
    private void ensureAllSftpDirectoriesExist(Sftp sftp, String uniqueId) {
        try {
           if(! sftp.isDir(remoteBasePath + "/XmlCfg")){
               sftp.mkDirs(remoteBasePath + "/XmlCfg");
           }
           if(! sftp.isDir(remoteBasePath + "/SO")){
               sftp.mkDirs(remoteBasePath + "/SO");
           }
           if(! sftp.isDir(remoteBasePath + "/cmbcfg")){
               sftp.mkDirs(remoteBasePath + "/cmbcfg");
           }
        } catch (Exception e) {
            log.warn("SFTP批量目录创建异常，继续尝试文件上传: {}", e.getMessage());
            String warnMsg = String.format("远程目录创建异常，但继续尝试文件上传: %s", e.getMessage());
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, warnMsg, false);
        }
    }


    @Override
    public boolean uploadMonitorUnitConfigViaScp(MonitorUnitDTO monitorUnit, File configFile,
                                                 Integer port, String username, String password, String uniqueId) {
        try (AutoCloseableSession session = createAutoCloseableSession(monitorUnit.getIpAddress(), port != null ? port : 22, username, password)) {

            // 使用统一的文件准备服务获取所有需要的文件
            IConfigFilePreparationService.ConfigFilePreparationResult fileResult =
                    configFilePreparationService.prepareAllFiles(monitorUnit);

            if (!fileResult.isSuccess()) {
                String errorMsg = "文件准备失败: " + fileResult.getMessage();
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
                return false;
            }

            boolean allSuccess = true;

            // 1. 下发SO文件（使用准备好的文件列表）
            boolean soUploadResult = uploadSoFilesViaScpOptimized(fileResult.getSoFileResult(), session, uniqueId);
            if (!soUploadResult) {
                allSuccess = false;
                log.warn("SO文件下发失败，但继续下发配置文件");
            }

            // 2. 下发CMB字典文件（使用准备好的文件）
            boolean cmbUploadResult = uploadCmbDictionaryViaScpOptimized(fileResult.getCmbDictionaryFile(), session, uniqueId);
            if (!cmbUploadResult) {
                allSuccess = false;
                log.warn("CMB字典下发失败，但继续下发配置文件");
            }

            // 3. 下发配置文件
            boolean configUploadResult = uploadConfigFileViaScp(session, configFile, monitorUnit, uniqueId);
            if (!configUploadResult) {
                allSuccess = false;
            }

            return allSuccess;

        } catch (Exception e) {
            log.error("SSH+SCP下发失败", e);
            String errorMsg = String.format("SSH+SCP下发监控单元[%s]失败: %s",
                    monitorUnit.getMonitorUnitName(), e.getMessage());
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
            return false;
        }
    }



    /**
     * 通过SSH+SCP下发单个SO文件
     */
    private boolean uploadSingleSoFileViaScp(SamplerUnit samplerUnit, Sampler sampler, Session session,
                                            String soPath, String uniqueId, Set<String> uploadedSoFiles, Set<String> notFoundSoFiles) {
        try {
            String soFileName = sampler.getDllPath();
            if (StrUtil.isBlank(samplerUnit.getDllPath())) {
                return true;
            }

            // 确保文件名以.so结尾
            if (!soFileName.endsWith(".so")) {
                soFileName = soFileName.substring(0, soFileName.lastIndexOf(".")) + ".so";
            }

            File soFile = new File(PathUtil.pathJoin("upload-dir", "protocol", sampler.getProtocolCode(), soFileName));

            if (soFile.exists()) {
                // 处理文件名不匹配的情况
                if (!samplerUnit.getDllPath().equals(sampler.getDllPath())) {
                    String soFileNameNew = samplerUnit.getDllPath();
                    if (!soFileNameNew.endsWith(".so")) {
                        soFileNameNew = soFileNameNew.substring(0, soFileNameNew.lastIndexOf(".")) + ".so";
                    }
                    File soFileNew = new File(PathUtil.pathJoin("upload-dir", "protocol", sampler.getProtocolCode(), soFileNameNew));
                    try {
                        FileUtils.copyFile(soFile, soFileNew);
                        soFile = soFileNew;
                    } catch (IOException e) {
                        log.error("SO文件复制失败[filepath={}]", soFile.getAbsolutePath(), e);
                        return false;
                    }
                }

                // 检查是否已经下发过相同的SO文件
                if (!uploadedSoFiles.contains(soFile.getName())) {
                    // 计算本地文件MD5
                    String localMd5 = DigestUtil.md5Hex(soFile);

                    // 下发SO文件（远程Unix路径）
                    String remoteFilePath = soPath + "/" + soFile.getName();
                    boolean uploadResult = uploadFileViaScp(session, soFile, remoteFilePath, uniqueId);

                    if (uploadResult) {
                        // 验证下发后的文件MD5
                        String remoteMd5 = getRemoteFileMd5(session, remoteFilePath, uniqueId);
                        if (remoteMd5 != null && localMd5.equals(remoteMd5)) {
                            String successMsg = String.format("SO文件下发成功，MD5验证通过[文件名称:%s]", soFile.getName());
                            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, successMsg, false);
                            log.info("SO文件下发成功，MD5验证通过[filepath={}]", soFile.getAbsolutePath());
                        } else {
                            String errorMsg = String.format("SO文件MD5验证失败[文件名称:%s] - 本地:%s, 远程:%s",
                                    soFile.getName(), localMd5, remoteMd5);
                            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
                            log.error("SO文件MD5验证失败[filepath={}] - 本地:{}, 远程:{}", soFile.getAbsolutePath(), localMd5, remoteMd5);
                            return false;
                        }
                    } else {
                        String failureMsg = String.format("SO文件下发失败[文件名称:%s]", soFile.getName());
                        taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, failureMsg, false);
                        log.error("SO文件下发失败[filepath={}]", soFile.getAbsolutePath());
                        return false;
                    }
                    uploadedSoFiles.add(soFile.getName());
                }

                // 删除复制的SO文件
                if (!samplerUnit.getDllPath().equals(sampler.getDllPath())) {
                    boolean deleted = soFile.delete();
                    log.info("删除复制的SO文件[filepath={}, deleted={}]", soFile.getAbsolutePath(), deleted);
                }

                return true;
            } else {
                // 检查是否已经报告过"未找到"的SO文件
                if (!notFoundSoFiles.contains(soFileName)) {
                    String notFoundMsg = String.format("未找到下发的.so文件[文件名称:%s]", soFileName);
                    taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, notFoundMsg, false);
                    log.error("SO文件不存在[filepath={}]", soFile.getAbsolutePath());
                    notFoundSoFiles.add(soFileName);
                }
                return false;
            }
        } catch (Exception e) {
            log.error("SSH+SCP下发SO文件异常", e);
            return false;
        }
    }



    /**
     * 确保远程目录存在
     */
    private void ensureRemoteDirectoryExists(Session session, String remotePath, String uniqueId) throws Exception {
        // 首先检查目录是否存在
        if (SshUtil.directoryExists(session, remotePath)) {
            log.debug("远程目录已存在: {}", remotePath);
            return;
        }

        // 创建目录
        boolean success = SshUtil.createDirectory(session, remotePath);
        if (!success) {
            // 如果创建失败，尝试创建父目录
            String parentPath = getParentPath(remotePath);
            if (parentPath != null && !parentPath.equals(remotePath)) {
                log.info("尝试创建父目录: {}", parentPath);
                ensureRemoteDirectoryExists(session, parentPath, uniqueId);
                // 再次尝试创建目标目录
                success = SshUtil.createDirectory(session, remotePath);
            }
        }

        if (!success) {
            throw new Exception("创建远程目录失败: " + remotePath);
        }

        log.info("远程目录创建成功: {}", remotePath);
    }

    /**
     * 获取父目录路径
     */
    private String getParentPath(String path) {
        if (path == null || path.isEmpty()) {
            return null;
        }

        // 标准化路径分隔符
        String normalizedPath = path.replace("\\", "/");

        // 移除末尾的分隔符
        if (normalizedPath.endsWith("/")) {
            normalizedPath = normalizedPath.substring(0, normalizedPath.length() - 1);
        }

        // 查找最后一个分隔符
        int lastSeparator = normalizedPath.lastIndexOf('/');
        if (lastSeparator > 0) {
            return normalizedPath.substring(0, lastSeparator);
        }

        return null;
    }

    /**
     * 通过纯SCP协议下发文件
     */
    private boolean uploadFileViaScp(Session session, File localFile, String remoteFilePath, String uniqueId) {
        return ScpUtil.uploadFile(session, localFile, remoteFilePath);
    }

    /**
     * 获取远程文件的MD5值
     */
    private String getRemoteFileMd5(Session session, String remoteFilePath, String uniqueId) {
        String md5 = SshUtil.getFileMd5(session, remoteFilePath);
        if (md5 == null) {
            String errorMsg = String.format("获取远程文件MD5失败: %s", remoteFilePath);
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
        } else {
            log.info("远程文件MD5: {} - {}", remoteFilePath, md5);
        }
        return md5;
    }


    /**
     * 创建自动关闭的SSH会话
     */
    private AutoCloseableSession createAutoCloseableSession(String host, int port, String username, String password) throws Exception {
        try {
            JSch jsch = new JSch();
            Session session = jsch.getSession(username, host, port);
            session.setPassword(password);
            session.setConfig("StrictHostKeyChecking", "no");

            // 设置SSH会话支持UTF-8编码
            session.setConfig("compression.s2c", "none");
            session.setConfig("compression.c2s", "none");

            session.setTimeout(sftpConnectionTimeout);
            session.connect(sftpConnectionTimeout);
            log.info("SSH连接创建成功: {}:{}", host, port);
            return new AutoCloseableSession(session);
        } catch (Exception e) {
            log.error("SSH连接创建失败: {}:{}", host, port, e);
            throw e;
        }
    }

    /**
     * 处理文件名编码（支持中文文件名）
     * 对于SCP协议，提供多种策略处理中文文件名
     */
    private String processFileName(String originalFileName) {
        if (originalFileName == null || originalFileName.isEmpty()) {
            return "config_" + System.currentTimeMillis() + ".xml";
        }

        // 现代Unix系统支持UTF-8编码的中文文件名，直接使用原文件名
        // 只需要处理一些可能导致shell问题的特殊字符
        String processedFileName = originalFileName
                .replace("'", "\\'")  // 转义单引号
                .replace("\"", "\\\"") // 转义双引号
                .replace("`", "\\`")   // 转义反引号
                .replace("$", "\\$");  // 转义美元符号

        log.debug("文件名处理: {} -> {}", originalFileName, processedFileName);
        return processedFileName;
    }

    /**
     * 使用已有session下发配置文件
     */
    private boolean uploadConfigFileViaScp(AutoCloseableSession session, File configFile,
                                          MonitorUnitDTO monitorUnit, String uniqueId) {
        try {
            // 计算本地文件MD5
            String localMd5 = DigestUtil.md5Hex(configFile);
            log.info("本地配置文件MD5: {} - {}", configFile.getName(), localMd5);

            // 确保远程目录存在（远程Unix路径）
            String xmlCfgPath = remoteBasePath + "/XmlCfg";
            ensureRemoteDirectoryExists(session.getSession(), xmlCfgPath, uniqueId);

            // 下发配置文件 - 支持中文文件名
            String processedFileName = processFileName(configFile.getName());
            String remoteFilePath = xmlCfgPath + "/" + processedFileName;
            boolean uploadResult = uploadFileViaScp(session.getSession(), configFile, remoteFilePath, uniqueId);

            if (!uploadResult) {
                return false;
            }

            // 验证下发后的文件MD5
            String remoteMd5 = getRemoteFileMd5(session.getSession(), remoteFilePath, uniqueId);
            if (remoteMd5 != null && localMd5.equals(remoteMd5)) {
                String successMsg = String.format("监控单元[%s]配置文件下发成功，MD5验证通过", monitorUnit.getMonitorUnitName());
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, successMsg, false);
                log.info("配置文件下发成功，MD5验证通过: {} -> {}", configFile.getName(), remoteFilePath);

                // 记录操作日志
                if (operationDetailService != null) {
                    try {
                        operationDetailService.recordOperationLog(
                                -1, // 系统操作
                                monitorUnit.getMonitorUnitId().toString(),
                                OperationObjectTypeEnum.MONITOR_UNIT,
                                "配置下发",
                                "下发",
                                "",
                                "SCP配置文件下发成功，MD5验证通过"
                        );
                    } catch (Exception e) {
                        log.warn("记录配置下发操作日志失败", e);
                    }
                }

                // 检查并解释中文文件名问题
                String explanation = ScpUtil.explainChineseFileNameIssue(configFile.getName(), remoteMd5, localMd5);
                if (explanation != null) {
                    taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, explanation, false);
                    log.info("中文文件名说明: {}", explanation);
                }

                return true;
            } else {
                String errorMsg = String.format("监控单元[%s]配置文件MD5验证失败 - 本地:%s, 远程:%s",
                        monitorUnit.getMonitorUnitName(), localMd5, remoteMd5);
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
                log.error("配置文件MD5验证失败: {} - 本地:{}, 远程:{}", configFile.getName(), localMd5, remoteMd5);
                return false;
            }

        } catch (Exception e) {
            log.error("下发配置文件失败", e);
            return false;
        }
    }

    /**
     * 使用已有session下发SO文件
     */
    private boolean uploadSoFilesViaScp(AutoCloseableSession session, MonitorUnitDTO monitorUnit, String uniqueId) {
        try {
            // 确保SO目录存在（远程Unix路径）
            String soPath = remoteBasePath + "/SO";
            ensureRemoteDirectoryExists(session.getSession(), soPath, uniqueId);

            // 获取需要下发的SO文件
            List<SamplerUnit> samplerUnits = samplerUnitService.findByMonitorUnitId(monitorUnit.getMonitorUnitId());
            if (samplerUnits.isEmpty()) {
                return true;
            }

            Set<String> uploadedSoFiles = new HashSet<>();
            Set<String> notFoundSoFiles = new HashSet<>();
            boolean allSuccess = true;

            for (SamplerUnit samplerUnit : samplerUnits) {
                Sampler sampler = samplerService.findById(samplerUnit.getSamplerId());
                Port portEntity = portService.findByPortId(samplerUnit.getPortId());

                if (sampler == null || portEntity == null) {
                    continue;
                }

                // 跳过自诊断设备和板载IO设备
                if ("comm_host_dev.so".equals(portEntity.getSetting()) || "comm_io_dev.so".equals(portEntity.getSetting())) {
                    continue;
                }

                boolean result = uploadSingleSoFileViaScp(samplerUnit, sampler, session.getSession(), soPath, uniqueId, uploadedSoFiles, notFoundSoFiles);
                if (!result) {
                    allSuccess = false;
                }
            }

            return allSuccess;

        } catch (Exception e) {
            log.error("下发SO文件失败", e);
            return false;
        }
    }

    /**
     * 使用已有session下发CMB字典文件
     */
    private boolean uploadCmbDictionaryViaScp(AutoCloseableSession session, String uniqueId) {
        try {
            File cmbDictionaryFile = new File(cmbDictionaryPath);

            if (!cmbDictionaryFile.exists()) {
                log.warn("CMB字典文件不存在: {}", cmbDictionaryPath);
                return true; // 不存在时不算失败
            }

            // 确保cmbcfg目录存在（远程Unix路径）
            String cmbcfgPath = remoteBasePath + "/cmbcfg";
            ensureRemoteDirectoryExists(session.getSession(), cmbcfgPath, uniqueId);

            // 计算本地文件MD5
            String localMd5 = DigestUtil.md5Hex(cmbDictionaryFile);
            log.info("本地CMB字典文件MD5: {} - {}", cmbDictionaryFile.getName(), localMd5);

            // 下发CMB字典文件
            String remoteFilePath = cmbcfgPath + "/" + cmbDictionaryFile.getName();
            boolean uploadResult = uploadFileViaScp(session.getSession(), cmbDictionaryFile, remoteFilePath, uniqueId);

            if (!uploadResult) {
                return false;
            }

            // 验证下发后的文件MD5
            String remoteMd5 = getRemoteFileMd5(session.getSession(), remoteFilePath, uniqueId);
            if (remoteMd5 != null && localMd5.equals(remoteMd5)) {
                String successMsg = "CMB字典文件下发成功，MD5验证通过";
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, successMsg, false);
                log.info("CMB字典文件下发成功，MD5验证通过: {} -> {}", cmbDictionaryFile.getName(), remoteFilePath);
                return true;
            } else {
                String errorMsg = String.format("CMB字典文件MD5验证失败 - 本地:%s, 远程:%s", localMd5, remoteMd5);
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
                log.error("CMB字典文件MD5验证失败: {} - 本地:{}, 远程:{}", cmbDictionaryFile.getName(), localMd5, remoteMd5);
                return false;
            }

        } catch (Exception e) {
            log.error("下发CMB字典文件失败", e);
            return false;
        }
    }

    /**
     * 使用已有FTP连接下发配置文件
     */
    private boolean uploadConfigFileViaFtp(Ftp ftp, File configFile, MonitorUnitDTO monitorUnit, String uniqueId) {
        try {
            // 确保XmlCfg目录存在（远程Unix路径）
            String xmlCfgPath = remoteBasePath + "/XmlCfg";
            ensureFtpDirectoryExists(ftp, xmlCfgPath);

            String uploadFileName = configFile.getName();
            String remotePath = xmlCfgPath + "/";
            boolean upload;
            try (FileInputStream fis = new FileInputStream(configFile)) {
                upload = ftp.upload(remotePath, uploadFileName, fis);
            }

            // 记录下发结果
            FileUploadUtil.logUploadInfo("ftp", configFile.getName(), uploadFileName, remotePath, upload);

            if (upload) {
                String successMsg = String.format("监控单元[%s]配置文件下发成功", monitorUnit.getMonitorUnitName());
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, successMsg, false);
                log.info("FTP配置文件下发成功: {}", configFile.getName());

                // 记录操作日志
                if (operationDetailService != null) {
                    try {
                        operationDetailService.recordOperationLog(
                                -1, // 系统操作
                                monitorUnit.getMonitorUnitId().toString(),
                                OperationObjectTypeEnum.MONITOR_UNIT,
                                "配置下发",
                                "下发",
                                "",
                                "FTP配置文件下发成功"
                        );
                    } catch (Exception e) {
                        log.warn("记录配置下发操作日志失败", e);
                    }
                }
            } else {
                String failureMsg = String.format("监控单元[%s]配置文件下发失败", monitorUnit.getMonitorUnitName());
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, failureMsg, false);
                log.error("FTP配置文件下发失败: {}", configFile.getName());
            }

            return upload;

        } catch (Exception e) {
            log.error("FTP下发配置文件失败", e);
            return false;
        }
    }

    /**
     * 使用已有SFTP连接下发配置文件
     */
    private boolean uploadConfigFileViaSftp(Sftp sftp, File configFile, MonitorUnitDTO monitorUnit, String uniqueId) {
        try {
            // XmlCfg目录已在uploadViaSftp开始时创建
            String xmlCfgPath = remoteBasePath + "/XmlCfg";

            // 下发配置文件 - SFTP支持UTF-8，不需要编码转换
            String uploadFileName = configFile.getName();// 这里URLEncoder.encode可以防止中文乱码fileName = URLEncodeUtil.encode(fileName);
            boolean upload;
            try (FileInputStream fis = new FileInputStream(configFile)) {
                upload = sftp.upload(xmlCfgPath, uploadFileName, fis);
            }

            // 记录下发结果
            FileUploadUtil.logUploadInfo("sftp", configFile.getName(), uploadFileName, xmlCfgPath, upload);

            if (upload) {
                String successMsg = String.format("监控单元[%s]配置文件下发成功", monitorUnit.getMonitorUnitName());
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, successMsg, false);
                log.info("SFTP配置文件下发成功: {}", configFile.getName());

                // 记录操作日志
                if (operationDetailService != null) {
                    try {
                        operationDetailService.recordOperationLog(
                                -1, // 系统操作
                                monitorUnit.getMonitorUnitId().toString(),
                                OperationObjectTypeEnum.MONITOR_UNIT,
                                "配置下发",
                                "下发",
                                "",
                                "SFTP配置文件下发成功"
                        );
                    } catch (Exception e) {
                        log.warn("记录配置下发操作日志失败", e);
                    }
                }
            } else {
                String failureMsg = String.format("监控单元[%s]配置文件下发失败", monitorUnit.getMonitorUnitName());
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, failureMsg, false);
                log.error("SFTP配置文件下发失败: {}", configFile.getName());
            }

            return upload;

        } catch (Exception e) {
            log.error("SFTP下发配置文件失败", e);
            return false;
        }
    }

    /**
     * 优化版本：使用准备好的SO文件列表进行SFTP上传
     */
    private boolean uploadSoFilesViaSftpOptimized(IConfigFilePreparationService.SoFilePreparationResult soResult,
                                                 Sftp sftp, String uniqueId) {
        try {
            if (soResult == null || soResult.getAvailableFiles() == null || soResult.getAvailableFiles().isEmpty()) {
                log.debug("没有SO文件需要上传");
                return true;
            }

            Set<String> uploadedSoFiles = new HashSet<>();

            for (IConfigFilePreparationService.SoFileInfo soFileInfo : soResult.getAvailableFiles()) {
                if (!soFileInfo.isExists()) {
                    continue;
                }

                // 避免重复上传相同的SO文件
                if (uploadedSoFiles.contains(soFileInfo.getFileName())) {
                    continue;
                }

                // SO目录已在uploadViaSftp开始时创建
                String soPath = remoteBasePath + "/SO";

                // 下发SO文件
                boolean uploadSo;
                try (FileInputStream fis = new FileInputStream(soFileInfo.getLocalFile())) {
                    uploadSo = sftp.upload(soPath, soFileInfo.getFileName(), fis);
                }

                if (uploadSo) {
                    String successMsg = String.format("SO文件下发成功[文件名称:%s]", soFileInfo.getFileName());
                    taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, successMsg, false);
                    log.info("SO文件下发成功[filepath={}]", soFileInfo.getLocalFile().getAbsolutePath());
                } else {
                    String failureMsg = String.format("SO文件下发失败[文件名称:%s]", soFileInfo.getFileName());
                    taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, failureMsg, false);
                    log.error("SO文件下发失败[filepath={}]", soFileInfo.getLocalFile().getAbsolutePath());
                }
                uploadedSoFiles.add(soFileInfo.getFileName());
            }

            // 报告未找到的文件
            if (soResult.getNotFoundFiles() != null && !soResult.getNotFoundFiles().isEmpty()) {
                for (String notFoundFile : soResult.getNotFoundFiles()) {
                    String notFoundMsg = String.format("未找到下发的.so文件[文件名称:%s]", notFoundFile);
                    taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, notFoundMsg, false);
                    log.warn("SO文件不存在: {}", notFoundFile);
                }
            }

            return true;
        } catch (Exception e) {
            log.error("SFTP下发SO文件失败", e);
            String errorMsg = "SO文件下发失败: " + e.getMessage();
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
            return false;
        }
    }

    /**
     * 优化版本：使用准备好的CMB字典文件进行SFTP上传
     */
    private boolean uploadCmbDictionaryViaSftpOptimized(File cmbDictionaryFile, Sftp sftp, String uniqueId) {
        try {
            if (cmbDictionaryFile == null || !cmbDictionaryFile.exists()) {
                log.debug("cmb_dictionary.xml文件不存在，跳过下发");
                return true;
            }

            // cmbcfg目录已在uploadViaSftp开始时创建
            String cmbcfgPath = remoteBasePath + "/cmbcfg";

            // 下发cmb_dictionary.xml文件
            boolean uploadSuccess;
            try (FileInputStream fis = new FileInputStream(cmbDictionaryFile)) {
                uploadSuccess = sftp.upload(cmbcfgPath, cmbDictionaryFile.getName(), fis);
            }

            if (uploadSuccess) {
                String successMsg = "cmb_dictionary.xml文件下发成功";
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, successMsg, false);
                log.info("cmb_dictionary.xml文件下发成功: {}", cmbDictionaryFile.getAbsolutePath());
            } else {
                String failureMsg = "cmb_dictionary.xml文件下发失败";
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, failureMsg, false);
                log.error("cmb_dictionary.xml文件下发失败: {}", cmbDictionaryFile.getAbsolutePath());
            }

            return uploadSuccess;
        } catch (Exception e) {
            log.error("SFTP下发cmb_dictionary.xml文件异常", e);
            String errorMsg = "cmb_dictionary.xml文件下发失败: " + e.getMessage();
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
            return false;
        }
    }

    /**
     * 优化版本：使用准备好的SO文件列表进行FTP上传
     */
    private boolean uploadSoFilesViaFtpOptimized(IConfigFilePreparationService.SoFilePreparationResult soResult,
                                                Ftp ftp, String uniqueId) {
        try {
            if (soResult == null || soResult.getAvailableFiles() == null || soResult.getAvailableFiles().isEmpty()) {
                log.debug("没有SO文件需要上传");
                return true;
            }

            Set<String> uploadedSoFiles = new HashSet<>();

            for (IConfigFilePreparationService.SoFileInfo soFileInfo : soResult.getAvailableFiles()) {
                if (!soFileInfo.isExists()) {
                    continue;
                }

                // 避免重复上传相同的SO文件
                if (uploadedSoFiles.contains(soFileInfo.getFileName())) {
                    continue;
                }

                // 确保SO目录存在（远程Unix路径）
                String soPath = remoteBasePath + "/SO";
                ensureFtpDirectoryExists(ftp, soPath);

                String encodedFileName = CharsetUtil.convert(soFileInfo.getFileName(), CharsetUtil.CHARSET_GBK, CharsetUtil.CHARSET_ISO_8859_1);
                boolean uploadSo = ftp.upload(soPath + "/", encodedFileName, soFileInfo.getLocalFile());

                if (uploadSo) {
                    String successMsg = String.format("SO文件下发成功[文件名称:%s]", soFileInfo.getFileName());
                    taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, successMsg, false);
                    log.info("SO文件下发成功[filepath={}]", soFileInfo.getLocalFile().getAbsolutePath());
                } else {
                    String failureMsg = String.format("SO文件下发失败[文件名称:%s]", soFileInfo.getFileName());
                    taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, failureMsg, false);
                    log.error("SO文件下发失败[filepath={}]", soFileInfo.getLocalFile().getAbsolutePath());
                }
                uploadedSoFiles.add(soFileInfo.getFileName());
            }

            // 报告未找到的文件
            if (soResult.getNotFoundFiles() != null && !soResult.getNotFoundFiles().isEmpty()) {
                for (String notFoundFile : soResult.getNotFoundFiles()) {
                    String notFoundMsg = String.format("未找到下发的.so文件[文件名称:%s]", notFoundFile);
                    taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, notFoundMsg, false);
                    log.warn("SO文件不存在: {}", notFoundFile);
                }
            }

            return true;
        } catch (Exception e) {
            log.error("FTP下发SO文件失败", e);
            String errorMsg = "SO文件下发失败: " + e.getMessage();
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
            return false;
        }
    }

    /**
     * 优化版本：使用准备好的CMB字典文件进行FTP上传
     */
    private boolean uploadCmbDictionaryViaFtpOptimized(File cmbDictionaryFile, Ftp ftp, String uniqueId) {
        try {
            if (cmbDictionaryFile == null || !cmbDictionaryFile.exists()) {
                log.debug("cmb_dictionary.xml文件不存在，跳过下发");
                return true;
            }

            // 确保cmbcfg目录存在（远程Unix路径）
            String cmbcfgPath = remoteBasePath + "/cmbcfg";
            ensureFtpDirectoryExists(ftp, cmbcfgPath);

            // 下发cmb_dictionary.xml文件
            String encodedFileName = CharsetUtil.convert(cmbDictionaryFile.getName(), CharsetUtil.CHARSET_GBK, CharsetUtil.CHARSET_ISO_8859_1);
            boolean uploadSuccess;
            try (FileInputStream fis = new FileInputStream(cmbDictionaryFile)) {
                uploadSuccess = ftp.upload(cmbcfgPath + "/", encodedFileName, fis);
            }

            if (uploadSuccess) {
                String successMsg = "cmb_dictionary.xml文件下发成功";
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, successMsg, false);
                log.info("cmb_dictionary.xml文件下发成功: {}", cmbDictionaryFile.getAbsolutePath());
            } else {
                String failureMsg = "cmb_dictionary.xml文件下发失败";
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, failureMsg, false);
                log.error("cmb_dictionary.xml文件下发失败: {}", cmbDictionaryFile.getAbsolutePath());
            }

            return uploadSuccess;
        } catch (Exception e) {
            log.error("FTP下发cmb_dictionary.xml文件异常", e);
            String errorMsg = "cmb_dictionary.xml文件下发失败: " + e.getMessage();
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
            return false;
        }
    }

    /**
     * 优化版本：使用准备好的SO文件列表进行SSH+SCP上传
     */
    private boolean uploadSoFilesViaScpOptimized(IConfigFilePreparationService.SoFilePreparationResult soResult,
                                                AutoCloseableSession session, String uniqueId) {
        try {
            if (soResult == null || soResult.getAvailableFiles() == null || soResult.getAvailableFiles().isEmpty()) {
                log.debug("没有SO文件需要上传");
                return true;
            }

            // 确保SO目录存在（远程Unix路径）
            String soPath = remoteBasePath + "/SO";
            ensureRemoteDirectoryExists(session.getSession(), soPath, uniqueId);

            Set<String> uploadedSoFiles = new HashSet<>();
            boolean allSuccess = true;

            for (IConfigFilePreparationService.SoFileInfo soFileInfo : soResult.getAvailableFiles()) {
                if (!soFileInfo.isExists()) {
                    continue;
                }

                // 避免重复上传相同的SO文件
                if (uploadedSoFiles.contains(soFileInfo.getFileName())) {
                    continue;
                }

                // 计算本地文件MD5
                String localMd5 = DigestUtil.md5Hex(soFileInfo.getLocalFile());

                // 下发SO文件（远程Unix路径）
                String remoteFilePath = soPath + "/" + soFileInfo.getFileName();
                boolean uploadResult = uploadFileViaScp(session.getSession(), soFileInfo.getLocalFile(), remoteFilePath, uniqueId);

                if (uploadResult) {
                    // 验证下发后的文件MD5
                    String remoteMd5 = getRemoteFileMd5(session.getSession(), remoteFilePath, uniqueId);
                    if (remoteMd5 != null && localMd5.equals(remoteMd5)) {
                        String successMsg = String.format("SO文件下发成功，MD5验证通过[文件名称:%s]", soFileInfo.getFileName());
                        taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, successMsg, false);
                        log.info("SO文件下发成功，MD5验证通过[filepath={}]", soFileInfo.getLocalFile().getAbsolutePath());
                    } else {
                        String errorMsg = String.format("SO文件MD5验证失败[文件名称:%s] - 本地:%s, 远程:%s",
                                soFileInfo.getFileName(), localMd5, remoteMd5);
                        taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
                        log.error("SO文件MD5验证失败[filepath={}] - 本地:{}, 远程:{}", soFileInfo.getLocalFile().getAbsolutePath(), localMd5, remoteMd5);
                        allSuccess = false;
                    }
                } else {
                    String failureMsg = String.format("SO文件下发失败[文件名称:%s]", soFileInfo.getFileName());
                    taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, failureMsg, false);
                    log.error("SO文件下发失败[filepath={}]", soFileInfo.getLocalFile().getAbsolutePath());
                    allSuccess = false;
                }
                uploadedSoFiles.add(soFileInfo.getFileName());
            }

            // 报告未找到的文件
            if (soResult.getNotFoundFiles() != null && !soResult.getNotFoundFiles().isEmpty()) {
                for (String notFoundFile : soResult.getNotFoundFiles()) {
                    String notFoundMsg = String.format("未找到下发的.so文件[文件名称:%s]", notFoundFile);
                    taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, notFoundMsg, false);
                    log.warn("SO文件不存在: {}", notFoundFile);
                }
            }

            return allSuccess;
        } catch (Exception e) {
            log.error("SSH+SCP下发SO文件失败", e);
            String errorMsg = "SO文件下发失败: " + e.getMessage();
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
            return false;
        }
    }

    /**
     * 优化版本：使用准备好的CMB字典文件进行SSH+SCP上传
     */
    private boolean uploadCmbDictionaryViaScpOptimized(File cmbDictionaryFile, AutoCloseableSession session, String uniqueId) {
        try {
            if (cmbDictionaryFile == null || !cmbDictionaryFile.exists()) {
                log.debug("cmb_dictionary.xml文件不存在，跳过下发");
                return true;
            }

            // 确保cmbcfg目录存在（远程Unix路径）
            String cmbcfgPath = remoteBasePath + "/cmbcfg";
            ensureRemoteDirectoryExists(session.getSession(), cmbcfgPath, uniqueId);

            // 计算本地文件MD5
            String localMd5 = DigestUtil.md5Hex(cmbDictionaryFile);
            log.info("本地CMB字典文件MD5: {} - {}", cmbDictionaryFile.getName(), localMd5);

            // 下发CMB字典文件
            String remoteFilePath = cmbcfgPath + "/" + cmbDictionaryFile.getName();
            boolean uploadResult = uploadFileViaScp(session.getSession(), cmbDictionaryFile, remoteFilePath, uniqueId);

            if (!uploadResult) {
                return false;
            }

            // 验证下发后的文件MD5
            String remoteMd5 = getRemoteFileMd5(session.getSession(), remoteFilePath, uniqueId);
            if (remoteMd5 != null && localMd5.equals(remoteMd5)) {
                String successMsg = "CMB字典文件下发成功，MD5验证通过";
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, successMsg, false);
                log.info("CMB字典文件下发成功，MD5验证通过: {} -> {}", cmbDictionaryFile.getName(), remoteFilePath);
                return true;
            } else {
                String errorMsg = String.format("CMB字典文件MD5验证失败 - 本地:%s, 远程:%s", localMd5, remoteMd5);
                taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
                log.error("CMB字典文件MD5验证失败: {} - 本地:{}, 远程:{}", cmbDictionaryFile.getName(), localMd5, remoteMd5);
                return false;
            }

        } catch (Exception e) {
            log.error("SSH+SCP下发CMB字典文件失败", e);
            String errorMsg = "CMB字典文件下发失败: " + e.getMessage();
            taskStatusService.sendMessage(uniqueId, TaskTypeEnum.CONFIGURATION_DISTRIBUTION, errorMsg, false);
            return false;
        }
    }
}
