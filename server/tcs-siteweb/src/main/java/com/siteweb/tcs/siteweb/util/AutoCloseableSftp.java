package com.siteweb.tcs.siteweb.util;

import cn.hutool.extra.ssh.Sftp;
import com.jcraft.jsch.Session;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;

/**
 * 自动关闭的SFTP连接包装器
 * 实现AutoCloseable接口，支持try-with-resources语法
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Slf4j
public class AutoCloseableSftp implements AutoCloseable {
    
    private final Sftp sftp;
    private final Session session;
    
    public AutoCloseableSftp(Sftp sftp, Session session) {
        this.sftp = sftp;
        this.session = session;
    }
    
    /**
     * 上传文件
     */
    public boolean upload(String destPath, String fileName, InputStream in) {
        return sftp.upload(destPath, fileName, in);
    }
    
    /**
     * 检查文件是否存在
     */
    public boolean exist(String path) {
        return sftp.exist(path);
    }
    
    /**
     * 检查是否为目录
     */
    public boolean isDir(String path) {
        return sftp.isDir(path);
    }
    
    /**
     * 创建目录
     */
    public boolean mkdir(String path) {
        return sftp.mkdir(path);
    }
    
    /**
     * 递归创建目录
     */
    public boolean mkDirs(String path) {
        try {
            sftp.mkDirs(path);
            return true;
        }catch (Exception e){
            log.error("创建目录失败: {}", path, e);
            return false;
        }
    }
    
    /**
     * 删除文件
     */
    public boolean delFile(String path) {
        return sftp.delFile(path);
    }
    
    /**
     * 获取原始SFTP对象（谨慎使用）
     */
    public Sftp getSftp() {
        return sftp;
    }
    
    /**
     * 获取SSH会话（谨慎使用）
     */
    public Session getSession() {
        return session;
    }
    
    @Override
    public void close() {
        // 优雅关闭SFTP连接
        if (sftp != null) {
            try {
                sftp.close();
                log.debug("SFTP连接已关闭");
            } catch (Exception e) {
                log.warn("关闭SFTP连接时发生异常", e);
            }
        }
        
        // 优雅关闭SSH会话
        if (session != null && session.isConnected()) {
            try {
                session.disconnect();
                log.debug("SSH会话已断开");
            } catch (Exception e) {
                log.warn("断开SSH会话时发生异常", e);
            }
        }
    }
    
    /**
     * 检查连接是否有效
     */
    public boolean isConnected() {
        return session != null && session.isConnected();
    }
    
    /**
     * 获取连接信息
     */
    public String getConnectionInfo() {
        if (session != null) {
            return String.format("%s@%s:%d", session.getUserName(), session.getHost(), session.getPort());
        }
        return "未知连接";
    }
}
