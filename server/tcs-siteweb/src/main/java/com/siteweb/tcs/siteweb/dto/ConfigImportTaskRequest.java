package com.siteweb.tcs.siteweb.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 配置导入任务请求DTO
 */
@Data
@Schema(description = "配置导入任务请求")
public class ConfigImportTaskRequest {

    @Schema(description = "用户ID", example = "admin")
    private String userId;

    @Schema(description = "是否覆盖现有配置", example = "true")
    private Boolean overwrite = true;

    @Schema(description = "导入模式", example = "FULL", allowableValues = {"FULL", "TEMPLATE_ONLY", "MONITOR_UNIT_ONLY"})
    private String importMode = "FULL";

    @Schema(description = "备注信息", example = "从备份文件导入配置")
    private String remarks;

    @Schema(description = "当前监控单元ID（用于智能删除设备模板时区分设备归属）", example = "123")
    private Integer currentMonitorUnitId;
}
