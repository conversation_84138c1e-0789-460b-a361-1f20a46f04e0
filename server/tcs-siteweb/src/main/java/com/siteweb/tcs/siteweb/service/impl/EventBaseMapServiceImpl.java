package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.dto.EventBaseMapDTO;
import com.siteweb.tcs.siteweb.entity.EventBaseMap;
import com.siteweb.tcs.siteweb.mapper.EventBaseMapMapper;
import com.siteweb.tcs.siteweb.service.IEventBaseMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Event Base Map Service Implementation
 */
@Service
public class EventBaseMapServiceImpl extends ServiceImpl<EventBaseMapMapper, EventBaseMap> implements IEventBaseMapService {
    @Autowired
    private EventBaseMapMapper eventBaseMapMapper;


    @Override
    public List<EventBaseMapDTO> getEventBaseMap(Integer standardId) {
        return eventBaseMapMapper.getEventBaseMap(standardId);
    }
}
