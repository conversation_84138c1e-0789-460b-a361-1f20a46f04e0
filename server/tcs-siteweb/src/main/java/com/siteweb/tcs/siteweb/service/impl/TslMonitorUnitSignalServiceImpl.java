package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.entity.TslMonitorUnitSignal;
import com.siteweb.tcs.siteweb.mapper.TslMonitorUnitSignalMapper;
import com.siteweb.tcs.siteweb.service.ITslMonitorUnitSignalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 监控单元信号服务实现类
 */
@Slf4j
@Service
public class TslMonitorUnitSignalServiceImpl extends ServiceImpl<TslMonitorUnitSignalMapper, TslMonitorUnitSignal> implements ITslMonitorUnitSignalService {

    @Autowired
    private TslMonitorUnitSignalMapper tslMonitorUnitSignalMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByStationId(Integer stationId) {
        if (stationId == null) {
            log.warn("Cannot delete monitor unit signals: station ID is null");
            return false;
        }
        
        try {
            int count = (int) count(new LambdaQueryWrapper<TslMonitorUnitSignal>()
                    .eq(TslMonitorUnitSignal::getStationId, stationId));
            
            if (count == 0) {
                log.info("No monitor unit signals found for station ID: {}", stationId);
                return true;
            }
            
            boolean result = remove(new LambdaQueryWrapper<TslMonitorUnitSignal>()
                    .eq(TslMonitorUnitSignal::getStationId, stationId));
            
            if (result) {
                log.info("Deleted {} monitor unit signals for station ID: {}", count, stationId);
            } else {
                log.warn("Failed to delete monitor unit signals for station ID: {}", stationId);
            }
            
            return result;
        } catch (Exception e) {
            log.error("Error deleting monitor unit signals for station ID: {}", stationId, e);
            throw e;
        }
    }

    // ==================== 设备管理相关方法实现 ====================

    @Override
    public List<TslMonitorUnitSignal> findByEquipmentIdAndSignalId(Integer equipmentId, Integer signalId) {
        try {
            return tslMonitorUnitSignalMapper.findByEquipmentIdAndSignalId(equipmentId, signalId);
        } catch (Exception e) {
            log.error("Failed to find monitor unit signals by equipment ID {} and signal ID {}", equipmentId, signalId, e);
            return List.of();
        }
    }

    @Override
    public List<TslMonitorUnitSignal> findByCondition(Map<String, Object> condition) {
        try {
            return tslMonitorUnitSignalMapper.findByCondition(condition);
        } catch (Exception e) {
            log.error("Failed to find monitor unit signals by condition", e);
            return List.of();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createOrUpdate(TslMonitorUnitSignal signal) {
        try {
            if (signal == null) {
                log.warn("Cannot create or update monitor unit signal: signal is null");
                return false;
            }

            int result = tslMonitorUnitSignalMapper.createOrUpdate(signal);
            if (result > 0) {
                log.info("Successfully created or updated monitor unit signal for equipment {} and signal {}",
                        signal.getEquipmentId(), signal.getSignalId());
                return true;
            } else {
                log.warn("Failed to create or update monitor unit signal for equipment {} and signal {}",
                        signal.getEquipmentId(), signal.getSignalId());
                return false;
            }
        } catch (Exception e) {
            log.error("Error creating or updating monitor unit signal", e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByEquipmentIdAndSignalId(Integer equipmentId, Integer signalId) {
        try {
            if (equipmentId == null || signalId == null) {
                log.warn("Cannot delete monitor unit signal: equipment ID or signal ID is null");
                return false;
            }

            int result = tslMonitorUnitSignalMapper.deleteByEquipmentIdAndSignalId(equipmentId, signalId);
            if (result > 0) {
                log.info("Successfully deleted monitor unit signal for equipment {} and signal {}", equipmentId, signalId);
                return true;
            } else {
                log.warn("No monitor unit signal found to delete for equipment {} and signal {}", equipmentId, signalId);
                return false;
            }
        } catch (Exception e) {
            log.error("Error deleting monitor unit signal for equipment {} and signal {}", equipmentId, signalId, e);
            throw e;
        }
    }

    @Override
    public List<TslMonitorUnitSignal> getMonitorUnitSignals() {
        try {
            return list();
        } catch (Exception e) {
            log.error("Failed to get all monitor unit signals", e);
            return List.of();
        }
    }
}
