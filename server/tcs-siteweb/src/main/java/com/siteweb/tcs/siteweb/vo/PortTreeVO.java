package com.siteweb.tcs.siteweb.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.siteweb.tcs.siteweb.entity.Port;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 端口树形结构 VO
 * 用于采样器树的端口节点展示
 */
@Data
public class PortTreeVO {
    private Integer portId;
    private String portName;
    private Integer portType;
    @JsonIgnore
    private Integer monitorUnitId;
    private List<SamplerUnitTreeVO> samplerUnits = new ArrayList<>();

    /**
     * 从 Port 实体复制数据
     *
     * @param port 端口实体
     * @return 当前 VO 对象
     */
    public PortTreeVO copy(Port port) {
        this.setPortId(port.getPortId());
        this.setPortName(port.getPortName());
        this.setPortType(port.getPortType());
        this.setMonitorUnitId(port.getMonitorUnitId());
        return this;
    }
}
