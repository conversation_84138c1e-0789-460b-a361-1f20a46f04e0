package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.tcs.siteweb.annotation.ChangeSource;
import lombok.Data;

import java.io.Serializable;

/**
 * Port entity
 */
@Data
@TableName("tsl_port")
@ChangeSource(channel = "tcs", product = "siteweb", source = "port")
public class Port implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "Id", type = IdType.AUTO)
    private Integer id;

    @TableField("PortId")
    private Integer portId;

    @TableField("MonitorUnitId")
    private Integer monitorUnitId;

    @TableField("PortNo")
    private Integer portNo;

    @TableField("PortName")
    private String portName;

    @TableField("PortType")
    private Integer portType;

    @TableField("Setting")
    private String setting;

    @TableField("PhoneNumber")
    private String phoneNumber;

    @TableField("LinkSamplerUnitId")
    private Integer linkSamplerUnitId;

    @TableField("Description")
    private String description;
}
