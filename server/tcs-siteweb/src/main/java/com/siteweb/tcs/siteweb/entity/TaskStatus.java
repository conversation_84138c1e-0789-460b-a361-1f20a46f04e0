package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 任务状态实体
 * 用于替代WebSocket状态管理，存储各种操作的状态信息
 */
@TableName("task_status")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TaskStatus {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务唯一标识符（对应原来的uniqueId）
     */
    @TableField("task_id")
    private String taskId;

    /**
     * 任务类型：CONFIGURATION_GENERATION, CONFIGURATION_DISTRIBUTION, TELNET_CONNECTION
     */
    @TableField("task_type")
    private String taskType;

    /**
     * 关联的监控单元ID（可选）
     */
    @TableField("monitor_unit_id")
    private String monitorUnitId;

    /**
     * 任务状态：RUNNING, SUCCESS, FAILED, COMPLETED
     */
    @TableField("status")
    private String status;

    /**
     * 当前步骤描述
     */
    @TableField("current_step")
    private String currentStep;

    /**
     * 进度百分比（0-100）
     */
    @TableField("progress")
    private Integer progress;

    /**
     * 状态消息
     */
    @TableField("message")
    private String message;

    /**
     * 是否是最终状态（完成或失败）
     */
    @TableField("is_final")
    private Boolean isFinal;

    /**
     * 错误信息（如果有）
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 过期时间（用于自动清理）
     */
    @TableField("expire_time")
    private LocalDateTime expireTime;
}
