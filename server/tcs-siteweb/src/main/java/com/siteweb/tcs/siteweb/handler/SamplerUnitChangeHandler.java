package com.siteweb.tcs.siteweb.handler;

import com.siteweb.tcs.siteweb.change.ChangeRecord;
import com.siteweb.tcs.siteweb.change.ObjectChangeHandlerAdapter;
import com.siteweb.tcs.siteweb.entity.SamplerUnit;
import com.siteweb.tcs.siteweb.service.IEquipmentService;
import com.siteweb.tcs.siteweb.service.IMonitorUnitStateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> (2024-04-12)
 **/
@Slf4j
@Component
public class SamplerUnitChangeHandler extends ObjectChangeHandlerAdapter {



    @Autowired
    private IMonitorUnitStateService monitorUnitStateService;


    @Autowired
    IEquipmentService equipmentService;


    @Override
    protected List<Class<?>> doRegisterHandler() {
        return List.of(SamplerUnit.class);
    }


    @Override
    public void onCreate(ChangeRecord changeRecord) {
        SamplerUnit samplerUnit = changeRecord.readMessageBody(SamplerUnit.class);
        monitorUnitStateService.updateMonitorUnit(samplerUnit.getMonitorUnitId());
    }


    @Override
    public void onUpdate(ChangeRecord changeRecord) {
        SamplerUnit samplerUnit = changeRecord.readMessageBody(SamplerUnit.class);
        monitorUnitStateService.updateMonitorUnit(samplerUnit.getMonitorUnitId());
    }

    @Override
    public void onDelete(ChangeRecord changeRecord) {
        SamplerUnit samplerUnit = changeRecord.readMessageBody(SamplerUnit.class);
        monitorUnitStateService.updateMonitorUnit(samplerUnit.getMonitorUnitId());
        // 删除采集单元下挂的设备
        equipmentService.deleteBySamplerUnitId(samplerUnit.getSamplerUnitId());
    }


}
