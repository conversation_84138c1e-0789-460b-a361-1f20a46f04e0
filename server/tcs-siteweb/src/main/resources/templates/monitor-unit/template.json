[{"templateName": "GFSU监控单元", "category": 12, "description": "GFSU监控单元", "standardPortNum": 8, "items": [{"port": {"portNo": 11, "portType": 34, "setting": "comm_host_dev.so"}, "samplerUnit": {"portNo": 11, "samplerUnitName": "GFSU-HOST设备", "address": 1, "spUnitInterval": 2, "dllPath": "GFSUHOST.so"}, "equipment": {"stationPrefix": true, "houseName": "自诊断设备", "equipmentName": "GFSU自诊断设备", "equipmentTemplateName": "GFSU-HOST设备", "samplerUnitName": "GFSU-HOST自诊断设备", "equipmentCategory": 99, "equipmentType": 2}}, {"port": {"portNo": 12, "portType": 35, "setting": "comm_io_dev.so"}, "samplerUnit": {"portNo": 12, "samplerUnitName": "GFSU-IO设备", "address": 1, "spUnitInterval": 2, "dllPath": "GFSUIO.so"}, "equipment": {"stationPrefix": true, "houseName": "IO设备", "equipmentName": "GFSU-IO设备", "equipmentTemplateName": "GFSU-IO设备", "samplerUnitName": "GFSU-IO设备", "equipmentCategory": 51, "equipmentType": 1}}]}, {"templateName": "GFSU2监控单元", "category": 25, "description": "GFSU2监控单元", "standardPortNum": 8, "items": [{"port": {"portNo": 11, "portType": 34, "setting": "comm_host_dev.so"}, "samplerUnit": {"portNo": 11, "samplerUnitName": "GFSUV2-HOST设备", "address": 1, "spUnitInterval": 2, "dllPath": "GFSUHOST.so"}, "equipment": {"stationPrefix": true, "houseName": "自诊断设备", "equipmentName": "GFSUV2自诊断设备", "equipmentTemplateName": "GFSUV2-HOST设备", "samplerUnitName": "GFSUV2-HOST自诊断设备", "equipmentCategory": 99, "equipmentType": 2}}, {"port": {"portNo": 12, "portType": 35, "setting": "comm_io_dev.so"}, "samplerUnit": {"portNo": 12, "samplerUnitName": "GFSUV2-IO设备", "address": 1, "spUnitInterval": 2, "dllPath": "GFSUIO.so"}, "equipment": {"stationPrefix": true, "houseName": "IO设备", "equipmentName": "GFSUV2-IO设备", "equipmentTemplateName": "GFSU-V2-IO设备", "samplerUnitName": "GFSU-V2-IO设备", "equipmentCategory": 51, "equipmentType": 1}}]}, {"templateName": "GFSU3监控单元", "category": 18, "description": "GFSU3监控单元", "standardPortNum": 9, "items": [{"port": {"portNo": 11, "portType": 34, "setting": "comm_host_dev.so"}, "samplerUnit": {"portNo": 11, "samplerUnitName": "GFSUV3-HOST设备", "address": 1, "spUnitInterval": 2, "dllPath": "GFSUV3HOST.so"}, "equipment": {"stationPrefix": true, "houseName": "自诊断设备", "equipmentName": "GFSUV3自诊断设备", "equipmentTemplateName": "GFSUV3-HOST设备", "samplerUnitName": "GFSUV3-HOST自诊断设备", "equipmentCategory": 99, "equipmentType": 2}}, {"port": {"portNo": 12, "portType": 35, "setting": "comm_io_dev.so"}, "samplerUnit": {"portNo": 12, "samplerUnitName": "GFSUV3-IO设备", "address": 1, "spUnitInterval": 2, "dllPath": "IO.so"}, "equipment": {"stationPrefix": true, "houseName": "IO设备", "equipmentName": "GFSUV3-IO设备", "equipmentTemplateName": "GFSUV3-IO设备", "samplerUnitName": "GFSUV3-IO设备", "equipmentCategory": 51, "equipmentType": 1}}]}, {"templateName": "ISU-V2监控单元", "category": 14, "description": "ISU-V2监控单元", "standardPortNum": 16, "items": [{"port": {"portNo": 19, "portType": 34, "setting": "comm_host_dev.so"}, "samplerUnit": {"portNo": 19, "samplerUnitName": "ISU-V2-HOST设备", "address": 1, "spUnitInterval": 2, "dllPath": "ISUHOST.so"}, "equipment": {"stationPrefix": true, "houseName": "自诊断设备", "equipmentName": "ISU-V2自诊断设备", "equipmentTemplateName": "ISU-V2-HOST设备", "samplerUnitName": "ISU-V2-HOST自诊断设备", "equipmentCategory": 99, "equipmentType": 2}}]}, {"templateName": "WorkStation监控单元", "category": 16, "description": "WorkStation监控单元", "standardPortNum": 12, "items": [{"port": {"portNo": 13, "portType": 34, "setting": "comm_host_dev.so"}, "samplerUnit": {"portNo": 13, "samplerUnitName": "WorkStation-HOST设备", "address": 1, "spUnitInterval": 2, "dllPath": "SPARKHOST.so"}, "equipment": {"stationPrefix": true, "houseName": "自诊断设备", "equipmentName": "WorkStation自诊断设备", "equipmentTemplateName": "WorkStation-Host设备", "samplerUnitName": "WorkStation-Host自诊断设备", "equipmentCategory": 99, "equipmentType": 2}}]}]