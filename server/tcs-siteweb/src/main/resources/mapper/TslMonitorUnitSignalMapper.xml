<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.siteweb.mapper.TslMonitorUnitSignalMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.siteweb.entity.TslMonitorUnitSignal">
        <result column="StationId" property="stationId" />
        <result column="MonitorUnitId" property="monitorUnitId" />
        <result column="EquipmentId" property="equipmentId" />
        <result column="SignalId" property="signalId" />
        <result column="ReferenceSamplerUnitId" property="referenceSamplerUnitId" />
        <result column="ReferenceChannelNo" property="referenceChannelNo" />
        <result column="Expression" property="expression" />
        <result column="InstanceType" property="instanceType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        StationId, MonitorUnitId, EquipmentId, SignalId, ReferenceSamplerUnitId, 
        ReferenceChannelNo, Expression, InstanceType
    </sql>

    <!-- 根据设备ID和信号ID查询监控单元信号 -->
    <select id="findByEquipmentIdAndSignalId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tsl_monitorunitsignal
        WHERE EquipmentId = #{equipmentId}
        AND SignalId = #{signalId}
    </select>

    <!-- 根据条件查询监控单元信号 -->
    <select id="findByCondition" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tsl_monitorunitsignal
        <where>
            <if test="condition.stationId != null">
                AND StationId = #{condition.stationId}
            </if>
            <if test="condition.monitorUnitId != null">
                AND MonitorUnitId = #{condition.monitorUnitId}
            </if>
            <if test="condition.equipmentId != null">
                AND EquipmentId = #{condition.equipmentId}
            </if>
            <if test="condition.signalId != null">
                AND SignalId = #{condition.signalId}
            </if>
        </where>
    </select>

    <!-- 创建或更新监控单元信号 -->
    <insert id="createOrUpdate">
        INSERT INTO tsl_monitorunitsignal (
            StationId, MonitorUnitId, EquipmentId, SignalId, 
            ReferenceSamplerUnitId, ReferenceChannelNo, Expression, InstanceType
        ) VALUES (
            #{stationId}, #{monitorUnitId}, #{equipmentId}, #{signalId},
            #{referenceSamplerUnitId}, #{referenceChannelNo}, #{expression}, #{instanceType}
        )
        ON DUPLICATE KEY UPDATE
            ReferenceSamplerUnitId = VALUES(ReferenceSamplerUnitId),
            ReferenceChannelNo = VALUES(ReferenceChannelNo),
            Expression = VALUES(Expression),
            InstanceType = VALUES(InstanceType)
    </insert>

    <!-- 删除监控单元信号 -->
    <delete id="deleteByEquipmentIdAndSignalId">
        DELETE FROM tsl_monitorunitsignal
        WHERE EquipmentId = #{equipmentId}
        AND SignalId = #{signalId}
    </delete>
</mapper>
