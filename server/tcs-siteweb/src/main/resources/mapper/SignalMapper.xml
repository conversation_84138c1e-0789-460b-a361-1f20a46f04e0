<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.siteweb.mapper.SignalMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.siteweb.entity.Signal">
        <id column="Id" property="id" />
        <result column="EquipmentTemplateId" property="equipmentTemplateId" />
        <result column="SignalId" property="signalId" />
        <result column="Enable" property="enable" />
        <result column="Visible" property="visible" />
        <result column="Description" property="description" />
        <result column="SignalName" property="signalName" />
        <result column="SignalCategory" property="signalCategory" />
        <result column="SignalType" property="signalType" />
        <result column="ChannelNo" property="channelNo" />
        <result column="ChannelType" property="channelType" />
        <result column="Expression" property="expression" />
        <result column="DataType" property="dataType" />
        <result column="ShowPrecision" property="showPrecision" />
        <result column="Unit" property="unit" />
        <result column="StoreInterval" property="storeInterval" />
        <result column="AbsValueThreshold" property="absValueThreshold" />
        <result column="PercentThreshold" property="percentThreshold" />
        <result column="StaticsPeriod" property="staticsPeriod" />
        <result column="BaseTypeId" property="baseTypeId" />
        <result column="ChargeStoreInterVal" property="chargeStoreInterVal" />
        <result column="ChargeAbsValue" property="chargeAbsValue" />
        <result column="DisplayIndex" property="displayIndex" />
        <result column="MDBSignalId" property="mdbSignalId" />
        <result column="ModuleNo" property="moduleNo" />
    </resultMap>
    
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        Id, EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, 
        SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, 
        ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, 
        StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, 
        MDBSignalId, ModuleNo
    </sql>
    
    <!-- 根据设备模板ID和信号ID查询 -->
    <select id="findSignalEntityByTemplateIdAndSignalId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tbl_signal
        WHERE EquipmentTemplateId = #{equipmentTemplateId} 
        AND SignalId = #{signalId}
    </select>
    
    <!-- 根据设备模板ID查询信号列表 -->
    <select id="findByTemplateId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tbl_signal
        WHERE EquipmentTemplateId = #{equipmentTemplateId}
        ORDER BY DisplayIndex ASC
    </select>
    
    <!-- 批量删除信号 -->
    <delete id="batchDelete">
        DELETE FROM tbl_signal
        WHERE EquipmentTemplateId = #{equipmentTemplateId}
        AND SignalId IN
        <foreach collection="signalIds" item="signalId" open="(" separator="," close=")">
            #{signalId}
        </foreach>
    </delete>
    
    <!-- 检查信号名称是否存在 -->
    <select id="checkSignalNameExists" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM tbl_signal
        WHERE EquipmentTemplateId = #{equipmentTemplateId}
        AND SignalName = #{signalName}
        <if test="signalId != null">
            AND SignalId != #{signalId}
        </if>
    </select>
    
    <!-- 查找设备模板下最大的信号ID -->
    <select id="findMaxSignalIdByEquipmentTemplateId" resultType="java.lang.Integer">
        SELECT MAX(SignalId)
        FROM tbl_signal
        WHERE EquipmentTemplateId = #{equipmentTemplateId}
    </select>

    <!-- SignalConfigItem结果映射 -->
    <resultMap id="signalConfigItemMap" type="com.siteweb.tcs.siteweb.dto.SignalConfigItem">
        <id column="id" property="id"/>
        <result column="equipmentTemplateId" property="equipmentTemplateId"/>
        <result column="signalId" property="signalId"/>
        <result column="enable" property="enable"/>
        <result column="visible" property="visible"/>
        <result column="description" property="description"/>
        <result column="signalName" property="signalName"/>
        <result column="signalCategory" property="signalCategory"/>
        <result column="signalType" property="signalType"/>
        <result column="channelNo" property="channelNo"/>
        <result column="channelType" property="channelType"/>
        <result column="expression" property="expression"/>
        <result column="dataType" property="dataType"/>
        <result column="showPrecision" property="showPrecision"/>
        <result column="unit" property="unit"/>
        <result column="storeInterval" property="storeInterval"/>
        <result column="absValueThreshold" property="absValueThreshold"/>
        <result column="percentThreshold" property="percentThreshold"/>
        <result column="staticsPeriod" property="staticsPeriod"/>
        <result column="baseTypeId" property="baseTypeId"/>
        <result column="chargeStoreInterVal" property="chargeStoreInterVal"/>
        <result column="chargeAbsValue" property="chargeAbsValue"/>
        <result column="displayIndex" property="displayIndex"/>
        <result column="mdbSignalId" property="mdbSignalId"/>
        <result column="moduleNo" property="moduleNo"/>
        <collection property="signalMeaningsList" ofType="com.siteweb.tcs.siteweb.entity.SignalMeanings" column="equipmentTemplateId,signalId" notNullColumn="meaningId">
            <id column="meaningId" property="id"/>
            <result column="EquipmentTemplateId" property="equipmentTemplateId"/>
            <result column="SignalId" property="signalId"/>
            <result column="stateValue" property="stateValue"/>
            <result column="meanings" property="meanings"/>
            <result column="baseCondId" property="baseCondId"/>
        </collection>
        <collection property="signalPropertyList" ofType="com.siteweb.tcs.siteweb.entity.SignalProperty" column="equipmentTemplateId,signalId" notNullColumn="propertyId">
            <id column="propertyId" property="id"/>
            <result column="EquipmentTemplateId" property="equipmentTemplateId"/>
            <result column="SignalId" property="signalId"/>
            <result column="SignalPropertyId" property="signalPropertyId"/>
        </collection>
    </resultMap>

    <!-- SQL片段：查询信号配置项的SQL -->
    <sql id="findSignalItemConfigSql">
        SELECT a.id,
               a.equipmentTemplateId,
               a.signalId,
               a.enable,
               a.visible,
               a.description,
               a.signalName,
               a.signalCategory,
               a.signalType,
               a.channelNo,
               a.channelType,
               a.expression,
               a.dataType,
               a.showPrecision,
               a.unit,
               a.storeInterval,
               a.absValueThreshold,
               a.percentThreshold,
               a.staticsPeriod,
               a.baseTypeId,
               a.chargeStoreInterVal,
               a.chargeAbsValue,
               a.displayIndex,
               a.mdbSignalId,
               a.moduleNo,
               b.Id as meaningId,
               b.stateValue,
               b.meanings,
               b.baseCondId,
               c.Id as propertyId,
               c.SignalPropertyId
        FROM tbl_signal a
                 LEFT JOIN tbl_signalmeanings b
                           ON a.EquipmentTemplateId = b.EquipmentTemplateId AND a.SignalId = b.SignalId
                 LEFT JOIN tbl_signalproperty c
                           ON a.EquipmentTemplateId = c.EquipmentTemplateId AND a.SignalId = c.SignalId
    </sql>

    <!-- 更新工作站信号名称 -->
    <update id="updateWorkStationSignalName">
        UPDATE tbl_signal
        SET SignalName =
        <choose>
            <when test="_databaseId == 'mysql'">
                CONCAT(#{prefix}, SignalName)
            </when>
            <when test="_databaseId == 'postgresql'">
                #{prefix} || SignalName
            </when>
            <otherwise>
                CONCAT(#{prefix}, SignalName)
            </otherwise>
        </choose>
        WHERE EquipmentTemplateId = #{equipmentTemplateId}
    </update>

    <!-- 更新自诊断信号ID -->
    <update id="updateSelfDiagnosisSignal">
        UPDATE tbl_signal
        SET SignalId = #{centerId} * 1000000 + SignalId
        WHERE EquipmentTemplateId = #{equipmentTemplateId};
        UPDATE tbl_signalmeanings
        SET SignalId = #{centerId} * 1000000 + SignalId
        WHERE EquipmentTemplateId = #{equipmentTemplateId};
    </update>

    <!-- 查找设备模板下最大的信号配置项 -->
    <select id="findMaxSignalByEquipmentTemplateId" resultMap="signalConfigItemMap">
        <include refid="findSignalItemConfigSql"/> 
        WHERE a.equipmentTemplateId = #{equipmentTemplateId}
        ORDER BY a.SignalId DESC
        <choose>
            <when test="_databaseId == 'mysql'">
                LIMIT 1
            </when>
            <when test="_databaseId == 'postgresql'">
                LIMIT 1
            </when>
            <otherwise>
                LIMIT 1
            </otherwise>
        </choose>
    </select>

    <!-- 查找设备模板中不在信号基类字典中的基类ID -->
    <select id="findBaseTypeIdsNotInSignalBaseDicForEquipmentTemplate" resultType="java.lang.Long">
        SELECT t.BaseTypeId
        FROM TBL_Signal t
        WHERE t.EquipmentTemplateId = #{equipmentTemplateId}
        AND t.BaseTypeId IS NOT NULL
        AND t.BaseTypeId != 0
        AND t.BaseTypeId NOT IN (SELECT BaseTypeId FROM TBL_SignalBaseDic)
    </select>

    <!-- 查找设备模板下最大的显示索引 -->
    <select id="findMaxDisplayIndexByEquipmentTemplateId" resultType="java.lang.Integer">
        SELECT MAX(DisplayIndex)
        FROM tbl_signal
        WHERE EquipmentTemplateId = #{equipmentTemplateId}
    </select>

    <select id="findSignalItemByEquipmentTemplateId" resultMap="signalConfigItemMap">
        <include refid="findSignalItemConfigSql"/> where a.equipmentTemplateId = #{equipmentTemplateId}
    </select>
    <select id="findExcelDtoByEquipmentTemplateId" resultType="com.siteweb.tcs.siteweb.dto.excel.SignalExcel">
        SELECT
        b.EquipmentId,
        b.EquipmentName,
        a.EquipmentTemplateId,
        a.EquipmentTemplateName,
        e.SignalId,
        e.SignalName,
        e.Expression,
        e.ShowPrecision,
        e.Unit,
        e.StoreInterval,
        e.AbsvalueThreshold
        FROM tbl_equipmentTemplate a
        LEFT JOIN
        tbl_equipment b ON a.EquipmentTemplateId = b.EquipmentTemplateId
        LEFT JOIN
        tbl_signal e ON e.EquipmentTemplateId = a.EquipmentTemplateId
        WHERE a.EquipmentTemplateId = #{equipmentTemplateId}
    </select>
</mapper>
