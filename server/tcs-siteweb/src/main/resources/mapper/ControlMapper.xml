<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.siteweb.mapper.ControlMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.siteweb.entity.Control">
        <!-- TODO: 请根据Control.java实体字段补充映射关系 -->
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        <!-- TODO: 请根据Control.java实体字段补充列名 -->
    </sql>
    <resultMap id="findVoMap" type="com.siteweb.tcs.siteweb.vo.ControlVO">
        <id column="id" property="id"/>
        <result column="equipmenttemplateid" property="equipmentTemplateId"/>
        <result column="controlid" property="controlId"/>
        <result column="controlname" property="controlName"/>
        <result column="controlcategory" property="controlCategory"/>
        <result column="cmdtoken" property="cmdToken"/>
        <result column="basetypeid" property="baseTypeId"/>
        <result column="controlseverity" property="controlSeverity"/>
        <result column="signalid" property="signalId"/>
        <result column="timeout" property="timeOut"/>
        <result column="retry" property="retry"/>
        <result column="description" property="description"/>
        <result column="enable" property="enable"/>
        <result column="visible" property="visible"/>
        <result column="displayindex" property="displayIndex"/>
        <result column="commandtype" property="commandType"/>
        <result column="controltype" property="controlType"/>
        <result column="datatype" property="dataType"/>
        <result column="maxvalue" property="maxValue"/>
        <result column="minvalue" property="minValue"/>
        <result column="defaultvalue" property="defaultValue"/>
        <result column="moduleno" property="moduleNo"/>
        <collection property="controlMeaningsList" ofType="com.siteweb.tcs.siteweb.entity.ControlMeanings" column="equipmenttemplateid,controlid" notNullColumn="meaningsId">
            <id column="meaningsId" property="id"/>
            <result column="equipmenttemplateid" property="equipmentTemplateId"/>
            <result column="controlid" property="controlId"/>
            <result column="ParameterValue" property="parameterValue"/>
            <result column="Meanings" property="meanings"/>
            <result column="BaseCondId" property="baseCondId"/>
        </collection>
    </resultMap>
    <resultMap id="ControlConfigItemResultMap" type="com.siteweb.tcs.siteweb.dto.ControlConfigItem">
        <id property="id" column="Id"/>
        <result property="equipmentTemplateId" column="EquipmentTemplateId"/>
        <result property="controlId" column="ControlId"/>
        <result property="controlName" column="ControlName"/>
        <result property="controlCategory" column="ControlCategory"/>
        <result property="cmdToken" column="CmdToken"/>
        <result property="baseTypeId" column="BaseTypeId"/>
        <result property="controlSeverity" column="ControlSeverity"/>
        <result property="signalId" column="SignalId"/>
        <result property="timeOut" column="TimeOut"/>
        <result property="retry" column="Retry"/>
        <result property="description" column="Description"/>
        <result property="enable" column="Enable"/>
        <result property="visible" column="Visible"/>
        <result property="displayIndex" column="DisplayIndex"/>
        <result property="commandType" column="CommandType"/>
        <result property="controlType" column="ControlType"/>
        <result property="dataType" column="DataType"/>
        <result property="maxValue" column="MaxValue"/>
        <result property="minValue" column="MinValue"/>
        <result property="defaultValue" column="DefaultValue"/>
        <result property="moduleNo" column="ModuleNo"/>
        <collection property="controlMeaningsList" ofType="com.siteweb.tcs.siteweb.entity.ControlMeanings">
            <id property="id" column="CM_Id"/>
            <result property="equipmentTemplateId" column="CM_EquipmentTemplateId"/>
            <result property="controlId" column="CM_ControlId"/>
            <result property="parameterValue" column="ParameterValue"/>
            <result property="meanings" column="Meanings"/>
            <result property="baseCondId" column="BaseCondId"/>
        </collection>
    </resultMap>

    <select id="findMaxControlIdByEquipmentTemplateId" resultType="java.lang.Integer">
        SELECT COALESCE(MAX(ControlId), 0) FROM tbl_control WHERE EquipmentTemplateId = #{equipmentTemplateId}
    </select>

    <select id="findMaxDisplayIndexByEquipmentTemplateId" resultType="java.lang.Integer">
        SELECT COALESCE(MAX(DisplayIndex), 0) FROM tbl_control WHERE EquipmentTemplateId = #{equipmentTemplateId}
    </select>

    <delete id="deleteControl">
        DELETE FROM tbl_control WHERE EquipmentTemplateId = #{equipmentTemplateId} AND ControlId = #{controlId}
    </delete>
    
    <select id="findControlItemByEquipmentTemplateId" resultMap="ControlConfigItemResultMap">
        SELECT 
            c.*,
            cm.Id AS CM_Id, 
            cm.EquipmentTemplateId AS CM_EquipmentTemplateId, 
            cm.ControlId AS CM_ControlId,
            cm.ParameterValue, 
            cm.Meanings, 
            cm.BaseCondId
        FROM tbl_control c
        LEFT JOIN tbl_controlmeanings cm ON c.EquipmentTemplateId = cm.EquipmentTemplateId AND c.ControlId = cm.ControlId
        WHERE c.EquipmentTemplateId = #{equipmentTemplateId}
        ORDER BY c.DisplayIndex, cm.ParameterValue
    </select>

    <select id="findByEquipmentTemplateIdAndControlId" resultMap="ControlConfigItemResultMap">
        SELECT 
            c.*,
            cm.Id AS CM_Id, 
            cm.EquipmentTemplateId AS CM_EquipmentTemplateId, 
            cm.ControlId AS CM_ControlId,
            cm.ParameterValue, 
            cm.Meanings, 
            cm.BaseCondId
        FROM tbl_control c
        LEFT JOIN tbl_controlmeanings cm ON c.EquipmentTemplateId = cm.EquipmentTemplateId AND c.ControlId = cm.ControlId
        WHERE c.EquipmentTemplateId = #{equipmentTemplateId} AND c.ControlId = #{controlId}
        ORDER BY cm.ParameterValue
    </select>
    
    <insert id="insertControl" parameterType="com.siteweb.tcs.siteweb.entity.Control" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tbl_control (
            EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, 
            ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, 
            CommandType, ControlType, DataType, MaxValue, MinValue, DefaultValue, ModuleNo
        ) VALUES (
            #{equipmentTemplateId}, #{controlId}, #{controlName}, #{controlCategory}, #{cmdToken}, #{baseTypeId}, 
            #{controlSeverity}, #{signalId}, #{timeOut}, #{retry}, #{description}, #{enable}, #{visible}, #{displayIndex}, 
            #{commandType}, #{controlType}, #{dataType}, #{maxValue}, #{minValue}, #{defaultValue}, #{moduleNo}
        )
    </insert>
    <insert id="batchInsertControl">
        INSERT INTO tbl_control(equipmenttemplateid, controlid, controlname, controlcategory, cmdtoken, basetypeid,
        controlseverity, signalid, timeout, retry, description, enable, visible, displayindex,
        commandtype, controltype, datatype, maxvalue, minvalue, defaultvalue, moduleno) VALUES
        <foreach collection="controlList" item="control" separator=",">
            (#{control.equipmentTemplateId}, #{control.controlId}, #{control.controlName}, #{control.controlCategory},
            #{control.cmdToken}, #{control.baseTypeId}, #{control.controlSeverity}, #{control.signalId},
            #{control.timeOut}, #{control.retry}, #{control.description}, #{control.enable}, #{control.visible},
            #{control.displayIndex}, #{control.commandType}, #{control.controlType}, #{control.dataType},
            #{control.maxValue}, #{control.minValue}, #{control.defaultValue}, #{control.moduleNo})
        </foreach>
    </insert>

    <update id="updateControl" parameterType="com.siteweb.tcs.siteweb.entity.Control">
        UPDATE tbl_control
        <set>
            <if test="controlName != null">ControlName = #{controlName},</if>
            <if test="controlCategory != null">ControlCategory = #{controlCategory},</if>
            <if test="cmdToken != null">CmdToken = #{cmdToken},</if>
            <if test="baseTypeId != null">BaseTypeId = #{baseTypeId},</if>
            <if test="controlSeverity != null">ControlSeverity = #{controlSeverity},</if>
            <if test="signalId != null">SignalId = #{signalId},</if>
            <if test="timeOut != null">TimeOut = #{timeOut},</if>
            <if test="retry != null">Retry = #{retry},</if>
            <if test="description != null">Description = #{description},</if>
            <if test="enable != null">Enable = #{enable},</if>
            <if test="visible != null">Visible = #{visible},</if>
            <if test="displayIndex != null">DisplayIndex = #{displayIndex},</if>
            <if test="commandType != null">CommandType = #{commandType},</if>
            <if test="controlType != null">ControlType = #{controlType},</if>
            <if test="dataType != null">DataType = #{dataType},</if>
            <if test="maxValue != null">MaxValue = #{maxValue},</if>
            <if test="minValue != null">MinValue = #{minValue},</if>
            <if test="defaultValue != null">DefaultValue = #{defaultValue},</if>
            <if test="moduleNo != null">ModuleNo = #{moduleNo}</if>
        </set>
        WHERE EquipmentTemplateId = #{equipmentTemplateId} AND ControlId = #{controlId}
    </update>

    <!-- 查找设备模板中不在控制基类字典中的基类ID -->
    <select id="findBaseTypeIdsNotInControlBaseDicForEquipmentTemplate" resultType="java.lang.Long">
        SELECT DISTINCT c.BaseTypeId
        FROM TBL_Control c
        WHERE c.EquipmentTemplateId = #{equipmentTemplateId}
        AND c.BaseTypeId IS NOT NULL
        AND c.BaseTypeId != 0
        AND c.BaseTypeId NOT IN (SELECT BaseTypeId FROM TBL_CommandBaseDic)
    </select>
    <select id="findExcelDtoByEquipmentTemplateId" resultType="com.siteweb.tcs.siteweb.dto.excel.ControlExcel">
        SELECT b.EquipmentTemplateId,
        a.EquipmentId,
        a.EquipmentName,
        b.ControlId,
        b.ControlName,
        c.ItemValue,
        b.cmdToken,
        b.maxvalue,
        b.minvalue,
        d.ParameterValue,
        d.Meanings
        FROM tbl_control b
        LEFT JOIN
        tbl_equipment a
        ON a.EquipmentTemplateId = b.EquipmentTemplateId
        LEFT JOIN
        tbl_dataitem c ON c.EntryId = 31
        AND c.ItemId = b.ControlCategory
        LEFT JOIN
        tbl_controlmeanings d
        ON d.EquipmentTemplateId = b.EquipmentTemplateId
        AND d.ControlId = b.ControlId
        WHERE b.EquipmentTemplateId = #{equipmentTemplateId}
    </select>
    <select id="findVoByEquipmentTemplateId" resultMap="findVoMap">
        SELECT a.id,
        a.equipmenttemplateid,
        a.controlid,
        a.controlname,
        a.controlcategory,
        a.cmdtoken,
        a.basetypeid,
        a.controlseverity,
        a.signalid,
        a.timeout,
        a.retry,
        a.description,
        a.enable,
        a.visible,
        a.displayindex,
        a.commandtype,
        a.controltype,
        a.datatype,
        a.maxvalue,
        a.minvalue,
        a.defaultvalue,
        a.moduleno,
        b.Id AS meaningsId,
        b.ParameterValue,
        b.Meanings,
        b.BaseCondId
        FROM tbl_control a
        LEFT JOIN tbl_controlmeanings b
        ON a.EquipmentTemplateId = b.EquipmentTemplateId AND a.ControlId = b.ControlId
        WHERE a.EquipmentTemplateId = #{equipmentTemplateId}
    </select>

    <update id="batchUpdateField">
        <foreach collection="controlList" item="control" separator=";">
            UPDATE tbl_control
            <set>
                <if test="control.equipmentTemplateId != null">
                    EquipmentTemplateId = #{control.equipmentTemplateId},
                </if>
                <if test="control.controlName != null">
                    ControlName = #{control.controlName},
                </if>
                <if test="control.controlCategory != null">
                    ControlCategory = #{control.controlCategory},
                </if>
                <if test="control.cmdToken != null">
                    CmdToken = #{control.cmdToken},
                </if>
                <if test="control.cmdToken != control.baseTypeId">
                    BaseTypeId = #{control.baseTypeId},
                </if>
                <if test="control.controlSeverity != null">
                    ControlSeverity = #{control.controlSeverity},
                </if>
                <if test="control.signalId != null">
                    SignalId = #{control.signalId},
                </if>
                <if test="control.timeOut != null">
                    TimeOut = #{control.timeOut},
                </if>
                <if test="control.retry != null">
                    Retry = #{control.retry},
                </if>
                <if test="control.description != null">
                    Description = #{control.description},
                </if>
                <if test="control.enable != null">
                    Enable = #{control.enable},
                </if>
                <if test="control.visible != null">
                    Visible = #{control.visible},
                </if>
                <if test="control.displayIndex != null">
                    DisplayIndex = #{control.displayIndex},
                </if>
                <if test="control.commandType != null">
                    CommandType = #{control.commandType},
                </if>
                <if test="control.controlType != null">
                    ControlType = #{control.controlType},
                </if>
                <if test="control.dataType != null">
                    DataType = #{control.dataType},
                </if>
                <if test="control.maxValue != null">
                    "maxvalue" = #{control.maxValue},
                </if>
                <if test="control.minValue != null">
                    "minvalue" = #{control.minValue},
                </if>
                <if test="control.defaultValue != null">
                    DefaultValue = #{control.defaultValue},
                </if>
                <if test="control.moduleNo != null">
                    ModuleNo = #{control.moduleNo},
                </if>
            </set>
            WHERE EquipmentTemplateId = #{control.equipmentTemplateId} and ControlId = #{control.controlId}
        </foreach>
    </update>

</mapper>
