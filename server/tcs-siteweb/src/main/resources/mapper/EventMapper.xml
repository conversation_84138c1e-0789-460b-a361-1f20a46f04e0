<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.siteweb.mapper.EventMapper">

    <!-- Event结果映射 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.siteweb.entity.Event">
        <id column="Id" property="id" />
        <result column="EquipmentTemplateId" property="equipmentTemplateId" />
        <result column="EventId" property="eventId" />
        <result column="EventName" property="eventName" />
        <result column="StartType" property="startType" />
        <result column="EndType" property="endType" />
        <result column="StartExpression" property="startExpression" />
        <result column="SuppressExpression" property="suppressExpression" />
        <result column="EventCategory" property="eventCategory" />
        <result column="SignalId" property="signalId" />
        <result column="Enable" property="enable" />
        <result column="Visible" property="visible" />
        <result column="Description" property="description" />
        <result column="DisplayIndex" property="displayIndex" />
        <result column="ModuleNo" property="moduleNo" />
    </resultMap>
    
    <!-- EventConfigItem结果映射 -->
    <resultMap id="EventConfigItemMap" type="com.siteweb.tcs.siteweb.dto.EventConfigItem">
        <id column="Id" property="id" />
        <result column="EquipmentTemplateId" property="equipmentTemplateId" />
        <result column="EventId" property="eventId" />
        <result column="EventName" property="eventName" />
        <result column="StartType" property="startType" />
        <result column="EndType" property="endType" />
        <result column="StartExpression" property="startExpression" />
        <result column="SuppressExpression" property="suppressExpression" />
        <result column="EventCategory" property="eventCategory" />
        <result column="SignalId" property="signalId" />
        <result column="Enable" property="enable" />
        <result column="Visible" property="visible" />
        <result column="Description" property="description" />
        <result column="DisplayIndex" property="displayIndex" />
        <result column="ModuleNo" property="moduleNo" />
        <result column="Turnover" property="turnover" />
        <collection property="eventConditionList" ofType="com.siteweb.tcs.siteweb.dto.EventConditionDTO">
            <id column="CondId" property="id" />
            <result column="EventConditionId" property="eventConditionId" />
            <result column="CondEquipmentTemplateId" property="equipmentTemplateId" />
            <result column="CondEventId" property="eventId" />
            <result column="StartOperation" property="startOperation" />
            <result column="StartCompareValue" property="startCompareValue" />
            <result column="StartDelay" property="startDelay" />
            <result column="EndOperation" property="endOperation" />
            <result column="EndCompareValue" property="endCompareValue" />
            <result column="EndDelay" property="endDelay" />
            <result column="Frequency" property="frequency" />
            <result column="FrequencyThreshold" property="frequencyThreshold" />
            <result column="Meanings" property="meanings" />
            <result column="EquipmentState" property="equipmentState" />
            <result column="BaseTypeId" property="baseTypeId" />
            <result column="EventSeverity" property="eventSeverity" />
            <result column="StandardName" property="standardName" />
        </collection>
    </resultMap>
    
    <!-- 基础列 -->
    <sql id="Base_Column_List">
        Id, EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, 
        SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo
    </sql>
    
    <!-- 根据设备模板ID查询事件配置项 -->
    <select id="findEventItemByEquipmentTemplateId" resultMap="EventConfigItemMap">
        SELECT 
            e.Id, e.EquipmentTemplateId, e.EventId, e.EventName, e.StartType, e.EndType, e.StartExpression, 
            e.SuppressExpression, e.EventCategory, e.SignalId, e.Enable, e.Visible, e.Description, e.DisplayIndex, e.ModuleNo,
            ex.Turnover,
            ec.Id AS CondId, ec.EventConditionId, ec.EquipmentTemplateId AS CondEquipmentTemplateId, ec.EventId AS CondEventId, 
            ec.StartOperation, ec.StartCompareValue, ec.StartDelay, ec.EndOperation, ec.EndCompareValue, ec.EndDelay, 
            ec.Frequency, ec.FrequencyThreshold, ec.Meanings, ec.EquipmentState, ec.BaseTypeId, ec.EventSeverity, ec.StandardName
        FROM 
            tbl_event e
        LEFT JOIN 
            tbl_eventcondition ec ON e.EquipmentTemplateId = ec.EquipmentTemplateId AND e.EventId = ec.EventId
        LEFT JOIN 
            tbl_eventex ex ON e.EquipmentTemplateId = ex.EquipmentTemplateId AND e.EventId = ex.EventId
        WHERE 
            e.EquipmentTemplateId = #{equipmentTemplateId}
        ORDER BY 
            e.DisplayIndex, e.EventId, ec.EventConditionId
    </select>
    
    <!-- 根据设备模板ID和事件ID查询事件配置项 -->
    <select id="findByEquipmentTemplateIdAndEventId" resultMap="EventConfigItemMap">
        SELECT 
            e.Id, e.EquipmentTemplateId, e.EventId, e.EventName, e.StartType, e.EndType, e.StartExpression, 
            e.SuppressExpression, e.EventCategory, e.SignalId, e.Enable, e.Visible, e.Description, e.DisplayIndex, e.ModuleNo,
            ex.Turnover,
            ec.Id AS CondId, ec.EventConditionId, ec.EquipmentTemplateId AS CondEquipmentTemplateId, ec.EventId AS CondEventId, 
            ec.StartOperation, ec.StartCompareValue, ec.StartDelay, ec.EndOperation, ec.EndCompareValue, ec.EndDelay, 
            ec.Frequency, ec.FrequencyThreshold, ec.Meanings, ec.EquipmentState, ec.BaseTypeId, ec.EventSeverity, ec.StandardName
        FROM 
            tbl_event e
        LEFT JOIN 
            tbl_eventcondition ec ON e.EquipmentTemplateId = ec.EquipmentTemplateId AND e.EventId = ec.EventId
        LEFT JOIN 
            tbl_eventex ex ON e.EquipmentTemplateId = ex.EquipmentTemplateId AND e.EventId = ex.EventId
        WHERE 
            e.EquipmentTemplateId = #{equipmentTemplateId} AND e.EventId = #{eventId}
        ORDER BY 
            ec.EventConditionId
    </select>
    
    <!-- 根据设备模板ID查询最大事件ID -->
    <select id="findMaxEventIdByEquipmentTemplateId" resultType="java.lang.Integer">
        SELECT 
            COALESCE(MAX(EventId), 0) 
        FROM 
            tbl_event 
        WHERE 
            EquipmentTemplateId = #{equipmentTemplateId}
    </select>
    
    <!-- 删除事件 -->
    <delete id="deleteEvent">
        DELETE FROM 
            tbl_event 
        WHERE 
            EquipmentTemplateId = #{equipmentTemplateId} AND EventId = #{eventId}
    </delete>
    
    <!-- 批量更新事件 -->
    <update id="batchUpdate">
        <foreach collection="eventList" item="event" separator=";">
            UPDATE tbl_event
            <set>
                <if test="event.eventName != null">EventName = #{event.eventName},</if>
                <if test="event.startType != null">StartType = #{event.startType},</if>
                <if test="event.endType != null">EndType = #{event.endType},</if>
                <if test="event.startExpression != null">StartExpression = #{event.startExpression},</if>
                <if test="event.suppressExpression != null">SuppressExpression = #{event.suppressExpression},</if>
                <if test="event.eventCategory != null">EventCategory = #{event.eventCategory},</if>
                <if test="event.signalId != null">SignalId = #{event.signalId},</if>
                <if test="event.enable != null">Enable = #{event.enable},</if>
                <if test="event.visible != null">Visible = #{event.visible},</if>
                <if test="event.description != null">Description = #{event.description},</if>
                <if test="event.displayIndex != null">DisplayIndex = #{event.displayIndex},</if>
                <if test="event.moduleNo != null">ModuleNo = #{event.moduleNo}</if>
            </set>
            WHERE EquipmentTemplateId = #{event.equipmentTemplateId} AND EventId = #{event.eventId}
        </foreach>
    </update>
    
    <!-- 根据设备模板ID查询最大显示索引 -->
    <select id="findMaxDisplayIndexByEquipmentTemplateId" resultType="java.lang.Integer">
        SELECT 
            COALESCE(MAX(DisplayIndex), 0) 
        FROM 
            tbl_event 
        WHERE 
            EquipmentTemplateId = #{equipmentTemplateId}
    </select>

    <!-- 更新工作站事件名称 -->
    <update id="updateWorkStationEventName">
        UPDATE tbl_event
        SET EventName = CONCAT(#{prefix}, EventName)
        WHERE EquipmentTemplateId = #{equipmentTemplateId}
    </update>

    <!-- 更新自诊断事件ID、启动表达式和信号ID -->
    <update id="updateEventIdAndStartExpressionAndSignalId">
        UPDATE tbl_event
        SET EventId = #{centerId} * 1000000 + EventId,
            StartExpression = CONCAT('[', '-1', ',', #{centerId} * 1000000 + EventId, ']'),
            SignalId = #{centerId} * 1000000 + SignalId
        WHERE EquipmentTemplateId = #{equipmentTemplateId};
        UPDATE tbl_eventcondition
        SET EventId = #{centerId} * 1000000 + EventId
        WHERE EquipmentTemplateId = #{equipmentTemplateId};
    </update>

    <!-- 查找设备模板下最大的事件配置项 -->
    <select id="findMaxEventByEquipmentTemplateId" resultMap="EventConfigItemMap">
        SELECT 
            e.Id, e.EquipmentTemplateId, e.EventId, e.EventName, e.StartType, e.EndType, e.StartExpression, 
            e.SuppressExpression, e.EventCategory, e.SignalId, e.Enable, e.Visible, e.Description, e.DisplayIndex, e.ModuleNo,
            ex.Turnover,
            ec.Id AS CondId, ec.EventConditionId, ec.EquipmentTemplateId AS CondEquipmentTemplateId, ec.EventId AS CondEventId, 
            ec.StartOperation, ec.StartCompareValue, ec.StartDelay, ec.EndOperation, ec.EndCompareValue, ec.EndDelay, 
            ec.Frequency, ec.FrequencyThreshold, ec.Meanings, ec.EquipmentState, ec.BaseTypeId, ec.EventSeverity, ec.StandardName
        FROM 
            tbl_event e
        LEFT JOIN 
            tbl_eventcondition ec ON e.EquipmentTemplateId = ec.EquipmentTemplateId AND e.EventId = ec.EventId
        LEFT JOIN 
            tbl_eventex ex ON e.EquipmentTemplateId = ex.EquipmentTemplateId AND e.EventId = ex.EventId
        WHERE 
            e.EquipmentTemplateId = #{equipmentTemplateId}
        ORDER BY 
            e.EventId DESC
        LIMIT 1
    </select>

    <!-- 查找设备模板中不在事件基类字典中的基类ID -->
    <select id="findBaseTypeIdsNotInEventBaseDicForEquipmentTemplate" resultType="java.lang.Long">
        SELECT DISTINCT ec.BaseTypeId
        FROM TBL_EventCondition ec
        WHERE ec.EquipmentTemplateId = #{equipmentTemplateId}
        AND ec.BaseTypeId IS NOT NULL
        AND ec.BaseTypeId != 0
        AND ec.BaseTypeId NOT IN (SELECT BaseTypeId FROM TBL_EventBaseDic)
    </select>
    <select id="findExcelDtoByEquipmentTemplateId" resultType="com.siteweb.tcs.siteweb.dto.excel.EventExcel">
        SELECT b.EquipmentId,
        b.EquipmentName,
        a.EventId,
        a.EventName,
        c.Id,
        c.eventconditionId,
        c.EquipmentTemplateId,
        c.StartOperation,
        c.StartCompareValue,
        c.StartDelay,
        c.EndOperation,
        c.EndCompareValue,
        c.EndDelay,
        c.Frequency,
        c.FrequencyThreshold,
        c.Meanings,
        c.EquipmentState,
        c.BaseTypeId,
        c.EventSeverity
        FROM tbl_event a
        LEFT JOIN
        tbl_equipment b ON b.EquipmentTemplateid = a.EquipmentTemplateId
        LEFT JOIN
        tbl_eventcondition c ON c.EquipmentTemplateId = a.EquipmentTemplateId AND c.EventId = a.EventId
        WHERE a.EquipmentTemplateId = #{equipmentTemplateId}
    </select>
</mapper>
