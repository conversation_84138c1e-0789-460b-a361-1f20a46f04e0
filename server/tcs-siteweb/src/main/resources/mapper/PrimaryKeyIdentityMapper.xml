<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.siteweb.mapper.PrimaryKeyIdentityMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.siteweb.entity.PrimaryKeyIdentity">
        <id column="TableId" property="tableId" />
        <result column="TableName" property="tableName" />
        <result column="Description" property="description" />
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        TableId, TableName, Description
    </sql>

    <select id="findTableIdByTableName" resultType="java.lang.Integer">
        SELECT table_id
        FROM primary_key_identity
        WHERE table_name = #{tableName}
    </select>
</mapper>
