<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.siteweb.mapper.OperationDetailMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.siteweb.entity.OperationDetail">
        <result column="UserId" property="userId"/>
        <result column="ObjectId" property="objectId"/>
        <result column="ObjectType" property="objectType"/>
        <result column="PropertyName" property="propertyName"/>
        <result column="OperationTime" property="operationTime"/>
        <result column="OperationType" property="operationType"/>
        <result column="OldValue" property="oldValue"/>
        <result column="NewValue" property="newValue"/>
    </resultMap>
    
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        UserId, ObjectId, ObjectType, PropertyName, OperationTime, OperationType, OldValue, NewValue
    </sql>
    
    <!-- 批量插入 -->
    <insert id="insertBatchSomeColumn">
        INSERT INTO tbl_operationdetail (
            UserId, ObjectId, ObjectType, PropertyName, OperationTime, OperationType, OldValue, NewValue
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.userId}, #{item.objectId}, #{item.objectType}, #{item.propertyName}, 
                #{item.operationTime}, #{item.operationType}, #{item.oldValue}, #{item.newValue}
            )
        </foreach>
    </insert>
    
    <!-- 分页查询操作日志 -->
    <select id="findPage" resultType="com.siteweb.tcs.siteweb.vo.OperationDetailVO">
        SELECT
            u.UserName, o.ObjectId, o.ObjectType, o.PropertyName, o.OperationTime, o.OperationType, o.OldValue, o.NewValue
        FROM
            tbl_operationdetail o LEFT JOIN tbl_account u ON o.UserId = u.UserId
        <where>
            <if test="operationDetailDTO.startTime != null">
                AND o.OperationTime &gt;= #{operationDetailDTO.startTime}
            </if>
            <if test="operationDetailDTO.endTime != null">
                AND o.OperationTime &lt;= #{operationDetailDTO.endTime}
            </if>
            <if test="operationDetailDTO.userId != null">
                AND o.UserId = #{operationDetailDTO.userId}
            </if>
            <if test="operationDetailDTO.objectId != null and operationDetailDTO.objectId != ''">
                AND o.ObjectId = #{operationDetailDTO.objectId}
            </if>
            <if test="operationDetailDTO.propertyName != null and operationDetailDTO.propertyName != ''">
                AND o.PropertyName = #{operationDetailDTO.propertyName}
            </if>
            <if test="operationDetailDTO.objectTypes != null and operationDetailDTO.objectTypes.size > 0">
                AND o.ObjectType IN
                <foreach collection="operationDetailDTO.objectTypes" item="objectType" open="(" close=")" separator=",">
                    #{objectType}
                </foreach>
            </if>
        </where>
        ORDER BY o.OperationTime DESC
    </select>
    
    <!-- 查询设备模板相关的操作日志 -->
    <select id="findEquipmentTemplateLogPage" resultType="com.siteweb.tcs.siteweb.vo.OperationDetailVO">
        SELECT
            u.UserName, o.ObjectId, o.ObjectType, o.PropertyName, o.OperationTime, o.OperationType, o.OldValue, o.NewValue
        FROM
            tbl_operationdetail o LEFT JOIN tbl_account u ON o.UserId = u.UserId
        WHERE o.ObjectType IN (10, 12, 15, 17) AND o.ObjectId LIKE CONCAT(#{operationDetailDTO.objectId}, '%')
        <if test="operationDetailDTO.startTime != null">
            AND o.OperationTime &gt;= #{operationDetailDTO.startTime}
        </if>
        <if test="operationDetailDTO.endTime != null">
            AND o.OperationTime &lt;= #{operationDetailDTO.endTime}
        </if>
        <if test="operationDetailDTO.userId != null">
            AND o.UserId = #{operationDetailDTO.userId}
        </if>
        <if test="operationDetailDTO.propertyName != null and operationDetailDTO.propertyName != ''">
            AND o.PropertyName = #{operationDetailDTO.propertyName}
        </if>
        ORDER BY o.OperationTime DESC
    </select>
    
    <!-- 查询设备相关的操作日志 -->
    <select id="findEquipmentLogPage" resultType="com.siteweb.tcs.siteweb.vo.OperationDetailVO">
        SELECT
            u.UserName, o.ObjectId, o.ObjectType, o.PropertyName, o.OperationTime, o.OperationType, o.OldValue, o.NewValue
        FROM
            tbl_operationdetail o LEFT JOIN tbl_account u ON o.UserId = u.UserId
        WHERE ((o.ObjectType IN (12, 15, 17) AND o.ObjectId LIKE CONCAT(#{equipmentTemplateId}, '%')) 
               OR (o.ObjectType = 11 AND o.ObjectId = #{operationDetailDTO.objectId}))
        <if test="operationDetailDTO.startTime != null">
            AND o.OperationTime &gt;= #{operationDetailDTO.startTime}
        </if>
        <if test="operationDetailDTO.endTime != null">
            AND o.OperationTime &lt;= #{operationDetailDTO.endTime}
        </if>
        <if test="operationDetailDTO.userId != null">
            AND o.UserId = #{operationDetailDTO.userId}
        </if>
        <if test="operationDetailDTO.propertyName != null and operationDetailDTO.propertyName != ''">
            AND o.PropertyName = #{operationDetailDTO.propertyName}
        </if>
        ORDER BY o.OperationTime DESC
    </select>

    <!-- ==================== 按tcs-config原始接口的SQL实现 ==================== -->

    <!-- 根据对象类型和对象ID分页查询操作日志 -->
    <select id="findPageByObjectTypeAndObjectId" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            tbl_operationdetail o
        WHERE o.ObjectType = #{objectType} AND o.ObjectId = #{objectId}
        ORDER BY o.OperationTime DESC
    </select>

    <!-- 获取操作类型列表 -->
    <select id="findOperationTypes" resultType="java.lang.String">
        SELECT DISTINCT OperationType
        FROM tbl_operationdetail
        WHERE OperationType IS NOT NULL AND OperationType != ''
        ORDER BY OperationType
    </select>
</mapper>
