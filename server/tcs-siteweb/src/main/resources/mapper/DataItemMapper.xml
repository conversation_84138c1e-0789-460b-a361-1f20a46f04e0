<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.siteweb.mapper.DataItemMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.siteweb.entity.DataItem">
        <id column="EntryItemId" property="entryItemId" />
        <result column="ParentEntryId" property="parentEntryId" />
        <result column="ParentItemId" property="parentItemId" />
        <result column="EntryId" property="entryId" />
        <result column="ItemId" property="itemId" />
        <result column="ItemValue" property="itemValue" />
        <result column="ItemAlias" property="itemAlias" />
        <result column="Enable" property="enable" />
        <result column="IsSystem" property="isSystem" />
        <result column="IsDefault" property="isDefault" />
        <result column="Description" property="description" />
        <result column="ExtendField1" property="extendField1" />
        <result column="ExtendField2" property="extendField2" />
        <result column="ExtendField3" property="extendField3" />
        <result column="ExtendField4" property="extendField4" />
        <result column="ExtendField5" property="extendField5" />
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias,
        Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3,
        ExtendField4, ExtendField5
    </sql>

    <select id="findItemIdByEntryId" resultType="java.lang.Integer">
        SELECT ItemId
        FROM tbl_dataitem
        WHERE EntryId = #{entryId,jdbcType=VARCHAR}
          AND Enable = 1 <!-- Assuming active items have Enable = 1 -->
        <choose>
            <when test="_databaseId == 'mysql'">
                LIMIT 1
            </when>
            <when test="_databaseId == 'postgresql'">
                LIMIT 1
            </when>
            <otherwise>
                LIMIT 1
            </otherwise>
        </choose>
    </select>
    <select id="findEquipmentCategory" resultType="com.siteweb.tcs.siteweb.entity.DataItem">
        SELECT DISTINCT b.ItemId,
        b.ItemValue
        FROM TBL_EquipmentTemplate a,
        TBL_DataItem b
        WHERE a.EquipmentCategory = b.ItemId
        AND b.EntryId = 7
    </select>
</mapper>
