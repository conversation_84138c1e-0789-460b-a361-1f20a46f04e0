<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.siteweb.mapper.DataItemMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.siteweb.entity.DataItem">
        <!-- TODO: 请根据DataItem.java实体字段补充映射关系 -->
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        <!-- TODO: 请根据DataItem.java实体字段补充列名 -->
    </sql>

    <select id="findItemIdByEntryId" resultType="java.lang.Integer">
        SELECT item_id
        FROM tbl_dataitem
        WHERE entry_id = #{entryId,jdbcType=VARCHAR}
          AND status = 1 <!-- Assuming active items have status 1 -->
        LIMIT 1;
    </select>
    <select id="findEquipmentCategory" resultType="com.siteweb.tcs.siteweb.entity.DataItem">
        SELECT DISTINCT b.ItemId,
        b.ItemValue
        FROM TBL_EquipmentTemplate a,
        TBL_DataItem b
        WHERE a.EquipmentCategory = b.ItemId
        AND b.EntryId = 7
    </select>
</mapper>
