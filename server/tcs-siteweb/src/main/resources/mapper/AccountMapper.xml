<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.siteweb.mapper.AccountMapper">
    
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.siteweb.entity.Account">
        <id column="UserId" property="userId" />
        <result column="UserName" property="userName" />
        <result column="LogonId" property="logonId" />
        <result column="Password" property="password" />
        <result column="Enable" property="enable" />
        <result column="MaxError" property="maxError" />
        <result column="Locked" property="locked" />
        <result column="ValidTime" property="validTime" />
        <result column="Description" property="description" />
        <result column="IsRemote" property="remote" />
        <result column="CenterId" property="centerId" />
        <result column="PasswordValidTime" property="passwordValidTime" />
        <result column="Avatar" property="avatar" />
        <result column="ThemeName" property="themeName" />
        <result column="NeedResetPwd" property="needResetPwd" />
    </resultMap>
    
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        UserId, UserName, LogonId, Password, Enable,
        MaxError, Locked, ValidTime, Description, IsRemote,
        CenterId, PasswordValidTime, Avatar, ThemeName, NeedResetPwd
    </sql>

    <!-- 根据手机号查找账户 -->
    <select id="findByMobile" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tbl_account 
        WHERE UserId IN (SELECT EmployeeId FROM tbl_employee WHERE Mobile = #{mobile})
    </select>

    <!-- updateCenterIdForNegativeUserId 方法已迁移到 Service 层实现 -->
    
</mapper> 