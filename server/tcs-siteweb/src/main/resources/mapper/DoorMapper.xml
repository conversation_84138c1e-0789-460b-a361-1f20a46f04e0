<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.siteweb.mapper.DoorMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.siteweb.entity.Door">
        <id column="DoorId" property="doorId" />
        <result column="DoorNo" property="doorNo" />
        <result column="DoorName" property="doorName" />
        <result column="StationId" property="stationId" />
        <result column="EquipmentId" property="equipmentId" />
        <result column="SamplerUnitId" property="samplerUnitId" />
        <result column="Category" property="category" />
        <result column="Address" property="address" />
        <result column="WorkMode" property="workMode" />
        <result column="Infrared" property="infrared" />
        <result column="Password" property="password" />
        <result column="DoorControlId" property="doorControlId" />
        <result column="DoorInterval" property="doorInterval" />
        <result column="OpenDelay" property="openDelay" />
        <result column="Description" property="description" />
        <result column="OpenMode" property="openMode" />
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        DoorId, DoorNo, DoorName, StationId, EquipmentId, SamplerUnitId, Category, Address,
        WorkMode, Infrared, Password, DoorControlId, DoorInterval, OpenDelay, Description, OpenMode
    </sql>

    <!-- 查找门控制器ID -->
    <select id="findDoorControlId" resultType="java.lang.Integer">
        SELECT 
            s.SignalChannelNo
        FROM 
            tbl_signal s
        INNER JOIN 
            tbl_equipment e ON s.EquipmentTemplateId = e.EquipmentTemplateId
        WHERE
            e.EquipmentId = #{equipmentId}
            AND s.SignalChannelType = 39
        <choose>
            <when test="_databaseId == 'mysql'">
                LIMIT 1
            </when>
            <when test="_databaseId == 'postgresql'">
                LIMIT 1
            </when>
            <otherwise>
                LIMIT 1
            </otherwise>
        </choose>
    </select>

    <!-- 查找门号，门控制器类型为4 -->
    <select id="findDoorNoByEquipmentIdAndFour" resultType="java.lang.Integer">
        SELECT 
            s.SignalChannelNo
        FROM 
            tbl_signal s
        INNER JOIN 
            tbl_equipment e ON s.EquipmentTemplateId = e.EquipmentTemplateId
        WHERE 
            e.EquipmentId = #{equipmentId}
            AND s.SignalChannelType = 20
            AND s.SignalChannelNo IN (1, 2, 3, 4)
    </select>

    <!-- 查找门号，门控制器类型为12 -->
    <select id="findDoorNoByEquipmentIdAndTwelve" resultType="java.lang.Integer">
        SELECT 
            s.SignalChannelNo
        FROM 
            tbl_signal s
        INNER JOIN 
            tbl_equipment e ON s.EquipmentTemplateId = e.EquipmentTemplateId
        WHERE 
            e.EquipmentId = #{equipmentId}
            AND s.SignalChannelType = 20
            AND s.SignalChannelNo IN (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12)
    </select>

    <!-- 查找门号，门控制器类型为20 -->
    <select id="findDoorNoByEquipmentIdAndTwenty" resultType="java.lang.Integer">
        SELECT 
            s.SignalChannelNo
        FROM 
            tbl_signal s
        INNER JOIN 
            tbl_equipment e ON s.EquipmentTemplateId = e.EquipmentTemplateId
        WHERE 
            e.EquipmentId = #{equipmentId}
            AND s.SignalChannelType = 20
            AND s.SignalChannelNo IN (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20)
    </select>

    <!-- 查找门号，门控制器类型为其他 -->
    <select id="findDoorNoByEquipmentIdAndOther" resultType="java.lang.Integer">
        SELECT 
            s.SignalChannelNo
        FROM 
            tbl_signal s
        INNER JOIN 
            tbl_equipment e ON s.EquipmentTemplateId = e.EquipmentTemplateId
        WHERE 
            e.EquipmentId = #{equipmentId}
            AND s.SignalChannelType = 20
    </select>
</mapper>
