<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.siteweb.mapper.ControlMeaningsMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.siteweb.entity.ControlMeanings">
        <id property="id" column="Id"/>
        <result property="equipmentTemplateId" column="EquipmentTemplateId"/>
        <result property="controlId" column="ControlId"/>
        <result property="parameterValue" column="ParameterValue"/>
        <result property="meanings" column="Meanings"/>
        <result property="baseCondId" column="BaseCondId"/>
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        Id, EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId
    </sql>

    <delete id="deleteByEquipmentTemplateIdAndControlId">
        DELETE FROM tbl_controlmeanings 
        WHERE EquipmentTemplateId = #{equipmentTemplateId} AND ControlId = #{controlId}
    </delete>

    <select id="findByEquipmentTemplateIdAndControlId" resultMap="BaseResultMap">
        SELECT * FROM tbl_controlmeanings
        WHERE EquipmentTemplateId = #{equipmentTemplateId} AND ControlId = #{controlId}
        ORDER BY ParameterValue
    </select>
</mapper>
