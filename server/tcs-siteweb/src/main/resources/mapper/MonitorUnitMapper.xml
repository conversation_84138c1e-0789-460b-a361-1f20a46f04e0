<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.siteweb.mapper.MonitorUnitMapper">
    <!-- 通用查询映射结果 -->

    <select id="selectByMonitorUnitId" resultType="com.siteweb.tcs.siteweb.dto.MonitorUnitDTO">
        SELECT
        mu.MonitorUnitId,
        mu.MonitorUnitName,
        mu.MonitorUnitCategory,
        mu.MonitorUnitCode,
        mu.WorkStationId,
        mu.StationId,
        ts.StationName,
        mu.IpAddress,
        mu.RunMode,
        mu.ConfigFileCode,
        mu.ConfigUpdateTime,
        mu.SampleConfigCode,
        mu.SoftwareVersion,
        mu.Description,
        mu.StartTime,
        mu.HeartbeatTime,
        mu.ConnectState,
        mu.UpdateTime,
        mu.IsSync,
        mu.SyncTime,
        mu.IsConfigOK,
        mu.ConfigFileCode_Old,
        mu.SampleConfigCode_Old,
        mu.AppCongfigId as appConfigId,
        mu.CanDistribute,
        mu.Enable,
        mu.ProjectName as rdsServer,
        mu.ContractNo as dataServer,
        mu.FSU,
        pi.MonitorUnitId,
        pi.ProjectName,
        pi.ContractNo,
        pi.InstallTime,
        ws.WorkStationName,
        GROUP_CONCAT(tp.PortNo ORDER BY tp.PortNo ASC SEPARATOR ',') as portNos
        FROM
        TSL_MonitorUnit mu LEFT JOIN TBL_MonitorUnitProjectInfo pi ON mu.MonitorUnitId = pi.MonitorUnitId
        LEFT JOIN tsl_port tp ON mu.MonitorUnitId = tp.MonitorUnitId
        LEFT JOIN tbl_workstation ws ON mu.WorkStationId = ws.WorkStationId
        LEFT JOIN tbl_station ts ON mu.StationId = ts.StationId
        WHERE
        mu.MonitorUnitId = #{monitorUnitId}
        GROUP BY
        mu.MonitorUnitId
    </select>
    <select id="selectByMonitorIds" resultType="com.siteweb.tcs.siteweb.dto.MonitorUnitDTO">
        SELECT
        mu.MonitorUnitId,
        mu.MonitorUnitName,
        mu.MonitorUnitCategory,
        mu.MonitorUnitCode,
        mu.WorkStationId,
        mu.StationId,
        mu.IpAddress,
        mu.RunMode,
        mu.ConfigFileCode,
        mu.ConfigUpdateTime,
        mu.SampleConfigCode,
        mu.SoftwareVersion,
        mu.Description,
        mu.StartTime,
        mu.HeartbeatTime,
        mu.ConnectState,
        mu.UpdateTime,
        mu.IsSync,
        mu.SyncTime,
        mu.IsConfigOK,
        mu.ConfigFileCode_Old,
        mu.SampleConfigCode_Old,
        mu.AppCongfigId as appConfigId,
        mu.CanDistribute,
        mu.Enable,
        mu.ProjectName as rdsServer,
        mu.ContractNo as dataServer,
        mu.FSU,
        pi.MonitorUnitId,
        pi.ProjectName,
        pi.ContractNo,
        pi.InstallTime,
        ws.WorkStationName,
        GROUP_CONCAT(tp.PortNo ORDER BY tp.PortNo ASC SEPARATOR ',') as portNos
        FROM
        TSL_MonitorUnit mu
        LEFT JOIN TBL_MonitorUnitProjectInfo pi ON mu.MonitorUnitId = pi.MonitorUnitId
        LEFT JOIN tsl_port tp ON mu.MonitorUnitId = tp.MonitorUnitId
        LEFT JOIN tbl_workstation ws ON mu.WorkStationId = ws.WorkStationId
        WHERE
        mu.MonitorUnitId IN
        <foreach collection="monitorUnitIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY
        mu.MonitorUnitId
    </select>

    <select id="selectAll" resultType="com.siteweb.tcs.siteweb.dto.MonitorUnitDTO">
        SELECT
        mu.MonitorUnitId,
        mu.MonitorUnitName,
        mu.MonitorUnitCategory,
        mu.MonitorUnitCode,
        mu.WorkStationId,
        mu.StationId,
        ts.StationName,
        mu.IpAddress,
        mu.RunMode,
        mu.ConfigFileCode,
        mu.ConfigUpdateTime,
        mu.SampleConfigCode,
        mu.SoftwareVersion,
        mu.Description,
        mu.StartTime,
        mu.HeartbeatTime,
        mu.ConnectState,
        mu.UpdateTime,
        mu.IsSync,
        mu.SyncTime,
        mu.IsConfigOK,
        mu.ConfigFileCode_Old,
        mu.SampleConfigCode_Old,
        mu.AppCongfigId as appConfigId,
        mu.CanDistribute,
        mu.Enable,
        mu.ProjectName as rdsServer,
        mu.ContractNo as dataServer,
        mu.FSU,
        pi.MonitorUnitId,
        pi.ProjectName,
        pi.ContractNo,
        pi.InstallTime,
        ws.WorkStationName,
        GROUP_CONCAT(tp.PortNo ORDER BY tp.PortNo ASC SEPARATOR ',') as portNos
        FROM
        TSL_MonitorUnit mu LEFT JOIN TBL_MonitorUnitProjectInfo pi ON mu.MonitorUnitId = pi.MonitorUnitId
        LEFT JOIN tbl_station ts ON mu.StationId = ts.StationId
        LEFT JOIN tsl_port tp ON mu.MonitorUnitId = tp.MonitorUnitId
        LEFT JOIN tbl_workstation ws ON mu.WorkStationId = ws.WorkStationId
        GROUP BY
        mu.MonitorUnitId
    </select>

    <select id="selectAllWithoutStation" resultType="com.siteweb.tcs.siteweb.dto.MonitorUnitDTO">
        SELECT
        mu.MonitorUnitId,
        mu.MonitorUnitName,
        mu.MonitorUnitCategory,
        mu.MonitorUnitCode,
        mu.WorkStationId,
        mu.StationId,
        mu.IpAddress,
        mu.RunMode,
        mu.ConfigFileCode,
        mu.ConfigUpdateTime,
        mu.SampleConfigCode,
        mu.SoftwareVersion,
        mu.Description,
        mu.StartTime,
        mu.HeartbeatTime,
        mu.ConnectState,
        mu.UpdateTime,
        mu.IsSync,
        mu.SyncTime,
        mu.IsConfigOK,
        mu.ConfigFileCode_Old,
        mu.SampleConfigCode_Old,
        mu.AppCongfigId as appConfigId,
        mu.CanDistribute,
        mu.Enable,
        mu.ProjectName as rdsServer,
        mu.ContractNo as dataServer,
        mu.FSU,
        pi.MonitorUnitId,
        pi.ProjectName,
        pi.ContractNo,
        pi.InstallTime,
        ws.WorkStationName,
        GROUP_CONCAT(tp.PortNo ORDER BY tp.PortNo ASC SEPARATOR ',') as portNos
        FROM
        TSL_MonitorUnit mu LEFT JOIN TBL_MonitorUnitProjectInfo pi ON mu.MonitorUnitId = pi.MonitorUnitId
        LEFT JOIN tsl_port tp ON mu.MonitorUnitId = tp.MonitorUnitId
        LEFT JOIN tbl_workstation ws ON mu.WorkStationId = ws.WorkStationId
        GROUP BY
        mu.MonitorUnitId
    </select>
    <select id="selectByMonitorUnitIdWithoutStation" resultType="com.siteweb.tcs.siteweb.dto.MonitorUnitDTO">
        SELECT
        mu.MonitorUnitId,
        mu.MonitorUnitName,
        mu.MonitorUnitCategory,
        mu.MonitorUnitCode,
        mu.WorkStationId,
        mu.StationId,
        mu.IpAddress,
        mu.RunMode,
        mu.ConfigFileCode,
        mu.ConfigUpdateTime,
        mu.SampleConfigCode,
        mu.SoftwareVersion,
        mu.Description,
        mu.StartTime,
        mu.HeartbeatTime,
        mu.ConnectState,
        mu.UpdateTime,
        mu.IsSync,
        mu.SyncTime,
        mu.IsConfigOK,
        mu.ConfigFileCode_Old,
        mu.SampleConfigCode_Old,
        mu.AppCongfigId as appConfigId,
        mu.CanDistribute,
        mu.Enable,
        mu.ProjectName as rdsServer,
        mu.ContractNo as dataServer,
        mu.FSU,
        pi.MonitorUnitId,
        pi.ProjectName,
        pi.ContractNo,
        pi.InstallTime,
        ws.WorkStationName,
        GROUP_CONCAT(tp.PortNo ORDER BY tp.PortNo ASC SEPARATOR ',') as portNos
        FROM
        TSL_MonitorUnit mu LEFT JOIN TBL_MonitorUnitProjectInfo pi ON mu.MonitorUnitId = pi.MonitorUnitId
        LEFT JOIN tsl_port tp ON mu.MonitorUnitId = tp.MonitorUnitId
        LEFT JOIN tbl_workstation ws ON mu.WorkStationId = ws.WorkStationId
        WHERE
        mu.MonitorUnitId = #{monitorUnitId}
        GROUP BY
        mu.MonitorUnitId
    </select>

    <insert id="insertDto">
        INSERT INTO TSL_MonitorUnit (MonitorUnitId, MonitorUnitName, MonitorUnitCategory, MonitorUnitCode,
        WorkStationId, StationId, IpAddress, RunMode, ConfigFileCode,
        ConfigUpdateTime, SampleConfigCode, SoftwareVersion, Description,
        StartTime, HeartbeatTime, ConnectState, UpdateTime, IsSync,
        SyncTime, IsConfigOK, ConfigFileCode_Old, SampleConfigCode_Old, AppCongfigId,
        CanDistribute, Enable, ProjectName, ContractNo, InstallTime, FSU)
        VALUES (#{monitorUnitId}, #{monitorUnitName}, #{monitorUnitCategory}, #{monitorUnitCode},
        #{workStationId}, #{stationId}, #{ipAddress}, #{runMode}, #{configFileCode},
        #{configUpdateTime}, #{sampleConfigCode}, #{softwareVersion}, #{description},
        #{startTime}, #{heartbeatTime}, #{connectState}, #{updateTime}, #{isSync},
        #{syncTime}, #{isConfigOK}, #{configFileCode_Old}, #{sampleConfigCode_Old}, #{appConfigId},
        #{canDistribute}, #{enable}, #{rdsServer}, #{dataServer}, #{installTime}, #{fsu});

        INSERT INTO TBL_MonitorUnitProjectInfo (StationId, MonitorUnitId, ProjectName, ContractNo, InstallTime) VALUES
        (#{stationId}, #{monitorUnitId}, #{projectName}, #{contractNo}, #{installTime});
    </insert>

    <update id="updateDto">
        UPDATE TSL_MonitorUnit SET
        MonitorUnitName = COALESCE(#{monitorUnitName}, MonitorUnitName),
        MonitorUnitCategory = COALESCE(#{monitorUnitCategory}, MonitorUnitCategory),
        MonitorUnitCode = COALESCE(#{monitorUnitCode}, MonitorUnitCode),
        WorkStationId = COALESCE(#{workStationId}, WorkStationId),
        StationId = COALESCE(#{stationId}, StationId),
        IpAddress = COALESCE(#{ipAddress}, IpAddress),
        RunMode = COALESCE(#{runMode}, RunMode),
        ConfigFileCode = COALESCE(#{configFileCode}, ConfigFileCode),
        ConfigUpdateTime = COALESCE(#{configUpdateTime}, ConfigUpdateTime),
        SampleConfigCode = COALESCE(#{sampleConfigCode}, SampleConfigCode),
        SoftwareVersion = COALESCE(#{softwareVersion}, SoftwareVersion),
        Description = COALESCE(#{description}, Description),
        StartTime = COALESCE(#{startTime}, StartTime),
        HeartbeatTime = COALESCE(#{heartbeatTime}, HeartbeatTime),
        ConnectState = COALESCE(#{connectState}, ConnectState),
        UpdateTime = COALESCE(#{updateTime}, UpdateTime),
        IsSync = COALESCE(#{isSync}, IsSync),
        SyncTime = COALESCE(#{syncTime}, SyncTime),
        IsConfigOK = COALESCE(#{isConfigOK}, IsConfigOK),
        ConfigFileCode_Old = COALESCE(#{configFileCode_Old}, ConfigFileCode_Old),
        SampleConfigCode_Old = COALESCE(#{sampleConfigCode_Old}, SampleConfigCode_Old),
        AppCongfigId = COALESCE(#{appConfigId}, AppCongfigId),
        CanDistribute = COALESCE(#{canDistribute}, CanDistribute),
        Enable = COALESCE(#{enable}, Enable),
        ProjectName = COALESCE(#{rdsServer}, ProjectName),
        ContractNo = COALESCE(#{dataServer}, ContractNo),
        InstallTime = COALESCE(#{installTime}, InstallTime),
        FSU = COALESCE(#{fsu}, FSU)
        WHERE MonitorUnitId = #{monitorUnitId} AND StationId = #{stationId};

        UPDATE TBL_MonitorUnitProjectInfo SET
        StationId = COALESCE(#{stationId}, StationId),
        ProjectName = COALESCE(#{projectName}, ProjectName),
        ContractNo = COALESCE(#{contractNo}, ContractNo),
        InstallTime = COALESCE(#{installTime}, InstallTime)
        WHERE MonitorUnitId = #{monitorUnitId} AND StationId = #{stationId};
    </update>

</mapper>
