<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.siteweb.mapper.EquipmentTemplateMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.siteweb.entity.EquipmentTemplate">
        <id column="EquipmentTemplateId" property="equipmentTemplateId"/>
        <result column="EquipmentTemplateName" property="equipmentTemplateName"/>
        <result column="ParentTemplateId" property="parentTemplateId"/>
        <result column="Memo" property="memo"/>
        <result column="ProtocolCode" property="protocolCode"/>
        <result column="EquipmentCategory" property="equipmentCategory"/>
        <result column="EquipmentType" property="equipmentType"/>
        <result column="Property" property="property"/>
        <result column="Description" property="description"/>
        <result column="EquipmentStyle" property="equipmentStyle"/>
        <result column="Unit" property="unit"/>
        <result column="Vendor" property="vendor"/>
        <result column="Photo" property="photo"/>
        <result column="EquipmentBaseType" property="equipmentBaseType"/>
        <result column="StationCategory" property="stationCategory"/>
        <result column="ExtendField1" property="extendField1"/>
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode,
        EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit,
        Vendor, Photo, EquipmentBaseType, StationCategory, ExtendField1
    </sql>

    <select id="findByNameLike" resultMap="BaseResultMap">
        SELECT *
        FROM tbl_equipmenttemplate
        WHERE EquipmentTemplateName LIKE
        <choose>
            <when test="_databaseId == 'mysql'">
                CONCAT('%', #{equipmentTemplateName}, '%')
            </when>
            <when test="_databaseId == 'postgresql'">
                '%' || #{equipmentTemplateName} || '%'
            </when>
            <otherwise>
                CONCAT('%', #{equipmentTemplateName}, '%')
            </otherwise>
        </choose>
    </select>

    <select id="getBInterfaceDeviceTemplateRootId" resultType="java.lang.Integer">
        SELECT EquipmentTemplateId
        FROM tbl_equipmenttemplate
        WHERE ProtocolCode = 'BInterfaceProtocol' AND ParentTemplateId IS NULL
        <choose>
            <when test="_databaseId == 'mysql'">
                LIMIT 1
            </when>
            <when test="_databaseId == 'postgresql'">
                LIMIT 1
            </when>
            <otherwise>
                LIMIT 1
            </otherwise>
        </choose>
    </select>

    <select id="countByParentTemplateId" resultType="int">
        SELECT COUNT(*)
        FROM tbl_equipmenttemplate
        WHERE ParentTemplateId = #{parentTemplateId}
    </select>

    <select id="queryTemplateByVO" resultMap="BaseResultMap" parameterType="com.siteweb.tcs.siteweb.vo.EquipmentTemplateVO">
        SELECT *
        FROM tbl_equipmenttemplate
        <where>
            <if test="vo.equipmentTemplateId != null">
                AND EquipmentTemplateId = #{vo.equipmentTemplateId}
            </if>
            <if test="vo.equipmentTemplateName != null and vo.equipmentTemplateName != ''">
                AND EquipmentTemplateName LIKE
                <choose>
                    <when test="_databaseId == 'mysql'">
                        CONCAT('%', #{vo.equipmentTemplateName}, '%')
                    </when>
                    <when test="_databaseId == 'postgresql'">
                        '%' || #{vo.equipmentTemplateName} || '%'
                    </when>
                    <otherwise>
                        CONCAT('%', #{vo.equipmentTemplateName}, '%')
                    </otherwise>
                </choose>
            </if>
            <if test="vo.parentTemplateId != null">
                AND ParentTemplateId = #{vo.parentTemplateId}
            </if>
            <if test="vo.protocolCode != null and vo.protocolCode != ''">
                AND ProtocolCode = #{vo.protocolCode}
            </if>
            <if test="vo.equipmentCategory != null">
                AND EquipmentCategory = #{vo.equipmentCategory}
            </if>
            <if test="vo.equipmentType != null">
                AND EquipmentType = #{vo.equipmentType}
            </if>
            <if test="vo.equipmentBaseType != null">
                AND EquipmentBaseType = #{vo.equipmentBaseType}
            </if>
            <if test="vo.stationCategory != null">
                AND StationCategory = #{vo.stationCategory}
            </if>
            <if test="vo.extendField1 != null and vo.extendField1 != ''">
                AND ExtendField1 = #{vo.extendField1}
            </if>
             <!-- Add other fields from EquipmentTemplateVO for query as needed -->
        </where>
        ORDER BY EquipmentTemplateId DESC
    </select>

    <update id="updateEquipmentBaseTypeToNull">
        UPDATE tbl_equipmenttemplate
        SET BaseTypeId = NULL
    </update>

    <select id="findEquipmentTemplateIdByEquipmentTemplateIdDiv" resultType="java.lang.Integer">
        SELECT EquipmentTemplateId
        FROM tbl_equipmenttemplate
        WHERE EquipmentTemplateIdDiv = #{equipmentTemplateIdDiv}
    </select>

    <select id="findEquipmentTemplateByProtocolCodes" resultType="com.siteweb.tcs.siteweb.vo.SamplerVO">
        SELECT a.protocolcode,a.EquipmentTemplateName FROM tbl_equipmenttemplate a
        WHERE a.ProtocolCode IN
        <foreach collection="protocolCodeList" item="protocolCode" open="(" close=")" separator=",">
            #{protocolCode}
        </foreach>
        AND ParentTemplateId = 0 AND a.protocolcode is not null
    </select>
    <select id="findReferenceEquipmentNameByProtocolCodes" resultType="java.lang.String">
        SELECT c.equipmentName FROM tbl_equipmenttemplate a inner JOIN tsl_sampler b on a.protocolcode = b.ProtocolCode
        INNER JOIN tbl_equipment c ON c.equipmentTemplateId = a.EquipmentTemplateId
        WHERE b.ProtocolCode IN
        <foreach collection="protocolCodeList" item="protocolCode" open="(" close=")" separator=",">
            #{protocolCode}
        </foreach>
    </select>

    <!-- 查询所有设备模板VO（用于协议管理页面显示） -->
    <select id="findVoAll" resultType="com.siteweb.tcs.siteweb.vo.EquipmentTemplateVO">
        SELECT EquipmentTemplateId,
               EquipmentTemplateName,
               ParentTemplateId,
               Memo,
               ProtocolCode,
               EquipmentCategory,
               EquipmentType,
               Property,
               Description,
               EquipmentStyle,
               Unit,
               Vendor,
               Photo,
               EquipmentBaseType,
               StationCategory,
               ExtendField1
        FROM tbl_equipmenttemplate
        ORDER BY EquipmentTemplateId
    </select>
    <select id="findBaseClassAll" resultType="com.siteweb.tcs.siteweb.vo.EquipmentTemplateBaseClassVO">
        select t.EquipmentTemplateId,
        t.EquipmentTemplateName,
        t.ParentTemplateId,
        (select EquipmentTemplateName from TBL_EquipmentTemplate where EquipmentTemplateId = t.ParentTemplateId) parentTemplateName,
        t.ProtocolCode protocolCode,
        t.EquipmentCategory,
        (select
        <choose>
            <when test="_databaseId == 'mysql'">
                concat(convert(ItemValue,char(32)),'[',convert(t.EquipmentCategory,char(32)),']')
            </when>
            <when test="_databaseId == 'postgresql'">
                ItemValue::text || '[' || t.EquipmentCategory::text || ']'
            </when>
            <otherwise>
                concat(convert(ItemValue,char(32)),'[',convert(t.EquipmentCategory,char(32)),']')
            </otherwise>
        </choose>
        from TBL_DataItem where EntryId = 7 and ItemId = t.EquipmentCategory
        <choose>
            <when test="_databaseId == 'mysql'">
                limit 1
            </when>
            <when test="_databaseId == 'postgresql'">
                limit 1
            </when>
            <otherwise>
                limit 1
            </otherwise>
        </choose>) equipmentCategoryName,
        t.EquipmentBaseType,
        (select
        <choose>
            <when test="_databaseId == 'mysql'">
                concat(BaseEquipmentName,'[',convert(t.EquipmentBaseType,char(32)),']')
            </when>
            <when test="_databaseId == 'postgresql'">
                BaseEquipmentName || '[' || t.EquipmentBaseType::text || ']'
            </when>
            <otherwise>
                concat(BaseEquipmentName,'[',convert(t.EquipmentBaseType,char(32)),']')
            </otherwise>
        </choose>
        from TBL_EquipmentBaseType where BaseEquipmentId = t.EquipmentBaseType
        <choose>
            <when test="_databaseId == 'mysql'">
                limit 1
            </when>
            <when test="_databaseId == 'postgresql'">
                limit 1
            </when>
            <otherwise>
                limit 1
            </otherwise>
        </choose>) equipmentBaseTypeName
        from TBL_EquipmentTemplate t order by t.EquipmentCategory ASC,ProtocolCode ASC
    </select>

    <!-- ==================== 新增设备模板迁移相关查询 ==================== -->

    <!-- 获取设备模板树结构数据 -->
    <select id="findTree" resultType="com.siteweb.tcs.siteweb.dto.EquipmentTemplateTreeDTO">
        SELECT ParentTemplateId AS parentId,
               EquipmentTemplateId AS id,
               EquipmentTemplateName AS name,
               equipmentCategory AS equipmentCategory
        FROM tbl_equipmenttemplate
    </select>

    <!-- 根据设备类别获取设备模板树 -->
    <select id="findTreeByEquipmentCategory" resultType="com.siteweb.tcs.siteweb.dto.EquipmentTemplateTreeDTO">
        SELECT ParentTemplateId AS parentId,
               EquipmentTemplateId AS id,
               EquipmentTemplateName AS name,
               equipmentCategory AS equipmentCategory
        FROM tbl_equipmenttemplate
        <where>
            <if test="equipmentCategory != null">
                EquipmentCategory = #{equipmentCategory}
            </if>
        </where>
    </select>

    <!-- 根据设备模板ID查询VO -->
    <select id="findVoByEquipmentTemplateId" resultType="com.siteweb.tcs.siteweb.vo.EquipmentTemplateVO">
        SELECT a.equipmenttemplateid,
               a.equipmenttemplatename,
               a.parenttemplateid,
               a.memo,
               a.protocolcode,
               a.equipmentcategory,
               a.equipmenttype,
               a.property,
               a.description,
               a.equipmentstyle,
               a.unit,
               a.vendor,
               a.equipmentbasetype,
               a.stationcategory,
               a.photo,
               b.SamplerName
        FROM tbl_equipmenttemplate a
                 LEFT JOIN tsl_sampler b ON a.protocolcode = b.ProtocolCode
        WHERE a.EquipmentTemplateId = #{equipmentTemplateId}
    </select>

    <!-- 根据设备模板名称查找设备模板 -->
    <select id="findByName" resultType="com.siteweb.tcs.siteweb.entity.EquipmentTemplate">
        SELECT equipmenttemplateid,
               equipmenttemplatename,
               parenttemplateid,
               memo,
               protocolcode,
               equipmentcategory,
               equipmenttype,
               property,
               description,
               equipmentstyle,
               unit,
               vendor,
               equipmentbasetype,
               stationcategory,
               photo
        FROM tbl_equipmenttemplate
        WHERE EquipmentTemplateName = #{equipmentTemplateName}
        <choose>
            <when test="_databaseId == 'mysql'">
                LIMIT 1
            </when>
            <when test="_databaseId == 'postgresql'">
                LIMIT 1
            </when>
            <otherwise>
                LIMIT 1
            </otherwise>
        </choose>
    </select>

    <!-- 查找指定父模板的所有子模板ID（递归） -->
    <select id="findAllChildId" resultType="java.lang.Integer">
        WITH RECURSIVE cte AS (
            SELECT EquipmentTemplateId, ParentTemplateId
            FROM tbl_equipmenttemplate
            WHERE EquipmentTemplateId = #{parentTemplateId}
            UNION ALL
            SELECT t.EquipmentTemplateId, t.ParentTemplateId
            FROM tbl_equipmenttemplate t
            INNER JOIN cte ON t.ParentTemplateId = cte.EquipmentTemplateId
        )
        SELECT equipmentTemplateId
        FROM cte
    </select>

    <!-- 根据设备类别、协议代码和模板名称查询设备模板 -->
    <select id="findByEquipmentCategoryAndProtocolCode" resultType="com.siteweb.tcs.siteweb.entity.EquipmentTemplate">
        SELECT EquipmentTemplateId, ParentTemplateId, EquipmentTemplateName, EquipmentCategory
        FROM tbl_equipmenttemplate
        <where>
            <if test="equipmentCategory != null">
                AND equipmentCategory = #{equipmentCategory}
            </if>
            <if test="protocolCode != null and protocolCode != ''">
                AND protocolCode = #{protocolCode}
            </if>
            <if test="equipmentTemplateName != null and equipmentTemplateName != ''">
                AND equipmentTemplateName LIKE
                <choose>
                    <when test="_databaseId == 'mysql'">
                        CONCAT('%', #{equipmentTemplateName}, '%')
                    </when>
                    <when test="_databaseId == 'postgresql'">
                        '%' || #{equipmentTemplateName} || '%'
                    </when>
                    <otherwise>
                        CONCAT('%', #{equipmentTemplateName}, '%')
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>

    <!-- 根据模板ID列表获取模板名称 -->
    <select id="findNameByIds" resultType="com.siteweb.tcs.siteweb.dto.IdValueDTO">
        SELECT EquipmentTemplateId AS id, EquipmentTemplateName AS value
        FROM tbl_equipmenttemplate
        WHERE EquipmentTemplateId IN
        <foreach collection="equipmentTemplateIds" item="equipmentTemplateId" open="(" close=")" separator=",">
            #{equipmentTemplateId}
        </foreach>
    </select>

    <!-- 根据模板ID列表查找DLL路径 -->
    <select id="findDLlPathByEquipmentTemplateIds" resultType="java.lang.String">
        SELECT DISTINCT s.DllPath
        FROM tbl_equipmenttemplate t
        INNER JOIN tsl_sampler s ON t.ProtocolCode = s.ProtocolCode
        WHERE t.EquipmentTemplateId IN
        <foreach collection="equipmentTemplateIds" item="equipmentTemplateId" open="(" close=")" separator=",">
            #{equipmentTemplateId}
        </foreach>
        AND s.DllPath IS NOT NULL AND s.DllPath != ''
    </select>

    <!-- 更新子模板的设备类别 -->
    <update id="updateChildrenEquipmentCategory">
        UPDATE tbl_equipmenttemplate
        SET EquipmentCategory = #{equipmentCategory}
        WHERE EquipmentTemplateId IN (
            WITH RECURSIVE cte AS (
                SELECT EquipmentTemplateId
                FROM tbl_equipmenttemplate
                WHERE ParentTemplateId = #{parentTemplateId}
                UNION ALL
                SELECT t.EquipmentTemplateId
                FROM tbl_equipmenttemplate t
                INNER JOIN cte ON t.ParentTemplateId = cte.EquipmentTemplateId
            )
            SELECT EquipmentTemplateId FROM cte
        )
    </update>

    <!-- 更新模板的设备类别 -->
    <update id="updateEquipmentCategory">
        UPDATE tbl_equipmenttemplate
        SET EquipmentCategory = #{equipmentCategory}
        WHERE EquipmentTemplateId = #{equipmentTemplateId}
    </update>

    <!-- 清空设备基类类型 -->
    <update id="clearEquipmentBaseType">
        UPDATE tbl_equipmenttemplate
        SET EquipmentBaseType = NULL
        WHERE EquipmentTemplateId = #{equipmentTemplateId}
    </update>

    <!-- 升级为根模板 -->
    <update id="upgradeToRootTemplate">
        UPDATE tbl_equipmenttemplate
        SET ParentTemplateId = 0
        WHERE EquipmentTemplateId = #{equipmentTemplateId}
    </update>

    <!-- 检查是否存在子模板 -->
    <select id="countByParentTemplateId" resultType="int">
        SELECT COUNT(*)
        FROM tbl_equipmenttemplate
        WHERE ParentTemplateId = #{parentTemplateId}
    </select>

    <!-- 比较模板变更影响 -->
    <select id="compareTemplateChanges" resultType="com.siteweb.tcs.siteweb.dto.EquipTemplateChangeDTO">
        SELECT
            e.EquipmentName as equipmentName,
            s.StationName as stationName,
            'Signal' as objectType,
            sig.SignalName as objectName,
            sig.Description as description,
            1 as objectChangeType
        FROM tbl_equipment e
        INNER JOIN tbl_station s ON e.StationId = s.StationId
        INNER JOIN tbl_signal sig ON sig.EquipmentTemplateId = #{originTemplateId}
        LEFT JOIN tbl_signal dest_sig ON dest_sig.EquipmentTemplateId = #{destTemplateId}
                                      AND dest_sig.SignalName = sig.SignalName
        WHERE e.EquipmentId IN
        <foreach collection="equipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
        AND dest_sig.SignalId IS NULL

        UNION ALL

        SELECT
            e.EquipmentName as equipmentName,
            s.StationName as stationName,
            'Event' as objectType,
            evt.EventName as objectName,
            evt.Description as description,
            1 as objectChangeType
        FROM tbl_equipment e
        INNER JOIN tbl_station s ON e.StationId = s.StationId
        INNER JOIN tbl_event evt ON evt.EquipmentTemplateId = #{originTemplateId}
        LEFT JOIN tbl_event dest_evt ON dest_evt.EquipmentTemplateId = #{destTemplateId}
                                     AND dest_evt.EventName = evt.EventName
        WHERE e.EquipmentId IN
        <foreach collection="equipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
        AND dest_evt.EventId IS NULL

        UNION ALL

        SELECT
            e.EquipmentName as equipmentName,
            s.StationName as stationName,
            'Control' as objectType,
            ctrl.ControlName as objectName,
            ctrl.Description as description,
            1 as objectChangeType
        FROM tbl_equipment e
        INNER JOIN tbl_station s ON e.StationId = s.StationId
        INNER JOIN tbl_control ctrl ON ctrl.EquipmentTemplateId = #{originTemplateId}
        LEFT JOIN tbl_control dest_ctrl ON dest_ctrl.EquipmentTemplateId = #{destTemplateId}
                                        AND dest_ctrl.ControlName = ctrl.ControlName
        WHERE e.EquipmentId IN
        <foreach collection="equipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
        AND dest_ctrl.ControlId IS NULL
    </select>

    <!-- 检查信号引用冲突 -->
    <select id="checkSignalReferenceConflict" resultType="int">
        SELECT COUNT(*)
        FROM tbl_signal origin_sig
        INNER JOIN tbl_signal dest_sig ON dest_sig.EquipmentTemplateId = #{destTemplateId}
                                       AND dest_sig.SignalName = origin_sig.SignalName
                                       AND dest_sig.SignalCategory != origin_sig.SignalCategory
        WHERE origin_sig.EquipmentTemplateId = #{originTemplateId}
    </select>
    <select id="findDynamicConfigTemplate" resultType="com.siteweb.tcs.siteweb.entity.EquipmentTemplate">
        SELECT EquipmentTemplateId,ParentTemplateId,EquipmentTemplateName,EquipmentCategory FROM tbl_equipmenttemplate
        <where>
            <if test="hideDynamicConfigTemplate != null and hideDynamicConfigTemplate == true">
                AND memo != '动态配置'
            </if>
        </where>
    </select>

</mapper>
