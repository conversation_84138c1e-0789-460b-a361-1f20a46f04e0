<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.siteweb.mapper.TaskStatusMapper">

    <!-- 删除过期的任务状态记录 -->
    <delete id="deleteExpiredTasks">
        DELETE FROM task_status
        WHERE expire_time &lt; #{expireTime}
    </delete>

    <!-- 根据任务类型查询活跃的任务 -->
    <select id="findActiveTasksByType" resultType="com.siteweb.tcs.siteweb.entity.TaskStatus">
        SELECT * FROM task_status
        WHERE task_type = #{taskType}
        AND is_final = false
        ORDER BY create_time DESC
    </select>

    <!-- 根据监控单元ID查询相关任务 -->
    <select id="findTasksByMonitorUnitId" resultType="com.siteweb.tcs.siteweb.entity.TaskStatus">
        SELECT * FROM task_status
        WHERE monitor_unit_id = #{monitorUnitId}
        ORDER BY create_time DESC
        LIMIT 50
    </select>

</mapper>