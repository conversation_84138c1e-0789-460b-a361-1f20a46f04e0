# 异步任务配置
tcs:
  siteweb:
    async:
      # 默认异步任务线程池配置
      core-pool-size: 8
      max-pool-size: 16
      queue-capacity: 100
      thread-name-prefix: siteweb-async-
      
      # 监控单元任务线程池配置
      monitor-unit:
        core-pool-size: ${tcs.siteweb.async.monitor-unit.core-pool-size:#{T(java.lang.Runtime).getRuntime().availableProcessors()}}
        max-pool-size: ${tcs.siteweb.async.monitor-unit.max-pool-size:#{T(java.lang.Runtime).getRuntime().availableProcessors() * 2}}
        queue-capacity: 50
        thread-name-prefix: MonitorUnit-
        
      # 远程操作任务线程池配置
      remote:
        core-pool-size: 4
        max-pool-size: 8
        queue-capacity: 20
        thread-name-prefix: Remote-
        
      # 任务超时配置（分钟）
      timeout:
        # 配置生成任务超时时间
        config-generation: 30
        # 配置下发任务超时时间
        config-distribution: 30
        # 远程配置下载任务超时时间
        remote-download: 15
        # 配置备份任务超时时间
        config-backup: 20
        
      # 任务重试配置
      retry:
        # 最大重试次数
        max-attempts: 3
        # 重试间隔（秒）
        delay-seconds: 5
        
      # 任务监控配置
      monitoring:
        # 是否启用任务监控
        enabled: true
        # 监控间隔（秒）
        interval-seconds: 30
        # 任务状态保留天数
        retention-days: 7
