package com.siteweb.tcs.siteweb.service;

import com.siteweb.tcs.siteweb.dto.ConfigDiffResult;
import com.siteweb.tcs.siteweb.dto.ConfigUploadAndDiffResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

/**
 * 配置上传和差异对比服务测试
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class ConfigUploadAndDiffServiceTest {

    @Resource
    private IConfigImportService configImportService;

    /**
     * 测试新的上传并对比流程
     */
    @Test
    public void testUploadAndCompareConfig() {
        try {
            // 创建模拟的配置文件
            String testContent = createTestXmlContent();
            MockMultipartFile mockFile = new MockMultipartFile(
                    "file", 
                    "test-config.zip", 
                    "application/zip", 
                    testContent.getBytes()
            );

            // 执行上传并对比
            ConfigUploadAndDiffResult result = configImportService.uploadAndCompareConfig(mockFile);

            // 验证结果
            log.info("上传并对比测试完成");
            log.info("任务ID: {}", result.getUploadTaskId());
            log.info("检测到的监控单元ID: {}", result.getDetectedMonitorUnitId());
            log.info("检测到的监控单元名称: {}", result.getDetectedMonitorUnitName());
            log.info("是否新建监控单元: {}", result.isNewMonitorUnit());
            log.info("是否成功: {}", result.isSuccess());
            
            if (result.getDiffSummary() != null) {
                log.info("总变更数: {}", result.getDiffSummary().getTotalChanges());
                log.info("是否有变更: {}", result.getDiffSummary().hasChanges());
                log.info("设备模板变更: {}", result.getDiffSummary().getEquipmentTemplateChanges());
                log.info("信号变更: {}", result.getDiffSummary().getSignalChanges());
                log.info("事件变更: {}", result.getDiffSummary().getEventChanges());
                log.info("控制变更: {}", result.getDiffSummary().getControlChanges());
            }

            // 验证差异结果的结构
            if (result.getDiffResult() != null) {
                ConfigDiffResult diffResult = result.getDiffResult();
                log.info("设备模板差异数量: {}", diffResult.getEquipmentTemplateDiffs().size());
                log.info("端口差异数量: {}", diffResult.getPortDiffs().size());
                log.info("采集单元差异数量: {}", diffResult.getSamplerUnitDiffs().size());
                log.info("设备差异数量: {}", diffResult.getEquipmentDiffs().size());
            }

        } catch (Exception e) {
            log.error("上传并对比测试失败", e);
        }
    }

    /**
     * 测试新建监控单元的场景
     */
    @Test
    public void testNewMonitorUnitScenario() {
        try {
            // 创建一个新的监控单元配置
            String testContent = createNewMonitorUnitXmlContent();
            MockMultipartFile mockFile = new MockMultipartFile(
                    "file",
                    "new-monitor-unit-config.zip",
                    "application/zip",
                    testContent.getBytes()
            );

            // 执行上传并对比
            ConfigUploadAndDiffResult result = configImportService.uploadAndCompareConfig(mockFile);

            // 验证新建监控单元的结果
            log.info("新建监控单元测试完成");
            log.info("是否新建监控单元: {}", result.isNewMonitorUnit());

            if (result.isNewMonitorUnit() && result.getDiffSummary() != null) {
                log.info("新建监控单元包含:");
                log.info("- 设备模板变更: {} 个 (新增: {}, 修改: {})",
                        result.getDiffSummary().getEquipmentTemplateChanges(),
                        result.getDiffSummary().getNewEquipmentTemplates(),
                        result.getDiffSummary().getModifiedEquipmentTemplates());
                log.info("- 信号变更: {} 个", result.getDiffSummary().getSignalChanges());
                log.info("- 事件变更: {} 个", result.getDiffSummary().getEventChanges());
                log.info("- 控制变更: {} 个", result.getDiffSummary().getControlChanges());
                log.info("- 端口: {} 个 (全部新增)", result.getDiffSummary().getPortChanges());
                log.info("- 采集单元: {} 个 (全部新增)", result.getDiffSummary().getSamplerUnitChanges());
                log.info("- 设备: {} 个 (全部新增)", result.getDiffSummary().getEquipmentChanges());

                // 验证差异结果的详细信息
                if (result.getDiffResult() != null) {
                    ConfigDiffResult diffResult = result.getDiffResult();

                    // 验证设备模板的差异类型
                    log.info("设备模板差异详情:");
                    for (var templateDiff : diffResult.getEquipmentTemplateDiffs()) {
                        log.info("  模板 {} ({}): {}",
                                templateDiff.getEquipmentTemplateName(),
                                templateDiff.getEquipmentTemplateId(),
                                templateDiff.getDiffType());

                        if (templateDiff.getDiffType() == ConfigDiffResult.DiffType.NEW) {
                            log.info("    - 新增设备模板，包含 {} 个信号, {} 个事件, {} 个控制",
                                    templateDiff.getSignalDiffs().size(),
                                    templateDiff.getEventDiffs().size(),
                                    templateDiff.getControlDiffs().size());
                        } else if (templateDiff.getDiffType() == ConfigDiffResult.DiffType.MODIFIED) {
                            log.info("    - 修改现有设备模板，字段变更: {}, 信号变更: {}, 事件变更: {}, 控制变更: {}",
                                    templateDiff.getFieldDiffs().size(),
                                    templateDiff.getSignalDiffs().size(),
                                    templateDiff.getEventDiffs().size(),
                                    templateDiff.getControlDiffs().size());
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("新建监控单元测试失败", e);
        }
    }

    /**
     * 测试新建监控单元但使用已存在设备模板的场景
     */
    @Test
    public void testNewMonitorUnitWithExistingTemplates() {
        try {
            // 创建一个新监控单元配置，但使用已存在的设备模板ID
            String testContent = createNewMonitorUnitWithExistingTemplatesXmlContent();
            MockMultipartFile mockFile = new MockMultipartFile(
                    "file",
                    "new-monitor-unit-existing-templates.zip",
                    "application/zip",
                    testContent.getBytes()
            );

            // 执行上传并对比
            ConfigUploadAndDiffResult result = configImportService.uploadAndCompareConfig(mockFile);

            // 验证结果
            log.info("新建监控单元（使用已存在设备模板）测试完成");
            log.info("是否新建监控单元: {}", result.isNewMonitorUnit());

            if (result.getDiffResult() != null) {
                ConfigDiffResult diffResult = result.getDiffResult();

                log.info("差异分析:");
                log.info("- 监控单元: 新建");
                log.info("- 端口: {} 个 (全部新增)", diffResult.getPortDiffs().size());
                log.info("- 采集单元: {} 个 (全部新增)", diffResult.getSamplerUnitDiffs().size());
                log.info("- 设备: {} 个 (全部新增)", diffResult.getEquipmentDiffs().size());

                // 重点验证设备模板的处理
                log.info("设备模板处理:");
                for (var templateDiff : diffResult.getEquipmentTemplateDiffs()) {
                    log.info("  模板 {} (ID: {}): {}",
                            templateDiff.getEquipmentTemplateName(),
                            templateDiff.getEquipmentTemplateId(),
                            templateDiff.getDiffType());

                    if (templateDiff.getDiffType() == ConfigDiffResult.DiffType.NEW) {
                        log.info("    -> 这是一个新的设备模板");
                    } else if (templateDiff.getDiffType() == ConfigDiffResult.DiffType.MODIFIED) {
                        log.info("    -> 这是对现有设备模板的修改");
                        log.info("       字段变更: {}", templateDiff.getFieldDiffs().size());
                        log.info("       信号变更: {}", templateDiff.getSignalDiffs().size());
                        log.info("       事件变更: {}", templateDiff.getEventDiffs().size());
                        log.info("       控制变更: {}", templateDiff.getControlDiffs().size());
                    } else if (templateDiff.getDiffType() == null &&
                              (templateDiff.getSignalDiffs().isEmpty() &&
                               templateDiff.getEventDiffs().isEmpty() &&
                               templateDiff.getControlDiffs().isEmpty())) {
                        log.info("    -> 设备模板无变更（完全相同）");
                    }
                }
            }

        } catch (Exception e) {
            log.error("新建监控单元（使用已存在设备模板）测试失败", e);
        }
    }

    /**
     * 测试完整的上传→对比→确认导入流程
     */
    @Test
    public void testCompleteImportFlow() {
        try {
            // 第一步：上传并对比
            String testContent = createTestXmlContent();
            MockMultipartFile mockFile = new MockMultipartFile(
                    "file",
                    "complete-flow-test.zip",
                    "application/zip",
                    testContent.getBytes()
            );

            ConfigUploadAndDiffResult uploadResult = configImportService.uploadAndCompareConfig(mockFile);

            log.info("=== 第一步：上传并对比 ===");
            log.info("上传任务ID: {}", uploadResult.getUploadTaskId());
            log.info("检测到监控单元: {} (ID: {})", uploadResult.getDetectedMonitorUnitName(), uploadResult.getDetectedMonitorUnitId());
            log.info("是否新建: {}", uploadResult.isNewMonitorUnit());
            log.info("总变更数: {}", uploadResult.getDiffSummary().getTotalChanges());

            // 第二步：确认导入
            if (uploadResult.isSuccess()) {
                log.info("=== 第二步：确认导入 ===");

                // 构建导入请求
                var request = new com.siteweb.tcs.siteweb.dto.ConfigImportTaskRequest();
                request.setUserId("test-user");
                request.setOverwrite(true);
                request.setImportMode("FULL");
                request.setRemarks("测试完整流程");

                // 执行确认导入
                var importReport = configImportService.confirmImportFromUpload(uploadResult.getUploadTaskId(), request);

                log.info("导入结果:");
                log.info("- 成功: {}", importReport.isSuccess());
                log.info("- 消息: {}", importReport.getMessage());
                log.info("- 耗时: {}ms", importReport.getDurationMs());
                log.info("- 监控单元: {}", importReport.getMonitorUnitName());
                log.info("- 是否新建: {}", importReport.isNewMonitorUnit());

                if (importReport.isSuccess()) {
                    log.info("✅ 完整导入流程测试成功");
                } else {
                    log.error("❌ 导入失败: {}", importReport.getErrorMessage());
                }
            } else {
                log.error("❌ 上传和对比失败: {}", uploadResult.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("完整导入流程测试失败", e);
        }
    }

    /**
     * 测试配置差异对比功能（传统方式）
     */
    @Test
    public void testConfigDiff() {
        try {
            // 创建模拟的配置文件
            String testContent = createTestXmlContent();
            MockMultipartFile mockFile = new MockMultipartFile(
                    "file", 
                    "test-config.zip", 
                    "application/zip", 
                    testContent.getBytes()
            );

            // 测试监控单元ID
            Integer testMonitorUnitId = 123;

            // 执行配置差异对比
            ConfigDiffResult diffResult = configImportService.compareConfigWithDatabase(mockFile, testMonitorUnitId);

            // 验证结果
            log.info("配置差异对比测试完成");
            log.info("是否有差异: {}", diffResult.hasDifferences());
            
            if (diffResult.getMonitorUnitDiff() != null) {
                log.info("监控单元差异数量: {}", diffResult.getMonitorUnitDiff().getFieldDiffs().size());
            }
            
            log.info("设备模板差异数量: {}", diffResult.getEquipmentTemplateDiffs().size());
            log.info("端口差异数量: {}", diffResult.getPortDiffs().size());
            log.info("采集单元差异数量: {}", diffResult.getSamplerUnitDiffs().size());
            log.info("设备差异数量: {}", diffResult.getEquipmentDiffs().size());

        } catch (Exception e) {
            log.error("配置差异对比测试失败", e);
        }
    }

    /**
     * 创建测试用的XML内容
     */
    private String createTestXmlContent() {
        return """
            <?xml version="1.0" encoding="UTF-8"?>
            <MainCfg>
                <MonitorUnit MonitorUnitId="123" MonitorUnitName="测试监控单元" 
                            MonitorUnitCategory="1" MonitorUnitCode="TEST001" 
                            StationId="1" IpAddress="*************" 
                            RunMode="1" Description="测试用监控单元">
                    <Ports>
                        <Port PortId="1" PortNo="1" PortName="测试端口1" 
                              PortType="1" Setting="9600,8,1,N" Description="测试端口"/>
                    </Ports>
                    <SamplerUnits>
                        <SamplerUnit SamplerUnitId="1" PortId="1" ParentSamplerUnitId="0" 
                                    SamplerUnitName="测试采集单元" SamplerType="1" 
                                    Address="1" SpUnitInterval="5.0" Description="测试采集单元"/>
                    </SamplerUnits>
                    <Equipments>
                        <Equipment EquipmentId="1" EquipmentName="测试设备" 
                                  EquipmentCategory="1" EquipmentTemplateId="1" 
                                  SamplerUnitId="1" Description="测试设备"/>
                    </Equipments>
                </MonitorUnit>
                <EquipmentTemplates>
                    <EquipmentTemplate EquipmentTemplateId="1" EquipmentTemplateName="测试设备模板" 
                                      ProtocolCode="TEST" EquipmentCategory="1" 
                                      EquipmentType="1" Description="测试设备模板">
                        <Signals>
                            <Signal SignalId="1" SignalName="测试信号" SignalCategory="1" 
                                   SignalType="1" ChannelNo="1" DataType="1" 
                                   Unit="V" Enable="true" Visible="true" Description="测试信号"/>
                        </Signals>
                        <Events>
                            <Event EventId="1" EventName="测试事件" SignalId="1" 
                                  EventCategory="1" StartType="1" EndType="2" 
                                  Enable="true" Visible="true" Description="测试事件"/>
                        </Events>
                        <Controls>
                            <Control ControlId="1" ControlName="测试控制" ControlCategory="1" 
                                    CmdToken="TEST_CMD" Enable="true" Visible="true" 
                                    Description="测试控制"/>
                        </Controls>
                    </EquipmentTemplate>
                </EquipmentTemplates>
            </MainCfg>
            """;
    }

    /**
     * 创建新建监控单元的XML内容
     */
    private String createNewMonitorUnitXmlContent() {
        return """
            <?xml version="1.0" encoding="UTF-8"?>
            <MainCfg>
                <MonitorUnit MonitorUnitId="999" MonitorUnitName="新建监控单元" 
                            MonitorUnitCategory="1" MonitorUnitCode="NEW001" 
                            StationId="1" IpAddress="*************" 
                            RunMode="1" Description="新建的监控单元">
                    <Ports>
                        <Port PortId="1001" PortNo="1" PortName="新建端口1" 
                              PortType="1" Setting="9600,8,1,N" Description="新建端口"/>
                        <Port PortId="1002" PortNo="2" PortName="新建端口2" 
                              PortType="2" Setting="115200,8,1,N" Description="新建端口2"/>
                    </Ports>
                    <SamplerUnits>
                        <SamplerUnit SamplerUnitId="2001" PortId="1001" ParentSamplerUnitId="0" 
                                    SamplerUnitName="新建采集单元1" SamplerType="1" 
                                    Address="1" SpUnitInterval="5.0" Description="新建采集单元"/>
                        <SamplerUnit SamplerUnitId="2002" PortId="1002" ParentSamplerUnitId="0" 
                                    SamplerUnitName="新建采集单元2" SamplerType="2" 
                                    Address="2" SpUnitInterval="10.0" Description="新建采集单元2"/>
                    </SamplerUnits>
                    <Equipments>
                        <Equipment EquipmentId="3001" EquipmentName="新建设备1" 
                                  EquipmentCategory="1" EquipmentTemplateId="4001" 
                                  SamplerUnitId="2001" Description="新建设备1"/>
                        <Equipment EquipmentId="3002" EquipmentName="新建设备2" 
                                  EquipmentCategory="2" EquipmentTemplateId="4002" 
                                  SamplerUnitId="2002" Description="新建设备2"/>
                    </Equipments>
                </MonitorUnit>
                <EquipmentTemplates>
                    <EquipmentTemplate EquipmentTemplateId="4001" EquipmentTemplateName="新建设备模板1" 
                                      ProtocolCode="NEW_PROTOCOL_1" EquipmentCategory="1" 
                                      EquipmentType="1" Description="新建设备模板1">
                        <Signals>
                            <Signal SignalId="5001" SignalName="新建信号1" SignalCategory="1" 
                                   SignalType="1" ChannelNo="1" DataType="1" 
                                   Unit="V" Enable="true" Visible="true" Description="新建信号1"/>
                            <Signal SignalId="5002" SignalName="新建信号2" SignalCategory="2" 
                                   SignalType="2" ChannelNo="2" DataType="2" 
                                   Unit="A" Enable="true" Visible="true" Description="新建信号2"/>
                        </Signals>
                        <Events>
                            <Event EventId="6001" EventName="新建事件1" SignalId="5001" 
                                  EventCategory="1" StartType="1" EndType="2" 
                                  Enable="true" Visible="true" Description="新建事件1"/>
                        </Events>
                        <Controls>
                            <Control ControlId="7001" ControlName="新建控制1" ControlCategory="1" 
                                    CmdToken="NEW_CMD_1" Enable="true" Visible="true" 
                                    Description="新建控制1"/>
                        </Controls>
                    </EquipmentTemplate>
                    <EquipmentTemplate EquipmentTemplateId="4002" EquipmentTemplateName="新建设备模板2" 
                                      ProtocolCode="NEW_PROTOCOL_2" EquipmentCategory="2" 
                                      EquipmentType="2" Description="新建设备模板2">
                        <Signals>
                            <Signal SignalId="5003" SignalName="新建信号3" SignalCategory="1" 
                                   SignalType="1" ChannelNo="1" DataType="1" 
                                   Unit="W" Enable="true" Visible="true" Description="新建信号3"/>
                        </Signals>
                        <Events>
                            <Event EventId="6002" EventName="新建事件2" SignalId="5003" 
                                  EventCategory="2" StartType="1" EndType="2" 
                                  Enable="true" Visible="true" Description="新建事件2"/>
                        </Events>
                        <Controls>
                            <Control ControlId="7002" ControlName="新建控制2" ControlCategory="2" 
                                    CmdToken="NEW_CMD_2" Enable="true" Visible="true" 
                                    Description="新建控制2"/>
                        </Controls>
                    </EquipmentTemplate>
                </EquipmentTemplates>
            </MainCfg>
            """;
    }

    /**
     * 创建新建监控单元但使用已存在设备模板的XML内容
     */
    private String createNewMonitorUnitWithExistingTemplatesXmlContent() {
        return """
            <?xml version="1.0" encoding="UTF-8"?>
            <MainCfg>
                <MonitorUnit MonitorUnitId="888" MonitorUnitName="新建监控单元（使用已存在模板）"
                            MonitorUnitCategory="1" MonitorUnitCode="NEW_EXISTING001"
                            StationId="1" IpAddress="*************"
                            RunMode="1" Description="新建监控单元，但使用已存在的设备模板">
                    <Ports>
                        <Port PortId="8001" PortNo="1" PortName="新建端口1"
                              PortType="1" Setting="9600,8,1,N" Description="新建端口"/>
                    </Ports>
                    <SamplerUnits>
                        <SamplerUnit SamplerUnitId="8002" PortId="8001" ParentSamplerUnitId="0"
                                    SamplerUnitName="新建采集单元" SamplerType="1"
                                    Address="1" SpUnitInterval="5.0" Description="新建采集单元"/>
                    </SamplerUnits>
                    <Equipments>
                        <Equipment EquipmentId="8003" EquipmentName="新建设备（使用已存在模板）"
                                  EquipmentCategory="1" EquipmentTemplateId="1"
                                  SamplerUnitId="8002" Description="新建设备，使用已存在的设备模板ID=1"/>
                    </Equipments>
                </MonitorUnit>
                <EquipmentTemplates>
                    <!-- 使用已存在的设备模板ID，但可能有一些字段修改 -->
                    <EquipmentTemplate EquipmentTemplateId="1" EquipmentTemplateName="测试设备模板（修改版）"
                                      ProtocolCode="TEST_MODIFIED" EquipmentCategory="1"
                                      EquipmentType="1" Description="修改后的测试设备模板">
                        <Signals>
                            <!-- 可能包含对现有信号的修改或新增信号 -->
                            <Signal SignalId="1" SignalName="测试信号（修改版）" SignalCategory="1"
                                   SignalType="1" ChannelNo="1" DataType="1"
                                   Unit="V" Enable="true" Visible="true" Description="修改后的测试信号"/>
                            <Signal SignalId="9001" SignalName="新增信号" SignalCategory="2"
                                   SignalType="2" ChannelNo="2" DataType="2"
                                   Unit="A" Enable="true" Visible="true" Description="新增的信号"/>
                        </Signals>
                        <Events>
                            <Event EventId="1" EventName="测试事件" SignalId="1"
                                  EventCategory="1" StartType="1" EndType="2"
                                  Enable="true" Visible="true" Description="测试事件"/>
                        </Events>
                        <Controls>
                            <Control ControlId="1" ControlName="测试控制" ControlCategory="1"
                                    CmdToken="TEST_CMD" Enable="true" Visible="true"
                                    Description="测试控制"/>
                        </Controls>
                    </EquipmentTemplate>
                </EquipmentTemplates>
            </MainCfg>
            """;
    }
}
