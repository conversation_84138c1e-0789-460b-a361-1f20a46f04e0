package com.siteweb.stream.core.runtime;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.jsontype.NamedType;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.siteweb.stream.common.annotations.*;
import com.siteweb.stream.common.options.SimpleShapeOption;
import com.siteweb.stream.common.runtime.AbstractShape;
import com.siteweb.stream.common.runtime.EnumDescriptor;
import com.siteweb.stream.common.stream.*;
import com.siteweb.stream.core.entity.StreamLibrary;
import com.siteweb.tcs.common.annotations.EnumScan;
import com.siteweb.tcs.common.annotations.IEnumerable;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.type.classreading.MetadataReader;
import org.springframework.core.type.classreading.SimpleMetadataReaderFactory;
import org.springframework.util.StreamUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.reflect.Modifier;
import java.net.URL;
import java.net.URLClassLoader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> (2025-05-27)
 **/
@Slf4j
public class StreamModuleInstance {
    private static final String STREAM_MODULE_ID = "stream.module.id";
    private static final String STREAM_MODULE_NAME = "stream.module.name";
    private static final String STREAM_MODULE_PROVIDER = "stream.module.provider";
    private static final String STREAM_MODULE_VERSION = "stream.module.version";


    @Getter
    private final String libraryId;

    @Getter
    private final String libraryPackage;

    @Getter
    private Boolean isLoaded;

    @Getter
    @Setter
    private boolean isEnabled;

    private URLClassLoader classLoader;

    @Getter
    private SimpleModule typeModule;

    @Getter
    private final Map<String, StreamShapeDescriptor> descriptors = new HashMap<>();
    @Getter
    private final StreamLibrary streamLibrary;

    private Map<String, JsonNode> schemas = new HashMap<>();
    private Map<String, List<StreamShapeInfo>> shapeI18nDescriptors = new HashMap<>();
    private HashSet<String> i18ns = new HashSet<>();
    private Map<String, Map<String, String>> documents = new HashMap<>();
    private Map<String, List<EnumDescriptor>> enums = new HashMap<>();
    private final Map<String, Properties> properties = new HashMap<>();


    public StreamModuleInstance(StreamLibrary streamLibrary, ClassLoader parentClassLoader) throws Exception {
        this.isLoaded = false;
        this.streamLibrary = streamLibrary;
        var jarFile = new File(streamLibrary.getJarFile());
        this.libraryId = streamLibrary.getLibraryId();
        this.libraryPackage = streamLibrary.getLibraryPackage();
        this.typeModule = new SimpleModule("stream.module." + libraryId);
        this.classLoader = new URLClassLoader(new URL[]{jarFile.toURI().toURL()}, getClass().getClassLoader());
    }

    public void load() {
        try {
            scanSchema();
            scanI18nProperties();
            scanDocuments();
            loadClassTypes();
            isLoaded = true;
        } catch (Exception ex) {
            unload();
            throw ex;
        }
    }


    public List<StreamShapeInfo> getShapes(String i18n) {
        if (!isEnabled) return List.of();
        if (Objects.isNull(i18n) || i18n.isEmpty()){
            var list = shapeI18nDescriptors.get("zh-CN");
            if (list == null)list = shapeI18nDescriptors.get("en-US");
            if (list == null) return List.of();
            return List.copyOf(list);
        }
        var list = shapeI18nDescriptors.get(i18n);
        if (list == null) return List.of();
        return List.copyOf(list);
    }

    public List<EnumDescriptor> getEnums(String i18n) {
        if (!isEnabled) return List.of();
        return enums.get(i18n);
    }


    private void loadClassTypes() {
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver(classLoader);
        SimpleMetadataReaderFactory factory = new SimpleMetadataReaderFactory();
        try {
            var packagePath = libraryPackage.replace(".", "/");
            if (!packagePath.endsWith("/")) packagePath += "/";
            Resource[] resources = resolver.getResources("classpath*:" + packagePath + "**/*.class");
            for (var resource : resources) {
                try {
                    MetadataReader reader = factory.getMetadataReader(resource);
                    String className = reader.getClassMetadata().getClassName();
                    var clazz = this.classLoader.loadClass(className);
                    if (StreamShapeOption.class.isAssignableFrom(clazz) && !Modifier.isAbstract(clazz.getModifiers())) {
                        // 处理注册配置类型
                        handleOptionTyped(clazz);
                    } else if (AbstractShape.class.isAssignableFrom(clazz) && !Modifier.isAbstract(clazz.getModifiers())) {
                        // 处理注册Shape类型
                        handleShapeTyped(clazz);
                    } else if (clazz.isEnum()) {
                        // 处理注册 Enum类型
                        var enumScan = clazz.getAnnotation(EnumScan.class);
                        if (enumScan != null && IEnumerable.class.isAssignableFrom(clazz)) {
                            var valuesMethod = clazz.getMethod("values");
                            var results = (IEnumerable[]) valuesMethod.invoke(null);
                            handleScanEnum(enumScan, results);
                        }
                    }
                } catch (IOException | ClassNotFoundException e) {
                    // 扫描过程中任何异常均视为不合法的资源,自动跳过
                    log.error("An anomaly occurred during the Shape scanning process", e);
                }
            }
        } catch (Exception ex) {
            // 扫描过程中任何异常均视为不合法的资源,自动跳过
            log.error("An exception occurred during the scanning process of Jar resources", ex);
        }

    }


    private void handleSchema(String shapeType, JsonNode schema) {
        schemas.put(shapeType, schema);
    }


    /***
     * 注册扫描到的Shape
     * <AUTHOR> (2025/5/28)
     * @param clazz
     */
    private void handleShapeTyped(Class<?> clazz) {
        var descriptor = parseShapeMetadata(clazz);
        if (descriptor == null) return;
        // 补全 schema
        if (schemas.containsKey(descriptor.getType())) {
            descriptor.setOptionSchema(schemas.get(descriptor.getType()));
        }
        descriptors.put(descriptor.getType(), descriptor);
        for (var i18nName : i18ns) {
            var shapeInfo = descriptor.toShapeInfo();
            var i18nMap = shapeI18nDescriptors.computeIfAbsent(i18nName.replace("_", "-"), k -> new ArrayList<>());
            shapeInfo.setShapeName(getI18n(descriptor, i18nName, "name"));
            shapeInfo.setShapeAlias(getI18n(descriptor, i18nName, "alias"));
            shapeInfo.setTooltip(getI18n(descriptor, i18nName, "tooltip"));
            shapeInfo.setGroups(getI18nArray(descriptor, i18nName, "groups"));
            shapeInfo.setTags(getI18nArray(descriptor, i18nName, "tags"));

            if (documents.get(i18nName) != null) {
                shapeInfo.setDocument(documents.get(i18nName).get(shapeInfo.getShapeType()));
            }

            for (var inlet : shapeInfo.getInlets()) {
                inlet.setLetName(getI18n(descriptor, i18nName, String.format("inlet%s.name", inlet.getLetId())));
            }
            for (var outlet : shapeInfo.getOutlets()) {
                outlet.setLetName(getI18n(descriptor, i18nName, String.format("outlet%s.name", outlet.getLetId())));
            }
            i18nMap.add(shapeInfo);
        }


    }


    /***
     * 注册扫描到的Option类型
     * <AUTHOR> (2025/5/28)
     * @param clazz
     */
    private void handleOptionTyped(Class<?> clazz) {
        var name = clazz.getName();
        typeModule.registerSubtypes(new NamedType(clazz, name));
    }


    /***
     *
     * <AUTHOR> (2025/5/28)
     * @param enumScan
     * @param values
     */
    private void handleScanEnum(EnumScan enumScan, IEnumerable[] values) {
        for (var i18nName : i18ns) {
            var enumDescriptor = new EnumDescriptor();
            enumDescriptor.setKey(enumScan.name());
            enumDescriptor.setDesc(getI18n(i18nName, enumScan.name()));
            for (IEnumerable ele : values) {
                var element = new EnumDescriptor.EnumElement();
                element.setValue(ele.getValue());
                element.setDesc(getI18n(i18nName, ele.getDescription()));
                enumDescriptor.getItems().add(element);
            }
            var list = enums.computeIfAbsent(i18nName, k -> new ArrayList<>());
            list.add(enumDescriptor);
        }
    }


    private void handleI18nProperties(String i18n, Properties properties) {
        i18ns.add(i18n);
        this.properties.put(i18n, properties);
    }


    private void handleI18nDocument(String i18n, String shapeType, String document) {
        i18ns.add(i18n);
        var i18nMap = documents.computeIfAbsent(i18n, k -> new HashMap<>());
        i18nMap.put(shapeType, document);
    }


    /**
     * 卸载Library，
     *
     * @return 是否卸载成功，如资源未释放完毕需要返回false并支持重试机制
     */
    public boolean unload() {
        try {
            this.typeModule = null;
            this.documents.clear();
            this.properties.clear();
            this.shapeI18nDescriptors.clear();
            this.descriptors.clear();
            this.i18ns.clear();
            this.schemas.clear();
            this.isLoaded = false;
            this.classLoader.close();
            this.classLoader = null;
        } catch (Throwable ex) {
            return false;
        } finally {
            System.gc();
            System.gc();
            System.gc();
            System.gc();
        }


        return true;
    }


    private void scanSchema() {
        String path = "classpath*:schema/*.json"; // 查找 document 目录下所有子目录的 md 文件
        Resource[] resources;
        try {
            resources = new PathMatchingResourcePatternResolver(classLoader).getResources(path);
            for (var resource : resources) {
                String fileName = resource.getFilename(); // ✅正确获取文件名
                String shapeType = fileName.contains(".") ? fileName.substring(0, fileName.lastIndexOf('.')) : fileName;
                try (InputStream inputStream = resource.getInputStream()) {
                    String schemaJson = StreamUtils.copyToString(inputStream, StandardCharsets.UTF_8);
                    var schema = toSchema(schemaJson);
                    handleSchema(shapeType, schema);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private JsonNode toSchema(String jsonString) {
        try {
            return JacksonTypeHandler.getObjectMapper().readValue(jsonString, JsonNode.class);
        } catch (JsonProcessingException e) {
            return null;
        }
    }

    /**
     * 扫描指定ClassLoader下的所有Shape的I18n属性配置文档，并写入到result
     *
     * @return
     */
    private void scanI18nProperties() {
        String path = "classpath*:i18n/shapes_*.properties"; // 查找 document 目录下所有子目录的 md 文件
        Resource[] resources;
        try {
            resources = new PathMatchingResourcePatternResolver(classLoader).getResources(path);
            for (var resource : resources) {
                String fileName = resource.getFilename(); // ✅正确获取文件名
                Pattern pattern = Pattern.compile("shapes_([a-z]{2}_[A-Z]{2})\\.properties$");
                Matcher matcher = pattern.matcher(fileName);
                if (matcher.find()) {
                    String i18nName = matcher.group(1);
                    Properties properties = new Properties();
                    try (InputStreamReader reader = new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8)) {
                        properties.load(reader);
                        handleI18nProperties(i18nName, properties);
                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 扫描指定ClassLoader下的所有Shape的I18n属性配置文档，并写入到result
     *
     * @return
     */
    private void scanDocuments() {
        String path = "classpath*:document/**/*.md"; // 查找 document 目录下所有子目录的 md 文件
        Resource[] resources;
        try {
            resources = new PathMatchingResourcePatternResolver(classLoader).getResources(path);
            for (var resource : resources) {
                // 资源名（如 overview.md）
                String fileName = resource.getFilename();
                // 完整路径（支持 file: 或 jar:file:）
                String fullPath = resource.getURL().toString();
                // 提取上级目录名（多语言标识）
                String i18nName = extractParentDirName(fullPath).replace("-", "_");
                // 去掉扩展名
                String shapeType = fileName.contains(".") ? fileName.substring(0, fileName.lastIndexOf('.')) : fileName;
                try (InputStream inputStream = resource.getInputStream()) {
                    String markdown = StreamUtils.copyToString(inputStream, StandardCharsets.UTF_8);
                    handleI18nDocument(i18nName, shapeType, markdown);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private String extractParentDirName(String urlPath) {
        // 示例 jar URL: jar:file:/xxx/app.jar!/document/zh-cn/intro.md
        // 示例 file URL: file:/xxx/classes/document/en-us/intro.md
        String path = urlPath.replace("\\", "/");
        int lastSlash = path.lastIndexOf('/');
        int secondLastSlash = path.lastIndexOf('/', lastSlash - 1);
        if (secondLastSlash >= 0 && lastSlash > secondLastSlash) {
            return path.substring(secondLastSlash + 1, lastSlash); // 获取如 zh-cn、en-us
        }
        return "default";
    }

    private StreamShapeDescriptor parseShapeMetadata(Class<?> clazz) {
        var graphShape = clazz.getAnnotation(Shape.class);
        if (graphShape == null) return null;

        var shapeIcon = clazz.getAnnotation(ShapeIcon.class);
        var shapeColor = clazz.getAnnotation(ShapeColor.class);
        var defaultOptions = clazz.getAnnotation(ShapeDefaultOptions.class);
        var shapeAuthor = clazz.getAnnotation(ShapeAuthor.class);
        var deprecated = clazz.getAnnotation(ShapeDeprecated.class);
        var shapeInlets = clazz.getAnnotationsByType(ShapeInlet.class);
        var shapeOutlets = clazz.getAnnotationsByType(ShapeOutlet.class);
        var shapeVersion = clazz.getAnnotation(ShapeVersion.class);
        var editorHidden = clazz.getAnnotation(EditorHidden.class);

        StreamShapeDescriptor descriptor = new StreamShapeDescriptor();
        descriptor.setI18nPrefix(graphShape.i18nPrefix());
        descriptor.setType(graphShape.type());
        descriptor.setDeprecated(graphShape.deprecated());
        descriptor.setClazz(clazz);
        descriptor.setClassName(clazz.getName());
        descriptor.setModuleId(libraryId);
        descriptor.setPackageName(clazz.getPackageName());
        descriptor.setVersion(StreamShapeVersion.fromAnnotation(shapeVersion));
        descriptor.setInternal(editorHidden != null);


        if (Objects.nonNull(shapeAuthor)) descriptor.setAuthor(shapeAuthor.value());
        // deprecated
        if (deprecated != null) descriptor.setDeprecated(true);
        // icon
        if (shapeIcon != null) descriptor.setIcon(shapeIcon.value());

        if (shapeColor != null) {
            descriptor.setBkColor(shapeColor.bkColor());
            descriptor.setIconColor(shapeColor.iconColor());
        }


        // default options
        var options = createDefaultOptions(defaultOptions);
        options.setIcon(descriptor.getIcon());
        options.setComment("");
        options.setBkColor(descriptor.getBkColor());
        options.setIconColor(descriptor.getIconColor());
        options.setVersion(descriptor.getVersion());
        descriptor.setDefaultOptions(options);
        // inlets
        for (ShapeInlet inlet : shapeInlets) {
            var let = new StreamShapeLetInfo();
            let.setLetId(inlet.id());
            let.setDataType(inlet.type());
            let.setMaxFan(inlet.maxFan());
            var existing = descriptor.getInlets().stream().filter(e -> e.getLetId() == inlet.id()).findAny();
            if (existing.isPresent())
                throw new RuntimeException(String.format("Shape[%s] 内重复的InletId: %d", clazz.getName(), inlet.id()));
            descriptor.getInlets().add(let);
        }
        // outlets
        for (ShapeOutlet outlet : shapeOutlets) {
            var let = new StreamShapeLetInfo();
            let.setLetId(outlet.id());
            let.setDynamic(outlet.dynamic());
            let.setDataType(outlet.type());
            let.setMaxFan(outlet.maxFan());
            var existing = descriptor.getOutlets().stream().filter(e -> e.getLetId() == outlet.id()).findAny();
            if (existing.isPresent())
                throw new RuntimeException(String.format("Shape[%s] 内重复的OutletId: %d", clazz.getName(), outlet.id()));
            descriptor.getOutlets().add(let);
        }
        return descriptor;
    }

    private StreamShapeOption createDefaultOptions(ShapeDefaultOptions defaultOptions) {
        try {
            AbstractShapeDefaultOption instance = defaultOptions.value().getConstructor().newInstance();
            return instance.option();
        } catch (Exception e) {
            return new SimpleShapeOption();
        }
    }

    private String getI18n(String i18nName, String i18nPath) {
        Properties i18nProperties = properties.get(i18nName);
        if (i18nProperties != null) {
            var value = i18nProperties.get(i18nPath);
            if (value != null) return String.valueOf(value);
        }
        return i18nPath;
    }

    private String getI18n(StreamShapeDescriptor descriptor, String i18nName, String key) {
        var i18nPath = String.format("%s.%s", descriptor.getType(), key);
        var i18nMap = properties.get(i18nName);
        if (i18nMap == null) return i18nPath;
        if (i18nMap.containsKey(i18nPath)) {
            return String.valueOf(i18nMap.get(i18nPath));
        }
        return i18nPath;
    }

    private String[] getI18nArray(StreamShapeDescriptor descriptor, String i18nName, String key) {
        var i18nPath = String.format("%s.%s", descriptor.getType(), key);
        var i18nMap = properties.get(i18nName);
        if (i18nMap == null) return new String[0];
        if (i18nMap.containsKey(i18nPath)) {
            return String.valueOf(i18nMap.get(i18nPath)).split(",");
        }
        return new String[0];
    }

}
