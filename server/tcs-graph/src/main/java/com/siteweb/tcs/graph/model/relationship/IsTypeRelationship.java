package com.siteweb.tcs.graph.model.relationship;

import com.siteweb.tcs.graph.model.node.DeviceTypeNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.neo4j.core.schema.RelationshipProperties;
import org.springframework.data.neo4j.core.schema.TargetNode;

/**
 * Is Type relationship entity for Neo4j
 * Used for device to device type relationships
 */
@RelationshipProperties
@Data
@EqualsAndHashCode(callSuper = true)
public class IsTypeRelationship extends BaseRelationship {
    
    @TargetNode
    private DeviceTypeNode deviceType;
}
