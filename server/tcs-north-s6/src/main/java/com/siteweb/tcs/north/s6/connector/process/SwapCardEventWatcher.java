package com.siteweb.tcs.north.s6.connector.process;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.common.runtime.PluginScope;
import com.siteweb.tcs.hub.domain.letter.EquipmentLoggingEvent;
import com.siteweb.tcs.hub.domain.letter.LoggingEvent;
import com.siteweb.tcs.hub.domain.letter.SwapCardEvent;
import com.siteweb.tcs.hub.domain.letter.enums.LoggingEventEnum;
import com.siteweb.tcs.north.s6.dal.entity.SynCardAuth;
import com.siteweb.tcs.north.s6.dal.mapper.LoggingEventMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.Props;
import org.apache.pekko.japi.pf.ReceiveBuilder;

import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: SwapCardEventWatcher
 * @descriptions: 刷卡事件监听者
 * @author: xsx
 * @date: 2024/9/25 19:07
 **/
@Slf4j
public class SwapCardEventWatcher extends AbstractActor {

    private final Integer MAX_LENGTH_OF_PARAMETER = 3300;
    private final Character SPLITTER_OF_PARAMETER = ',';
    DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private LoggingEventMapper loggingEventMapper = PluginScope.getBean(LoggingEventMapper.class);


    public static Props props(){
        return Props.create(SwapCardEventWatcher.class);
    }

    @Override
    public Receive createReceive() {
        return ReceiveBuilder.create()
                .match(EquipmentLoggingEvent.class,this::onEquipmentLoggingEvent).build();
    }

    private void onEquipmentLoggingEvent(EquipmentLoggingEvent equipmentLoggingEvent) {
        if(!ObjectUtil.equal(equipmentLoggingEvent.getLoggingEventEnum(), LoggingEventEnum.SWAP_CARD)
        || CollectionUtil.isEmpty(equipmentLoggingEvent.getLoggingEvent()))
            return;
        List<LoggingEvent> swapCardEventList = equipmentLoggingEvent.getLoggingEvent();
        int signalTotal = 0;
        Integer eventSize = swapCardEventList.size();
        StringBuilder sequenceId = new StringBuilder();
        StringBuilder sampleTime = new StringBuilder();
        StringBuilder doorId = new StringBuilder();
        StringBuilder cardId = new StringBuilder();
        StringBuilder enter = new StringBuilder();
        StringBuilder swapFlag = new StringBuilder();
        for (int i = 0; i < eventSize; i++) {
            signalTotal++;
            SwapCardEvent swapCardEvent = (SwapCardEvent) swapCardEventList.get(i);
            if (sequenceId.length() > MAX_LENGTH_OF_PARAMETER
                    || sampleTime.length() > MAX_LENGTH_OF_PARAMETER
                    || doorId.length() > MAX_LENGTH_OF_PARAMETER
                    || cardId.length() > MAX_LENGTH_OF_PARAMETER
                    || enter.length() > MAX_LENGTH_OF_PARAMETER
                    || swapFlag.length() > MAX_LENGTH_OF_PARAMETER
                    || signalTotal == eventSize
                    || i == eventSize - 1){
                sequenceId.append(swapCardEvent.getSequenceId());
                sampleTime.append(swapCardEvent.getSampleTime().format(df));
                doorId.append(swapCardEvent.getDoorNo());
                cardId.append(swapCardEvent.getCardCode());
                enter.append(swapCardEvent.getEnter());
                swapFlag.append(swapCardEvent.getSwapFlag());
                Map<String,Object> params = new HashMap<>();
                params.put("StationId",equipmentLoggingEvent.getStationId());
                params.put("HostId",equipmentLoggingEvent.getMonitorUnitId());
                params.put("EquipmentId",equipmentLoggingEvent.getEquipmentId());
                params.put("SignalTotal",signalTotal);
                params.put("BSequenceId",sequenceId.toString());
                params.put("BSampleTime",sampleTime.toString());
                params.put("BDoorId",doorId.toString());
                params.put("BCardId",cardId.toString());
                params.put("BEnter",enter.toString());
                params.put("BSwapFlag",swapFlag.toString());
                params.put("ret",null);
                //todo xsx 卡授权同步
                List<SynCardAuth> synCardAuthList = loggingEventMapper.batchSaveSwapCardEvent(params);
                log.info("刷卡事件入库，入库参数是{}，返回结果是{}",params,synCardAuthList);
                signalTotal = 0;
                sequenceId.delete(0, sequenceId.length());
                sampleTime.delete(0, sampleTime.length());
                doorId.delete(0, doorId.length());
                cardId.delete(0, cardId.length());
                enter.delete(0, enter.length());
                swapFlag.delete(0, swapFlag.length());
            }else {
                sequenceId.append(swapCardEvent.getSequenceId()).append(SPLITTER_OF_PARAMETER);
                sampleTime.append(swapCardEvent.getSampleTime().format(df)).append(SPLITTER_OF_PARAMETER);
                doorId.append(swapCardEvent.getDoorNo()).append(SPLITTER_OF_PARAMETER);
                cardId.append(swapCardEvent.getCardCode()).append(SPLITTER_OF_PARAMETER);
                enter.append(swapCardEvent.getEnter()).append(SPLITTER_OF_PARAMETER);
                swapFlag.append(swapCardEvent.getSwapFlag()).append(SPLITTER_OF_PARAMETER);
            }
        }
    }
}

