package com.siteweb.tcs.north.s6.config;

import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;

/**
 * 定义Mybatis 数据库数据源
 **/
@Configuration
// TODO MapperScan 根据插件包名修改插件Mapper扫描路径
@MapperScan(basePackages = "com.siteweb.tcs.north.s6.dal.mapper", sqlSessionTemplateRef = "myPluginSqlSessionTemplate")
public class MyBatisConfig {
    @Bean(name = "myPluginSqlSessionFactory")
    public SqlSessionFactory myPluginSqlSessionFactory(@Qualifier("myPluginDataSource") DataSource primaryDataSource) throws Exception {
        MybatisSqlSessionFactoryBean sessionFactory = new MybatisSqlSessionFactoryBean();
        sessionFactory.setDataSource(primaryDataSource);
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        //此处添加主项目的config文件
        sessionFactory.setConfigLocation(resolver.getResource("classpath:mybatis/mybatis-config.xml"));
        //此处加载本插件的mapper目录下的xml文件
        sessionFactory.setMapperLocations(resolver.getResources("classpath*:mapper/tcs-north-s6/*.xml"));
        return sessionFactory.getObject();
    }

    @Bean(name = "myPluginSqlSessionTemplate")
    public SqlSessionTemplate myPluginSqlSessionTemplate(@Qualifier("myPluginSqlSessionFactory") SqlSessionFactory primarySqlSessionFactory) {
        return new SqlSessionTemplate(primarySqlSessionFactory);
    }
}