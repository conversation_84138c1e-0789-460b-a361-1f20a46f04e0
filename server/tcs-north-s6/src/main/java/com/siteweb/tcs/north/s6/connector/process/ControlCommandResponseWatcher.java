package com.siteweb.tcs.north.s6.connector.process;

import com.siteweb.tcs.common.o11y.ActorProbe;
import com.siteweb.tcs.common.runtime.PluginScope;
import com.siteweb.tcs.hub.domain.letter.ControlCommandResponse;
import com.siteweb.tcs.hub.domain.letter.EquipmentControlCommandResponse;
import com.siteweb.tcs.north.s6.dal.mapper.ControlMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.Props;

import java.util.HashMap;
import java.util.Map;

import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.createProbe;
import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.removeProbe;

/**
 * <AUTHOR> (2024-08-13)
 **/
@Slf4j
public class ControlCommandResponseWatcher extends AbstractActor {
    private final ActorProbe probe = createProbe(this);
    private final ControlMapper controlMapper;

    public ControlCommandResponseWatcher() {
        this.controlMapper = PluginScope.getBean(ControlMapper.class);
    }


    public static Props props() {
        return Props.create(ControlCommandResponseWatcher.class);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(EquipmentControlCommandResponse.class, this::updateEquipmentControlCommandResponse)
                .build();
    }


    private void updateEquipmentControlCommandResponse(EquipmentControlCommandResponse response) {
        // todo  调用存储过程PBL_SaveControlResult保存
        log.trace("CommandResponseWatcher 收到控制结果 {} {}", response.getEquipmentId(), response.getCommandResponseList().size());
        for (ControlCommandResponse ccr : response.getCommandResponseList()) {
            Map<String, Object> params = new HashMap<>();
            params.put("stationId", ccr.getStationId());
            params.put("monitorUnitId", ccr.getMonitorUnitId());
            params.put("equipmentId", ccr.getEquipmentId());
            params.put("controlId", ccr.getControlId());
            params.put("serialNo", ccr.getSequenceNo());
            params.put("startTime", ccr.getStartTime());  // ???
            params.put("endTime", ccr.getEndTime());
            params.put("userId", null);
            params.put("baseTypeId", null);  // ???
            params.put("resultCode", ccr.getResultCode());
            params.put("result", ccr.getResultDesc());
            params.put("controlPhase", 4);
            log.info("控制命令结果入库，{} [{}] [{}.{}.{}.{}] {}", ccr.getStartTime(), ccr.getSequenceNo(), ccr.getStationId(), ccr.getMonitorUnitId(), ccr.getEquipmentId(), ccr.getControlId(), ccr.getResultDesc());
            this.controlMapper.saveControlResult(params);

        }
    }

    @Override
    public void postStop() {
        removeProbe(this.probe);
    }


}

