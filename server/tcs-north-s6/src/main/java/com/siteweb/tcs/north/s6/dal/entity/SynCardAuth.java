package com.siteweb.tcs.north.s6.dal.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @ClassName: SynCardAuth
 * @descriptions: 同步卡授权实体
 * @author: xsx
 * @date: 2024/9/25 19:54
 **/
@Data
public class SynCardAuth {
    private Integer stationId;
    private Integer equipmentId;
    private Integer controlId;
    private Integer doorNo;
    private Integer timeGroupType;
    private Integer status;
    private String password;
    private String cardCode;
    private LocalDateTime endTime;
}
