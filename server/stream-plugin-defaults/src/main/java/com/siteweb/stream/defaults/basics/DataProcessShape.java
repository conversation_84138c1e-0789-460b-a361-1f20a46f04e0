package com.siteweb.stream.defaults.basics;

import com.siteweb.stream.common.annotations.*;
import com.siteweb.stream.common.messages.StreamMessage;
import com.siteweb.stream.common.runtime.AbstractShape;
import com.siteweb.stream.common.stream.ShapeRuntimeContext;
import com.siteweb.stream.common.stream.StreamShapeOption;
import com.siteweb.stream.common.types.Any;
import com.siteweb.stream.common.types.Inferred;
import lombok.extern.slf4j.Slf4j;

/**
 * TODO 测试用，后期删除
 **/
@Slf4j
@EditorHidden
@Shape(type = "data-process")
@ShapeVersion(major = 1, minor = 0, patch = 0)
@ShapeIcon("icon-font")
@ShapeAuthor("Vertiv")
@ShapeInlet(id = 0x01, type = Any.class)
@ShapeOutlet(id = 0x01, type = Inferred.class)
public class DataProcessShape extends AbstractShape {

    @Recoverable
    protected int count;


    @Recoverable
    private StreamMessage current;

    public DataProcessShape(ShapeRuntimeContext context) {
        super(context);

    }

    @Override
    protected void onOptionReset(StreamShapeOption options) {
        count = 99; // 模拟保存Options
    }

    @Override
    protected void onRestart(Throwable reason) {
        log.info("节点重启完毕，[count]状态已恢复。");
        if (current != null) {
            try {
                processMessage(current);
            } catch (Exception ex) {
                log.error("数据异常，无法处理，");
            }
        }
    }


    @Override
    protected void processMessage(StreamMessage message) {
        // 将消息发送给出口
        var outlet = context.getOutLet((short) 0x01);
        outlet.broadcast(message);
    }


    @Override
    protected final void onStart() {
        //

    }

    @Override
    protected final void onStop() {
        //

    }


}
