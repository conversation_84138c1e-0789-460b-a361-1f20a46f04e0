# ==================================================
# Comment Component
# ==================================================
streams.shapes.comment.name=注释
streams.shapes.comment.alias=文本注释
streams.shapes.comment.groups=通用
streams.shapes.comment.tags=注释,注解
# ==================================================
# Switch Component
# ==================================================
streams.shapes.data-switch.name=数据条件分支
streams.shapes.data-switch.alias=条件分支
streams.shapes.data-switch.groups=数据逻辑,数据分支
streams.shapes.data-switch.tags=Switch,分支,判断,条件
streams.shapes.data-switch.inlet1.name=In
streams.shapes.data-switch.outlet1.name=Out-{index}
# ==================================================
# Timer Component
# ==================================================
streams.shapes.fixed-timer.name=固定间隔定时器
streams.shapes.fixed-timer.alias=定时器
streams.shapes.fixed-timer.groups=定时器
streams.shapes.fixed-timer.tags=定时器,Time,Timer
streams.shapes.fixed-timer.outlet1.name=Out
# ==================================================
# Tracer Component
# ==================================================
streams.shapes.data-tracer.name=数据跟踪器
streams.shapes.data-tracer.alias=跟踪器
streams.shapes.data-tracer.groups=调试
streams.shapes.data-tracer.tags=调试,跟踪
streams.shapes.data-tracer.inlet1.name=输入
# ==================================================
# DataMapper Component
# ==================================================
streams.shapes.data-mapper.name=数据映射
streams.shapes.data-mapper.alias=映射
streams.shapes.data-mapper.groups=数据转换
streams.shapes.data-mapper.tags=Mapper,映射,转换
streams.shapes.data-mapper.inlet1.name=输入
streams.shapes.data-mapper.outlet1.name=输出
# ==================================================
# DataProcess Component
# ==================================================
streams.shapes.data-process.name=数据处理
streams.shapes.data-process.alias=处理
streams.shapes.data-process.groups=数据
streams.shapes.data-process.tags=处理,转换
streams.shapes.data-process.inlet1.name=数据输入
streams.shapes.data-process.outlet1.name=数据输出
