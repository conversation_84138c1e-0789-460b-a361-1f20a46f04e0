按属性值来分配消息的传送路线。

#### 详细

根据接收到的消息评估指定的规则，然后将消息发送到与匹配的规则相对应的输出端口。
可以将节点设置为一旦发现一个匹配的规则，则停止后续的匹配。
对于评估规则，可以使用消息属性，流上下文/全局上下文属性，环境变量和JSONata表达式的评估结果。

#### 规则

有四种规则：

值根据配置的属性评估规则
顺序可用于消息序列的规则，例如由“拆分”节点生成的规则
JSONata表达式评估整个消息，如果结果为真，则匹配。
其他上述规则都不匹配时适用
注释
is true/false与is null 规则将对类型进行严格的匹配。匹配之前的类型转化不会发生。
is empty规则与零字节的字符串，数组，缓冲区或没有属性的对象相匹配。与null或者undefined等不匹配。

#### 处理消息序列

默认情况下，节点不会修改msg.parts属性。
可以启用重建消息序列选项来为每条匹配的规则生成新的消息序列。在这种模式下，节点将在发送新序列之前对整个传入序列进行缓存。运行时的设定nodeMessageBufferMaxLength可以用来限制可缓存的消息数目。