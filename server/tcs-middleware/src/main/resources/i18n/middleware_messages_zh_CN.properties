# tcs-middleware 模块国际化资源文件（中文）
# 命名空间: middleware

# 控制器相关消息
middleware.controller.function.not_implemented=功能未实现
middleware.controller.create.failed=创建{0}失败
middleware.controller.update.failed=更新{0}失败
middleware.controller.delete.failed=删除{0}失败
middleware.controller.query.failed=查询{0}失败

# 资源配置相关消息
middleware.resource.config.create.success=资源配置创建成功
middleware.resource.config.create.failed=创建资源配置失败
middleware.resource.config.update.success=资源配置更新成功
middleware.resource.config.update.failed=更新资源配置失败
middleware.resource.config.delete.success=资源配置删除成功
middleware.resource.config.delete.failed=删除资源配置失败
middleware.resource.config.not_found=资源配置未找到

# 服务配置相关消息
middleware.service.config.create.success=服务配置创建成功
middleware.service.config.create.failed=创建服务配置失败
middleware.service.config.update.success=服务配置更新成功
middleware.service.config.update.failed=更新服务配置失败
middleware.service.config.delete.success=服务配置删除成功
middleware.service.config.delete.failed=删除服务配置失败
middleware.service.config.not_found=服务配置未找到

# 资源类型相关消息
middleware.resource.type.create.success=资源类型创建成功
middleware.resource.type.create.failed=创建资源类型失败
middleware.resource.type.update.success=资源类型更新成功
middleware.resource.type.update.failed=更新资源类型失败
middleware.resource.type.delete.success=资源类型删除成功
middleware.resource.type.delete.failed=删除资源类型失败
middleware.resource.type.not_found=资源类型未找到

# 服务类型相关消息
middleware.service.type.create.success=服务类型创建成功
middleware.service.type.create.failed=创建服务类型失败
middleware.service.type.update.success=服务类型更新成功
middleware.service.type.update.failed=更新服务类型失败
middleware.service.type.delete.success=服务类型删除成功
middleware.service.type.delete.failed=删除服务类型失败
middleware.service.type.not_found=服务类型未找到
