# tcs-middleware 模块国际化资源文件
# 命名空间: middleware

# 控制器相关消息
middleware.controller.function.not_implemented=Function not implemented
middleware.controller.create.failed=Failed to create {0}
middleware.controller.update.failed=Failed to update {0}
middleware.controller.delete.failed=Failed to delete {0}
middleware.controller.query.failed=Failed to query {0}

# 资源配置相关消息
middleware.resource.config.create.success=Resource configuration created successfully
middleware.resource.config.create.failed=Failed to create resource configuration
middleware.resource.config.update.success=Resource configuration updated successfully
middleware.resource.config.update.failed=Failed to update resource configuration
middleware.resource.config.delete.success=Resource configuration deleted successfully
middleware.resource.config.delete.failed=Failed to delete resource configuration
middleware.resource.config.not_found=Resource configuration not found

# 服务配置相关消息
middleware.service.config.create.success=Service configuration created successfully
middleware.service.config.create.failed=Failed to create service configuration
middleware.service.config.update.success=Service configuration updated successfully
middleware.service.config.update.failed=Failed to update service configuration
middleware.service.config.delete.success=Service configuration deleted successfully
middleware.service.config.delete.failed=Failed to delete service configuration
middleware.service.config.not_found=Service configuration not found

# 资源类型相关消息
middleware.resource.type.create.success=Resource type created successfully
middleware.resource.type.create.failed=Failed to create resource type
middleware.resource.type.update.success=Resource type updated successfully
middleware.resource.type.update.failed=Failed to update resource type
middleware.resource.type.delete.success=Resource type deleted successfully
middleware.resource.type.delete.failed=Failed to delete resource type
middleware.resource.type.not_found=Resource type not found

# 服务类型相关消息
middleware.service.type.create.success=Service type created successfully
middleware.service.type.create.failed=Failed to create service type
middleware.service.type.update.success=Service type updated successfully
middleware.service.type.update.failed=Failed to update service type
middleware.service.type.delete.success=Service type deleted successfully
middleware.service.type.delete.failed=Failed to delete service type
middleware.service.type.not_found=Service type not found
