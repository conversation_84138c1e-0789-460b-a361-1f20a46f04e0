<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.middleware.mapper.ServiceConfigurationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.middleware.entity.ServiceConfigurationEntity">
        <id column="id" property="id" />
        <result column="service_id" property="serviceId" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="config" property="config" typeHandler="com.siteweb.tcs.middleware.util.MapStringObjectTypeHandler" />
        <result column="resource_configuration_id" property="resourceConfigurationId" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
    </resultMap>

    <!-- 扩展的服务配置映射结果，包含服务类型名称和资源配置名称 -->
    <resultMap id="ExtendedResultMap" type="com.siteweb.tcs.middleware.entity.ServiceConfigurationEntity" extends="BaseResultMap">
        <result column="service_type_name" property="serviceTypeName" />
        <result column="resource_configuration_name" property="resourceConfigurationName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, service_id, name, description, config, resource_configuration_id, status, create_time, update_time, created_by, updated_by
    </sql>

    <!-- 分页查询服务配置，包含服务类型名称和资源配置名称 -->
    <select id="pageServiceConfigurations" resultMap="ExtendedResultMap">
        SELECT
            sc.id,
            sc.service_id,
            st.name AS service_type_name,
            sc.name,
            sc.description,
            sc.config,
            sc.resource_configuration_id,
            rc.name AS resource_configuration_name,
            sc.status,
            sc.create_time,
            sc.update_time,
            sc.created_by,
            sc.updated_by
        FROM
            mw_service_configuration sc
        LEFT JOIN
            mw_service_type st ON sc.service_id = st.id
        LEFT JOIN
            mw_resource_configuration rc ON sc.resource_configuration_id = rc.id
        <where>
            <if test="serviceId != null and serviceId != ''">
                AND sc.service_id = #{serviceId}
            </if>
            <if test="name != null and name != ''">
                AND sc.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="status != null and status != ''">
                AND sc.status = #{status}
            </if>
        </where>
        ORDER BY
            sc.id DESC
    </select>

    <!-- 根据ID查询服务配置，包含服务类型名称和资源配置名称 -->
    <select id="getServiceConfigurationById" resultMap="ExtendedResultMap">
        SELECT
            sc.id,
            sc.service_id,
            st.name AS service_type_name,
            sc.name,
            sc.description,
            sc.config,
            sc.resource_configuration_id,
            rc.name AS resource_configuration_name,
            sc.status,
            sc.create_time,
            sc.update_time,
            sc.created_by,
            sc.updated_by
        FROM
            mw_service_configuration sc
        LEFT JOIN
            mw_service_type st ON sc.service_id = st.id
        LEFT JOIN
            mw_resource_configuration rc ON sc.resource_configuration_id = rc.id
        WHERE
            sc.id = #{id}
    </select>

</mapper>
