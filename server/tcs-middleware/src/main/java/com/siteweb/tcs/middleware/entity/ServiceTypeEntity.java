package com.siteweb.tcs.middleware.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.siteweb.tcs.middleware.util.MapStringObjectTypeHandler;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 服务类型实体�?
 */
@Data
@TableName(value = "mw_service_type", autoResultMap = true)
public class ServiceTypeEntity {

    /**
     * 服务类型唯一标识
     */
    @TableId
    private String id;

    /**
     * 服务类型名称
     */
    private String name;

    /**
     * 服务类型描述
     */
    private String description;

    /**
     * 配置模板（JSON
     */
    @TableField(value = "default_config", typeHandler = MapStringObjectTypeHandler.class)
    private Map<String, Object> defaultConfig;

    /**
     * 前端页面资源配置组件
     */
    @TableField("ui_component")
    private String uiComponent;

    /**
     * 支持的资源类
     */
    @TableField("supported_resource_category")
    private String supportedResourceCategory;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;
}

