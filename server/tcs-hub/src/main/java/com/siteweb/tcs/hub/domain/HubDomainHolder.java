package com.siteweb.tcs.hub.domain;

import com.siteweb.tcs.common.system.ClusterContext;
import com.siteweb.tcs.common.util.ActorPathBuilder;
import com.siteweb.tcs.common.util.LogUtil;
import com.siteweb.tcs.hub.dal.entity.ForeignGateway;
import com.siteweb.tcs.hub.domain.process.lifecycle.GatewayPersistHandler;
import com.siteweb.tcs.hub.domain.process.lifecycle.HubLifeCycleEntry;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorInitializationException;
import org.apache.pekko.actor.InvalidActorNameException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component(value = "hubDomainHolder")
@Slf4j
public class HubDomainHolder {

    @Autowired
    private GatewayPersistHandler gatewayPersistHandler;

    @Autowired
    private HubConnectorDataHolder hubConnectorDataHolder;






    public void start() throws Exception {
        List<ForeignGateway> foreignGateways = gatewayPersistHandler.getAllGateways();

        try {
            var hubEntryActor = ClusterContext.getActorSystem().actorOf(HubLifeCycleEntry.props(gatewayPersistHandler, foreignGateways),
                    ActorPathBuilder.create().append("hubEntry").build());

            hubConnectorDataHolder.setHubEntry(hubEntryActor);

        } catch (ActorInitializationException e) {
            LogUtil.error(log, "hub.startup.failed.actor.init", e);            
        } catch (InvalidActorNameException e) {
            LogUtil.error(log, "hub.startup.failed.actor.name", e);
        } catch (IllegalStateException e) {
            LogUtil.error(log, "hub.startup.failed.actor.state", e);
        } catch (Exception e) {
            LogUtil.error(log, "hub.startup.failed.unexpected", e);
        }
    }

}
