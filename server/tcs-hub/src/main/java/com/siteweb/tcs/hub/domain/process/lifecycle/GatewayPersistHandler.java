package com.siteweb.tcs.hub.domain.process.lifecycle;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.common.util.SpringBeanUtil;
import com.siteweb.tcs.siteweb.dto.MonitorUnitDTO;
import com.siteweb.tcs.hub.dal.entity.*;
import com.siteweb.tcs.hub.domain.letter.ForeignGatewayConfigChange;
import com.siteweb.tcs.hub.domain.letter.LifeCycleEvent;
import com.siteweb.tcs.hub.service.RegionService;
import com.siteweb.tcs.hub.service.impl.*;
import com.siteweb.tcs.siteweb.dto.CreateMonitorUnitDTO;
import com.siteweb.tcs.siteweb.provider.MonitorUnitProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

@Component
@Slf4j
public class GatewayPersistHandler {

    private final ForeignGatewayService foreignGatewayService = SpringBeanUtil.getBean("foreignGatewayService", ForeignGatewayService.class);
    private final MonitorUnitProvider monitorUnitProvider = SpringBeanUtil.getBean("monitorUnitProvider",MonitorUnitProvider.class);
    private final PlatformDefaultIdService platformDefaultIdService = SpringBeanUtil.getBean("platformDefaultIdService",PlatformDefaultIdService.class);
    private final ForeignDeviceService foreignDeviceService = SpringBeanUtil.getBean(ForeignDeviceService.class);
    private final ForeignSignalService foreignSignalService = SpringBeanUtil.getBean(ForeignSignalService.class);
    private final ForeignAlarmService foreignAlarmService = SpringBeanUtil.getBean(ForeignAlarmService.class);
    private final ForeignControlService foreignControlService = SpringBeanUtil.getBean(ForeignControlService.class);

    private final RegionService regionService = SpringBeanUtil.getBean(RegionService.class);

    public ForeignGateway getForeignGateway(String foreignGatewayId,String pluginInstanceId) {

        ForeignGateway foreignGateway = foreignGatewayService.getForeignGateway(foreignGatewayId, pluginInstanceId);
        if (ObjectUtil.isEmpty(foreignGateway)) {
            return null;
        }

        //load foreignGateway's alarm,signal,control
        if (ObjectUtil.isNotEmpty(foreignGateway)) {
            foreignGateway.setForeignDeviceList(foreignDeviceService.selectForeignDeviceByMonitorUnitId(foreignGateway.getMonitorUnitID()));
            for (ForeignDevice foreignDevice : foreignGateway.getForeignDeviceList()){
                foreignDevice.setForeignSignalList(foreignSignalService.selectForeignSignalByEquipmentId(foreignDevice.getEquipmentId()));
                foreignDevice.setForeignAlarmList(foreignAlarmService.selectForeignAlarmByEquipmentId(foreignDevice.getEquipmentId()));
                foreignDevice.setForeignControlList(foreignControlService.selectForeignControlByEquipmentId(foreignDevice.getEquipmentId()));
            }
        }

        return foreignGateway;
    }

    public void handleDeleteGateway(String pluginInstanceId, String foreignGatewayId) throws Exception{
        ForeignGateway foreignGateway = foreignGatewayService.removeGateway(foreignGatewayId, pluginInstanceId);
        if (ObjectUtil.isEmpty(foreignGateway)) {
            return ;
        }

        try{
            monitorUnitProvider.deleteConfig(foreignGateway.getMonitorUnitID(),true);
        }catch (Exception ex){
            throw new Exception("删除网关失败");
        }
    }

    public boolean handleGatewayIdChange(LifeCycleEvent event) {
        ForeignGatewayConfigChange config = (ForeignGatewayConfigChange) event.getForeignConfigChange();

        String newForeignGatewayId = config.getForeignGatewayID();
        String pluginInstanceId = config.getPluginID();
        String oldForeignGatewayId = event.getForeignGatewayId();

        return  foreignGatewayService.updateForeignGatewayId(pluginInstanceId, oldForeignGatewayId, newForeignGatewayId);
    }

    public void handleCreateGateway(LifeCycleEvent event) throws Exception{
        ForeignGatewayConfigChange config = (ForeignGatewayConfigChange) event.getForeignConfigChange();
        if(ObjectUtil.isEmpty(config)){
            log.error("网关配置为空，忽略创建操作: pluginInstanceId={}, gatewayId={}",
                    event.getPluginInstanceId(),event.getForeignGatewayId());
            return;
        }
        if (foreignGatewayService.hasForeignGateway(event.getForeignGatewayId())) {
            log.warn("网关已存在数据库，忽略创建操作: pluginInstanceId={}, gatewayId={}",event.getPluginInstanceId(),event.getForeignGatewayId());
            return;
        }

        CreateMonitorUnitDTO createMonitorUnitDTO = config.toCreateMonitorUnitDTO();

        //s6层级id确定
        if(ObjectUtil.isEmpty(config.getRegionId())){
            Integer resourceStructureRootId = platformDefaultIdService.getResourceStructureRootId();
            if(ObjectUtil.isEmpty(resourceStructureRootId)){
                return;
            }
            createMonitorUnitDTO.setResourceStructureId(resourceStructureRootId);
        }else{
            Region region = regionService.findByRegionId(config.getRegionId());
            if(ObjectUtil.isEmpty(region)){
                return;
            }
            createMonitorUnitDTO.setResourceStructureId(region.getResourceStructureId());
        }

        MonitorUnitDTO monitorUnitDTO = new MonitorUnitDTO();
        try {
            com.siteweb.tcs.siteweb.dto.MonitorUnitDTO monitorUnit = monitorUnitProvider.createConfig(createMonitorUnitDTO);
            BeanUtils.copyProperties(monitorUnit, monitorUnitDTO);
        }catch (Exception ex){
            return;
        }

        ForeignGateway foreignGateway = new ForeignGateway(config.getForeignGatewayID(), monitorUnitDTO.getMonitorUnitId(), config.getPluginID(),monitorUnitDTO.getStationId(), null);

        foreignGatewayService.save(foreignGateway);

    }

    public void handleUpdateGateway(LifeCycleEvent event) {
        ForeignGatewayConfigChange foreignGatewayConfigChange = (ForeignGatewayConfigChange) event.getForeignConfigChange();
        String foreignGatewayID = foreignGatewayConfigChange.getForeignGatewayID();
        String pluginID = foreignGatewayConfigChange.getPluginID();

        ForeignGateway foreignGateway = foreignGatewayService.getForeignGateway(foreignGatewayID, pluginID);
        if (ObjectUtil.isEmpty(foreignGateway)) {
            return;
        }

        MonitorUnitDTO monitorUnitDTO =
                foreignGatewayConfigChange.toMonitorUnitDTO(foreignGateway.getMonitorUnitID());

        MonitorUnitDTO temp = monitorUnitProvider.findByID(foreignGateway.getMonitorUnitID());
        monitorUnitDTO.setStationId(temp.getStationId());
        monitorUnitProvider.update(monitorUnitDTO);
    }

    public List<ForeignGateway> getAllGateways() {
        log.info("[HUB CACHE INIT] hub初始化缓存，从数据库加载配置信息");

        List<ForeignGateway> gatewayList = foreignGatewayService.list();

        if (CollectionUtil.isNotEmpty(gatewayList)) {

            List<ForeignDevice> deviceList = foreignDeviceService.list();
            List<ForeignSignal> signalList = foreignSignalService.list();
            List<ForeignAlarm> alarmList = foreignAlarmService.list();
            List<ForeignControl> controlList = foreignControlService.list();

            log.info("[HUB CACHE INIT] hub初始化缓存，从数据库加载配置信息结束，开始组装信息");

            // 将信号、报警和控制列表转换为 Map，以便快速查找
            Map<Integer, List<ForeignSignal>> signalMap = signalList.stream()
                    .collect(Collectors.groupingBy(ForeignSignal::getEquipmentId));
            Map<Integer, List<ForeignAlarm>> alarmMap = alarmList.stream()
                    .collect(Collectors.groupingBy(ForeignAlarm::getEquipmentId));
            Map<Integer, List<ForeignControl>> controlMap = controlList.stream()
                    .collect(Collectors.groupingBy(ForeignControl::getEquipmentId));

            gatewayList.parallelStream().forEach(e -> {
                List<ForeignDevice> foreignDeviceList = new CopyOnWriteArrayList<>();
                e.setForeignDeviceList(foreignDeviceList);

                deviceList.stream()
                        .filter(d -> NumberUtil.equals(e.getMonitorUnitID(), d.getMonitorUnitId()))
                        .forEach(d -> {
                            d.setForeignSignalList(signalMap.getOrDefault(d.getEquipmentId(), Collections.emptyList())
                                    .stream()
                                    .map(s -> s.setEquipmentId(d.getEquipmentId())
                                            .setForeignDeviceID(d.getForeignDeviceID())
                                            .setForeignGatewayID(e.getForeignGatewayID())
                                            .setMonitorUnitId(e.getMonitorUnitID()))
                                    .collect(Collectors.toList()));

                            d.setForeignAlarmList(alarmMap.getOrDefault(d.getEquipmentId(), Collections.emptyList())
                                    .stream()
                                    .map(s -> s.setEquipmentId(d.getEquipmentId())
                                            .setForeignDeviceID(d.getForeignDeviceID())
                                            .setForeignGatewayID(e.getForeignGatewayID())
                                            .setMonitorUnitId(e.getMonitorUnitID()))
                                    .collect(Collectors.toList()));

                            d.setForeignControlList(controlMap.getOrDefault(d.getEquipmentId(), Collections.emptyList())
                                    .stream()
                                    .map(s -> s.setEquipmentId(d.getEquipmentId())
                                            .setForeignDeviceId(d.getForeignDeviceID())
                                            .setForeignGatewayId(e.getForeignGatewayID())
                                            .setMonitorUnitId(e.getMonitorUnitID()))
                                    .collect(Collectors.toList()));

                            d.setStationId(e.getStationId()).setForeignGatewayID(e.getForeignGatewayID()).setPluginId(e.getPluginId());
                            foreignDeviceList.add(d);
                        });
            });
        }

        log.info("[HUB CACHE INIT] hub初始化缓存，从数据库加载配置信息结束，组装信息结束");
        return gatewayList;
    }

}
