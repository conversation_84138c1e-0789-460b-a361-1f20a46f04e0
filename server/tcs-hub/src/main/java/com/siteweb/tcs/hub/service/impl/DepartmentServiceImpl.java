package com.siteweb.tcs.hub.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.tcs.hub.dal.dto.DepartmentDTO;
import com.siteweb.tcs.hub.dal.entity.Department;
import com.siteweb.tcs.hub.dal.mapper.DepartmentMapper;
import com.siteweb.tcs.hub.service.DepartmentService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 部门服务实现类
 */
@Service
@Slf4j
public class DepartmentServiceImpl implements DepartmentService {

    @Autowired
    private DepartmentMapper departmentMapper;

    @Override
    public List<DepartmentDTO> getDepartmentTree(Integer status, String name) {
        // 获取数据库中的树形结构数据
        List<DepartmentDTO> flatList = departmentMapper.selectDepartmentTree(status, name);
        
        // 如果有搜索条件，直接返回扁平列表
        if (StringUtils.isNotBlank(name)) {
            return flatList;
        }
        
        // 构建树形结构
        return buildTree(flatList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DepartmentDTO createDepartment(DepartmentDTO departmentDTO) {
        // 参数验证
        if (StringUtils.isBlank(departmentDTO.getName())) {
            throw new IllegalArgumentException("部门名称不能为空");
        }
        if (StringUtils.isBlank(departmentDTO.getCode())) {
            throw new IllegalArgumentException("部门编码不能为空");
        }

        // 检查部门编码是否重复
        if (checkCodeExists(departmentDTO.getCode(), null)) {
            throw new IllegalStateException("部门编码已存在: " + departmentDTO.getCode());
        }

        // 验证父部门存在性（如果指定了父部门）
        if (departmentDTO.getParentId() != null) {
            Department parentDept = departmentMapper.selectById(departmentDTO.getParentId());
            if (parentDept == null) {
                throw new IllegalArgumentException("父部门不存在");
            }
        }

        try {
            Department department = new Department();
            BeanUtils.copyProperties(departmentDTO, department);
            
            // 设置默认值
            if (department.getStatus() == null) {
                department.setStatus(1); // 默认启用
            }
            if (department.getSort() == null) {
                department.setSort(0); // 默认排序
            }

            departmentMapper.insert(department);

            // 返回创建的部门信息
            DepartmentDTO result = new DepartmentDTO();
            BeanUtils.copyProperties(department, result);
            
            // 设置父部门名称
            if (department.getParentId() != null) {
                Department parentDept = departmentMapper.selectById(department.getParentId());
                if (parentDept != null) {
                    result.setParentName(parentDept.getName());
                }
            }
            
            result.setHasChildren(false);
            
            log.info("创建部门成功: {}", result);
            return result;
            
        } catch (Exception e) {
            log.error("创建部门失败: {}", departmentDTO, e);
            throw new RuntimeException("创建部门失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DepartmentDTO updateDepartment(DepartmentDTO departmentDTO) {
        // 参数验证
        if (departmentDTO.getId() == null) {
            throw new IllegalArgumentException("部门ID不能为空");
        }
        if (StringUtils.isBlank(departmentDTO.getName())) {
            throw new IllegalArgumentException("部门名称不能为空");
        }
        if (StringUtils.isBlank(departmentDTO.getCode())) {
            throw new IllegalArgumentException("部门编码不能为空");
        }

        // 检查部门是否存在
        Department existDept = departmentMapper.selectById(departmentDTO.getId());
        if (existDept == null) {
            throw new IllegalArgumentException("部门不存在");
        }

        // 检查部门编码是否重复（排除自身）
        if (checkCodeExists(departmentDTO.getCode(), departmentDTO.getId())) {
            throw new IllegalStateException("部门编码已存在: " + departmentDTO.getCode());
        }

        // 验证父部门关系（避免循环引用）
        if (departmentDTO.getParentId() != null) {
            if (!validateParentRelation(departmentDTO.getId(), departmentDTO.getParentId())) {
                throw new IllegalArgumentException("不能将部门设置为自己或子部门的父部门");
            }
        }

        try {
            Department department = new Department();
            BeanUtils.copyProperties(departmentDTO, department);

            departmentMapper.updateById(department);

            // 返回更新后的部门信息
            return getById(department.getId());
            
        } catch (Exception e) {
            log.error("更新部门失败: {}", departmentDTO, e);
            throw new RuntimeException("更新部门失败: " + e.getMessage(), e);
        }
    }

    @Override
    public DepartmentDTO getById(Integer id) {
        if (id == null) {
            return null;
        }

        Department department = departmentMapper.selectById(id);
        if (department == null) {
            return null;
        }

        DepartmentDTO result = new DepartmentDTO();
        BeanUtils.copyProperties(department, result);
        
        // 设置父部门名称
        if (department.getParentId() != null) {
            Department parentDept = departmentMapper.selectById(department.getParentId());
            if (parentDept != null) {
                result.setParentName(parentDept.getName());
            }
        }
        
        // 检查是否有子部门
        result.setHasChildren(hasChildren(id));
        
        return result;
    }

    @Override
    public Department getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        return departmentMapper.selectByCode(code);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(Integer id, Integer status, boolean cascadeChildren) {
        if (id == null || status == null) {
            throw new IllegalArgumentException("部门ID和状态不能为空");
        }

        try {
            // 更新当前部门状态
            departmentMapper.updateStatus(id, status);
            
            // 如果需要级联更新子部门
            if (cascadeChildren) {
                updateChildrenStatusRecursive(id, status);
            }
            
            log.info("更新部门状态成功: id={}, status={}, cascade={}", id, status, cascadeChildren);
            return true;
            
        } catch (Exception e) {
            log.error("更新部门状态失败: id={}, status={}", id, status, e);
            throw new RuntimeException("更新部门状态失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDepartment(Integer id) {
        if (id == null) {
            throw new IllegalArgumentException("部门ID不能为空");
        }

        // 检查是否存在子部门
        if (hasChildren(id)) {
            throw new IllegalStateException("该部门下存在子部门，请先删除子部门");
        }

        try {
            int result = departmentMapper.deleteById(id);
            log.info("删除部门成功: id={}", id);
            return result > 0;
            
        } catch (Exception e) {
            log.error("删除部门失败: id={}", id, e);
            throw new RuntimeException("删除部门失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean checkCodeExists(String code, Integer excludeId) {
        if (StringUtils.isBlank(code)) {
            return false;
        }
        
        excludeId = excludeId != null ? excludeId : -1;
        int count = departmentMapper.checkCodeExists(code, excludeId);
        return count > 0;
    }

    @Override
    public boolean hasChildren(Integer parentId) {
        if (parentId == null) {
            return false;
        }
        
        int count = departmentMapper.countChildrenByParentId(parentId);
        return count > 0;
    }

    @Override
    public List<Integer> getAllChildrenIds(Integer parentId) {
        if (parentId == null) {
            return new ArrayList<>();
        }
        
        List<Integer> allChildrenIds = new ArrayList<>();
        collectAllChildrenIds(parentId, allChildrenIds);
        return allChildrenIds;
    }
    
    /**
     * 递归收集所有子部门ID
     */
    private void collectAllChildrenIds(Integer parentId, List<Integer> result) {
        List<Integer> directChildren = departmentMapper.selectAllChildrenIds(parentId);
        result.addAll(directChildren);
        
        // 递归处理每个直接子部门
        for (Integer childId : directChildren) {
            collectAllChildrenIds(childId, result);
        }
    }

    @Override
    public List<Department> getChildrenByParentId(Integer parentId) {
        if (parentId == null) {
            return new ArrayList<>();
        }
        
        return departmentMapper.selectChildrenByParentId(parentId);
    }

    @Override
    public List<DepartmentDTO> getAllDepartments() {
        LambdaQueryWrapper<Department> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Department::getDeleted, 0)
               .orderByAsc(Department::getSort)
               .orderByAsc(Department::getId);
        
        List<Department> departments = departmentMapper.selectList(wrapper);
        
        return departments.stream().map(dept -> {
            DepartmentDTO dto = new DepartmentDTO();
            BeanUtils.copyProperties(dept, dto);
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public boolean validateParentRelation(Integer departmentId, Integer parentId) {
        if (departmentId == null || parentId == null) {
            return true;
        }
        
        // 不能将自己设为父部门
        if (departmentId.equals(parentId)) {
            return false;
        }
        
        // 获取所有子部门ID，检查父部门是否在子部门中
        List<Integer> allChildrenIds = getAllChildrenIds(departmentId);
        return !allChildrenIds.contains(parentId);
    }

    /**
     * 构建树形结构
     */
    private List<DepartmentDTO> buildTree(List<DepartmentDTO> flatList) {
        if (flatList == null || flatList.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 创建ID到对象的映射
        Map<Integer, DepartmentDTO> deptMap = flatList.stream()
                .collect(Collectors.toMap(DepartmentDTO::getId, dept -> dept));
        
        List<DepartmentDTO> rootNodes = new ArrayList<>();
        
        for (DepartmentDTO dept : flatList) {
            if (dept.getParentId() == null) {
                // 根节点
                rootNodes.add(dept);
            } else {
                // 子节点，添加到父节点的children列表中
                DepartmentDTO parent = deptMap.get(dept.getParentId());
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(dept);
                }
            }
        }
        
        return rootNodes;
    }

    /**
     * 递归更新子部门状态
     */
    private void updateChildrenStatusRecursive(Integer parentId, Integer status) {
        List<Department> children = getChildrenByParentId(parentId);
        for (Department child : children) {
            departmentMapper.updateStatus(child.getId(), status);
            // 递归更新子部门的子部门
            updateChildrenStatusRecursive(child.getId(), status);
        }
    }
}