package com.siteweb.tcs.hub.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.hub.dal.entity.Account;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;

public interface AccountMapper  extends BaseMapper<Account> {

    @Update("UPDATE tcs_account SET PasswordValidTime = #{validTime} WHERE UserId = #{userId}")
    void updatePasswordValidTime(@Param("userId") Integer userId, @Param("validTime") LocalDateTime validTime);

    @Update("UPDATE tcs_account SET ValidTime = #{validTime} WHERE UserId = #{userId}")
    void updateValidTime(@Param("userId") Integer userId, @Param("validTime") LocalDateTime validTime);
}
