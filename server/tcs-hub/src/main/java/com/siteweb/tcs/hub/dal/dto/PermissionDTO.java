package com.siteweb.tcs.hub.dal.dto;

import com.siteweb.tcs.hub.dal.entity.Permission;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.Date;

@Data
public class PermissionDTO {
    private Integer permissionId;
    /**
     * 插件ID
     */
    private String pluginId;
    /**
     * 权限名称
     */
    private String permissionName;
    /**
     * 权限类型
     */
    private Integer permissionType;
    /**
     * 描述
     */
    private String description;
    /**
     * 更新时间
     */
    private Date updateTime;

    public Permission build() {
        Permission permission = new Permission();
        BeanUtils.copyProperties(this, permission);
        return permission;
    }
}
