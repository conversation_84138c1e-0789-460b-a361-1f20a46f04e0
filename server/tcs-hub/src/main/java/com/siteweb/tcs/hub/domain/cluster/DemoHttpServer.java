package com.siteweb.tcs.hub.domain.cluster;

import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.ActorSystem;
import org.apache.pekko.cluster.sharding.ClusterSharding;
import org.apache.pekko.http.javadsl.Http;
import org.apache.pekko.http.javadsl.marshallers.jackson.Jackson;
import org.apache.pekko.http.javadsl.model.StatusCodes;
import org.apache.pekko.http.javadsl.server.AllDirectives;
import org.apache.pekko.http.javadsl.server.Route;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

public class DemoHttpServer extends AllDirectives {
    private static final Logger logger = LoggerFactory.getLogger(DemoHttpServer.class);
    private final ActorRef clusterSharding;
    private final ActorSystem system;
    private static final String GATEWAY_PROXY_NAME = "GatewayProxy";
    private static final int SERVER_PORT = 8080;

    public DemoHttpServer(ActorRef gatewayProxyShardRegion, ActorSystem system) throws IOException {
        this.clusterSharding = gatewayProxyShardRegion;
        this.system = system;
    }

    public void start() throws IOException {
        final Http http = Http.get(system);
        Route route = createRoute();

        http.newServerAt("0.0.0.0", SERVER_PORT)
            .bind(route)
            .whenComplete((binding, ex) -> handleServerStartup(binding, ex));
    }

    private Route createRoute() {
        return pathPrefix("api", () ->
            pathPrefix("data", () ->
                concat(
                    post(() -> handleDataPost()),
                    get(() -> complete("API is running"))
                )
            )
        );
    }

    private Route handleDataPost() {
        return entity(Jackson.unmarshaller(BusinessData.class), data -> {
            try {
                String foreignGatewayId = data.getForeignGatewayId();
                logger.debug("Received data for gateway: {}", foreignGatewayId);
                sendToSharding(foreignGatewayId, data);
                return complete("Data processed successfully");
            } catch (Exception e) {
                logger.error("Error processing data: {}", e.getMessage(), e);
                return complete(StatusCodes.INTERNAL_SERVER_ERROR, "Error processing data");
            }
        });
    }

    private void handleServerStartup(Object binding, Throwable ex) {
        if (ex != null) {
            logger.error("HTTP server failed to start: {}", ex.getMessage(), ex);
            system.terminate();
        } else {
            logger.info("HTTP server started at port {}", SERVER_PORT);
        }
    }

    private void sendToSharding(String entityId, BusinessData data) {
        try {
            ClusterSharding.get(system)
                .shardRegion(GATEWAY_PROXY_NAME)
                .tell(new GatewayProxy.ProcessData(data), ActorRef.noSender());
            logger.debug("Data sent to sharding for gateway: {}", entityId);
        } catch (Exception e) {
            logger.error("Error sending data to sharding: {}", e.getMessage(), e);
            throw e;
        }
    }
}