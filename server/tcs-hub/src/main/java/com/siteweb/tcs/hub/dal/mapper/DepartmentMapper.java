package com.siteweb.tcs.hub.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.hub.dal.entity.Department;
import com.siteweb.tcs.hub.dal.dto.DepartmentDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 部门Mapper接口
 */
public interface DepartmentMapper extends BaseMapper<Department> {

    /**
     * 获取部门树形结构列表
     * @param status 状态筛选，null表示不筛选
     * @param name 名称筛选，null表示不筛选
     * @return 部门DTO列表
     */
    List<DepartmentDTO> selectDepartmentTree(@Param("status") Integer status, @Param("name") String name);

    /**
     * 根据父ID获取子部门列表
     * @param parentId 父部门ID
     * @return 子部门列表
     */
    List<Department> selectChildrenByParentId(@Param("parentId") Integer parentId);

    /**
     * 检查部门编码是否存在（排除自身）
     * @param code 部门编码
     * @param excludeId 排除的部门ID
     * @return 存在数量
     */
    @Select("SELECT COUNT(*) FROM tcs_department WHERE code = #{code} AND deleted = 0 AND id != #{excludeId}")
    int checkCodeExists(@Param("code") String code, @Param("excludeId") Integer excludeId);

    /**
     * 检查部门是否有子部门
     * @param parentId 父部门ID
     * @return 子部门数量
     */
    @Select("SELECT COUNT(*) FROM tcs_department WHERE parent_id = #{parentId} AND deleted = 0")
    int countChildrenByParentId(@Param("parentId") Integer parentId);

    /**
     * 更新部门状态
     * @param id 部门ID
     * @param status 状态
     */
    @Update("UPDATE tcs_department SET status = #{status}, update_time = CURRENT_TIMESTAMP WHERE id = #{id}")
    void updateStatus(@Param("id") Integer id, @Param("status") Integer status);

    /**
     * 递归更新子部门状态
     * @param parentId 父部门ID
     * @param status 状态
     */
    void updateChildrenStatus(@Param("parentId") Integer parentId, @Param("status") Integer status);

    /**
     * 获取部门的所有子部门ID（递归）
     * @param parentId 父部门ID
     * @return 子部门ID列表
     */
    List<Integer> selectAllChildrenIds(@Param("parentId") Integer parentId);

    /**
     * 根据部门编码获取部门信息
     * @param code 部门编码
     * @return 部门信息
     */
    @Select("SELECT * FROM tcs_department WHERE code = #{code} AND deleted = 0")
    Department selectByCode(@Param("code") String code);
}