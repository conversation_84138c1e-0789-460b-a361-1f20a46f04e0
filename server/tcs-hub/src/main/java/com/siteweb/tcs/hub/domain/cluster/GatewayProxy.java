package com.siteweb.tcs.hub.domain.cluster;


import lombok.Getter;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.Props;

import java.io.Serializable;

public class GatewayProxy extends AbstractActor {

    public static Props props() {
        return Props.create(GatewayProxy.class);
    }

    @Getter
    public static class ProcessData implements Serializable {
        private final BusinessData data;

        public ProcessData(BusinessData data) {
            this.data = data;
        }

    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(ProcessData.class, this::onProcessData)
                .build();
    }

    private void onProcessData(ProcessData message) {
        BusinessData data = message.getData();
        // 在这里处理实际的业务逻辑, 比如：CMCC可能的业务
        // 1 原始数据过滤+LOGIN逻辑
        // 如果这个gatewayID系统不存在（查数据库，如果系统之前存在或人知道会提前创建该GatewayProxy）
        // 只有，id，name，password，预注册（CTCC， 配置上下同步）
        // 如果不存在，则通知给某个对象，给UI看，未注册FSU列表（web）
        // 如果存在，则加载FSU配置，判断用户名密码是否OK，如果不OK，拒绝。
        // 如果存在，配置存在，已经加载，确定验证成功，但计算图不存在，则加载运行计算图（从数据库读配置）
        // 2 删除等UI操作
        // 也会通过ID映射过来，这里如果是删除，则停止计算图，删除配置。但本对象stop自己
        // 3 业务数据接入
        // 将业务数据送入计算图，根据packet解包器，+ switch转给对应 shape处理
        // 4 如果是低端配置同步，则由本对象关闭计算图，进行配置存储成功后，重新加载计算图
        // 预注册机制：可以创建ID，用户密码等基本信息来。

    }
}