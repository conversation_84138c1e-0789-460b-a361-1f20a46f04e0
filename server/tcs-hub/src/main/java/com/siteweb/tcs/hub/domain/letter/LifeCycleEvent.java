package com.siteweb.tcs.hub.domain.letter;

import cn.hutool.json.JSONUtil;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventType;
import com.siteweb.tcs.hub.domain.letter.enums.ThingType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class LifeCycleEvent implements Serializable {

    private String foreignGatewayId;

    private String foreignDeviceId;
    
    /**
     * 南向插件实例id，必须带上（plugin在一个tcs可能会有多个instance，所以要用instanceId）
     */
    private String pluginInstanceId;

    /**
     * 物对象类型，根据实际操作对象类型进行填写
     */
    private ThingType thingType;
    private LifeCycleEventType eventType;
    private Object foreignConfigChange;

    public String toJson(){
        return JSONUtil.toJsonStr(this);
    }
}
