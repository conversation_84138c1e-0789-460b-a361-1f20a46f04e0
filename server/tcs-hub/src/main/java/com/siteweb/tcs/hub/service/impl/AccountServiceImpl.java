package com.siteweb.tcs.hub.service.impl;


import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.tcs.hub.dal.dto.AccountDTO;
import com.siteweb.tcs.hub.dal.dto.ResetPasswordDTO;
import com.siteweb.tcs.hub.dal.dto.UpdatePasswordDTO;
import com.siteweb.tcs.hub.dal.entity.Account;
import com.siteweb.tcs.hub.dal.entity.Role;
import com.siteweb.tcs.hub.dal.mapper.AccountMapper;
import com.siteweb.tcs.hub.dal.mapper.RoleMapper;
import com.siteweb.tcs.hub.security.TokenUserUtil;
import com.siteweb.tcs.hub.service.AccountService;
import com.siteweb.tcs.hub.service.OperationLogService;
import com.siteweb.tcs.siteweb.util.EncryptUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AccountServiceImpl implements AccountService {

    @Autowired
    private AccountMapper accountMapper;
    @Autowired
    private RoleMapper roleMapper;
    @Autowired
    private OperationLogService operationLogService;

    @Override
    public List<AccountDTO> findByLoginId(String loginId) {
        if (StringUtils.isBlank(loginId)) {
            throw new IllegalArgumentException("Login ID cannot be empty");
        }

        List<AccountDTO> result = new ArrayList<>();
        List<Account> accountList = accountMapper.selectList(new QueryWrapper<Account>().eq("LoginId", loginId));
        for (Account account : accountList) {
            AccountDTO accountDTO = new AccountDTO();
            BeanUtils.copyProperties(account, accountDTO);
            accountDTO.setRoleIds(roleMapper.findRolesByUserId(account.getUserId()).stream()
                    .map(Role::getRoleId)
                    .map(String::valueOf)
                    .collect(Collectors.joining(",")));
            result.add(accountDTO);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AccountDTO createAccount(AccountDTO accountDTO) {
        // 参数验证
        if (StringUtils.isBlank(accountDTO.getLoginId())) {
            throw new IllegalArgumentException("Login ID is required");
        }
        if (StringUtils.isBlank(accountDTO.getUserName())) {
            throw new IllegalArgumentException("User name is required");
        }
        if (StringUtils.isBlank(accountDTO.getPassword())) {
            throw new IllegalArgumentException("Password is required");
        }

        // 检查loginId是否重复
        if (isLoginIdExists(accountDTO.getLoginId())) {
            throw new IllegalStateException("LoginId is repeat: " + accountDTO.getLoginId());
        }

        try {
            Account account = new Account();
            BeanUtils.copyProperties(accountDTO, account);

            // 密码加密处理
            String encryptedPassword = EncryptUtil.sha256(accountDTO.getPassword());
            account.setPassword(encryptedPassword);
            
            account.setEnable(accountDTO.isEnable());
            account.setMaxError(accountDTO.getMaxError() != null ? accountDTO.getMaxError() : 10);
            account.setLocked(false);

            accountMapper.insert(account);

            if (StringUtils.isNotBlank(accountDTO.getRoleIds())) {
                try {
                    saveRoles(account.getUserId(), accountDTO.getRoleIds());
                } catch (NumberFormatException e) {
                    throw new IllegalArgumentException("Invalid role ID format: " + accountDTO.getRoleIds());
                }
            }

            // 记录操作日志，如果当前没有登录用户（如系统初始化），则使用系统用户ID
            try {
                Integer operatorUserId = TokenUserUtil.getLoginUserId();
                operationLogService.createLog(
                        operatorUserId,
                        account.getUserId(),
                        account.getUserName(),
                        1,
                        "1",
                        null,
                        account.toString()
                );
            } catch (Exception e) {
                // 如果获取当前用户失败（如系统初始化或管理员操作），使用系统默认用户ID
                log.warn("Failed to get current user for operation log, using system user. Error: {}", e.getMessage());
                operationLogService.createLog(
                        -1, // 系统用户ID
                        account.getUserId(),
                        account.getUserName(),
                        1,
                        "1",
                        null,
                        account.toString()
                );
            }

            return getById(account.getUserId());
        } catch (Exception e) {
            if (e instanceof IllegalArgumentException || e instanceof IllegalStateException) {
                throw e;
            }
            throw new RuntimeException("Failed to create account: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AccountDTO updateAccount(AccountDTO accountDTO) {
        if (accountDTO.getUserId() == null) {
            throw new IllegalArgumentException("User ID is required");
        }

        Account existingAccount = accountMapper.selectById(accountDTO.getUserId());
        if (existingAccount == null) {
            return null;
        }

        if (ObjectUtil.isNotNull(accountDTO.getLoginId()) &&
                !existingAccount.getLoginId().equals(accountDTO.getLoginId())) {
            throw new IllegalStateException("Login ID cannot be modified");
        }

        try {
            String oldValue = existingAccount.toString();
            Account account = new Account();
            BeanUtils.copyProperties(accountDTO, account);

            // 处理密码有效期
            if (accountDTO.getPasswordValidTime() == null) {
                accountMapper.updatePasswordValidTime(account.getUserId(), null);
                account.setPasswordValidTime(null);
            }

            // 处理账号有效期
            if (accountDTO.getValidTime() == null) {
                accountMapper.updateValidTime(account.getUserId(), null);
                account.setValidTime(null);
            }

            accountMapper.updateById(account);

            // 更新角色
            if (StringUtils.isNotBlank(accountDTO.getRoleIds())) {
                roleMapper.deleteRolesByUserId(account.getUserId());
                try {
                    saveRoles(account.getUserId(), accountDTO.getRoleIds());
                } catch (NumberFormatException e) {
                    throw new IllegalArgumentException("Invalid role ID format: " + accountDTO.getRoleIds());
                }
            }

            operationLogService.createLog(
                    TokenUserUtil.getLoginUserId(),
                    account.getUserId(),
                    account.getUserName(),
                    1,
                    "2",
                    oldValue,
                    account.toString()
            );

            return getById(account.getUserId());
        } catch (Exception e) {
            if (e instanceof IllegalArgumentException || e instanceof IllegalStateException) {
                throw e;
            }
            throw new RuntimeException("Failed to update account: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePassword(UpdatePasswordDTO updatePasswordDTO) {
        if (updatePasswordDTO.getUserId() == null) {
            throw new IllegalArgumentException("User ID is required");
        }
        if (StringUtils.isBlank(updatePasswordDTO.getOldPassword())) {
            throw new IllegalArgumentException("Old password is required");
        }
        if (StringUtils.isBlank(updatePasswordDTO.getNewPassword())) {
            throw new IllegalArgumentException("New password is required");
        }

        Account account = accountMapper.selectById(updatePasswordDTO.getUserId());
        if (account == null) {
            return false;
        }

        // 验证原密码（原密码需要加密后比较）
        String encryptedOldPassword = EncryptUtil.sha256(updatePasswordDTO.getOldPassword());
        if (!account.getPassword().equals(encryptedOldPassword)) {
            throw new IllegalArgumentException("Invalid old password");
        }

        try {
            String oldValue = account.toString();
            // 新密码加密处理
            String encryptedNewPassword = EncryptUtil.sha256(updatePasswordDTO.getNewPassword());
            account.setPassword(encryptedNewPassword);
            account.setPasswordValidTime(updatePasswordDTO.getPasswordValidTime());
            boolean result = accountMapper.updateById(account) > 0;

            if (result) {
                try {
                    Integer operatorUserId = TokenUserUtil.getLoginUserId();
                    operationLogService.createLog(
                            operatorUserId,
                            account.getUserId(),
                            account.getUserName(),
                            1,
                            "2",
                            oldValue,
                            account.toString()
                    );
                } catch (Exception e) {
                    // 如果获取当前用户失败，使用系统默认用户ID
                    log.warn("Failed to get current user for operation log, using system user. Error: {}", e.getMessage());
                    operationLogService.createLog(
                            -1, // 系统用户ID
                            account.getUserId(),
                            account.getUserName(),
                            1,
                            "2",
                            oldValue,
                            account.toString()
                    );
                }
            }

            return result;
        } catch (Exception e) {
            throw new RuntimeException("Failed to update password: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetPassword(ResetPasswordDTO resetPasswordDTO) {
        if (resetPasswordDTO.getUserId() == null) {
            throw new IllegalArgumentException("User ID is required");
        }
        if (StringUtils.isBlank(resetPasswordDTO.getPassword())) {
            throw new IllegalArgumentException("New password is required");
        }

        Account account = accountMapper.selectById(resetPasswordDTO.getUserId());
        if (account == null) {
            return false;
        }

        try {
            // 重置密码时也需要加密
            String encryptedPassword = EncryptUtil.sha256(resetPasswordDTO.getPassword());
            account.setPassword(encryptedPassword);
            account.setPasswordValidTime(resetPasswordDTO.getPasswordValidTime());
            return accountMapper.updateById(account) > 0;
        } catch (Exception e) {
            throw new RuntimeException("Failed to reset password: " + e.getMessage());
        }
    }

    @Override
    public AccountDTO getById(Integer userId) {
        if (userId == null) {
            throw new IllegalArgumentException("User ID is required");
        }

        Account account = accountMapper.selectById(userId);
        return convertToDTO(account);
    }

    @Override
    public List<AccountDTO> getAllAccounts() {
        List<Account> accounts = accountMapper.selectList(null);
        return accounts.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateEnable(Integer userId, Boolean enable) {
        if (userId == null || enable == null) {
            throw new IllegalArgumentException("User ID and enable status are required");
        }

        Account account = accountMapper.selectById(userId);
        if (account == null) {
            return false;
        }

        try {
            String oldValue = account.toString();
            account.setEnable(enable);
            boolean result = accountMapper.updateById(account) > 0;

            if (result) {
                operationLogService.createLog(
                        TokenUserUtil.getLoginUserId(),
                        userId,
                        account.getUserName(),
                        1,
                        "2",
                        oldValue,
                        account.toString()
                );
            }

            return result;
        } catch (Exception e) {
            throw new RuntimeException("Failed to update enable status: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeById(Integer userId) {
        if (userId == null) {
            throw new IllegalArgumentException("User ID is required");
        }

        Account existingAccount = accountMapper.selectById(userId);
        if (existingAccount == null) {
            return false;
        }

        try {
            String oldValue = existingAccount.toString();

            roleMapper.deleteRolesByUserId(userId);
            boolean result = accountMapper.deleteById(userId) > 0;

            if (result) {
                operationLogService.createLog(
                        TokenUserUtil.getLoginUserId(),
                        userId,
                        existingAccount.getUserName(),
                        1,
                        "3",
                        oldValue,
                        null
                );
            }

            return result;
        } catch (Exception e) {
            throw new RuntimeException("Failed to delete account: " + e.getMessage());
        }
    }

    private boolean isLoginIdExists(String loginId) {
        LambdaQueryWrapper<Account> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Account::getLoginId, loginId);
        return accountMapper.selectCount(wrapper) > 0;
    }

    private void saveRoles(Integer userId, String roleIds) {
        if (StringUtils.isNotBlank(roleIds)) {
            try {
                Arrays.stream(roleIds.split(","))
                        .map(String::trim)
                        .map(Integer::parseInt)
                        .forEach(roleId -> roleMapper.insertUserRole(userId, roleId));
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("Invalid role ID format: " + roleIds);
            }
        }
    }

    private AccountDTO convertToDTO(Account account) {
        if (account == null) {
            return null;
        }

        AccountDTO dto = new AccountDTO();
        BeanUtils.copyProperties(account, dto);

        List<Role> roles = roleMapper.findRolesByUserId(account.getUserId());
        if (roles != null && !roles.isEmpty()) {
            String roleIds = roles.stream()
                    .map(Role::getRoleId)
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
            dto.setRoleIds(roleIds);
        }

        return dto;
    }
}