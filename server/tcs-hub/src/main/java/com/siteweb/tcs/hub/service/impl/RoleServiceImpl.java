package com.siteweb.tcs.hub.service.impl;


import com.siteweb.tcs.hub.dal.entity.Role;
import com.siteweb.tcs.hub.dal.mapper.PermissionMapper;
import com.siteweb.tcs.hub.dal.mapper.RoleMapper;
import com.siteweb.tcs.hub.security.TokenUserUtil;
import com.siteweb.tcs.hub.service.OperationLogService;
import com.siteweb.tcs.hub.service.RoleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RoleServiceImpl implements RoleService {

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private OperationLogService operationLogService;
    @Autowired
    private PermissionMapper permissionMapper;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Role createRole(Role roleDTO) {
        if (StringUtils.isBlank(roleDTO.getRoleName())) {
            throw new RuntimeException("Role name is required");
        }

        try {
            Role role = new Role();
            BeanUtils.copyProperties(roleDTO, role);
            roleMapper.insert(role);
            // Log the creation
            operationLogService.createLog(
                    TokenUserUtil.getLoginUserId(),
                    role.getRoleId(),
                    role.getRoleName(),
                    2,
                    "1",
                    null,
                    role.toString()
            );
            return convertToDTO(role);
        } catch (Exception e) {
            throw new RuntimeException("Failed to create role: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Role updateRole(Role roleDTO) {
        if (roleDTO.getRoleId() == null) {
            throw new RuntimeException("Role ID is required");
        }
        if (StringUtils.isBlank(roleDTO.getRoleName())) {
            throw new RuntimeException("Role name is required");
        }

        Role existingRole = roleMapper.selectById(roleDTO.getRoleId());
        if (existingRole == null) {
            return null;
        }

        try {
            Role role = new Role();
            BeanUtils.copyProperties(roleDTO, role);
            roleMapper.updateById(role);
            operationLogService.createLog(
                    TokenUserUtil.getLoginUserId(),
                    role.getRoleId(),
                    role.getRoleName(),
                    2,
                    "2",
                    existingRole.toString(),
                    role.toString()
            );
            return convertToDTO(role);
        } catch (Exception e) {
            throw new RuntimeException("Failed to update role: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeRole(Integer roleId) {
        try {
            // 检查角色是否存在
            Role existingRole = roleMapper.selectById(roleId);
            if (existingRole == null) {
                throw new RuntimeException("Role not found with ID: " + roleId);
            }
            boolean result = roleMapper.deleteById(roleId) > 0;
            //删除账号角色映射
            roleMapper.deleteRoleAccountMapByRoleId(roleId);
            //删除权限角色映射
            permissionMapper.deletePermissionRoleMapByRoleId(roleId);
            if (result) {
                // Log the deletion
                operationLogService.createLog(
                        TokenUserUtil.getLoginUserId(),
                        roleId,
                        existingRole.getRoleName(),
                        2,
                        "3",
                        existingRole.toString(),
                        null
                );
            }

            return result;
        } catch (Exception e) {
            throw new RuntimeException("Failed to delete role: " + e.getMessage());
        }
    }

    @Override
    public Role getById(Integer roleId) {
        Role role = roleMapper.selectById(roleId);
        if (role == null) {
            throw new RuntimeException("Role not found with ID: " + roleId);
        }
        return convertToDTO(role);
    }

    @Override
    public List<Role> getAllRoles() {
        try {
            List<Role> roles = roleMapper.selectList(null);
            return roles.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            throw new RuntimeException("Failed to get all roles: " + e.getMessage());
        }
    }

    @Override
    public List<Role> getRolesByUserId(Integer userId) {
        try {
            if (userId == null) {
                throw new RuntimeException("User ID is required");
            }
            List<Role> roles = roleMapper.findRolesByUserId(userId);
            return roles.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            throw new RuntimeException("Failed to get roles for user " + userId + ": " + e.getMessage());
        }
    }
    private Role convertToDTO(Role role) {
        if (role == null) {
            return null;
        }
        Role dto = new Role();
        BeanUtils.copyProperties(role, dto);
        return dto;
    }
}