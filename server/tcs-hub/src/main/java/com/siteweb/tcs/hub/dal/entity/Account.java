package com.siteweb.tcs.hub.dal.entity;


import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@TableName("tcs_account")
public class Account {

    @TableId(value = "UserId", type = IdType.AUTO)
    private Integer userId;

    @TableField("UserName")
    private String userName;

    @TableField("LoginId")
    private String loginId;

    @TableField("Password")
    private String password;

    @TableField("Enable")
    private Boolean enable;

    @TableField("MaxError")
    private Integer maxError;

    @TableField("Locked")
    private Boolean locked;

    @TableField(value = "ValidTime", updateStrategy = FieldStrategy.IGNORED)
    private Date validTime;

    @TableField("PasswordValidTime")
    private Date passwordValidTime;

    @TableField("Description")
    private String description;
}
