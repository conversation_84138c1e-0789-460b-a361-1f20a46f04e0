package com.siteweb.tcs.hub.service.impl;

import com.siteweb.tcs.hub.dal.entity.Region;
import com.siteweb.tcs.hub.dal.mapper.RegionMapper;
import com.siteweb.tcs.hub.service.RegionService;
import jakarta.annotation.PostConstruct;
import com.siteweb.tcs.siteweb.provider.ResourceStructureProvider;
import com.siteweb.tcs.siteweb.dto.StructureTreeNodeDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (2024-06-05)
 **/
@Service
public class RegionServiceImpl implements RegionService {

    private static final Logger logger = LoggerFactory.getLogger(RegionServiceImpl.class);
    private final List<Region> regionCache = new ArrayList<>();

    // 根据RegionId映射Region
    private final Map<Long, Region> regionMap = new ConcurrentHashMap<>();

    // 根据RsId（ResourceStructureId）映射Region
    private final Map<Integer, Region> rsIdRegionMap = new ConcurrentHashMap<>();

    //局站类型常量
    private static final int STATION_TYPE = 104;

    @Autowired
    private ResourceStructureProvider resourceStructureProvider;

    @Autowired
    private RegionMapper regionMapper;

    @PostConstruct
    public void init() {
        refreshCache();
    }

    private synchronized void refreshCache() {
        List<Region> freshData = regionMapper.selectAll();
        regionCache.clear();
        regionCache.addAll(freshData);
        regionMap.clear();
        rsIdRegionMap.clear();
        for (Region region : freshData) {
            regionMap.put(region.getRegionId(), region);
            rsIdRegionMap.put(region.getResourceStructureId(), region);
        }
    }

    @Override
    public List<Region> findAll() {
        return new ArrayList<>(regionCache);
    }

    @Override
    public Region findByRegionId(Integer regionId) {
        return regionMap.get(regionId.longValue());
    }

    @Override
    public Region findByRsId(Integer rsId) {
        return rsIdRegionMap.get(rsId);
    }

    @Override
    public boolean create(Region region) {
        region.setRegionId(null);
        if (regionMapper.insert(region) > 0) {
            refreshCache();
            return true;
        }
        return false;
    }

    @Override
    public boolean update(Region region) {
        if (regionMapper.updateById(region) > 0) {
            refreshCache();
            return true;
        }
        return false;
    }

    @Override
    public boolean deleteBy(Integer regionId) {
        if (regionMapper.deleteById(regionId) > 0) {
            refreshCache();
            return true;
        }
        return false;
    }


    @Override
    public List<List<Region>> syncByS6() {
        logger.info("syncByS6 - 开始与S6进行同步。");
        Boolean reloadTreeRes = resourceStructureProvider.reloadTree();
        if (!reloadTreeRes) {
            logger.error("syncByS6 - 重新加载S6资源结构树失败。");
            return null;
        }

        // 获取并处理来自S6的最新区域数据
        StructureTreeNodeDTO tree = resourceStructureProvider.getTree();
        logger.debug("syncByS6 - 从S6获取的资源结构树: {}", tree);

        tree = filterTree(tree);
        logger.debug("syncByS6 - 过滤后的结构树: {}", tree);

        List<StructureTreeNodeDTO> structureNodes = new ArrayList<>();
        traverseTree(tree, structureNodes);
        logger.debug("syncByS6 - 遍历并收集的结构节点: {}", structureNodes);

        List<Region> latestS6Regions = convertToRegionList(structureNodes);
        logger.debug("syncByS6 - 转换为区域列表: {}", latestS6Regions);

        // 获取数据库中现有的区域数据
        List<Region> existingDbRegions = regionMapper.selectAll();
        logger.debug("syncByS6 - 获取现有数据库中的区域数据: {}", existingDbRegions);

        // 找出需要新增、删除和更新的区域
        List<Region> regionsToAdd = findRegionsToAdd(latestS6Regions, existingDbRegions);
        List<Region> regionsToDelete = findRegionsToDelete(latestS6Regions, existingDbRegions);
        logger.info("syncByS6 - 需要新增的区域数量: {}, 需要删除的区域数量: {}", regionsToAdd.size(), regionsToDelete.size());

        deleteRegions(regionsToDelete);
        insertAndUpdateNewRegions(regionsToAdd);

        // 重新获取数据库中现有的区域数据
        existingDbRegions = regionMapper.selectAll();
        logger.debug("syncByS6 - 插入/更新后的现有数据库区域数据: {}", existingDbRegions);

        List<Region> regionsWithChangedParent = findRegionsWithChangedParent(latestS6Regions, existingDbRegions);
        logger.info("syncByS6 - 需要更新父节点的区域数量: {}", regionsWithChangedParent.size());

        updateRegions(regionsWithChangedParent);

        // 更新缓存
        refreshCache();
        logger.info("syncByS6 - 缓存已刷新。当前缓存中的区域数据: {}", regionCache);

        return Arrays.asList(regionsToAdd, regionsToDelete, regionsWithChangedParent);
    }

    public StructureTreeNodeDTO filterTree(StructureTreeNodeDTO root) {
        if (root == null) {
            return null;
        }
        if (Objects.equals(root.getStructureTypeId(), 105)) {
            return null;
        }
        List<StructureTreeNodeDTO> filteredChildren = new ArrayList<>();
        for (StructureTreeNodeDTO child : root.getChildren()) {
            StructureTreeNodeDTO filteredChild = filterTree(child);
            if (filteredChild != null) {
                filteredChildren.add(filteredChild);
            }
        }
        root.setChildren(filteredChildren);
        if (!root.hasChildren()) {
            if (!Objects.equals(root.getStructureTypeId(), STATION_TYPE)) {
                return null;
            }
        }

        return root;
    }

    private void traverseTree(StructureTreeNodeDTO node, List<StructureTreeNodeDTO> list) {
        if (node == null) {
            return;
        }
        list.add(node);
        for (StructureTreeNodeDTO child : node.getChildren()) {
            traverseTree(child, list);
        }
    }

    //把StructureTreeNodeDTO列表转换成Region列表
    private List<Region> convertToRegionList(List<StructureTreeNodeDTO> list) {
        List<Region> regions = new ArrayList<>();
        for (StructureTreeNodeDTO node : list) {
            Region region = new Region();
            region.setResourceStructureId(node.getResourceStructureId());
            region.setRegionName(node.getResourceStructureName());
            region.setParentResourceStructureId(node.getParentResourceStructureId());
            regions.add(region);
        }
        return regions;
    }

    private List<Region> findRegionsToAdd(List<Region> latestS6Regions, List<Region> existingDbRegions) {
        Map<Integer, Region> existingDbRegionsMap = existingDbRegions.stream()
                .collect(Collectors.toMap(Region::getResourceStructureId, r -> r));

        return latestS6Regions.stream()
                .filter(r -> !existingDbRegionsMap.containsKey(r.getResourceStructureId()))
                .collect(Collectors.toList());
    }

    private List<Region> findRegionsToDelete(List<Region> latestS6Regions, List<Region> existingDbRegions) {
        Map<Integer, Region> latestS6RegionsMap = latestS6Regions.stream()
                .collect(Collectors.toMap(Region::getResourceStructureId, r -> r));

        return existingDbRegions.stream()
                .filter(r -> !latestS6RegionsMap.containsKey(r.getResourceStructureId()))
                .collect(Collectors.toList());
    }

    private List<Region> findRegionsWithChangedParent(List<Region> latestS6Regions, List<Region> existingDbRegions) {
        Map<Integer, Region> existingDbResourcesMap = existingDbRegions.stream()
                .collect(Collectors.toMap(Region::getResourceStructureId, r -> r));

        Map<Integer, Region> latestS6ResourcesMap = latestS6Regions.stream()
                .collect(Collectors.toMap(Region::getResourceStructureId, r -> r));

        Map<Long, Region> existingDbRegionsMap = existingDbRegions.stream()
                .collect(Collectors.toMap(Region::getRegionId, r -> r));

        List<Region> regionsToUpdate = new ArrayList<>();

        for (Region existingRegion : existingDbRegions) {
            //获取s6新拿到的区域
            Region s6Region = latestS6ResourcesMap.get(existingRegion.getResourceStructureId());
            if (!Objects.isNull(existingRegion.getParentId())) {

                // 比较父节点的 resourceStructureId 或者name是否发生变化
                if (Objects.isNull(existingDbRegionsMap.get(existingRegion.getParentId())) ||
                        !Objects.equals(existingDbRegionsMap.get(existingRegion.getParentId()).getResourceStructureId(),
                                s6Region.getParentResourceStructureId()) ||
                        !Objects.equals(existingRegion.getRegionName(),s6Region.getRegionName()) ) {
                    // 父节点发生变化，需要更新
                    Region updatedRegion = new Region();
                    updatedRegion.setDescription(existingRegion.getDescription());
                    updatedRegion.setDisplayIndex(existingRegion.getDisplayIndex());
                    updatedRegion.setRegionId(existingRegion.getRegionId());
                    updatedRegion.setResourceStructureId(existingRegion.getResourceStructureId());
                    updatedRegion.setRegionName(s6Region.getRegionName());
                    updatedRegion.setParentId(existingDbResourcesMap.get(s6Region.getParentResourceStructureId()).getRegionId());
                    regionsToUpdate.add(updatedRegion);
                }
            }
        }

        return regionsToUpdate;
    }

    private void insertAndUpdateNewRegions(List<Region> regionsToAdd) {
        // 第一步：插入所有新记录，让数据库自动生成 regionId
        for (Region region : regionsToAdd) {
            regionMapper.insert(region);
        }

        // 获取插入后的所有区域（包括新插入的）
        List<Region> allRegions = regionMapper.selectAll();

        // 创建 resourceStructureId 到 parentResourceStructureId 的映射
        Map<Integer, Integer> parentResourceStructureIdMap = regionsToAdd.stream()
                .filter(r -> r.getParentResourceStructureId() != null)
                .collect(Collectors.toMap(Region::getResourceStructureId, Region::getParentResourceStructureId));

        // 更新 allRegions 中的 parentResourceStructureId
        for (Region region : allRegions) {
            Integer parentResourceStructureId = parentResourceStructureIdMap.get(region.getResourceStructureId());
            if (parentResourceStructureId != null) {
                region.setParentResourceStructureId(parentResourceStructureId);
            }
        }

        // 创建 resourceStructureId 到 Region 的映射
        Map<Integer, Region> regionMap = allRegions.stream()
                .collect(Collectors.toMap(Region::getResourceStructureId, r -> r));

        // 第二步：更新 parentId
        for (Region region : allRegions) {
            if (region.getParentResourceStructureId() != null) {
                Region parentRegion = regionMap.get(region.getParentResourceStructureId());
                if (parentRegion != null) {
                    region.setParentId(parentRegion.getRegionId());
                    regionMapper.updateById(region);
                }
            }
        }
    }

    //批量删除区域
    private void deleteRegions(List<Region> regionsToDelete) {
        for (Region region : regionsToDelete) {
            regionMapper.deleteById(region.getRegionId());
        }
    }


    //批量更新区域
    private void updateRegions(List<Region> regionsToUpdate) {
        for (Region region : regionsToUpdate) {
            regionMapper.updateById(region);
        }
    }


}
