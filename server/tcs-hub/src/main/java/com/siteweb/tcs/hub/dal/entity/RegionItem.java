package com.siteweb.tcs.hub.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("tcs_region_items")
public class RegionItem {

    @TableId(value = "regionItemId", type = IdType.AUTO)
    private Long regionItemId;

    private Long regionId;
    private String pluginId;
    private String itemId;
    private String itemName;
    private String itemType;
}
