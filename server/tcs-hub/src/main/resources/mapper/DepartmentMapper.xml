<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.hub.dal.mapper.DepartmentMapper">

    <!-- 部门DTO结果映射 -->
    <resultMap id="DepartmentDTOMap" type="com.siteweb.tcs.hub.dal.dto.DepartmentDTO">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="parent_id" property="parentId"/>
        <result column="parent_name" property="parentName"/>
        <result column="leader" property="leader"/>
        <result column="phone" property="phone"/>
        <result column="email" property="email"/>
        <result column="status" property="status"/>
        <result column="sort" property="sort"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
        <result column="has_children" property="hasChildren"/>
    </resultMap>

    <!-- 获取部门树形结构列表 -->
    <select id="selectDepartmentTree" resultMap="DepartmentDTOMap">
        SELECT 
            d.id,
            d.name,
            d.code,
            d.parent_id,
            p.name as parent_name,
            d.leader,
            d.phone,
            d.email,
            d.status,
            d.sort,
            d.create_time,
            d.update_time,
            d.remark,
            CASE WHEN EXISTS(
                SELECT 1 FROM tcs_department cd 
                WHERE cd.parent_id = d.id AND cd.deleted = 0
            ) THEN TRUE ELSE FALSE END as has_children
        FROM tcs_department d
        LEFT JOIN tcs_department p ON d.parent_id = p.id AND p.deleted = 0
        WHERE d.deleted = 0
            <if test="status != null">
                AND d.status = #{status}
            </if>
            <if test="name != null and name != ''">
                AND d.name LIKE CONCAT('%', #{name}, '%')
            </if>
        ORDER BY 
            CASE WHEN d.parent_id IS NULL THEN d.sort ELSE 999 END,
            d.parent_id NULLS FIRST,
            d.sort,
            d.id
    </select>

    <!-- 根据父ID获取子部门列表 -->
    <select id="selectChildrenByParentId" resultType="com.siteweb.tcs.hub.dal.entity.Department">
        SELECT * FROM tcs_department 
        WHERE parent_id = #{parentId} 
            AND deleted = 0 
        ORDER BY sort ASC, id ASC
    </select>

    <!-- 递归更新子部门状态 -->
    <update id="updateChildrenStatus">
        UPDATE tcs_department 
        SET status = #{status}, update_time = CURRENT_TIMESTAMP
        WHERE parent_id = #{parentId} AND deleted = 0
    </update>

    <!-- 获取部门的所有子部门ID（递归） -->
    <select id="selectAllChildrenIds" resultType="java.lang.Integer">
        SELECT id FROM tcs_department 
        WHERE parent_id = #{parentId} AND deleted = 0
    </select>

</mapper>