[{"controlId": "tcs_control_door_1", "controlName": "删除所有门禁卡", "controlCategory": 17, "commandType": 2, "controlType": 1, "cmdToken": "45", "baseTypeId": 1001102001}, {"controlId": "tcs_control_door_2", "controlName": "开门命令", "controlCategory": 2, "commandType": 2, "controlType": 1, "cmdToken": "10", "baseTypeId": 1001010001}, {"controlId": "tcs_control_door_3", "controlName": "布防红外", "controlCategory": 18, "commandType": 2, "controlType": 1, "cmdToken": "16", "baseTypeId": null}, {"controlId": "tcs_control_door_4", "controlName": "撤防红外", "controlCategory": 19, "commandType": 2, "controlType": 1, "cmdToken": "17", "baseTypeId": null}, {"controlId": "tcs_control_door_5", "controlName": "开门超时时间", "controlCategory": 20, "commandType": 1, "controlType": 1, "cmdToken": "30", "baseTypeId": 1001306001}, {"controlId": "tcs_control_door_6", "controlName": "刷卡进门密码工作方式", "controlCategory": 21, "commandType": 2, "controlType": 1, "cmdToken": "31", "baseTypeId": null}, {"controlId": "tcs_control_door_7", "controlName": "设置时间", "controlCategory": 22, "commandType": 1, "controlType": 1, "cmdToken": "32", "baseTypeId": null}, {"controlId": "tcs_control_door_8", "controlName": "增加门禁卡", "controlCategory": 12, "commandType": 1, "controlType": 1, "cmdToken": "36", "baseTypeId": null}, {"controlId": "tcs_control_door_9", "controlName": "删除门禁卡", "controlCategory": 13, "commandType": 1, "controlType": 1, "cmdToken": "37", "baseTypeId": 1001302001}, {"controlId": "tcs_control_door_10", "controlName": "修改门禁卡设置", "controlCategory": 14, "commandType": 1, "controlType": 1, "cmdToken": "38", "baseTypeId": 1001304001}, {"controlId": "tcs_control_door_11", "controlName": "设置星期准进时间段", "controlCategory": 15, "commandType": 1, "controlType": 1, "cmdToken": "42", "baseTypeId": 1001305001}, {"controlId": "tcs_control_door_12", "controlName": "修改验证控制密码", "controlCategory": 16, "commandType": 1, "controlType": 1, "cmdToken": "62", "baseTypeId": null}, {"controlId": "tcs_control_door_13", "controlName": "非法开门告警结束确定命令", "controlCategory": 23, "commandType": 1, "controlType": 1, "cmdToken": "100", "baseTypeId": null}, {"controlId": "tcs_control_door_14", "controlName": "非法开门告警状态", "controlCategory": 1, "commandType": 2, "controlType": 1, "cmdToken": "200", "baseTypeId": null}, {"controlId": "tcs_control_door_15", "controlName": "刷卡后在规定时间不开门告警确认", "controlCategory": 1, "commandType": 2, "controlType": 1, "cmdToken": "201", "baseTypeId": null}, {"controlId": "tcs_control_door_16", "controlName": "远程遥控规定时间不开门告警确认", "controlCategory": 1, "commandType": 2, "controlType": 1, "cmdToken": "202", "baseTypeId": null}, {"controlId": "tcs_control_door_17", "controlName": "手动开门规定时间不开门告警状态", "controlCategory": 1, "commandType": 2, "controlType": 1, "cmdToken": "203", "baseTypeId": null}, {"controlId": "tcs_control_door_18", "controlName": "刷卡后在规定时间不关门告警状态", "controlCategory": 1, "commandType": 2, "controlType": 1, "cmdToken": "204", "baseTypeId": null}, {"controlId": "tcs_control_door_19", "controlName": "远程遥控规定时间不关门告警状态", "controlCategory": 1, "commandType": 2, "controlType": 1, "cmdToken": "205", "baseTypeId": null}, {"controlId": "tcs_control_door_20", "controlName": "手动开门规定时间不关门告警状态", "controlCategory": 1, "commandType": 2, "controlType": 1, "cmdToken": "206", "baseTypeId": null}, {"controlId": "tcs_control_door_21", "controlName": "加卡", "controlCategory": 12, "commandType": 1, "controlType": 1, "cmdToken": "36", "baseTypeId": 1001301001}, {"controlId": "tcs_control_door_22", "controlName": "删卡", "controlCategory": 13, "commandType": 1, "controlType": 1, "cmdToken": "37", "baseTypeId": 1001302001}, {"controlId": "tcs_control_door_23", "controlName": "设置控制器时间", "controlCategory": 22, "commandType": 1, "controlType": 1, "cmdToken": "32", "baseTypeId": 1001307001}, {"controlId": "tcs_control_door_24", "controlName": "删除全部用户", "controlCategory": 17, "commandType": 1, "controlType": 1, "cmdToken": "45", "baseTypeId": 1001102001}, {"controlId": "tcs_control_door_25", "controlName": "远程开门", "controlCategory": 2, "commandType": 2, "controlType": 1, "cmdToken": "75", "baseTypeId": 1001010001}, {"controlId": "tcs_control_door_26", "controlName": "设置准进时间段", "controlCategory": 15, "commandType": 1, "controlType": 1, "cmdToken": "64", "baseTypeId": 1001305001}, {"controlId": "tcs_control_door_27", "controlName": "修改门禁卡", "controlCategory": 14, "commandType": 1, "controlType": 1, "cmdToken": "38", "baseTypeId": null}, {"controlId": "tcs_control_door_28", "controlName": "新增控制510002182", "controlCategory": 20, "commandType": 1, "controlType": 1, "cmdToken": "20", "baseTypeId": 1001306001}, {"controlId": "tcs_control_door_29", "controlName": "修改控制器登录密码", "controlCategory": 16, "commandType": 1, "controlType": 1, "cmdToken": "62", "baseTypeId": null}, {"controlId": "tcs_control_door_30", "controlName": "添加人脸信息", "controlCategory": 33, "commandType": 1, "controlType": 1, "cmdToken": "95", "baseTypeId": null}, {"controlId": "tcs_control_door_31", "controlName": "删除人脸信息", "controlCategory": 34, "commandType": 1, "controlType": 1, "cmdToken": "96", "baseTypeId": null}, {"controlId": "tcs_control_door_32", "controlName": "添加修改用户", "controlCategory": 35, "commandType": 1, "controlType": 1, "cmdToken": "101", "baseTypeId": null}, {"controlId": "tcs_control_door_33", "controlName": "删除用户", "controlCategory": 36, "commandType": 1, "controlType": 1, "cmdToken": "102", "baseTypeId": null}, {"controlId": "tcs_control_door_34", "controlName": "采集人脸信息", "controlCategory": 37, "commandType": 1, "controlType": 1, "cmdToken": "110", "baseTypeId": null}, {"controlId": "tcs_control_door_35", "controlName": "采集指纹信息", "controlCategory": 38, "commandType": 1, "controlType": 1, "cmdToken": "111", "baseTypeId": null}, {"controlId": "tcs_control_door_36", "controlName": "添加指纹信息", "controlCategory": 31, "commandType": 1, "controlType": 1, "cmdToken": "93", "baseTypeId": null}, {"controlId": "tcs_control_door_37", "controlName": "删除指纹信息", "controlCategory": 32, "commandType": 1, "controlType": 1, "cmdToken": "94", "baseTypeId": null}, {"controlId": "tcs_control_door_38", "controlName": "开门方式设置", "controlCategory": 50, "commandType": 2, "controlType": 1, "cmdToken": "120", "baseTypeId": null}, {"controlId": "tcs_control_door_39", "controlName": "刷卡进门时延（分）", "controlCategory": 1, "commandType": 1, "controlType": 1, "cmdToken": "63", "baseTypeId": null}, {"controlId": "tcs_control_door_40", "controlName": "按钮出门时延（秒）", "controlCategory": 20, "commandType": 1, "controlType": 1, "cmdToken": "30", "baseTypeId": null}, {"controlId": "tcs_control_door_41", "controlName": "读取所有用户资料", "controlCategory": 1, "commandType": 1, "controlType": 1, "cmdToken": "99", "baseTypeId": null}, {"controlId": "tcs_control_door_42", "controlName": "远程关门", "controlCategory": 1, "commandType": 1, "controlType": 1, "cmdToken": "99", "baseTypeId": null}]