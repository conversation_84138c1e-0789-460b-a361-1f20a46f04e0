package com.siteweb.tcs.hub.mock;

import io.netty.bootstrap.Bootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.codec.bytes.ByteArrayDecoder;
import io.netty.handler.codec.bytes.ByteArrayEncoder;
import io.netty.buffer.ByteBuf;
import lombok.AllArgsConstructor;
import org.checkerframework.checker.units.qual.A;

import java.util.function.Consumer;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-06-18 13:46
 **/

public class NettyTcpClient {

    private final String host;
    private final int port;
    private Channel channel;
    private static final EventLoopGroup group = new NioEventLoopGroup();;
    private Consumer<byte[]> messageConsumer;

    public NettyTcpClient(String host, int port,Consumer<byte[]> messageConsumer) {
        this.host = host;
        this.port = port;
        this.messageConsumer = messageConsumer;
    }

    public void start() throws InterruptedException {

        Bootstrap bootstrap = new Bootstrap();
        bootstrap.group(group)
                .channel(NioSocketChannel.class)
                .option(ChannelOption.SO_KEEPALIVE, true)
                .handler(new ChannelInitializer<SocketChannel>() {
                    @Override
                    public void initChannel(SocketChannel ch) {
                        ChannelPipeline pipeline = ch.pipeline();
                        // 添加编码器用于发送字节数组
                        pipeline.addLast(new ByteArrayEncoder());
                        // 添加解码器用于接收字节数组
                        pipeline.addLast(new ByteArrayDecoder());
                        pipeline.addLast(new TcpClientHandler(messageConsumer));
                    }
                });

        ChannelFuture future = bootstrap.connect(host, port).sync();
        this.channel = future.channel();

        System.out.println("===> TCP 客户端连接成功: " + host + ":" + port);
    }

    public void send(byte[] message) {
        if (channel != null && channel.isActive()) {
            System.out.println("发送字节数组长度：" + message.length + ", 内容：" + java.util.Arrays.toString(message));
            ChannelFuture future = channel.writeAndFlush(message);
            future.addListener(new ChannelFutureListener() {
                @Override
                public void operationComplete(ChannelFuture channelFuture) throws Exception {
                    if (channelFuture.isSuccess()) {
                        System.out.println("消息发送成功");
                    } else {
                        System.err.println("消息发送失败：" + channelFuture.cause().getMessage());
                    }
                }
            });
        } else {
            System.err.println("连接未建立或已断开！无法发送消息");
        }
    }

    public void stop() {
        if (group != null) {
            group.shutdownGracefully();
        }
    }

    // 客户端处理逻辑
    @AllArgsConstructor
    private static class TcpClientHandler extends SimpleChannelInboundHandler<byte[]> {

        private Consumer<byte[]> messageConsumer;

        @Override
        protected void channelRead0(ChannelHandlerContext ctx, byte[] msg) {
            System.out.println("Received bytes: " + java.util.Arrays.toString(msg));
            System.out.println("客户端收到响应字节数组长度：" + msg.length);
            if (messageConsumer != null) {
                messageConsumer.accept(msg);
            }
        }

        @Override
        public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
            System.err.println("连接异常: " + cause.getMessage());
            ctx.close();
        }
    }
}
