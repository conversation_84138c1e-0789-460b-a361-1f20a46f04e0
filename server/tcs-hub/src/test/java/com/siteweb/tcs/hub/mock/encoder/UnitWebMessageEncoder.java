package com.siteweb.tcs.hub.mock.encoder;

import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;

import java.nio.charset.StandardCharsets;

/**
 * @program: tcs2
 * @description: String消息编码器
 * @author: xsx
 * @create: 2025-06-19 09:34
 **/

public class UnitWebMessageEncoder extends MessageToByteEncoder<String> {

    private static final byte EOT = 0x04;
    private static final int COMMAND_ID = 202;

    @Override
    protected void encode(ChannelHandlerContext ctx, String msg, ByteBuf out) throws Exception {
        out.writeInt(COMMAND_ID); // 4字节命令ID
        
        if (msg != null && !msg.isEmpty()) {
            byte[] content = msg.getBytes(StandardCharsets.UTF_8);
            out.writeInt(content.length); // 4字节消息长度
            out.writeBytes(content); // 消息内容
        } else {
            out.writeInt(0); // 4字节消息长度为0
            out.writeByte(EOT); // msglen == 0 时附加 EOT (0x04)
        }
    }
}
