package com.siteweb.tcs.hub.mock;

import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioSocketChannel;
import lombok.AllArgsConstructor;

import java.nio.charset.StandardCharsets;
import java.util.function.Consumer;

public class TcpClient {
    private final String host;
    private final int port;
    private Channel channel;
    private Consumer<byte[]> messageHandler;

    public TcpClient(String host, int port) {
        this.host = host;
        this.port = port;
    }

    public void start() throws Exception {
        EventLoopGroup group = new NioEventLoopGroup();
        try {
            Bootstrap bootstrap = new Bootstrap()
                    .group(group)
                    .channel(NioSocketChannel.class)
                    .handler(new ChannelInitializer<Channel>() {
                        @Override
                        protected void initChannel(Channel ch) {
                            ch.pipeline().addLast(new ClientHandler(messageHandler));
                        }
                    });

            // 连接服务器
            ChannelFuture future = bootstrap.connect(host, port).sync();
            this.channel = future.channel();
            System.out.println("Client connected to " + host + ":" + port);
        } catch (Exception e) {
            group.shutdownGracefully();
            throw e;
        }
    }

    // 发送 byte 数组
    public void send(byte[] data) {
        if (channel != null && channel.isActive()) {
            ByteBuf buf = Unpooled.copiedBuffer(data);
            channel.writeAndFlush(buf);
        } else {
            System.out.println("Channel is not active");
        }
    }

    // 关闭客户端
    public void shutdown() {
        if (channel != null) {
            channel.close();
        }
    }

    @AllArgsConstructor
    // 客户端处理器
    private static class ClientHandler extends SimpleChannelInboundHandler<ByteBuf> {

        private Consumer<byte[]> messageHandler;
        @Override
        protected void channelRead0(ChannelHandlerContext ctx, ByteBuf msg) {
            // 接收 byte 数组
            byte[] data = new byte[msg.readableBytes()];
            msg.readBytes(data);
            System.out.println("Received: " + new String(data, StandardCharsets.UTF_8));
            // 这里可以添加处理接收到的 byte 数组的逻辑
            messageHandler.accept( data);
        }

        @Override
        public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
            cause.printStackTrace();
            ctx.close();
        }
    }
}