package com.siteweb.tcs.hub.mock;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-06-18 11:16
 **/
@Slf4j
public class test {
    public static String getTcpResponse(String ipAddress) {

        // 配置参数

        final int PORT = 6790;

        final int COMMAND_ID = 202;

        final String REQUEST_MESSAGE = "{\"code\": 24}";

        try (

                // 使用try-with-resources自动关闭资源

                Socket socket = new Socket()

        ) {

            // 设置连接超时时间

            socket.connect(new InetSocketAddress(ipAddress, PORT), 6000);

            // 设置读取数据超时时间

            socket.setSoTimeout(15000);

            try (

                    OutputStream outputStream = socket.getOutputStream();

                    InputStream inputStream = socket.getInputStream()

            ) {

                // 发送请求

                sendRequest(outputStream, COMMAND_ID, REQUEST_MESSAGE);

                // 读取并解析响应

                String response = readResponse(inputStream);

                log.info("Successfully received response from {}:{}", ipAddress, PORT);

                return response;

            }

        } catch (SocketTimeoutException ste) {

            log.info("Connection timed out: {}, IP: {}", ste.getMessage(), ipAddress, ste);

        } catch (SocketException se) {

            log.info("TCP connection failed: {} - {}, IP: {}", se.getMessage(), se.getCause(), ipAddress, se);

        } catch (IOException ioe) {

            log.info("IO error occurred: {}, IP: {}", ioe.getMessage(), ipAddress, ioe);

        } catch (Exception ex) {

            log.info("Unexpected error: {}, IP: {}", ex.getMessage(), ipAddress, ex);

        }

        return "";

    }

    /**

     * 发送TCP请求

     *

     * @param outputStream 输出流

     * @param commandId 命令ID

     * @param message 请求消息内容

     * @throws IOException 如果发送失败

     */

    private static void sendRequest(OutputStream outputStream, int commandId, String message) throws IOException {

        // 1. 准备命令ID (4字节)

        ByteBuffer cmdIdBuffer = ByteBuffer.allocate(4);

        cmdIdBuffer.order(ByteOrder.BIG_ENDIAN);

        cmdIdBuffer.putInt(commandId);

        // 2. 准备消息内容和长度

        byte[] msgBytes = message.getBytes(StandardCharsets.UTF_8);

        ByteBuffer msgLenBuffer = ByteBuffer.allocate(4);

        msgLenBuffer.order(ByteOrder.BIG_ENDIAN);

        msgLenBuffer.putInt(msgBytes.length);

        // 3. 组装完整的TCP请求包 (cmdId + msgLen + msg)

        ByteBuffer requestPacket = ByteBuffer.allocate(8 + msgBytes.length);

        requestPacket.put(cmdIdBuffer.array());   // 命令ID (4字节)

        requestPacket.put(msgLenBuffer.array());  // 消息长度 (4字节)

        requestPacket.put(msgBytes);              // 消息内容

        // 4. 发送数据

        outputStream.write(requestPacket.array());

        outputStream.flush();

    }

    /**

     * 读取TCP响应并解析

     *

     * @param inputStream 输入流

     * @return 解析后的响应消息

     * @throws IOException 如果读取失败

     */

    private static String readResponse(InputStream inputStream) throws IOException {

        // 1. 读取头部(8字节): 命令ID(4字节) + 消息长度(4字节)

        byte[] headerBuffer = new byte[8];

        int headerBytesRead = 0;

        int bytesRead;

        // 确保读取完整的头部

        while (headerBytesRead < 8 && (bytesRead = inputStream.read(headerBuffer, headerBytesRead, 8 - headerBytesRead)) > 0) {

            headerBytesRead += bytesRead;

        }

        if (headerBytesRead != 8) {

            throw new IOException("Failed to read complete header: expected 8 bytes, got " + headerBytesRead);

        }

        // 2. 解析头部信息

        ByteBuffer headerByteBuffer = ByteBuffer.wrap(headerBuffer);

        headerByteBuffer.order(ByteOrder.BIG_ENDIAN);

        int responseCommandId = headerByteBuffer.getInt();

        int responseLength = headerByteBuffer.getInt();

        log.info("Response header: Command ID={}, Length={}", responseCommandId, responseLength);

        // 防止异常大的长度值

        if (responseLength <= 0 || responseLength > 1024 * 1024) {

            throw new IOException("Invalid response length: " + responseLength);

        }

        // 3. 读取消息内容(实际有用的数据)

        byte[] messageBuffer = new byte[responseLength];

        int messageBytesRead = 0;

        // 确保读取完整的消息

        while (messageBytesRead < responseLength &&

                (bytesRead = inputStream.read(messageBuffer, messageBytesRead, responseLength - messageBytesRead)) > 0) {

            messageBytesRead += bytesRead;

        }

        if (messageBytesRead != responseLength) {

            throw new IOException("Failed to read complete message: expected " + responseLength +

                    " bytes, got " + messageBytesRead);

        }

        // 4. 将有效负载转换为字符串并返回

        String response = new String(messageBuffer, StandardCharsets.UTF_8);

        // 5. 检查并移除尾部的特殊字符

        if (responseLength >= 2) {

            // 移除常见的尾部标记

            if (response.endsWith("\0\0")) {

                response = response.substring(0, response.length() - 2);

            } else if (response.endsWith("\r\n")) {

                response = response.substring(0, response.length() - 2);

            } else {

                // 如果是其他字符，可以检查最后两个字节

                // 这里需要根据实际情况判断处理方式

                byte[] lastTwoBytes = new byte[2];

                System.arraycopy(messageBuffer, responseLength - 2, lastTwoBytes, 0, 2);

                // 如果确定最后两个字节始终是协议的一部分而非实际数据

                response = new String(messageBuffer, 0, responseLength - 2, StandardCharsets.UTF_8);

            }

        }

        return response;

    }

}
