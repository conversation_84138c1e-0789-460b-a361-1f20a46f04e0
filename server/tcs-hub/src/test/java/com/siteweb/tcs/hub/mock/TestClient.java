package com.siteweb.tcs.hub.mock;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-06-18 18:45
 **/

public class TestClient {
    public static void main(String[] args) {
        try {
            UnitWebTcpClient unitWebTcpClient = new UnitWebTcpClient("*************", 6790);
            unitWebTcpClient.send(UnitWebCmdEnum.cmd_get_sigdata, "{\"code\":63,\"equipid\":\"29000187\"}");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }
}
