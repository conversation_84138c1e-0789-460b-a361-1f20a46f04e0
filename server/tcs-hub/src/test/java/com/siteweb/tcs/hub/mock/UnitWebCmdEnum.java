package com.siteweb.tcs.hub.mock;

import lombok.Getter;

@Getter
public enum UnitWebCmdEnum {
    cmd_get_sigdata(62,"获取信号数据"),
    cmd_get_eventlist(63,"获取事件列表"),
    cmd_get_ctrllist(64,"获取控制器列表"),
    cmd_send_control(65,"发送控制命令");


    private int cmdId;
    private String cmdName;

    private UnitWebCmdEnum(int cmdId, String cmdName) {
        this.cmdId = cmdId;
        this.cmdName = cmdName;
    }
}
