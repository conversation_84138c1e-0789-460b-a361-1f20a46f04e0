package com.siteweb.tcs.hub.mock;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-06-18 14:09
 **/
@Slf4j
public class UnitWebTcpClient {
    private String ip;
    private int port;
    private NettyTcpClient nettyTcpClient;

    public UnitWebTcpClient(String ip, int port) throws  Exception{
        this.ip = ip;
        this.port = port;
        this.nettyTcpClient = new NettyTcpClient(ip, port, res -> parseResponse(res));
        nettyTcpClient.start();
    }

    public void send(UnitWebCmdEnum cmdEnum, String data) throws Exception{
        ByteBuffer cmdIdBuffer = null;
        ByteBuffer msgLenBuffer = null;
        ByteBuffer requestPacket = null;
        try {
            cmdIdBuffer = ByteBuffer.allocate(4);

            cmdIdBuffer.order(ByteOrder.BIG_ENDIAN);

            cmdIdBuffer.putInt(202);

            // 2. 准备消息内容和长度

            byte[] msgBytes = data.getBytes(StandardCharsets.UTF_8);

            msgLenBuffer = ByteBuffer.allocate(4);

            msgLenBuffer.order(ByteOrder.BIG_ENDIAN);

            msgLenBuffer.putInt(msgBytes.length);

            // 3. 组装完整的TCP请求包 (cmdId + msgLen + msg)

            requestPacket = ByteBuffer.allocate(8 + msgBytes.length);

            requestPacket.put(cmdIdBuffer.array());   // 命令ID (4字节)

            requestPacket.put(msgLenBuffer.array());  // 消息长度 (4字节)

            requestPacket.put(msgBytes);              // 消息内容

            byte[] sendData = requestPacket.array();

            nettyTcpClient.send(sendData);
        }finally {
            if (ObjectUtil.isNotEmpty(cmdIdBuffer)){
                cmdIdBuffer.clear();
            }
            if (ObjectUtil.isNotEmpty(msgLenBuffer)){
                msgLenBuffer.clear();
            }
            if (ObjectUtil.isNotEmpty(requestPacket)){
                requestPacket.clear();
            }
        }
    }


    private String parseResponse(byte[] responseData) {
        if (responseData == null || responseData.length < 8) {
            throw new IllegalArgumentException("Response data too short: expected at least 8 bytes.");
        }

        ByteBuffer buffer = ByteBuffer.wrap(responseData);
        buffer.order(ByteOrder.BIG_ENDIAN);

        // 1. 解析头部（命令ID + 消息长度）
        int responseCommandId = buffer.getInt();
        int responseLength = buffer.getInt();

        log.info("Response header: Command ID={}, Length={}", responseCommandId, responseLength);

        // 2. 校验长度
        if (responseLength <= 0 || responseLength > 1024 * 1024) {
            throw new IllegalArgumentException("Invalid response length: " + responseLength);
        }

        // 3. 校验总数据是否足够
        if (responseData.length < 8 + responseLength) {
            throw new IllegalArgumentException("Incomplete response data: expected " + (8 + responseLength) + " bytes, got " + responseData.length);
        }

        // 4. 提取消息体内容
        byte[] messageBuffer = Arrays.copyOfRange(responseData, 8, 8 + responseLength);
        String response = new String(messageBuffer, StandardCharsets.UTF_8);

        // 5. 检查尾部特殊字符并移除（\0\0、\r\n 或其他）
        if (responseLength >= 2) {
            if (response.endsWith("\0\0")) {
                response = response.substring(0, response.length() - 2);
            } else if (response.endsWith("\r\n")) {
                response = response.substring(0, response.length() - 2);
            } else {
                // 默认去掉最后两个字节（如果协议定义就是这样）
                response = new String(messageBuffer, 0, responseLength - 2, StandardCharsets.UTF_8);
            }
        }

        // 输出或写入缓存
        System.out.println("收到响应数据内容: " + response);
        return response;
    }

}
