package com.siteweb.tcs.hub.mock;

import cn.hutool.core.util.ObjectUtil;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-06-18 14:09
 **/

public class UnitWebTcpClient {
    private String ip;
    private int port;
    private NettyTcpClient nettyTcpClient;

    public UnitWebTcpClient(String ip, int port) throws  Exception{
        this.ip = ip;
        this.port = port;
        this.nettyTcpClient = new NettyTcpClient(ip, port, res -> parseResponse(res));
        nettyTcpClient.start();
    }

    public void send(UnitWebCmdEnum cmdEnum, String data) throws Exception{
        ByteBuffer cmdIdBuffer = null;
        ByteBuffer msgLenBuffer = null;
        ByteBuffer requestPacket = null;
        try {
            cmdIdBuffer = ByteBuffer.allocate(4);

            cmdIdBuffer.order(ByteOrder.BIG_ENDIAN);

            cmdIdBuffer.putInt(cmdEnum.getCmdId());

            // 2. 准备消息内容和长度

            byte[] msgBytes = data.getBytes(StandardCharsets.UTF_8);

            msgLenBuffer = ByteBuffer.allocate(4);

            msgLenBuffer.order(ByteOrder.BIG_ENDIAN);

            msgLenBuffer.putInt(msgBytes.length);

            // 3. 组装完整的TCP请求包 (cmdId + msgLen + msg)

            requestPacket = ByteBuffer.allocate(8 + msgBytes.length);

            requestPacket.put(cmdIdBuffer.array());   // 命令ID (4字节)

            requestPacket.put(msgLenBuffer.array());  // 消息长度 (4字节)

            requestPacket.put(msgBytes);              // 消息内容

            byte[] sendData = requestPacket.array();

            nettyTcpClient.send(sendData);
        }finally {
            if (ObjectUtil.isNotEmpty(cmdIdBuffer)){
                cmdIdBuffer.clear();
            }
            if (ObjectUtil.isNotEmpty(msgLenBuffer)){
                msgLenBuffer.clear();
            }
            if (ObjectUtil.isNotEmpty(requestPacket)){
                requestPacket.clear();
            }
        }
    }


    private String parseResponse(byte[] responseDate){
        //写进缓存
        return null;
    }
}
