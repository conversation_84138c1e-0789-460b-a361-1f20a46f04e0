-- 卡片表
CREATE TABLE tbl_card (
                          CardId SERIAL PRIMARY KEY,  -- 自动增长的主键
                          CardCode VARCHAR(20) NOT NULL,
                          CardName VARCHAR(128) DEFAULT NULL,
                          CardCategory INT DEFAULT NULL,
                          <PERSON>Group INT DEFAULT NULL,
                          UserId INT DEFAULT NULL,
                          StationId INT DEFAULT NULL,
                          CardStatus INT DEFAULT NULL,
                          StartTime TIMESTAMP DEFAULT NULL,
                          EndTime TIMESTAMP DEFAULT NULL,
                          RegisterTime TIMESTAMP DEFAULT NULL,
                          UnRegisterTime TIMESTAMP DEFAULT NULL,
                          LostTime TIMESTAMP DEFAULT NULL,
                          Description VARCHAR(255) DEFAULT NULL
);

-- 门禁设备表
CREATE TABLE tbl_door (
                          DoorId SERIAL PRIMARY KEY,  -- 自动增长的主键
                          DoorNo INT NOT NULL,
                          DoorName VARCHAR(128) DEFAULT NULL,
                          StationId INT NOT NULL,
                          EquipmentId INT NOT NULL,
                          SamplerUnitId INT DEFAULT NULL,
                          Category INT NOT NULL,
                          Address VARCHAR(255) DEFAULT NULL,
                          WorkMode INT DEFAULT NULL,
                          Infrared INT DEFAULT NULL,
                          Password VARCHAR(10) DEFAULT NULL,
                          DoorControlId INT DEFAULT NULL,
                          DoorInterval INT DEFAULT NULL,
                          OpenDelay INT DEFAULT NULL,
                          Description VARCHAR(255) DEFAULT NULL,
                          OpenMode INT DEFAULT NULL
);

-- 创建索引：设备ID和门ID
CREATE INDEX idx_door_equipment ON tbl_door (EquipmentId, DoorId);

-- 卡片与门权限关联表
CREATE TABLE tbl_doorcard (
                              CardId INT NOT NULL,
                              TimeGroupId INT NOT NULL,
                              DoorId INT NOT NULL,
                              StartTime TIMESTAMP DEFAULT NULL,
                              EndTime TIMESTAMP DEFAULT NULL,
                              Password VARCHAR(30) DEFAULT NULL,
                              timegrouptype INT NOT NULL,
                              PRIMARY KEY (CardId, DoorId, TimeGroupId, timegrouptype)
);

-- 门时间组表
CREATE TABLE tbl_doortimegroup (
                                   DoorId INT NOT NULL,
                                   TimeGroupId INT NOT NULL,
                                   TimeGroupType INT NOT NULL,
                                   PRIMARY KEY (DoorId, TimeGroupId, TimeGroupType)
);

-- 面部数据表
CREATE TABLE tbl_facedata (
                              FaceId SERIAL PRIMARY KEY,  -- 自动增长的主键
                              FaceData BYTEA NOT NULL  -- 使用 BYTEA 存储二进制数据
);

-- 指纹数据表
CREATE TABLE tbl_fingerprint (
                                 FingerPrintId SERIAL PRIMARY KEY,  -- 自动增长的主键
                                 FingerPrintNO INT NOT NULL,
                                 FingerPrintData BYTEA NOT NULL,  -- 使用 BYTEA 存储二进制数据
                                 CONSTRAINT fk_fingerprint UNIQUE(FingerPrintId, FingerPrintNO)  -- 唯一约束
);
