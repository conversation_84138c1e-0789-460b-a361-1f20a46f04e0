CREATE TABLE `tbl_equipmentbasetype` (
  `BaseEquipmentId` INT NOT NULL,
  `BaseEquipmentName` VARCHAR(128) DEFAULT NULL,
  `EquipmentTypeId` INT NOT NULL,
  `EquipmentSubTypeId` INT NOT NULL,
  `Description` TEXT,
  `ExtField` VARCHAR DEFAULT NULL,
  PRIMARY KEY (`BaseEquipmentId`),
  CONSTRAINT `tbl_equipmentbasetype_BaseEquipmentId` UNIQUE (`BaseEquipmentId`)
);

CREATE TABLE `tbl_baseclassdic` (
  `BaseClassId` INT NOT NULL,
  `BaseClassName` VARCHAR(255) NOT NULL,
  `BaseClassIcon` VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (`BaseClassId`)
);

CREATE TABLE `tbl_basecommandcode` (
  `CodeId` INT NOT NULL,
  `Command` VARCHAR(255) NOT NULL,
  `Description` VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (`CodeId`)
);

CREATE TABLE `tbl_baseequipmentcategorymap` (
  `BaseEquipmentID` INT NOT NULL,
  `EquipmentCategory` INT NOT NULL,
  PRIMARY KEY (`BaseEquipmentID`,`EquipmentCategory`)
);

CREATE TABLE `tbl_basesignaleventcode` (
  `CodeId` INT NOT NULL,
  `Category` VARCHAR(255) NOT NULL,
  `Signal` VARCHAR(255) DEFAULT NULL,
  `EVENT` VARCHAR(255) DEFAULT NULL,
  `Description` VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (`CodeId`)
);

CREATE TABLE `tbl_baseunitdic` (
  `BaseUnitID` INT NOT NULL,
  `BaseUnitName` VARCHAR(255) NOT NULL,
  `BaseUnitSymbol` VARCHAR(255) NOT NULL,
  `BaseUnitDescription` VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (`BaseUnitID`)
);

CREATE TABLE `tbl_signalbasedic` (
  `BaseTypeId` DECIMAL(12,0) NOT NULL,
  `BaseTypeName` VARCHAR(128) NOT NULL,
  `BaseEquipmentId` INT NOT NULL,
  `EnglishName` TEXT,
  `BaseLogicCategoryId` INT DEFAULT NULL,
  `StoreInterval` INT DEFAULT NULL,
  `AbsValueThreshold` DOUBLE DEFAULT NULL,
  `PercentThreshold` DOUBLE DEFAULT NULL,
  `StoreInterval2` INT DEFAULT NULL,
  `AbsValueThreshold2` DOUBLE DEFAULT NULL,
  `PercentThreshold2` DOUBLE DEFAULT NULL,
  `ExtendField1` TEXT,
  `ExtendField2` TEXT,
  `ExtendField3` TEXT,
  `UnitId` INT DEFAULT NULL,
  `BaseStatusId` INT DEFAULT NULL,
  `BaseHysteresis` DOUBLE DEFAULT NULL,
  `BaseFreqPeriod` INT DEFAULT NULL,
  `BaseFreqCount` INT DEFAULT NULL,
  `BaseShowPrecision` VARCHAR(30) DEFAULT NULL,
  `BaseStatPeriod` INT DEFAULT NULL,
  `CGElement` VARCHAR(128) DEFAULT NULL,
  `Description` TEXT,
  `BaseNameExt` VARCHAR(128) DEFAULT NULL,
  `IsSystem` BOOLEAN NOT NULL DEFAULT TRUE,
  PRIMARY KEY (`BaseTypeId`),
  CONSTRAINT `tbl_signalbasedic_BaseTypeId` UNIQUE (`BaseTypeId`)
);

CREATE TABLE `tbl_signalbasemap` (
  `StandardDicId` INT NOT NULL,
  `StandardType` INT NOT NULL,
  `StationBaseType` INT NOT NULL,
  `BaseTypeId` DECIMAL(12,0) NOT NULL,
  `BaseCondId` INT DEFAULT NULL,
  PRIMARY KEY (`BaseTypeId`,`StandardDicId`,`StandardType`,`StationBaseType`)
);

CREATE TABLE `tbl_signalbaseconfirm` (
  `EquipmentTemplateId` INT NOT NULL,
  `SignalId` INT NOT NULL,
  `StateValue` INT DEFAULT NULL,
  `SubState` VARCHAR(16) DEFAULT NULL
);
CREATE INDEX `idxsignalbaseconfirmID` ON `tbl_signalbaseconfirm` (`EquipmentTemplateId`,`SignalId`);

CREATE TABLE tbl_statusbasedic (
  BaseStatusId INT NOT NULL,
  BaseStatusName VARCHAR(128) NOT NULL,
  BaseCondId INT NOT NULL,
  Operator VARCHAR(30) NOT NULL,
  "Value" INT DEFAULT NULL, -- 使用双引号
  Meaning VARCHAR(128) DEFAULT NULL,
  Description TEXT,
  PRIMARY KEY (BaseCondId, BaseStatusId)
);

CREATE TABLE `tbl_commandbasedic` (
  `BaseTypeId` DECIMAL(12,0) NOT NULL,
  `BaseTypeName` VARCHAR(128) NOT NULL,
  `BaseEquipmentId` INT NOT NULL,
  `EnglishName` TEXT,
  `BaseLogicCategoryId` INT DEFAULT NULL,
  `CommandType` INT NOT NULL,
  `BaseStatusId` INT DEFAULT NULL,
  `ExtendField1` TEXT,
  `ExtendField2` TEXT,
  `ExtendField3` TEXT,
  `Description` TEXT,
  `BaseNameExt` VARCHAR(128) DEFAULT NULL,
  `IsSystem` BOOLEAN NOT NULL DEFAULT TRUE,
  PRIMARY KEY (`BaseTypeId`),
  CONSTRAINT `tbl_commandbasedic_BaseTypeId` UNIQUE (`BaseTypeId`)
);

CREATE TABLE `tbl_commandbasemap` (
  `StandardDicId` INT NOT NULL,
  `StandardType` INT NOT NULL,
  `StationBaseType` INT NOT NULL,
  `BaseTypeId` DECIMAL(12,0) NOT NULL,
  `BaseCondId` INT DEFAULT NULL,
  PRIMARY KEY (`BaseTypeId`,`StandardDicId`,`StandardType`,`StationBaseType`)
);

CREATE TABLE `tbl_controlbaseconfirm` (
  `EquipmentTemplateId` INT NOT NULL,
  `ControlId` INT NOT NULL,
  `ParameterValue` INT DEFAULT NULL,
  `SubState` VARCHAR(16) DEFAULT NULL
);

CREATE TABLE `tbl_eventbasedic` (
  `BaseTypeId` DECIMAL(12,0) NOT NULL,
  `BaseTypeName` VARCHAR(128) NOT NULL,
  `BaseEquipmentId` INT NOT NULL,
  `EnglishName` TEXT,
  `EventSeverityId` INT NOT NULL,
  `ComparedValue` DOUBLE DEFAULT NULL,
  `BaseLogicCategoryId` INT DEFAULT NULL,
  `StartDelay` INT DEFAULT NULL,
  `EndDelay` INT DEFAULT NULL,
  `ExtendField1` TEXT,
  `ExtendField2` TEXT,
  `ExtendField3` TEXT,
  `ExtendField4` TEXT,
  `ExtendField5` TEXT,
  `Description` TEXT,
  `BaseNameExt` VARCHAR(128) DEFAULT NULL,
  `IsSystem` BOOLEAN NOT NULL DEFAULT TRUE,
  PRIMARY KEY (`BaseTypeId`),
  CONSTRAINT `tbl_eventbasedic_BaseTypeId` UNIQUE (`BaseTypeId`)
);

CREATE TABLE `tbl_eventbaseconfirm` (
  `EquipmentTemplateId` INT NOT NULL,
  `EventId` INT NOT NULL,
  `EventConditionId` INT NOT NULL,
  `SubState` VARCHAR(16) DEFAULT NULL,
  PRIMARY KEY (`EquipmentTemplateId`,`EventConditionId`,`EventId`)
);

CREATE TABLE `tbl_eventbasemap` (
  `StandardDicId` INT NOT NULL,
  `StandardType` INT NOT NULL,
  `StationBaseType` INT NOT NULL,
  `BaseTypeId` DECIMAL(12,0) NOT NULL,
  PRIMARY KEY (`BaseTypeId`,`StandardDicId`,`StandardType`,`StationBaseType`)
);