-- Flyway migration script for Hub initial data (H2 compatible)

-- Insert initial data for tcs_plugin_dependencies
-- Note: H2 does not support JSON type directly, dependencies stored as VARCHAR(MAX)
INSERT INTO tcs_plugin_dependencies (applicationName, pluginId, dependencies) VALUES ('default', 'south-ctcc-plugin', '["north-s6-plugin"]');
INSERT INTO tcs_plugin_dependencies (applicationName, pluginId, dependencies) VALUES ('default', 'north-s6-plugin', '[]');

-- Insert initial data for tcs_account
-- Note: H2 uses BOOLEAN for bit(1), TRUE/FALSE for b'1'/b'0', TIMESTAMP for datetime
INSERT INTO tcs_account (UserId, UserName, LoginId, Password, Enable, MaxError, Locked, ValidTime, PasswordValidTime, Description)
VALUES(-1, '系统管理员', 'admin', 'e9bd73aab7ddbac5d1d1b723f10edb169eb36603132a964d7ff8d98529c1bd8a', TRUE, 5, FALSE, '2050-05-02 23:59:59', '2050-05-02 23:59:59', '系统默认超级管理员');

-- Insert initial data for tcs_role
INSERT INTO tcs_role (RoleId, RoleName, Description) VALUES(-1, '系统管理员', '拥有系统所有权限');

-- Insert initial data for tcs_account_role_map
INSERT INTO tcs_account_role_map (UserId,RoleId) VALUES (-1,-1);

-- Insert initial data for tcs_menu_item
-- Note: H2 uses BOOLEAN for bit(1), TRUE/FALSE for b'1'/b'0'
INSERT INTO tcs_menu_item (MenuItemId, PluginId, MenuItemName, ParentMenuItemId, Path, Icon, Selected, Expanded, LayoutPosition, IsSystemConfig, IsExternalWeb, Description) VALUES
(1,'tcs_hub','TCS菜单',0,'#',NULL,FALSE,FALSE,NULL,FALSE,FALSE,NULL),
(2,'tcs_hub','总览',1,'dashboard',NULL,FALSE,FALSE,NULL,FALSE,FALSE,NULL),
(3,'tcs_hub','维护',1,'maintenance',NULL,FALSE,FALSE,NULL,FALSE,FALSE,NULL),
(4,'tcs_hub','调试',1,'debug',NULL,FALSE,FALSE,NULL,FALSE,FALSE,NULL),
(5,'tcs_hub','插件',1,'plugins',NULL,FALSE,FALSE,NULL,FALSE,FALSE,NULL),
(6,'tcs_hub','插件管理',3,'plugin-manage',NULL,FALSE,FALSE,NULL,FALSE,FALSE,NULL),
(7,'tcs_hub','区域管理',3,'region-manage',NULL,FALSE,FALSE,NULL,FALSE,FALSE,NULL),
(8,'tcs_hub','系统配置',3,'system-config',NULL,FALSE,FALSE,NULL,FALSE,FALSE,NULL),
(9,'tcs_hub','Actor总览',4,'actor-tree',NULL,FALSE,FALSE,NULL,FALSE,FALSE,NULL),
(10,'tcs_hub','跟踪图',4,'trace-graph',NULL,FALSE,FALSE,NULL,FALSE,FALSE,NULL),
(11,'south-ctcc-plugin','电信B接口',5,'ctcc',NULL,FALSE,FALSE,NULL,FALSE,FALSE,NULL),
(12,'south-ctcc-plugin','运行总览',11,'runningoverview',NULL,FALSE,FALSE,NULL,FALSE,FALSE,NULL),
(13,'south-ctcc-plugin','维护总览',11,'maintenanceoverview',NULL,FALSE,FALSE,NULL,FALSE,FALSE,NULL),
(14,'south-ctcc-plugin','FSU配置',11,'fsuconfig',NULL,FALSE,FALSE,NULL,FALSE,FALSE,NULL),
(15,'tcs_hub','账号管理',3,'account-manage',NULL,NULL,NULL,NULL,NULL,NULL,NULL),
(16,'tcs_hub','角色管理',3,'role-manage',NULL,FALSE,FALSE,NULL,FALSE,FALSE,NULL),
(17,'tcs_hub','采集器',4,'gateway',NULL,FALSE,FALSE,NULL,FALSE,FALSE,NULL),
(18,'tcs_hub','活动告警',4,'activity-alarm',NULL,FALSE,FALSE,NULL,FALSE,FALSE,NULL),
(19,'tcs_hub','活动控制命令',4,'activity-control',NULL,FALSE,FALSE,NULL,FALSE,FALSE,NULL),
(20,'tcs_hub','系统健康',4,'system-health',NULL,FALSE,FALSE,NULL,FALSE,FALSE,NULL),
(21,'south-ctcc-plugin','电信字典配置',11,'signalOptionConfig',NULL,FALSE,FALSE,NULL,FALSE,FALSE,NULL);

-- Insert initial data for tcs_regions
-- Note: H2 does not require LOCK TABLES/UNLOCK TABLES
INSERT INTO tcs_regions (regionId, regionName, parentId, displayIndex, description, resourceStructureId) VALUES 
(1,'南山智园',NULL,NULL,NULL, 0), -- Assuming resourceStructureId is required and defaulting to 0 if not specified in original
(2,'B1栋',1,NULL,NULL, 0),
(3,'B2栋',1,NULL,NULL, 0),
(4,'C1栋',1,NULL,NULL, 0),
(5,'C2栋',1,NULL,NULL, 0),
(6,'B2-1F',3,NULL,NULL, 0),
(7,'B2-2F',3,NULL,NULL, 0),
(8,'B2-3F',3,NULL,NULL, 0),
(9,'B2-4F',3,NULL,NULL, 0),
(10,'B2-5F',3,NULL,NULL, 0),
(11,'B2-6F',3,NULL,NULL, 0),
(12,'B2-7F',3,NULL,NULL, 0),
(13,'501',10,NULL,NULL, 0),
(14,'502',10,NULL,NULL, 0),
(15,'503',10,NULL,NULL, 0);

INSERT INTO `tcs_plugins` (`pluginId`,`pluginName`,`version`,`provider`,`description`,`className`,`buildTime`,`fileName`,`enabled`,`uploadJARDate`,`updateJARDate`,`changeDate`,`operateDate`,`applicationName`) VALUES ('south-cucc-plugin','中国联通南向接入插件','1.2.3','Undefined','','com.siteweb.tcs.south.cucc.SouthCuccPlugin','2025-03-13 03:06:40','tcs-south-cucc-0.0.1-SNAPSHOT.jar',1,'2025-03-13 14:27:02','2025-03-13 14:27:02',NULL,NULL,NULL);
INSERT INTO `tcs_plugins` (`pluginId`,`pluginName`,`version`,`provider`,`description`,`className`,`buildTime`,`fileName`,`enabled`,`uploadJARDate`,`updateJARDate`,`changeDate`,`operateDate`,`applicationName`) VALUES ('south-cmcc-plugin','中国移动南向接入插件','1.2.3','Undefined','','com.siteweb.tcs.south.cmcc.SouthCmccPlugin','2025-03-13 03:06:40','tcs-south-cmcc-0.0.1-SNAPSHOT.jar',1,'2025-03-13 14:27:02','2025-03-13 14:27:02',NULL,NULL,NULL);
INSERT INTO `tcs_plugins` (`pluginId`,`pluginName`,`version`,`provider`,`description`,`className`,`buildTime`,`fileName`,`enabled`,`uploadJARDate`,`updateJARDate`,`changeDate`,`operateDate`,`applicationName`) VALUES ('south-crcc-plugin','中国铁路南向接入插件','1.2.3','Undefined','','com.siteweb.tcs.south.crcc.SouthCrccPlugin','2025-03-13 03:06:40','tcs-south-crcc-0.0.1-SNAPSHOT.jar',1,'2025-03-13 14:27:02','2025-03-13 14:27:02',NULL,NULL,NULL);
INSERT INTO `tcs_plugins` (`pluginId`,`pluginName`,`version`,`provider`,`description`,`className`,`buildTime`,`fileName`,`enabled`,`uploadJARDate`,`updateJARDate`,`changeDate`,`operateDate`,`applicationName`) VALUES ('south-omc-siteweb','siteweb-omc','1.2.3','Undefined','','com.siteweb.tcs.south.omc.OmcSitewebPlugin','2025-03-13 03:06:40','tcs-south-omc-siteweb-0.0.1-SNAPSHOT.jar',1,'2025-03-13 14:27:02','2025-03-13 14:27:02',NULL,NULL,NULL);

-- todo xsx 测试数据后面删除
INSERT INTO tcs_foreign_gateway (
  ForeignGatewayId,
  PluginId,
  MonitorUnitId,
  StationId
) VALUES (
  'FSU123456',
  'tcs-south-cmcc',
  201010002,
  -201
);

