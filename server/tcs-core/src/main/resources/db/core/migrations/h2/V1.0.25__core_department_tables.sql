-- Flyway migration script for Department tables (H2 compatible)

-- Table: tcs_department
-- Comment: 部门表
CREATE TABLE tcs_department (
  id BIGINT AUTO_INCREMENT PRIMARY KEY, -- 部门ID
  name VARCHAR(128) NOT NULL, -- 部门名称
  code VARCHAR(64) NOT NULL, -- 部门编码，唯一标识
  parent_id BIGINT DEFAULT NULL, -- 父部门ID，顶级部门为NULL
  leader VARCHAR(64) DEFAULT NULL, -- 负责人
  phone VARCHAR(32) DEFAULT NULL, -- 联系电话
  email VARCHAR(128) DEFAULT NULL, -- 邮箱
  status INT DEFAULT 1, -- 启用状态：1-启用，0-停用
  sort INT DEFAULT 0, -- 排序字段，同级部门按此字段排序
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间
  deleted INT DEFAULT 0, -- 逻辑删除：0-未删除，1-已删除
  remark VARCHAR(512) DEFAULT NULL -- 备注
);

-- Create indexes for better performance
CREATE INDEX idx_department_parent_id ON tcs_department (parent_id);
CREATE INDEX idx_department_code ON tcs_department (code);
CREATE INDEX idx_department_status ON tcs_department (status);
CREATE INDEX idx_department_deleted ON tcs_department (deleted);
CREATE INDEX idx_department_sort ON tcs_department (sort);

-- Add unique constraint for department code (excluding deleted records)
ALTER TABLE tcs_department ADD CONSTRAINT uk_department_code_deleted UNIQUE (code, deleted);