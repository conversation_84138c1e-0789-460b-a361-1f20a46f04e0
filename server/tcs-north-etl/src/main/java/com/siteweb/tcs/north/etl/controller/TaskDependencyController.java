package com.siteweb.tcs.north.etl.controller;

import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.north.etl.model.Task;
import com.siteweb.tcs.north.etl.model.TaskDependency;
import com.siteweb.tcs.north.etl.service.TaskDependencyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 任务依赖关系控制器
 */
@Slf4j
@RestController
@RequestMapping("/etl/task-dependencies")
@Api(tags = "任务依赖关系管理")
public class TaskDependencyController {
    
    @Autowired
    private TaskDependencyService taskDependencyService;
    
    /**
     * 添加任务依赖关系
     */
    @ApiOperation("添加任务依赖关系")
    @PostMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> addDependency(@RequestBody Map<String, Object> request) {
        Integer taskId = (Integer) request.get("taskId");
        Integer dependsOnTaskId = (Integer) request.get("dependsOnTaskId");
        String dependencyType = (String) request.get("dependencyType");
        
        TaskDependency dependency = taskDependencyService.addDependency(taskId, dependsOnTaskId, dependencyType);
        return ResponseEntity.ok(ResponseResult.success(dependency));
    }
    
    /**
     * 删除任务依赖关系
     */
    @ApiOperation("删除任务依赖关系")
    @DeleteMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> removeDependency(@RequestParam Integer taskId, @RequestParam Integer dependsOnTaskId) {
        taskDependencyService.removeDependency(taskId, dependsOnTaskId);
        return ResponseEntity.ok(ResponseResult.success());
    }
    
    /**
     * 获取任务的所有依赖任务
     */
    @ApiOperation("获取任务的所有依赖任务")
    @GetMapping(value = "/dependencies/{taskId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getDependencies(@PathVariable Integer taskId) {
        List<Task> dependencies = taskDependencyService.getDependencies(taskId);
        return ResponseEntity.ok(ResponseResult.success(dependencies));
    }
    
    /**
     * 获取依赖于指定任务的所有任务
     */
    @ApiOperation("获取依赖于指定任务的所有任务")
    @GetMapping(value = "/dependents/{taskId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getDependents(@PathVariable Integer taskId) {
        List<Task> dependents = taskDependencyService.getDependents(taskId);
        return ResponseEntity.ok(ResponseResult.success(dependents));
    }
    
    /**
     * 检查任务依赖是否满足
     */
    @ApiOperation("检查任务依赖是否满足")
    @GetMapping(value = "/check/{taskId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> checkDependencies(@PathVariable Integer taskId) {
        boolean satisfied = taskDependencyService.areDependenciesSatisfied(taskId);
        return ResponseEntity.ok(ResponseResult.success(satisfied));
    }
}
