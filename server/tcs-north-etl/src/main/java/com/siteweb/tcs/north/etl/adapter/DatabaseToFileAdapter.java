package com.siteweb.tcs.north.etl.adapter;

import com.siteweb.tcs.north.etl.model.Reader;
import com.siteweb.tcs.north.etl.model.Strategy;
import com.siteweb.tcs.north.etl.model.Writer;
import com.siteweb.tcs.north.etl.nifi.service.NiFiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 数据库到文件的策略适配器
 */
@Slf4j
@Component
public class DatabaseToFileAdapter implements StrategyAdapter {
    
    @Autowired
    private NiFiService nifiService;
    
    @Override
    public boolean supports(Reader reader, Writer writer) {
        return "DATABASE".equals(reader.getType()) && "FILE".equals(writer.getType());
    }
    
    @Override
    public String createNifiFlow(Strategy strategy, Reader reader, Writer writer) {
        log.info("Creating NiFi flow for database to file strategy: {}", strategy.getName());
        
        try {
            // 1. 创建流程组
            String processGroupId = nifiService.createEtlProcessGroup("ETL_" + strategy.getName());
            
            // 2. 获取读取器配置
            Map<String, Object> readerConfig = reader.getConfigJson();
            String databaseType = (String) readerConfig.get("databaseType");
            String connectionUrl = (String) readerConfig.get("connectionUrl");
            String username = (String) readerConfig.get("username");
            String password = (String) readerConfig.get("password");
            String query = (String) readerConfig.get("query");
            
            // 3. 获取存储器配置
            Map<String, Object> writerConfig = writer.getConfigJson();
            String directory = (String) writerConfig.get("directory");
            String filename = (String) writerConfig.get("filename");
            
            // 4. 创建数据库提取处理器
            String extractProcessorId = nifiService.createDatabaseExtractProcessor(
                    processGroupId,
                    "Extract_" + reader.getName(),
                    databaseType,
                    connectionUrl,
                    username,
                    password,
                    query
            );
            
            // 5. 创建转换处理器（如果有转换规则）
            String transformProcessorId = null;
            if (strategy.getRulesJson() != null && !strategy.getRulesJson().isEmpty()) {
                String scriptBody = generateTransformScript(strategy);
                transformProcessorId = nifiService.createTransformProcessor(
                        processGroupId,
                        "Transform_" + strategy.getName(),
                        "groovy",
                        scriptBody
                );
            }
            
            // 6. 创建文件写入处理器
            String writeProcessorId = nifiService.createFileWriteProcessor(
                    processGroupId,
                    "Write_" + writer.getName(),
                    directory,
                    filename
            );
            
            // 7. 创建连接
            if (transformProcessorId != null) {
                // 如果有转换处理器，创建提取->转换->写入的连接
                nifiService.createConnection(processGroupId, extractProcessorId, transformProcessorId);
                nifiService.createConnection(processGroupId, transformProcessorId, writeProcessorId);
            } else {
                // 如果没有转换处理器，直接创建提取->写入的连接
                nifiService.createConnection(processGroupId, extractProcessorId, writeProcessorId);
            }
            
            log.info("Created NiFi flow for database to file strategy: {} with process group ID: {}", 
                    strategy.getName(), processGroupId);
            
            return processGroupId;
        } catch (Exception e) {
            log.error("Error creating NiFi flow for database to file strategy: {}", strategy.getName(), e);
            throw new RuntimeException("Failed to create NiFi flow", e);
        }
    }
    
    @Override
    public String updateNifiFlow(String processGroupId, Strategy strategy, Reader reader, Writer writer) {
        log.info("Updating NiFi flow for database to file strategy: {}", strategy.getName());
        
        try {
            // 删除旧的流程
            deleteNifiFlow(processGroupId);
            
            // 创建新的流程
            return createNifiFlow(strategy, reader, writer);
        } catch (Exception e) {
            log.error("Error updating NiFi flow for database to file strategy: {}", strategy.getName(), e);
            throw new RuntimeException("Failed to update NiFi flow", e);
        }
    }
    
    @Override
    public void deleteNifiFlow(String processGroupId) {
        log.info("Deleting NiFi flow with process group ID: {}", processGroupId);
        
        try {
            // 停止流程组
            nifiService.stopProcessGroup(processGroupId);
            
            // TODO: 实现删除流程组的逻辑
            // 这需要在NiFiService中添加删除流程组的方法
            
            log.info("Deleted NiFi flow with process group ID: {}", processGroupId);
        } catch (Exception e) {
            log.error("Error deleting NiFi flow with process group ID: {}", processGroupId, e);
            throw new RuntimeException("Failed to delete NiFi flow", e);
        }
    }
    
    /**
     * 生成转换脚本
     */
    private String generateTransformScript(Strategy strategy) {
        StringBuilder scriptBuilder = new StringBuilder();
        
        scriptBuilder.append("import org.apache.nifi.processor.ProcessSession\n");
        scriptBuilder.append("import org.apache.nifi.processor.FlowFile\n\n");
        
        scriptBuilder.append("def flowFile = session.get()\n");
        scriptBuilder.append("if (!flowFile) return\n\n");
        
        scriptBuilder.append("// 转换逻辑\n");
        scriptBuilder.append("flowFile = session.write(flowFile, { inputStream, outputStream ->\n");
        scriptBuilder.append("    def text = inputStream.text\n");
        scriptBuilder.append("    def json = new groovy.json.JsonSlurper().parseText(text)\n\n");
        
        // 添加转换规则
        for (Strategy.ProcessingRule rule : strategy.getRulesJson()) {
            if (rule.getEnabled()) {
                scriptBuilder.append("    // ").append(rule.getName()).append("\n");
                
                switch (rule.getType()) {
                    case "FIELD_MAPPING":
                        appendFieldMappingRule(scriptBuilder, rule);
                        break;
                    case "FIELD_FILTER":
                        appendFieldFilterRule(scriptBuilder, rule);
                        break;
                    case "TYPE_CONVERSION":
                        appendTypeConversionRule(scriptBuilder, rule);
                        break;
                    case "DEFAULT_VALUE":
                        appendDefaultValueRule(scriptBuilder, rule);
                        break;
                    case "SCRIPT_TRANSFORM":
                        appendScriptTransformRule(scriptBuilder, rule);
                        break;
                }
                
                scriptBuilder.append("\n");
            }
        }
        
        scriptBuilder.append("    outputStream.write(new groovy.json.JsonBuilder(json).toString().getBytes('UTF-8'))\n");
        scriptBuilder.append("})\n\n");
        
        scriptBuilder.append("session.transfer(flowFile, REL_SUCCESS)\n");
        
        return scriptBuilder.toString();
    }
    
    /**
     * 添加字段映射规则
     */
    private void appendFieldMappingRule(StringBuilder scriptBuilder, Strategy.ProcessingRule rule) {
        Map<String, Object> parameters = rule.getParameters();
        Map<String, String> fieldMappings = (Map<String, String>) parameters.get("fieldMappings");
        
        for (Map.Entry<String, String> mapping : fieldMappings.entrySet()) {
            String sourceField = mapping.getKey();
            String targetField = mapping.getValue();
            
            scriptBuilder.append("    json['").append(targetField).append("'] = json['").append(sourceField).append("']\n");
        }
    }
    
    /**
     * 添加字段过滤规则
     */
    private void appendFieldFilterRule(StringBuilder scriptBuilder, Strategy.ProcessingRule rule) {
        Map<String, Object> parameters = rule.getParameters();
        boolean isWhitelist = (boolean) parameters.getOrDefault("isWhitelist", true);
        List<String> fields = (List<String>) parameters.get("fields");
        
        if (isWhitelist) {
            // 白名单模式：只保留指定字段
            scriptBuilder.append("    def filteredJson = [:]\n");
            for (String field : fields) {
                scriptBuilder.append("    if (json.containsKey('").append(field).append("')) {\n");
                scriptBuilder.append("        filteredJson['").append(field).append("'] = json['").append(field).append("']\n");
                scriptBuilder.append("    }\n");
            }
            scriptBuilder.append("    json = filteredJson\n");
        } else {
            // 黑名单模式：移除指定字段
            for (String field : fields) {
                scriptBuilder.append("    json.remove('").append(field).append("')\n");
            }
        }
    }
    
    /**
     * 添加数据类型转换规则
     */
    private void appendTypeConversionRule(StringBuilder scriptBuilder, Strategy.ProcessingRule rule) {
        Map<String, Object> parameters = rule.getParameters();
        Map<String, String> typeConversions = (Map<String, String>) parameters.get("typeConversions");
        
        for (Map.Entry<String, String> conversion : typeConversions.entrySet()) {
            String field = conversion.getKey();
            String targetType = conversion.getValue();
            
            scriptBuilder.append("    if (json.containsKey('").append(field).append("')) {\n");
            scriptBuilder.append("        try {\n");
            
            switch (targetType) {
                case "INTEGER":
                    scriptBuilder.append("            json['").append(field).append("'] = json['").append(field).append("'].toString().toInteger()\n");
                    break;
                case "DOUBLE":
                    scriptBuilder.append("            json['").append(field).append("'] = json['").append(field).append("'].toString().toDouble()\n");
                    break;
                case "BOOLEAN":
                    scriptBuilder.append("            json['").append(field).append("'] = json['").append(field).append("'].toString().toBoolean()\n");
                    break;
                case "STRING":
                    scriptBuilder.append("            json['").append(field).append("'] = json['").append(field).append("'].toString()\n");
                    break;
                case "DATE":
                    scriptBuilder.append("            json['").append(field).append("'] = new java.text.SimpleDateFormat('yyyy-MM-dd').parse(json['").append(field).append("'].toString())\n");
                    break;
            }
            
            scriptBuilder.append("        } catch (Exception e) {\n");
            scriptBuilder.append("            log.warn(\"Failed to convert field '").append(field).append("' to ").append(targetType).append(": \" + e.getMessage())\n");
            scriptBuilder.append("        }\n");
            scriptBuilder.append("    }\n");
        }
    }
    
    /**
     * 添加默认值填充规则
     */
    private void appendDefaultValueRule(StringBuilder scriptBuilder, Strategy.ProcessingRule rule) {
        Map<String, Object> parameters = rule.getParameters();
        Map<String, Object> defaultValues = (Map<String, Object>) parameters.get("defaultValues");
        
        for (Map.Entry<String, Object> defaultValue : defaultValues.entrySet()) {
            String field = defaultValue.getKey();
            Object value = defaultValue.getValue();
            
            scriptBuilder.append("    if (!json.containsKey('").append(field).append("') || json['").append(field).append("'] == null) {\n");
            
            if (value instanceof String) {
                scriptBuilder.append("        json['").append(field).append("'] = '").append(value).append("'\n");
            } else {
                scriptBuilder.append("        json['").append(field).append("'] = ").append(value).append("\n");
            }
            
            scriptBuilder.append("    }\n");
        }
    }
    
    /**
     * 添加脚本转换规则
     */
    private void appendScriptTransformRule(StringBuilder scriptBuilder, Strategy.ProcessingRule rule) {
        Map<String, Object> parameters = rule.getParameters();
        String script = (String) parameters.get("script");
        
        // 直接添加用户定义的脚本
        scriptBuilder.append("    ").append(script.replace("\n", "\n    ")).append("\n");
    }
}
