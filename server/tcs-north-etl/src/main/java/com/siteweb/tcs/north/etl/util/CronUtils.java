package com.siteweb.tcs.north.etl.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.support.CronExpression;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Optional;

/**
 * Cron表达式工具类
 * 用于解析Cron表达式并计算下次执行时间
 */
@Slf4j
public class CronUtils {
    
    /**
     * 计算下次执行时间
     * 
     * @param cronExpression Cron表达式
     * @param fromTime 起始时间
     * @return 下次执行时间，如果表达式无效则返回空
     */
    public static Optional<LocalDateTime> getNextExecutionTime(String cronExpression, LocalDateTime fromTime) {
        try {
            if (cronExpression == null || cronExpression.trim().isEmpty()) {
                return Optional.empty();
            }
            
            CronExpression cron = CronExpression.parse(cronExpression);
            ZonedDateTime zonedDateTime = fromTime.atZone(ZoneId.systemDefault());
            ZonedDateTime nextExecution = cron.next(zonedDateTime);
            
            if (nextExecution == null) {
                return Optional.empty();
            }
            
            return Optional.of(nextExecution.toLocalDateTime());
        } catch (IllegalArgumentException e) {
            log.error("Invalid cron expression: {}", cronExpression, e);
            return Optional.empty();
        }
    }
    
    /**
     * 验证Cron表达式是否有效
     * 
     * @param cronExpression Cron表达式
     * @return 是否有效
     */
    public static boolean isValidCronExpression(String cronExpression) {
        try {
            if (cronExpression == null || cronExpression.trim().isEmpty()) {
                return false;
            }
            
            CronExpression.parse(cronExpression);
            return true;
        } catch (IllegalArgumentException e) {
            log.error("Invalid cron expression: {}", cronExpression, e);
            return false;
        }
    }
    
    /**
     * 获取Cron表达式的描述
     * 
     * @param cronExpression Cron表达式
     * @return 描述
     */
    public static String getDescription(String cronExpression) {
        if (!isValidCronExpression(cronExpression)) {
            return "无效的Cron表达式";
        }
        
        // 这里可以实现更详细的Cron表达式描述
        // 例如："每天上午8点执行"、"每周一至周五的下午2点执行"等
        // 这需要解析Cron表达式的各个部分
        
        // 简单实现，返回下次执行时间
        Optional<LocalDateTime> nextTime = getNextExecutionTime(cronExpression, LocalDateTime.now());
        if (nextTime.isPresent()) {
            return "下次执行时间: " + nextTime.get();
        } else {
            return "无法确定下次执行时间";
        }
    }
}
