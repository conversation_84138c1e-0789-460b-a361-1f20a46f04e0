# tcs-middleware-common 模块国际化资源文件
# 命名空间: middleware.common

# 业务错误码
middleware.common.error.biz.resource.name_duplicate=Resource name duplicate
middleware.common.error.biz.resource.type_invalid=Resource type invalid
middleware.common.error.biz.resource.config_invalid=Resource configuration invalid
middleware.common.error.biz.resource.dependency_missing=Resource dependency missing
middleware.common.error.biz.resource.limit_exceeded=Resource limit exceeded

middleware.common.error.biz.service.name_duplicate=Service name duplicate
middleware.common.error.biz.service.type_invalid=Service type invalid
middleware.common.error.biz.service.config_invalid=Service configuration invalid
middleware.common.error.biz.service.dependency_missing=Service dependency missing
middleware.common.error.biz.service.limit_exceeded=Service limit exceeded

middleware.common.error.biz.config.field_missing=Configuration field missing
middleware.common.error.biz.config.field_invalid=Configuration field invalid
middleware.common.error.biz.config.field_type_mismatch=Configuration field type mismatch
middleware.common.error.biz.config.field_value_invalid=Configuration field value invalid

middleware.common.error.biz.operation.not_allowed=Operation not allowed
middleware.common.error.biz.operation.timeout=Operation timeout
middleware.common.error.biz.operation.cancelled=Operation cancelled
middleware.common.error.biz.operation.conflict=Operation conflict

middleware.common.error.biz.permission.denied=Permission denied
middleware.common.error.biz.permission.resource_access_denied=Resource access denied
middleware.common.error.biz.permission.service_access_denied=Service access denied

# 技术错误码
middleware.common.error.tech.resource.not_found=Resource not found
middleware.common.error.tech.resource.already_exists=Resource already exists
middleware.common.error.tech.resource.initialization_failed=Resource initialization failed
middleware.common.error.tech.resource.start_failed=Resource start failed
middleware.common.error.tech.resource.stop_failed=Resource stop failed
middleware.common.error.tech.resource.destroy_failed=Resource destroy failed

middleware.common.error.tech.service.not_found=Service not found
middleware.common.error.tech.service.already_exists=Service already exists
middleware.common.error.tech.service.initialization_failed=Service initialization failed
middleware.common.error.tech.service.start_failed=Service start failed
middleware.common.error.tech.service.stop_failed=Service stop failed
middleware.common.error.tech.service.destroy_failed=Service destroy failed

middleware.common.error.tech.config.invalid=Invalid configuration
middleware.common.error.tech.config.missing=Missing configuration

middleware.common.error.tech.db.connection_failed=Database connection failed
middleware.common.error.tech.db.query_failed=Database query failed
middleware.common.error.tech.db.update_failed=Database update failed

middleware.common.error.tech.cache.connection_failed=Cache connection failed
middleware.common.error.tech.cache.operation_failed=Cache operation failed

middleware.common.error.tech.mq.connection_failed=Message queue connection failed
middleware.common.error.tech.mq.send_failed=Message send failed
middleware.common.error.tech.mq.receive_failed=Message receive failed

middleware.common.error.tech.registry.operation_failed=Registry operation failed
middleware.common.error.tech.lifecycle.operation_failed=Lifecycle operation failed
middleware.common.error.tech.annotation.processing_failed=Annotation processing failed

# KeyValueStore服务相关消息
middleware.common.service.kvstore.channel_empty=Channel name cannot be empty
middleware.common.service.kvstore.message_null=Message content cannot be null
