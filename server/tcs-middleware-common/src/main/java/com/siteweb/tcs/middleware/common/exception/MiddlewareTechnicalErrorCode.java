package com.siteweb.tcs.middleware.common.exception;

import com.siteweb.tcs.common.exception.code.ErrorCode.ErrorCategory;
import com.siteweb.tcs.common.exception.code.TechnicalErrorCode;

/**
 * 中间件技术错误码
 * 实现tcs-common中的TechnicalErrorCode接口
 */
public enum MiddlewareTechnicalErrorCode implements TechnicalErrorCode {

    // 系统错误码
    SYSTEM_ERROR("TECH-SYSTEM-001", "系统错误"),

    // 资源错误码
    RESOURCE_NOT_FOUND("TECH-RESOURCE-001", "资源未找到"),
    RESOURCE_ALREADY_EXISTS("TECH-RESOURCE-002", "资源已存在"),
    RESOURCE_INITIALIZATION_FAILED("TECH-RESOURCE-003", "资源初始化失败"),
    RESOURCE_START_FAILED("TECH-RESOURCE-004", "资源启动失败"),
    RESOURCE_STOP_FAILED("TECH-RESOURCE-005", "资源停止失败"),
    RESOURCE_DESTROY_FAILED("TECH-RESOURCE-006", "资源销毁失败"),
    RESOURCE_DESTRUCTION_FAILED("TECH-RESOURCE-007", "资源销毁失败"),

    // 服务错误码
    SERVICE_NOT_FOUND("TECH-SERVICE-001", "服务未找到"),
    SERVICE_ALREADY_EXISTS("TECH-SERVICE-002", "服务已存在"),
    SERVICE_INITIALIZATION_FAILED("TECH-SERVICE-003", "服务初始化失败"),
    SERVICE_START_FAILED("TECH-SERVICE-004", "服务启动失败"),
    SERVICE_STOP_FAILED("TECH-SERVICE-005", "服务停止失败"),
    SERVICE_DESTROY_FAILED("TECH-SERVICE-006", "服务销毁失败"),
    SERVICE_PROVIDER_NOT_FOUND("TECH-SERVICE-007", "服务提供者未找到"),

    // 配置错误码
    CONFIGURATION_INVALID("TECH-CONFIG-001", "配置无效"),
    CONFIGURATION_MISSING("TECH-CONFIG-002", "配置缺失"),
    RESOURCE_CONFIG_INVALID("TECH-CONFIG-003", "资源配置无效"),

    // 数据库错误码
    DATABASE_CONNECTION_FAILED("TECH-DB-001", "数据库连接失败"),
    DATABASE_QUERY_FAILED("TECH-DB-002", "数据库查询失败"),
    DATABASE_UPDATE_FAILED("TECH-DB-003", "数据库更新失败"),
    DATABASE_OPERATION_FAILED("TECH-DB-004", "数据库操作失败"),
    DATABASE_TRANSACTION_FAILED("TECH-DB-005", "数据库事务失败"),

    // 缓存错误码
    CACHE_CONNECTION_FAILED("TECH-CACHE-001", "缓存连接失败"),
    CACHE_OPERATION_FAILED("TECH-CACHE-002", "缓存操作失败"),

    // 消息队列错误码
    MESSAGE_QUEUE_CONNECTION_FAILED("TECH-MQ-001", "消息队列连接失败"),
    MESSAGE_QUEUE_SEND_FAILED("TECH-MQ-002", "消息发送失败"),
    MESSAGE_QUEUE_RECEIVE_FAILED("TECH-MQ-003", "消息接收失败"),

    // 注册表错误码
    REGISTRY_OPERATION_FAILED("TECH-REGISTRY-001", "注册表操作失败"),

    // 生命周期错误码
    LIFECYCLE_OPERATION_FAILED("TECH-LIFECYCLE-001", "生命周期操作失败"),

    // 注解错误码
    ANNOTATION_PROCESSING_FAILED("TECH-ANNOTATION-001", "注解处理失败");

    private final String code;
    private final String message;

    MiddlewareTechnicalErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    @Override
    public ErrorCategory getCategory() {
        return ErrorCategory.TECHNICAL;
    }
}
