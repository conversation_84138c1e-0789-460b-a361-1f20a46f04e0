package com.siteweb.tcs.middleware.common.service;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.middleware.common.service.sitewebpersistent.config.SitewebBizConfig;
import com.siteweb.tcs.middleware.common.service.sitewebpersistent.config.SitewebMyBatisConfig;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.model.HealthStatus;
import com.siteweb.tcs.middleware.common.resource.Resource;
import com.siteweb.tcs.middleware.common.service.sitewebpersistent.BInterfaceAPI;
import com.siteweb.tcs.middleware.common.service.sitewebpersistent.ConfigAPI;
import com.siteweb.tcs.middleware.common.service.sitewebpersistent.HistoryAPI;
import com.siteweb.tcs.middleware.common.service.sitewebpersistent.QueryAPI;
import com.siteweb.tcs.middleware.common.service.sitewebpersistent.config.SitewebContextTransactionConfig;
import org.springframework.transaction.annotation.Transactional;
import com.siteweb.tcs.siteweb.entity.*;
import com.siteweb.tcs.siteweb.mapper.HouseMapper;
import com.siteweb.tcs.siteweb.mapper.SamplerMapper;
import com.siteweb.tcs.siteweb.mapper.DoorMapper;
import com.siteweb.tcs.siteweb.util.TableExistenceChecker;
import lombok.Getter;
import lombok.Setter;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.mapper.ClassPathMapperScanner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.core.ResolvableType;

import javax.sql.DataSource;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Siteweb持久化服务
 * 用于封装tcs-siteweb模块中的各种service，提供统一的访问接口
 * 此服务关联关系型数据库Resource，使用Resource的DataSource创建独立的MyBatis配置
 */
public class SitewebPersistentService extends BaseService {

    private static final Logger logger = LoggerFactory.getLogger(SitewebPersistentService.class);

    /**
     * Siteweb包路径标识，用于筛选相关的IService实现
     */
    private static final String SITEWEB_PACKAGE_IDENTIFIER = "tcs.siteweb";

    private ApplicationContext applicationContext;
    /**
     * 独立数据源
     */
    private AnnotationConfigApplicationContext sitewebContext = new AnnotationConfigApplicationContext();

    /**
     * 独立的SqlSessionFactory，使用关联Resource的DataSource
     */
    private SqlSessionFactory sqlSessionFactory;

    /**
     * 独立的SqlSessionTemplate
     */
    private SqlSessionTemplate sqlSessionTemplate;

    /**
     * 缓存实体类型到对应IService的映射
     * Key: 实体类的Class对象
     * Value: 对应的IService实例
     */
    private final Map<Class<?>, IService<?>> serviceCache = new ConcurrentHashMap<>();

    /**
     * 缓存服务名称到IService的映射
     * Key: 服务名称（如"houseServiceImpl"）
     * Value: 对应的IService实例
     */
    private final Map<String, Object> namedServiceCache = new ConcurrentHashMap<>();

    /**
     * 构造函数
     *
     * @param id 服务ID
     * @param name 服务名称
     * @param description 服务描述
     * @param resource 关联的数据库Resource
     */
    public SitewebPersistentService(String id, String name, String description, Resource resource) {
        super(id, ServiceType.SITEWEB_PERSISTENT.getCode(), name, description, resource);
    }

    /**
     * 设置Spring应用上下文
     *
     * @param applicationContext Spring应用上下文
     */
    @Autowired
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

   @Getter
   @Setter
    private ConfigAPI configAPI;

    @Getter
    @Setter
    private BInterfaceAPI bInterfaceAPI;


    @Getter
    @Setter
    private QueryAPI queryAPI;

    @Getter
    @Setter
    private HistoryAPI historyAPI;


    public void newInitAPI(){
        if (configAPI != null && sitewebContext != null) {
            configAPI.initAPI(sitewebContext);
        } else if (configAPI != null) {
            // 如果克隆上下文创建失败，回退到SqlSessionTemplate方式
            logger.warn("Cloned context not available, using fallback mode for ConfigAPI");
            // 这里可以添加回退逻辑
        }
    }

    /**
     * 获取tcs-siteweb中的服务实例（内部使用）
     *
     * @param serviceClass 服务类型
     * @param <T> 服务类型泛型
     * @return 服务实例
     * @throws MiddlewareTechnicalException 获取服务失败时抛出异常
     */
    private <T> T getSitewebService(Class<T> serviceClass) throws MiddlewareTechnicalException {
        try {
            if (applicationContext == null) {
                throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                    "ApplicationContext not initialized"
                );
            }
            return applicationContext.getBean(serviceClass);
        } catch (Exception e) {
            logger.error("Failed to get siteweb service: {}", serviceClass.getName(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "Failed to get siteweb service: " + serviceClass.getName(),
                e
            );
        }
    }

    /**
     * 根据服务名称获取tcs-siteweb中的服务实例（内部使用）
     *
     * @param serviceName 服务名称
     * @param serviceClass 服务类型
     * @param <T> 服务类型泛型
     * @return 服务实例
     * @throws MiddlewareTechnicalException 获取服务失败时抛出异常
     */
    private <T> T getSitewebService(String serviceName, Class<T> serviceClass) throws MiddlewareTechnicalException {
        try {
            if (applicationContext == null) {
                throw new MiddlewareTechnicalException(
                        MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                        "ApplicationContext not initialized"
                );
            }
            return applicationContext.getBean(serviceName, serviceClass);
        } catch (Exception e) {
            logger.error("Failed to get siteweb service by name: {} ({})", serviceName, serviceClass.getName(), e);
            throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                    "Failed to get siteweb service by name: " + serviceName,
                    e
            );
        }
    }

    /**
     * 根据实体类型获取对应的IService实例
     *
     * @param entityClass 实体类型
     * @param <T> 实体类型泛型
     * @param <S> IService类型泛型
     * @return 对应的IService实例
     * @throws MiddlewareTechnicalException 获取失败时抛出异常
     */
    @SuppressWarnings("unchecked")
    private <T, S extends IService<T>> S getServiceByEntityType(Class<T> entityClass) throws MiddlewareTechnicalException {
        // 尝试从缓存获取，避免重复查找
        IService<?> service = serviceCache.get(entityClass);
        if (service != null) {
            return (S) service;
        }

        try {
            if (applicationContext == null) {
                throw new MiddlewareTechnicalException(
                        MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                        "ApplicationContext not initialized"
                );
            }

            // 遍历 Spring 容器中所有 IService 类型的 Bean
            Map<String, IService> allServices = applicationContext.getBeansOfType(IService.class);
            logger.debug("Found {} IService beans in Spring context, looking for entity type: {}",
                    allServices.size(), entityClass.getSimpleName());

            for (IService<?> iService : allServices.values()) {
                // 筛选包路径：只处理tcs.siteweb包下的IService实现
                String serviceClassName = iService.getClass().getName();
                if (!serviceClassName.contains(SITEWEB_PACKAGE_IDENTIFIER)) {
                    logger.trace("Skipping non-siteweb service: {}", serviceClassName);
                    continue;
                }

                // 使用 ResolvableType 获取 IService 的实际泛型参数
                ResolvableType resolvableType = ResolvableType.forClass(iService.getClass()).as(IService.class);

                // 检查是否存在泛型参数，并获取第一个泛型参数的类型
                if (resolvableType.hasGenerics()) {
                    Class<?> genericType = resolvableType.getGeneric(0).resolve();

                    // 如果泛型参数类型与传入的 entityClass 匹配，则找到了对应的 Service
                    if (genericType != null && genericType.equals(entityClass)) {
                        // 缓存找到的 Service 实例，提高后续访问性能
                        serviceCache.put(entityClass, iService);
                        logger.debug("Found and cached siteweb service for entity {}: {} ({})",
                                entityClass.getSimpleName(), iService.getClass().getSimpleName(), genericType.getSimpleName());
                        return (S) iService;
                    }
                } else {
                    logger.trace("Service {} has no generics, skipping", serviceClassName);
                }
            }

            // 如果遍历完所有 IService 仍未找到匹配的，则抛出异常
            // 收集所有siteweb服务的信息用于调试
            StringBuilder sitewebServices = new StringBuilder();
            for (IService<?> iService : allServices.values()) {
                String serviceClassName = iService.getClass().getName();
                if (serviceClassName.contains(SITEWEB_PACKAGE_IDENTIFIER)) {
                    ResolvableType resolvableType = ResolvableType.forClass(iService.getClass()).as(IService.class);
                    if (resolvableType.hasGenerics()) {
                        Class<?> genericType = resolvableType.getGeneric(0).resolve();
                        sitewebServices.append(String.format("%s<%s>, ",
                                iService.getClass().getSimpleName(),
                                genericType != null ? genericType.getSimpleName() : "?"));
                    }
                }
            }

            String errorMessage = String.format(
                    "No IService found for entity type: %s. Available siteweb services: [%s]",
                    entityClass.getName(),
                    sitewebServices.length() > 0 ? sitewebServices.toString() : "none"
            );

            throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                    errorMessage
            );

        } catch (Exception e) {
            logger.error("Failed to get service for entity type: {}", entityClass.getName(), e);
            throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                    "Failed to get service for entity type: " + entityClass.getName(),
                    e
            );
        }
    }

    // =====================================================
    // 通用IService方法
    // =====================================================

    /**
     * 根据ID查询实体
     *
     * @param entityClass 实体类型
     * @param id 实体ID
     * @param <T> 实体类型泛型
     * @return 实体对象，如果不存在则返回null
     * @throws MiddlewareTechnicalException 查询失败时抛出异常
     */
    public <T> T getById(Class<T> entityClass, Serializable id) throws MiddlewareTechnicalException {
        try {
            if (entityClass == null || id == null) {
                logger.warn("Entity class or ID cannot be null");
                return null;
            }

            // 检查SqlSessionTemplate是否可用
            if (sqlSessionTemplate == null) {
                throw new MiddlewareTechnicalException(
                        MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                        "SqlSessionTemplate not initialized for service: " + getId()
                );
            }

            // 获取对应的Mapper并使用MyBatis-Plus的selectById方法
            BaseMapper<T> mapper = getMapperForEntity(entityClass);
            T result = mapper.selectById(id);

            logger.debug("Found entity by ID {} using independent SqlSession: {}", id, result != null ? "found" : "not found");
            return result;

        } catch (Exception e) {
            logger.error("Failed to get {} by ID: {}", entityClass.getSimpleName(), id, e);
            throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                    "Failed to get " + entityClass.getSimpleName() + " by ID: " + id,
                    e
            );
        }
    }

    /**
     * 查询所有实体
     *
     * @param entityClass 实体类型
     * @param <T> 实体类型泛型
     * @return 实体列表
     * @throws MiddlewareTechnicalException 查询失败时抛出异常
     */
    public <T> List<T> list(Class<T> entityClass) throws MiddlewareTechnicalException {
        try {
            if (entityClass == null) {
                logger.warn("Entity class cannot be null");
                return List.of();
            }

            // 检查SqlSessionTemplate是否可用
            if (sqlSessionTemplate == null) {
                throw new MiddlewareTechnicalException(
                        MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                        "SqlSessionTemplate not initialized for service: " + getId()
                );
            }

            // 获取对应的Mapper并使用MyBatis-Plus的selectList方法
            BaseMapper<T> mapper = getMapperForEntity(entityClass);
            List<T> result = mapper.selectList(null); // null表示查询所有

            logger.debug("Found {} entities of type {} using independent SqlSession", result != null ? result.size() : 0, entityClass.getSimpleName());
            return result != null ? result : List.of();

        } catch (Exception e) {
            logger.error("Failed to list entities of type: {}", entityClass.getSimpleName(), e);
            throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                    "Failed to list entities of type: " + entityClass.getSimpleName(),
                    e
            );
        }
    }

    /**
     * 保存实体
     *
     * @param entity 实体对象
     * @param <T> 实体类型泛型
     * @return 是否保存成功
     * @throws MiddlewareTechnicalException 保存失败时抛出异常
     */
    @Transactional(rollbackFor = Exception.class)
    public <T> boolean save(T entity) throws MiddlewareTechnicalException {
        try {
            if (entity == null) {
                logger.warn("Entity cannot be null");
                return false;
            }

            @SuppressWarnings("unchecked")
            Class<T> entityClass = (Class<T>) entity.getClass();
            IService<T> service = getServiceByEntityType(entityClass);

            // 直接调用save方法，无需反射
            boolean result = service.save(entity);

            logger.debug("Save entity result: {}", result);
            return result;

        } catch (Exception e) {
            logger.error("Failed to save entity: {}", entity.getClass().getSimpleName(), e);
            throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                    "Failed to save entity: " + entity.getClass().getSimpleName(),
                    e
            );
        }
    }

    /**
     * 根据ID删除实体
     *
     * @param entityClass 实体类型
     * @param id 实体ID
     * @param <T> 实体类型泛型
     * @return 是否删除成功
     * @throws MiddlewareTechnicalException 删除失败时抛出异常
     */
    @Transactional(rollbackFor = Exception.class)
    public <T> boolean removeById(Class<T> entityClass, Serializable id) throws MiddlewareTechnicalException {
        try {
            if (entityClass == null || id == null) {
                logger.warn("Entity class or ID cannot be null");
                return false;
            }

            IService<T> service = getServiceByEntityType(entityClass);

            // 直接调用removeById方法，无需反射
            boolean result = service.removeById(id);

            logger.debug("Remove entity by ID {} result: {}", id, result);
            return result;

        } catch (Exception e) {
            logger.error("Failed to remove {} by ID: {}", entityClass.getSimpleName(), id, e);
            throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                    "Failed to remove " + entityClass.getSimpleName() + " by ID: " + id,
                    e
            );
        }
    }

    /**
     * 判断资源类型是否支持
     *
     * @param resourceType 资源类型
     * @return 是否支持
     */
    private boolean isSupportedResourceType(String resourceType) {
        // 支持的关系型数据库资源类型
        return "MYSQL".equalsIgnoreCase(resourceType) ||
                "H2".equalsIgnoreCase(resourceType) ||
                "POSTGRESQL".equalsIgnoreCase(resourceType);
    }

    /**
     * 获取实体对应的Mapper
     *
     * @param entityClass 实体类
     * @param <T> 实体类型
     * @return BaseMapper实例
     * @throws MiddlewareTechnicalException 如果找不到对应的Mapper
     */
    @SuppressWarnings("unchecked")
    private <T> BaseMapper<T> getMapperForEntity(Class<T> entityClass) throws MiddlewareTechnicalException {
        try {
            // 根据实体类型获取对应的Mapper
            if (House.class.equals(entityClass)) {
                return (BaseMapper<T>) sqlSessionTemplate.getMapper(HouseMapper.class);
            } else if (Sampler.class.equals(entityClass)) {
                return (BaseMapper<T>) sqlSessionTemplate.getMapper(SamplerMapper.class);
            } else if (Door.class.equals(entityClass)) {
                return (BaseMapper<T>) sqlSessionTemplate.getMapper(DoorMapper.class);
            } else {
                throw new MiddlewareTechnicalException(
                        MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                        "Unsupported entity type: " + entityClass.getName()
                );
            }
        } catch (Exception e) {
            logger.error("Failed to get mapper for entity: {}", entityClass.getName(), e);
            throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                    "Failed to get mapper for entity: " + entityClass.getName(),
                    e
            );
        }
    }

    @Override
    protected void doInitialize() throws MiddlewareTechnicalException {
        logger.info("Initializing SitewebPersistentService: {}", getId());

        // 验证关联的Resource
        Resource resource = getResource();
        if (resource == null) {
            throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                    "SitewebPersistentService requires a database resource: " + getId()
            );
        }

        // 验证Resource类型
        String resourceType = resource.getType();
        if (!isSupportedResourceType(resourceType)) {
            throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                    "Unsupported resource type for SitewebPersistentService: " + resourceType + ", service: " + getId()
            );
        }

        logger.info("SitewebPersistentService initialized with resource: {} ({})", resource.getId(), resourceType);
    }


    @Override
    protected void doStart() throws MiddlewareTechnicalException {
        logger.info("Starting SitewebPersistentService: {}", getId());

        try {
            // 获取关联的Resource
            Resource resource = getResource();

            // 获取DataSource
            DataSource dataSource = resource.getNativeResource();

            // 验证DataSource
            SitewebMyBatisConfig.validateDataSource(dataSource, getId());

            // 创建独立的SqlSessionFactory
            this.sqlSessionFactory = SitewebMyBatisConfig.createSqlSessionFactory(dataSource, getId());

            // 创建SqlSessionTemplate
            this.sqlSessionTemplate = SitewebMyBatisConfig.createSqlSessionTemplate(sqlSessionFactory, getId());

            sitewebContext.setParent(applicationContext);
            TableExistenceChecker bean = applicationContext.getBean(TableExistenceChecker.class);
            bean.setDataSource(dataSource);

            // 注册独立上下文需要的Bean
            sitewebContext.getBeanFactory().registerSingleton("sitewebDataSource", dataSource);
            sitewebContext.getBeanFactory().registerSingleton("sitewebSqlSessionFactory", sqlSessionFactory);
            sitewebContext.getBeanFactory().registerSingleton("sitewebSqlSessionTemplate", sqlSessionTemplate);
            sitewebContext.getBeanFactory().destroyBean(TableExistenceChecker.class);
            sitewebContext.getBeanFactory().registerSingleton("tableExistenceChecker", bean);

            logger.info("Registered beans in siteweb context: sitewebDataSource, sitewebSqlSessionFactory, sitewebSqlSessionTemplate");

            ClassPathMapperScanner scanner = new ClassPathMapperScanner(sitewebContext);
            scanner.setSqlSessionFactory(sqlSessionFactory); // 重点：绑定你动态构建的SqlSessionFactory
            scanner.setAnnotationClass(Mapper.class); // 只扫描有 @Mapper 注解的接口
            scanner.registerFilters();
            scanner.scan("com.siteweb.tcs.siteweb.mapper");

            // 注册业务配置和事务配置
            sitewebContext.register(SitewebBizConfig.class);
            sitewebContext.register(SitewebContextTransactionConfig.class);

//            sitewebContext.scan("com.siteweb.tcs.siteweb");
            sitewebContext.refresh();
            newInitAPI();

            // 创建克隆上下文并初始化子API
//            initAPI();

            logger.info("SitewebPersistentService started successfully: {}", getId());

        } catch (Exception e) {
            logger.error("Failed to start SitewebPersistentService: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SERVICE_START_FAILED,
                    "Failed to start SitewebPersistentService: " + getId() + ", error: " + e.getMessage(),
                    e
            );
        }
    }

    @Override
    protected void doStop() throws MiddlewareTechnicalException {
        logger.info("Stopping SitewebPersistentService: {}", getId());
        // MyBatis资源会随着DataSource的关闭而自动清理
    }

    @Override
    protected void doDestroy() throws MiddlewareTechnicalException {
        logger.info("Destroying SitewebPersistentService: {}", getId());

        // 清理克隆的Spring上下文
        if (sitewebContext != null) {
            try {
                sitewebContext.close();
                logger.info("Cloned Spring context closed successfully");
            } catch (Exception e) {
                logger.warn("Failed to close cloned Spring context", e);
            }
            this.sitewebContext = null;
        }

        // 清理MyBatis资源
        this.sqlSessionTemplate = null;
        this.sqlSessionFactory = null;

        // 清理缓存
        this.serviceCache.clear();
        this.namedServiceCache.clear();

        // 清理Spring上下文引用
        this.applicationContext = null;

        logger.info("SitewebPersistentService destroyed: {}", getId());
    }

    @Override
    public HealthStatus checkHealth() {
        try {
            // 检查ApplicationContext是否可用
            if (applicationContext == null) {
                return HealthStatus.down("ApplicationContext not available");
            }

            // 检查关联的Resource
            Resource resource = getResource();
            if (resource == null) {
                return HealthStatus.down("Database resource not available");
            }

            // 检查Resource健康状态
            HealthStatus resourceHealth = resource.checkHealth();
            if (!resourceHealth.isUp()) {
                return HealthStatus.down("Database resource unhealthy: " + resourceHealth.getMessage());
            }

            // 检查SqlSessionFactory
            if (sqlSessionFactory == null) {
                return HealthStatus.down("SqlSessionFactory not initialized");
            }

            // 检查SqlSessionTemplate
            if (sqlSessionTemplate == null) {
                return HealthStatus.down("SqlSessionTemplate not initialized");
            }

            return HealthStatus.up("SitewebPersistentService is healthy");
        } catch (Exception e) {
            logger.error("Health check failed for SitewebPersistentService: {}", getId(), e);
            return HealthStatus.down("Health check failed: " + e.getMessage());
        }
    }
}
