package com.siteweb.tcs.middleware.common.model.config;

import lombok.Data;

/**
 * HTTP服务器配置
 */
@Data
public class HttpServerConfig {

    /**
     * 主机地址
     */
    private String host = "0.0.0.0";

    /**
     * 端口
     */
    private int port = 8080;

    /**
     * 空闲超时时间（秒）
     */
    private int idleTimeout = 60;

    /**
     * 连接队列大小
     */
    private int backlog = 100;

    /**
     * 是否启用HTTPS
     */
    private boolean enableHttps = false;

    /**
     * HTTPS证书路径
     */
    private String certPath;

    /**
     * HTTPS私钥路径
     */
    private String keyPath;

    /**
     * HTTPS私钥密码
     */
    private String keyPassword;

    /**
     * 是否启用压缩
     */
    private boolean enableCompression = true;

    /**
     * 最大请求大小（字节）
     */
    private int maxRequestSize = 1048576; // 1MB

    /**
     * 是否启用CORS
     */
    private boolean enableCors = true;

    /**
     * CORS允许的源
     */
    private String corsAllowedOrigins = "*";

    /**
     * CORS允许的方法
     */
    private String corsAllowedMethods = "GET, POST, PUT, DELETE, OPTIONS";

    /**
     * CORS允许的头
     */
    private String corsAllowedHeaders = "Origin, X-Requested-With, Content-Type, Accept, Authorization";

    /**
     * CORS最大缓存时间（秒）
     */
    private int corsMaxAge = 3600;
}
