package com.siteweb.tcs.middleware.common.resource;

import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.model.HealthStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * Redis资源
 */
public class RedisResource extends BaseResource {

    private static final Logger logger = LoggerFactory.getLogger(RedisResource.class);

    private RedisConnectionFactory connectionFactory;
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 构造函数
     *
     * @param id          资源ID
     * @param type        资源类型
     * @param name        资源名称
     * @param description 资源描述
     * @param connectionFactory Redis连接工厂
     * @param redisTemplate     Redis模板
     */
    public RedisResource(String id, String type, String name, String description, RedisConnectionFactory connectionFactory, RedisTemplate<String, Object> redisTemplate) {
        super(id, type, name, description);
        this.connectionFactory = connectionFactory;
        this.redisTemplate = redisTemplate;
    }

    @Override
    protected void doInitialize() throws MiddlewareTechnicalException {
        logger.debug("初始化Redis资源: {}", getId());
        // 初始化阶段不创建客户端，只在启动时创建
    }

    @Override
    protected void doStart() throws MiddlewareTechnicalException {
        logger.debug("启动Redis资源: {}", getId());
        try {
            // 测试连接
            if (redisTemplate == null) {
                throw new MiddlewareBusinessException(
                    MiddlewareBusinessErrorCode.RESOURCE_CONFIG_INVALID,
                    "Redis模板未初始化"
                );
            }

            // 测试连接
            Boolean result = redisTemplate.execute((RedisCallback<Boolean>) connection -> {
                try {
                    return connection.ping() != null;
                } catch (Exception e) {
                    logger.error("测试Redis连接失败", e);
                    return false;
                }
            });

            if (!Boolean.TRUE.equals(result)) {
                throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.DATABASE_CONNECTION_FAILED,
                    "Redis连接测试失败"
                );
            }

            logger.info("Redis资源启动成功: {}", getId());
        } catch (Exception e) {
            logger.error("启动Redis资源失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_START_FAILED,
                "启动Redis资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    protected void doStop() throws MiddlewareTechnicalException {
        logger.debug("停止Redis资源: {}", getId());
        try {
            // 清空引用，允许GC回收
            redisTemplate = null;
            connectionFactory = null;
            logger.info("Redis资源停止成功: {}", getId());
        } catch (Exception e) {
            logger.error("停止Redis资源失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_STOP_FAILED,
                "停止Redis资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    protected void doDestroy() throws MiddlewareTechnicalException {
        logger.debug("销毁Redis资源: {}", getId());
        try {
            // 清空引用，允许GC回收
            redisTemplate = null;
            connectionFactory = null;
            logger.info("Redis资源销毁成功: {}", getId());
        } catch (Exception e) {
            logger.error("销毁Redis资源失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_DESTROY_FAILED,
                "销毁Redis资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public HealthStatus checkHealth() {
        if (redisTemplate == null) {
            return HealthStatus.down("Redis模板未初始化");
        }

        try {
            // 测试连接
            Boolean result = redisTemplate.execute((RedisCallback<Boolean>) connection -> {
                try {
                    return connection.ping() != null;
                } catch (Exception e) {
                    logger.error("测试Redis连接失败", e);
                    return false;
                }
            });

            if (!Boolean.TRUE.equals(result)) {
                return HealthStatus.down("Redis连接失败");
            }

            // 获取Redis服务器信息
            String info = redisTemplate.execute((RedisCallback<String>) connection -> {
                try {
                    Properties infoProps = connection.serverCommands().info();
                    return infoProps != null ? infoProps.toString() : "";
                } catch (Exception e) {
                    logger.error("获取Redis服务器信息失败", e);
                    return "";
                }
            });

            Map<String, Object> details = new HashMap<>();
            details.put("serverInfo", info);

            // 解析一些关键指标
            Map<String, String> infoMap = parseRedisInfo(info);
            details.put("version", infoMap.getOrDefault("redis_version", "unknown"));
            details.put("uptime", infoMap.getOrDefault("uptime_in_seconds", "0"));
            details.put("connectedClients", infoMap.getOrDefault("connected_clients", "0"));
            details.put("usedMemory", infoMap.getOrDefault("used_memory_human", "0"));

            // 获取键总数
            Long dbSize = redisTemplate.execute((RedisCallback<Long>) connection -> {
                try {
                    return connection.serverCommands().dbSize();
                } catch (Exception e) {
                    logger.error("获取Redis键总数失败", e);
                    return -1L;
                }
            });
            details.put("totalKeys", dbSize);

            return HealthStatus.up("连接正常", details);
        } catch (Exception e) {
            logger.error("检查Redis资源健康状态失败: {}", getId(), e);
            return HealthStatus.down("连接失败: " + e.getMessage());
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> T getNativeResource() {
        if (redisTemplate == null) {
            throw new IllegalStateException("Redis资源未启动");
        }
        return (T) redisTemplate;
    }

    /**
     * 获取Redis模板
     *
     * @return Redis模板
     */
    public RedisTemplate<String, Object> getRedisTemplate() {
        if (redisTemplate == null) {
            throw new IllegalStateException("Redis资源未启动");
        }
        return redisTemplate;
    }

    /**
     * 解析Redis INFO命令返回的信息
     *
     * @param info INFO命令返回的信息
     * @return 解析后的键值对
     */
    private Map<String, String> parseRedisInfo(String info) {
        Map<String, String> result = new HashMap<>();
        if (info == null || info.isEmpty()) {
            return result;
        }

        String[] lines = info.split("\r\n");
        for (String line : lines) {
            if (line.isEmpty() || line.startsWith("#")) {
                continue;
            }

            String[] parts = line.split(":");
            if (parts.length >= 2) {
                result.put(parts[0], parts[1]);
            }
        }

        return result;
    }

    /**
     * 执行原生Redis操作
     *
     * @param callback 回调函数
     * @param <T> 返回类型
     * @return 操作结果
     */
    public <T> T executeNative(Function<RedisConnection, T> callback) {
        if (redisTemplate == null) {
            throw new IllegalStateException("Redis资源未启动");
        }

        return redisTemplate.execute((RedisCallback<T>) callback::apply);
    }

    /**
     * 设置键值
     *
     * @param key 键
     * @param value 值
     */
    public void set(String key, String value) {
        redisTemplate.opsForValue().set(key, (Object)value);
    }

    /**
     * 设置键值并设置过期时间
     *
     * @param key 键
     * @param value 值
     * @param seconds 过期时间（秒）
     */
    public void setex(String key, String value, long seconds) {
        redisTemplate.opsForValue().set(key, (Object)value, seconds, TimeUnit.SECONDS);
    }

    /**
     * 获取键值
     *
     * @param key 键
     * @return 值
     */
    public String get(String key) {
        Object value = redisTemplate.opsForValue().get(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 删除键
     *
     * @param key 键
     * @return 删除的键数量
     */
    public Boolean del(String key) {
        return redisTemplate.delete(key);
    }

    /**
     * 检查键是否存在
     *
     * @param key 键
     * @return 是否存在
     */
    public Boolean exists(String key) {
        return redisTemplate.hasKey(key);
    }

    /**
     * 设置键的过期时间
     *
     * @param key 键
     * @param seconds 过期时间（秒）
     * @return 操作结果
     */
    public Boolean expire(String key, long seconds) {
        return redisTemplate.expire(key, seconds, TimeUnit.SECONDS);
    }

    /**
     * 获取键的剩余过期时间
     *
     * @param key 键
     * @return 剩余过期时间（秒）
     */
    public Long ttl(String key) {
        return redisTemplate.getExpire(key, TimeUnit.SECONDS);
    }

    /**
     * 获取所有匹配的键
     *
     * @param pattern 匹配模式
     * @return 匹配的键集合
     */
    public Set<String> keys(String pattern) {
        return redisTemplate.keys(pattern);
    }

    /**
     * 获取数据库大小
     *
     * @return 数据库大小
     */
    public Long dbSize() {
        return redisTemplate.execute(connection -> connection.serverCommands().dbSize(), true);
    }

    /**
     * 获取列表长度
     *
     * @param key 键
     * @return 列表长度
     */
    public Long llen(String key) {
        return redisTemplate.opsForList().size(key);
    }

    /**
     * 获取列表指定范围的元素
     *
     * @param key 键
     * @param start 开始索引
     * @param end 结束索引
     * @return 元素列表
     */
    public List<String> lrange(String key, long start, long end) {
        List<Object> values = redisTemplate.opsForList().range(key, start, end);
        if (values == null) {
            return null;
        }
        return values.stream()
                .map(obj -> obj != null ? obj.toString() : null)
                .toList();
    }

    /**
     * 获取集合所有成员
     *
     * @param key 键
     * @return 成员集合
     */
    public Set<String> smembers(String key) {
        Set<Object> members = redisTemplate.opsForSet().members(key);
        if (members == null) {
            return null;
        }
        return members.stream()
                .map(obj -> obj != null ? obj.toString() : null)
                .collect(java.util.stream.Collectors.toSet());
    }

    /**
     * 获取哈希表所有字段和值
     *
     * @param key 键
     * @return 字段和值的映射
     */
    public Map<Object, Object> hgetAll(String key) {
        return redisTemplate.opsForHash().entries(key);
    }

    /**
     * 获取有序集合指定范围的成员
     *
     * @param key 键
     * @param start 开始索引
     * @param end 结束索引
     * @return 成员列表
     */
    public Set<String> zrange(String key, long start, long end) {
        Set<Object> range = redisTemplate.opsForZSet().range(key, start, end);
        if (range == null) {
            return null;
        }
        return range.stream()
                .map(obj -> obj != null ? obj.toString() : null)
                .collect(java.util.stream.Collectors.toSet());
    }

    /**
     * 获取有序集合的成员数量
     *
     * @param key 键
     * @return 成员数量
     */
    public Long zcard(String key) {
        return redisTemplate.opsForZSet().size(key);
    }

    /**
     * 获取连接池统计信息
     *
     * @return 连接池统计信息
     */
    public Map<String, Object> getPoolStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("connectionFactory", connectionFactory.getClass().getName());
        return stats;
    }

    /**
     * 获取所有键的值
     *
     * @param pattern 键的模式
     * @return 键值对的Map
     */
    public Map<String, String> getAll(String pattern) {
        Set<String> keys = redisTemplate.keys(pattern);
        Map<String, String> result = new HashMap<>();

        if (keys != null && !keys.isEmpty()) {
            for (String key : keys) {
                Object value = redisTemplate.opsForValue().get(key);
                result.put(key, value != null ? value.toString() : null);
            }
        }

        return result;
    }

    /**
     * 批量设置键值
     *
     * @param keyValues 键值对的Map
     */
    public void mset(Map<String, String> keyValues) {
        Map<String, Object> objectMap = new HashMap<>();
        keyValues.forEach((key, value) -> objectMap.put(key, value));
        redisTemplate.opsForValue().multiSet(objectMap);
    }

    /**
     * 批量获取键值
     *
     * @param keys 键的数组
     * @return 值的列表
     */
    public List<String> mget(String... keys) {
        List<Object> values = redisTemplate.opsForValue().multiGet(Arrays.asList(keys));
        if (values == null) {
            return null;
        }
        return values.stream()
                .map(obj -> obj != null ? obj.toString() : null)
                .toList();
    }

    /**
     * 将值添加到列表
     *
     * @param key 列表的键
     * @param values 要添加的值
     * @return 操作后列表的长度
     */
    public Long rpush(String key, String... values) {
        // 将String[]转换为Object[]
        Object[] objectValues = Arrays.stream(values).toArray();
        return redisTemplate.opsForList().rightPushAll(key, objectValues);
    }

    /**
     * 将值添加到集合
     *
     * @param key 集合的键
     * @param members 要添加的成员
     * @return 添加的成员数量
     */
    public Long sadd(String key, String... members) {
        // 将String[]转换为Object[]
        Object[] objectMembers = Arrays.stream(members).toArray();
        return redisTemplate.opsForSet().add(key, objectMembers);
    }

    /**
     * 将键值对添加到哈希表
     *
     * @param key 哈希表的键
     * @param hash 要添加的键值对
     */
    public void hset(String key, Map<String, String> hash) {
        Map<Object, Object> objectMap = new HashMap<>();
        hash.forEach((k, v) -> objectMap.put(k, v));
        redisTemplate.opsForHash().putAll(key, objectMap);
    }

    /**
     * 执行Lua脚本
     *
     * @param script Lua脚本
     * @param keys 脚本中使用的键
     * @param args 脚本中使用的参数
     * @return 脚本执行结果
     */
    public Object eval(String script, List<String> keys, List<String> args) {
        return executeNative((connection) -> {
            byte[] scriptBytes = script.getBytes();

            // 将keys和args合并为一个数组
            byte[][] keysAndArgs = new byte[keys.size() + args.size()][];

            // 添加keys
            for (int i = 0; i < keys.size(); i++) {
                keysAndArgs[i] = keys.get(i).getBytes();
            }

            // 添加args
            for (int i = 0; i < args.size(); i++) {
                keysAndArgs[keys.size() + i] = args.get(i).getBytes();
            }

            // 使用eval方法
            return connection.scriptingCommands().eval(scriptBytes, org.springframework.data.redis.connection.ReturnType.MULTI, keys.size(), keysAndArgs);
        });
    }
}
