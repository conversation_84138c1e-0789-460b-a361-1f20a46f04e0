package com.siteweb.tcs.middleware.common.service.sitewebpersistent.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.common.util.StringUtils;
import com.siteweb.tcs.middleware.common.service.sitewebpersistent.BInterfaceAPI;
import com.siteweb.tcs.siteweb.dto.ControlConfigItem;
import com.siteweb.tcs.siteweb.dto.CreateEquipmentDto;
import com.siteweb.tcs.siteweb.dto.EquipmentDetailDTO;
import com.siteweb.tcs.siteweb.entity.*;
import com.siteweb.tcs.siteweb.provider.*;
import com.siteweb.tcs.siteweb.vo.EquipmentTemplateVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: hub中platform equipmenttemplate
 */
@Service
@Slf4j
public class BInterfaceAPIImpl implements BInterfaceAPI {
    @Autowired
    private EquipmentTemplateProvider equipmentTemplateProvider;
    @Autowired
    private ResourceStructureProvider resourceStructureProvider;
    @Autowired
    private SamplerUnitProvider samplerUnitProvider;
    @Autowired
    private SamplerProvider samplerProvider;
    @Autowired
    private PortProvider portProvider;
    @Autowired
    private EquipmentProvider equipmentProvider;

    private Integer resourceStructureRootId;

    private static Integer bInterfaceDeviceTemplateRootId;

    private static Integer bInterfaceSamplerId;

    private static final String bInterfaceSamplerProtocolCode = "BInterface-HOST设备6-00";

    private static final String portNameTemplate = "COM%s";

    private Map<Integer,Integer> categoryIdTemplateIdMap = new HashMap<>();


//    @PostConstruct
    private void initId(){
        bInterfaceDeviceTemplateRootId = equipmentTemplateProvider.getBInterfaceDeviceTemplateRootId();
        try {
            resourceStructureRootId = resourceStructureProvider.getTreeRootId();
        }catch (Exception ex){
            log.error(ex.getMessage());
        }
        try {
            bInterfaceSamplerId = samplerProvider.getSamplerByProtocolCode(bInterfaceSamplerProtocolCode).getSamplerId();
        }catch (Exception ex){
            log.error(ex.getMessage());
        }
    }
    @Override
    public Integer getResourceStructureRootId(){
        if(ObjectUtil.isEmpty(resourceStructureRootId)){
            try{
                resourceStructureRootId = resourceStructureProvider.getTreeRootId();
            }catch(Exception ex){
                log.error(ex.getMessage());
            }
        }
        return resourceStructureRootId;
    }
    @Override
    public Integer getBInterfaceDeviceTemplateRootId(){
        if(ObjectUtil.isEmpty(bInterfaceDeviceTemplateRootId)){
            bInterfaceDeviceTemplateRootId = equipmentTemplateProvider.getBInterfaceDeviceTemplateRootId();
        }
        return bInterfaceDeviceTemplateRootId;
    }
    @Override
    public Integer getBInterfaceSampleUnitRootId(Integer monitorUnitId){
        if(ObjectUtil.isEmpty(monitorUnitId)){
            return null;
        }
        List<SamplerUnit> samplerUnitList = samplerUnitProvider.selectSamplerUnitWithPort(monitorUnitId);
        if(CollectionUtil.isEmpty(samplerUnitList)){
            return null;
        }
        Integer bInterfaceSampleUnitRootId = samplerUnitList.stream().map(SamplerUnit::getSamplerUnitId).findFirst().get();
        return bInterfaceSampleUnitRootId;
    }
    @Override
    public Integer getDefaultSamplerIdByProtocolCode(){
        if(ObjectUtil.isEmpty(bInterfaceSamplerId)){
            Sampler tslSampler = samplerProvider.getSamplerByProtocolCode(bInterfaceSamplerProtocolCode);
            bInterfaceSamplerId = tslSampler.getSamplerId();
            return bInterfaceSamplerId;
        }else {
            return bInterfaceSamplerId;
        }
    }

    @Override
    public EquipmentTemplate createEquipmentTemplate(EquipmentTemplateVO equipmentTemplateVO){
        if(ObjectUtil.isEmpty(equipmentTemplateVO)){
            log.error("[设备模板创建]参数为空，不进行配置工具调用");
            return null;
        }

        // 调用tcs-siteweb的createTemplate方法，返回EquipmentTemplateVO
        EquipmentTemplateVO result = equipmentTemplateProvider.createTemplate(equipmentTemplateVO);

        // 将返回的VO转换为实体
        return result != null ? result.toEntity() : null;
    }
    @Override
    public EquipmentTemplate queryEquipmentTemplate(EquipmentTemplateVO equipmentTemplateVO){
        // 调用tcs-siteweb的queryTemplate方法
        List<EquipmentTemplateVO> templateVOList = equipmentTemplateProvider.queryTemplate(equipmentTemplateVO);

        if(CollectionUtil.isEmpty(templateVOList)) return null;

        // 将VO转换为实体后返回
        return templateVOList.get(0).toEntity();
    }

    @Override
    public SamplerUnit createDefaultSamplerUnit(Integer monitorUnitId, String equipmentName, Integer portId){
        if(ObjectUtil.isEmpty(monitorUnitId) || ObjectUtil.isEmpty(portId)) return null;
        SamplerUnit samplerUnit = new SamplerUnit();
        samplerUnit.setMonitorUnitId(monitorUnitId);
        samplerUnit.setPortId(portId);
        Integer samplerId = getDefaultSamplerIdByProtocolCode();
        samplerUnit.setSamplerId(samplerId);
        samplerUnit.setParentSamplerUnitId(0);
        samplerUnit.setSamplerUnitName(equipmentName);
        samplerUnit.setAddress(1);
        samplerUnit.setSamplerType((short) 18);
        samplerUnit.setSpUnitInterval(2.0);
        samplerUnit.setDllPath("IDUHOST.so");
        samplerUnit.setConnectState(0);
        samplerUnit.setDescription("TCS create");
        SamplerUnit result = samplerUnitProvider.createConfig(samplerUnit);
        return result;
    }



    @Override
    public Port createPort(Integer monitorUnitId){
        Integer maxPortNo = portProvider.getMaxPortNoByMonitorUnitId(monitorUnitId);
        Integer createPortNo = maxPortNo+1;
        Port port = new Port();
        port.setMonitorUnitId(monitorUnitId);
        port.setPortNo(createPortNo);
        port.setPortName(String.format(portNameTemplate,createPortNo));
        port.setDescription("TCS create");
        port.setLinkSamplerUnitId(0);
        //虚拟端口
        port.setPortType(34);
        port.setSetting("comm_host_dev.so");
        Port result = portProvider.createConfig(port);
        return result;
    }

    /**
     *  为避免tcs-hub的循环依赖，使用了拆分的方法，
     * @param monitorUnitId
     * @param deviceName
     * @param equipmentCategory
     * @param equipmentCategoryName
     * @param equipmentTemplateFlag
     * @param resourceStructureId
     * @return
     */
    @Override
    public Equipment createEquipment(
            Integer monitorUnitId,
            String deviceName,
            Integer equipmentCategory,
            String equipmentCategoryName,
            String equipmentTemplateFlag,
            Integer resourceStructureId
    ) {
        // 1. 创建端口
        Port port = createPort(monitorUnitId);
        if (port == null) {
            log.error("[DEVICE LIFE CYCLE MANAGER]调用配置工具创建采集单元端口，执行失败，采集单元id是{}",monitorUnitId);
            return null;
        }

        // 2. 创建采集单元
        SamplerUnit samplerUnit = createDefaultSamplerUnit(
                monitorUnitId,
                deviceName,
                port.getPortId()
        );
        if (samplerUnit == null) {
            log.error("[DEVICE LIFE CYCLE MANAGER]调用配置工具创建采集单元，执行失败，采集单元id是{}，采集单元名称是{}，采集端口是{}",monitorUnitId,deviceName,port.getPortId());
            return null;
        }
        CreateEquipmentDto createEquipmentDto = new CreateEquipmentDto();
        if (ObjectUtil.isNotEmpty(equipmentCategory)){
            if(categoryIdTemplateIdMap.containsKey(equipmentCategory)){
                createEquipmentDto = toCreateEquipmentDto(monitorUnitId, resourceStructureId, categoryIdTemplateIdMap.get(equipmentCategory),samplerUnit.getSamplerUnitId(),deviceName);
            }
            else {
                EquipmentTemplateVO equipmentTemplateVO = new EquipmentTemplateVO();
                equipmentTemplateVO.setEquipmentCategory(equipmentCategory);
                equipmentTemplateVO.setExtendField1(equipmentTemplateFlag);
                equipmentTemplateVO.setParentTemplateId(0);
                EquipmentTemplate tblEquipmentTemplate = queryEquipmentTemplate(equipmentTemplateVO);
                if (ObjectUtil.isEmpty(tblEquipmentTemplate)) {
                    //创建模板
                    EquipmentTemplateVO query = new EquipmentTemplateVO();
                    String templateName = StringUtils.isEmpty(equipmentCategoryName) ? "TCS-Equipment-Template" : equipmentCategoryName;
                    query.setEquipmentTemplateName(templateName);
                    query.setEquipmentCategory(equipmentCategory);
                    query.setParentTemplateId(0);
                    query.setMemo("TCS Pre-made Equipment Template");
                    //BInterface-HOST设备6-00，用于寻找采集器
                    query.setProtocolCode("BInterface-HOST设备6-00");
                    query.setExtendField1(equipmentTemplateFlag);
                    query.setDescription("TCS Pre-made Equipment Template");
                    //1是监控设备额
                    query.setEquipmentType(1);

                    EquipmentTemplate equipmentTemplate = createEquipmentTemplate(query);
                    if (ObjectUtil.isEmpty(equipmentTemplate)) {
                        log.error("[DEVICE LIFE CYCLE MANAGER]调用配置工具创建设备模板，执行失败，请求参数是{}", query);
                        return null;
                    }
                    //添加缓存
                    categoryIdTemplateIdMap.put(equipmentTemplate.getEquipmentCategory(), equipmentTemplate.getEquipmentTemplateId());
                    createEquipmentDto = toCreateEquipmentDto(monitorUnitId, resourceStructureId, equipmentTemplate.getEquipmentTemplateId(), samplerUnit.getSamplerUnitId(), deviceName);

                } else {
                    categoryIdTemplateIdMap.put(equipmentTemplateVO.getEquipmentCategory(), tblEquipmentTemplate.getEquipmentTemplateId());
                    createEquipmentDto = toCreateEquipmentDto(monitorUnitId, resourceStructureId, tblEquipmentTemplate.getEquipmentTemplateId(), samplerUnit.getSamplerUnitId(), deviceName);
                }
            }
        }
        else{
            createEquipmentDto = toCreateEquipmentDto(monitorUnitId, resourceStructureId, getBInterfaceDeviceTemplateRootId(), samplerUnit.getSamplerUnitId(), deviceName);
        }

        return equipmentProvider.createEquipment(createEquipmentDto);
    }

    @Override
    public boolean deleteEquipment(Integer equipmentId){
        if(ObjectUtil.isEmpty(equipmentId)) {
            return true;
        }
        return equipmentProvider.deleteEquipment(equipmentId);
    }

    @Override
    public Equipment updateEquipment(EquipmentDetailDTO equipmentDetailDTO){
        return equipmentProvider.updateEquipment(equipmentDetailDTO);
    }

    @Override
    public Equipment getEquipment(Integer equipmentId){
        if(ObjectUtil.isEmpty(equipmentId)) {
            return null;
        }
        return equipmentProvider.getConfig(equipmentId);
    }


    CreateEquipmentDto toCreateEquipmentDto(Integer monitorUnitId,Integer resourceStructureRootId,Integer bInterfaceDeviceTemplateRootId,Integer samplerUnitId,String deviceName) {
        CreateEquipmentDto createEquipmentDto = new CreateEquipmentDto();
        createEquipmentDto.setMonitorUnitId(monitorUnitId);
        createEquipmentDto.setEquipmentName(deviceName);
        createEquipmentDto.setResourceStructureId(resourceStructureRootId);
        createEquipmentDto.setEquipmentTemplateId(bInterfaceDeviceTemplateRootId);
        createEquipmentDto.setSamplerUnitId(samplerUnitId);
        //全实例化
        createEquipmentDto.setInstantiated(true);
        return createEquipmentDto;
    }

}
