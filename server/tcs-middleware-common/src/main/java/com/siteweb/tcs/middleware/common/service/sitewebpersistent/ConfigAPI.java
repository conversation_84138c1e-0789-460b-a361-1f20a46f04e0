package com.siteweb.tcs.middleware.common.service.sitewebpersistent;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.tcs.siteweb.dto.*;
import com.siteweb.tcs.siteweb.entity.*;
import com.siteweb.tcs.siteweb.enums.ProtocolTypeEnum;
import com.siteweb.tcs.siteweb.enums.TaskTypeEnum;
import com.siteweb.tcs.siteweb.vo.*;
import org.apache.ibatis.session.SqlSessionFactory;
import org.dom4j.Element;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.annotation.*;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.ResponseEntity;

import javax.sql.DataSource;

import java.io.File;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 */
public interface ConfigAPI {

    /**
     * 初始化API，注入克隆的Spring上下文
     * 由SitewebPersistentService调用，用于使用独立数据源的完整Service和Provider功能
     *
     * @param clonedContext 克隆的Spring上下文（包含使用独立数据源的Bean）
     */
    void initAPI(ApplicationContext clonedContext);

    //增加模板
    EquipmentTemplate importTemplate(Element equipmentTemplatesElement);
    //增加so库
    //删除协议

    Equipment createEquipment(CreateEquipmentDto info);

    // ==================== OMC初始化相关方法 ====================
    void initOMC();

    // ==================== Sampler相关方法 ====================

    /**
     * 查询所有采集器VO列表
     *
     * @return 采集器VO列表
     */
    List<SamplerVO> findAllVoForSampler();

    List<Sampler> findByIdsForSampler(List<Integer> samplerIds);

    /**
     * 根据ID查询采集器
     *
     * @param samplerId 采集器ID
     * @return 采集器实体，如果不存在则返回null
     */
    Sampler findByIdForSampler(Integer samplerId);

    /**
     * 根据协议代码查询采集器
     *
     * @param protocolCode 协议代码
     * @return 采集器实体，如果不存在则返回null
     */
    Sampler findByProtocolCodeForSampler(String protocolCode);

    /**
     * 更新采集器
     *
     * @param sampler 采集器实体
     * @return 是否更新成功
     */
    boolean updateForSampler(Sampler sampler);

    /**
     * 验证批量删除采集器的合法性
     *
     * @param samplerIds 采集器ID列表（逗号分隔）
     * @return 如果有引用关系，返回错误信息；否则返回null
     */
    String validateBatchDeleteForSampler(String samplerIds);

    /**
     * 批量删除采集器
     *
     * @param samplerIds 采集器ID列表（逗号分隔）
     * @return 是否删除成功
     */
    boolean batchDeleteForSampler(String samplerIds);

    /**
     * 验证单个删除采集器的合法性
     *
     * @param samplerId 采集器ID
     * @return 如果有引用关系，返回错误信息；否则返回null
     */
    String validateDeleteByIdForSampler(Integer samplerId);

    /**
     * 删除单个采集器
     *
     * @param samplerId 采集器ID
     * @return 是否删除成功
     */
    boolean deleteByIdForSampler(Integer samplerId);

    /**
     * 上传协议文件
     *
     * @param sampler          采集器实体
     * @param protocolTypeEnum 协议类型枚举
     * @param bytes            文件字节数组
     * @param fileName         文件名
     */
    void uploadProtocolFileForSampler(Sampler sampler, ProtocolTypeEnum protocolTypeEnum, byte[] bytes, String fileName, String filePath);

    /**
     * 删除协议文件
     *
     * @param protocolCode     协议代码
     * @param protocolTypeEnum 协议类型枚举
     */
    void deleteProtocolFileForSampler(String protocolCode, ProtocolTypeEnum protocolTypeEnum);

    /**
     * 下载协议文件
     *
     * @param protocolCode 协议代码
     * @param protocolType 协议类型
     * @return 响应结果
     */
    File downloadProtocolFileForSampler(String protocolCode, Integer protocolType);

    // ==================== EquipmentTemplate相关方法 ====================

    /**
     * 查询所有设备模板VO（用于协议管理页面显示）
     *
     * @return 设备模板VO列表
     */
    List<EquipmentTemplateVO> findVoAllForEquipmentTemplate();

    /**
     * 导入设备模板（协议上传功能）
     *
     * @param equipmentTemplatesElement 设备模板XML元素
     * @return 导入的设备模板
     */
    EquipmentTemplate importTemplateForEquipmentTemplate(Element equipmentTemplatesElement);

    /**
     * 获取设备模板树（隐藏动态配置模板）
     * @param hideDynamicConfigTemplate 是否隐藏动态配置模板
     * @return 设备模板树列表
     */
    List<Tree<Integer>> findTreeForEquipmentTemplate(Boolean hideDynamicConfigTemplate);

    /**
     * 根据条件获取设备模板树（排除分类）
     * @param equipmentCategory 设备分类
     * @param protocolCode 协议代码
     * @param equipmentTemplateName 设备模板名称
     * @return 设备模板树列表
     */
    List<EquipmentTemplateTreeDTO> findTreeExcludeCategoryForEquipmentTemplate(Integer equipmentCategory, String protocolCode, String equipmentTemplateName);

    /**
     * 根据ID获取单个模板的详细信息
     * @param equipmentTemplateId 设备模板ID
     * @return 设备模板VO
     */
    EquipmentTemplateVO findVoByIdForEquipmentTemplate(Integer equipmentTemplateId);

    /**
     * 更新模板的设备类型（继承更新）
     * @param equipmentTemplate 设备模板实体
     * @return 是否更新成功
     */
    boolean inheritUpdateForEquipmentTemplate(EquipmentTemplate equipmentTemplate);

    /**
     * 传入完整模板信息以进行更新
     * @param equipmentTemplate 设备模板实体
     * @return 是否更新成功
     */
    boolean updateForEquipmentTemplate(EquipmentTemplate equipmentTemplate);

    /**
     * 复制一个设备模板
     * @param copyEquipmentTemplateDTO 复制设备模板DTO
     * @return 复制后的设备模板
     */
    int copyForEquipmentTemplate(CopyEquipmentTemplateDTO copyEquipmentTemplateDTO);

    /**
     * 根据ID删除指定模板
     * @param equipmentTemplateId 设备模板ID
     * @return 是否删除成功
     */
    boolean deleteByIdForEquipmentTemplate(Integer equipmentTemplateId);

    /**
     * 根据模板ID导出模板文件
     * @param equipmentTemplateId 设备模板ID
     * @return 导出的XML字符串
     */
    String exportForEquipmentTemplate(Integer equipmentTemplateId);

    /**
     * 将指定模板升级为根模板
     * @param equipmentTemplateId 设备模板ID
     * @return 是否升级成功
     */
    boolean upgradeToRootTemplateForEquipmentTemplate(Integer equipmentTemplateId);

    /**
     * 新增一个设备模板
     * @param equipmentTemplate 设备模板实体
     * @return 创建的设备模板
     */
    EquipmentTemplate createForEquipmentTemplate(EquipmentTemplate equipmentTemplate);

    /**
     * 根据模板ID列表获取相关的DLL路径
     * @param equipmentTemplateIds 设备模板ID列表
     * @return DLL路径列表
     */
    List<String> getDllPathForEquipmentTemplate(List<Integer> equipmentTemplateIds);

    /**
     * 根据模板ID获取其关联的信号、事件和控制
     * @param equipmentTemplateId 设备模板ID
     * @return 关联信息（可以是Map或自定义DTO）
     */
    Map<String, Object> getAssociatedDataForEquipmentTemplate(Integer equipmentTemplateId);

    /**
     * 根据模板ID导出其详细配置为Excel文件
     * @param response HTTP响应
     * @param equipmentTemplateId 设备模板ID
     */
    void exportExcelForEquipmentTemplate(HttpServletResponse response, Integer equipmentTemplateId);

    boolean applyStandardizationToChildrenForEquipmentTemplate(Integer equipmentTemplateId);

    /**
     * 更新设备类别
     * @param equipmentTemplateId 设备模板ID
     * @param equipmentCategory 设备类别
     * @return 影响行数
     */
    int updateEquipmentCategoryForEquipmentTemplate(Integer equipmentTemplateId, Integer equipmentCategory);

    // ==================== DataItem相关方法 ====================

    EquipmentTemplate findByIdForEquipmentTemplate(Integer equipmentTemplateId);

    /**
     * 根据entryId查询数据项
     *
     * @param entryId 字典项ID
     * @return 数据项列表
     */
    List<DataItem> findByEntryIdForDataItem(Integer entryId);

    /**
     * 更新数据项
     *
     * @param dataItemUpdateDTO
     * @return 是否更新成功
     */
    boolean updateForDataItem(DataItemUpdateDTO dataItemUpdateDTO);

    /**
     * 创建数据项
     *
     * @param dataItemCreateDTO
     * @return 创建结果
     */
    int createDataItemForDataItem(DataItemCreateDTO dataItemCreateDTO);

    /**
     * 删除数据项
     *
     * @param entryItemIds 数据项ID列表（逗号分隔）
     * @return 删除结果
     */
    int deleteByEntryItemIdsForDataItem(String entryItemIds);

    // ==================== CategoryIdMap相关方法 ====================

    /**
     * 根据原始设备类别获取业务类别
     *
     * @param originCategoryKey 原始设备类别key
     * @return 业务类别列表
     */
    Map<Integer, String> findBusinessCategoryFromOriginCategoryForCategoryIdMap(Integer originCategoryKey);

    // ==================== MonitorUnit相关方法 ====================

    /**
     * 根据ID查询监控单元
     *
     * @param monitorUnitId 监控单元ID
     * @return 监控单元DTO
     */
    MonitorUnitDTO findByIdForMonitorUnit(Integer monitorUnitId);

    /**
     * 创建监控单元
     *
     * @param monitorUnit         监控单元数据
     * @param resourceStructureId 资源结构ID
     * @return 是否创建成功
     */
    boolean createForMonitorUnit(MonitorUnitDTO monitorUnit, Integer resourceStructureId);

    /**
     * 创建监控单元 (V3 API)
     *
     * @param monitorUnit 监控单元数据
     * @return 是否创建成功
     */
    boolean createV3ForMonitorUnit(MonitorUnitDTO monitorUnit);

    /**
     * 更新监控单元
     *
     * @param monitorUnit 监控单元数据
     * @return 是否更新成功
     */
    boolean updateForMonitorUnit(MonitorUnitDTO monitorUnit);

    /**
     * 删除监控单元
     *
     * @param monitorUnitId 监控单元ID
     * @param isDelEqs      是否删除关联设备
     * @return 是否删除成功
     */
    boolean deleteForMonitorUnit(Integer monitorUnitId, Boolean isDelEqs);


    /**
     * 获取监控单元类型列表
     *
     * @return 监控单元类型列表
     */
    List<TypeItemDTO> findTypesForMonitorUnit();

    /**
     * 获取监控单元采样器树
     *
     * @param monitorUnitId 监控单元ID
     * @return 采样器树结构
     */
    List<PortTreeVO> getSamplerTreeForMonitorUnit(Integer monitorUnitId);

    /**
     * 根据ID列表查询监控单元
     *
     * @param monitorUnitIds 监控单元ID列表
     * @return 监控单元列表
     */
    List<MonitorUnitDTO> findByIdsForMonitorUnit(List<Integer> monitorUnitIds);

    // ==================== Equipment相关方法 ====================

    /**
     * 根据ID查询设备
     *
     * @param equipmentId 设备ID
     * @return 设备实体
     */
    Equipment findByIdForEquipment(Integer equipmentId);

    /**
     * 创建设备
     *
     * @param info 设备创建实体DTO
     * @return 创建的设备
     */
    Equipment createForEquipment(CreateEquipmentDto info);

    /**
     * 创建设备 (V3 API)
     *
     * @param equipment 设备实体
     * @return 创建的设备
     */
    Equipment createV3ForEquipment(Equipment equipment);

    /**
     * 更新设备
     *
     * @param equipmentDetailDTO 设备详情DTO
     * @return 更新的设备
     */
    Equipment updateForEquipment(EquipmentDetailDTO equipmentDetailDTO);

    /**
     * 删除设备
     *
     * @param equipmentId 设备ID
     * @return 是否删除成功
     */
    boolean deleteForEquipment(Integer equipmentId);

    /**
     * 根据监控单元ID查询设备
     *
     * @param monitorUnitId 监控单元ID
     * @return 设备列表
     */
    List<Equipment> findByMonitorUnitIdForEquipment(Integer monitorUnitId);

    /**
     * 切换设备模板
     *
     * @param switchTemplateDTO 切换模板DTO
     * @return 是否切换成功
     */
    boolean switchTemplateForEquipment(SwitchTemplateDTO switchTemplateDTO);

    /**
     * 搜索设备列表
     *
     * @param monitorUnitId 监控单元ID (可选)
     * @param equipmentName 设备名称 (可选，支持模糊查询)
     * @return 设备列表
     */
    List<Equipment> searchEquipmentList(Integer monitorUnitId, String equipmentName);

    /**
     * 设备实例化
     *
     * @param equipmentId 设备ID
     * @return 模板ID
     */
    Integer equipmentInstanceForEquipment(Integer equipmentId);

    /**
     * 检查设备名称是否重复
     *
     * @param equipmentId   设备ID（更新时使用，新增时为null）
     * @param monitorUnitId 监控单元ID
     * @param equipmentName 设备名称
     * @return 是否重复
     */
    Boolean checkEquipmentNameExistsForEquipment(Integer equipmentId, Integer monitorUnitId, String equipmentName);

    /**
     * 获取设备详情
     *
     * @param equipmentId 设备ID
     * @return 设备详情DTO
     */
    EquipmentDetailDTO findEquipmentDetailForEquipment(Integer equipmentId);


    // ==================== SamplerUnit相关方法 ====================

    /**
     * 创建采样器单元
     *
     * @param samplerUnit 采样器单元实体
     * @return 创建的采样器单元
     */
    SamplerUnit createForSamplerUnit(SamplerUnit samplerUnit);

    /**
     * 根据ID查找采样器单元
     *
     * @param samplerUnitId 采样器单元ID
     * @return 采样器单元实体
     */
    SamplerUnit findByIdForSamplerUnit(Integer samplerUnitId);

    /**
     * 更新采样器单元
     *
     * @param samplerUnit 采样器单元实体
     * @return 是否更新成功
     */
    boolean updateForSamplerUnit(SamplerUnit samplerUnit);

    /**
     * 删除采样器单元
     *
     * @param samplerUnitId 采样器单元ID
     * @return 是否删除成功
     */
    boolean deleteForSamplerUnit(Integer samplerUnitId);

    /**
     * 根据监控单元ID和端口ID查找采样器单元列表
     *
     * @param monitorUnitId 监控单元ID
     * @param portId        端口ID
     * @return 采样器单元列表
     */
    List<SamplerUnit> findByMonitorUnitIdAndPortIdForSamplerUnit(Integer monitorUnitId, Integer portId);

    // ==================== Workstation相关方法 ====================

    /**
     * 获取工作站服务器源列表
     * 包含不同类型的工作站信息(RDS、RMU、DS等)
     *
     * @return 服务器源列表
     */
    List<ServerSourceVO> getServerSourceListForWorkstation();

    /**
     * 获取所有工作站列表
     *
     * @return 工作站列表
     */
    List<?> findAllForWorkstation();

    /**
     * 根据工作站类型获取工作站列表
     *
     * @param workStationType 工作站类型
     * @return 工作站列表
     */
    List<WorkStation> findByWorkStationTypeForWorkstation(Integer workStationType);

    /**
     * 根据ID获取工作站详情
     *
     * @param workStationId 工作站ID
     * @return 工作站详情
     */
    WorkStation findByIdForWorkstation(Integer workStationId);


    // ==================== Port相关方法 ====================

    /**
     * 根据端口ID查询端口
     *
     * @param portId 端口ID
     * @return 端口实体
     */
    Port findByPortIdForPort(Integer portId);

    /**
     * 创建端口
     *
     * @param port 端口实体
     * @return 创建的端口
     */
    Port createForPort(Port port);

    /**
     * 更新端口
     *
     * @param port 端口实体
     * @return 是否更新成功
     */
    boolean updateForPort(Port port);

    /**
     * 删除端口
     *
     * @param portId 端口ID
     * @return 是否删除成功
     */
    boolean deleteForPort(Integer portId);

    /**
     * 根据监控单元ID查询端口
     *
     * @param monitorUnitId 监控单元ID
     * @return 端口列表
     */
    List<Port> findByMonitorUnitIdForPort(Integer monitorUnitId);

    /**
     * 获取端口类型列表
     *
     * @param monitorUnitCategory 监控单元类别（可选）
     * @return 端口类型列表
     */
    List<DataItem> findTypesForPort(Integer monitorUnitCategory);

    /**
     * 获取监控单元下的最大端口号
     *
     * @param monitorUnitId 监控单元ID
     * @return 最大端口号
     */
    Integer getMaxPortNoForPort(Integer monitorUnitId);

    /**
     * 获取端口下的采集单元列表
     *
     * @param portId 端口ID
     * @return 采集单元列表
     */
    List<SamplerUnitTreeVO> getSamplerUnitsForPort(Integer portId);

    // ==================== 设备管理相关方法 ====================

    /**
     * 获取设备树结构
     *
     * @return 设备树VO列表
     */
    List<EquipmentTreeVO> getEquipmentTreeForDeviceManagement();

    /**
     * 获取设备信息
     *
     * @param equipmentId 设备ID
     * @return 设备信息
     */
    Equipment getEquipmentInfoForDeviceManagement(Integer equipmentId);

    /**
     * 修改设备信息
     *
     * @param equipment 设备实体
     * @return 是否修改成功
     */
    boolean updateEquipmentConfigForDeviceManagement(Equipment equipment);

    /**
     * 获取设备模板配置
     *
     * @param id 设备模板ID
     * @return 设备模板
     */
    EquipmentTemplate getEquipmentTemplateConfigForDeviceManagement(Integer id);

    /**
     * 添加新模板实例
     *
     * @param equipmentId 设备ID
     * @return 模板ID
     */
    Integer addEquipmentInstanceForDeviceManagement(Integer equipmentId);


    List<Equipment> getSimplifyEquipmentsForDeviceManagement();

    Equipment getSimplifyEquipmentForDeviceManagement(Integer equipmentId);

    /**
     * 根据设备模板ID查询设备引用信息
     *
     * @param equipmentTemplateId 设备模板ID
     * @return 设备引用信息列表
     */
    List<EquipmentReferenceVO> findReferenceByEquipmentTemplateIdForDeviceManagement(Integer equipmentTemplateId);

    /**
     * 导出设备引用信息
     *
     * @param equipmentTemplateId 设备模板ID
     * @return 导出的字节数组
     */
    byte[] exportReferenceByEquipmentTemplateIdForDeviceManagement(Integer equipmentTemplateId);

    /**
     * 检查模板切换影响
     *
     * @param switchTemplateDTO 切换模板DTO
     * @return 模板变更影响列表
     */
    List<EquipTemplateChangeDTO> checkTemplateChangeForDeviceManagement(SwitchTemplateDTO switchTemplateDTO);

    /**
     * 导出模板切换影响
     *
     * @param switchTemplateDTO 切换模板DTO
     * @return 导出的字节数组
     */
    byte[] exportTemplateChangeForDeviceManagement(SwitchTemplateDTO switchTemplateDTO);



    /**
     * 获取顶层设备模板信息
     *
     * @param equipmentTemplateId 设备模板ID
     * @return 顶层设备模板
     */
    EquipmentTemplate getTopParentEquipmentTemplateForDeviceManagement(Integer equipmentTemplateId);

    /**
     * 修改子模板设备类型
     *
     * @param parentId 父模板ID
     * @param category 设备类型
     * @return 是否修改成功
     */
    boolean updateChildrenCategoryForDeviceManagement(Integer parentId, Integer category);

    /**
     * 获取设备基类列表
     *
     * @return 设备基类列表
     */
    List<EquipmentBaseType> getEquipmentBaseTypeListForDeviceManagement();

    /**
     * 获取电池设备类型
     *
     * @return 数据项列表
     */
    List<DataItem> getEquipmentCategorysForDeviceManagement();

    /**
     * 获取监控单元信息
     *
     * @param muId 监控单元ID
     * @return 监控单元信息
     */
    MonitorUnit getMonitorUnitConfigForDeviceManagement(Integer muId);

    /**
     * 获取事件实例信息
     *
     * @param equipmentId 设备ID
     * @param eventId     事件ID
     * @return 监控单元事件列表
     */
    List<TslMonitorUnitEvent> getMonitorUnitEventConditionForDeviceManagement(Integer equipmentId, Integer eventId);

    /**
     * 添加事件实例
     *
     * @param event 监控单元事件
     * @return 是否添加成功
     */
    boolean addMonitorUnitEventForDeviceManagement(TslMonitorUnitEvent event);

    /**
     * 删除事件实例
     *
     * @param equipmentId 设备ID
     * @param eventId     事件ID
     * @return 是否删除成功
     */
    boolean deleteMonitorUnitEventForDeviceManagement(Integer equipmentId, Integer eventId);

    /**
     * 获取信号实例信息
     *
     * @param equipmentId 设备ID
     * @param signalId    信号ID
     * @return 监控单元信号列表
     */
    List<TslMonitorUnitSignal> getMonitorUnitSignalConditionForDeviceManagement(Integer equipmentId, Integer signalId);

    /**
     * 添加信号实例
     *
     * @param signal 监控单元信号
     * @return 是否添加成功
     */
    boolean addMonitorUnitSignalForDeviceManagement(TslMonitorUnitSignal signal);

    /**
     * 删除信号实例
     *
     * @param equipmentId 设备ID
     * @param signalId    信号ID
     * @return 是否删除成功
     */
    boolean deleteMonitorUnitSignalForDeviceManagement(Integer equipmentId, Integer signalId);

    /**
     * 获取采样单元列表
     *
     * @return 采样单元带端口信息VO列表
     */
    List<SamplerUnitWithPortVO> getSamplerUnitWithPortForDeviceManagement();

    /**
     * 获取跨站信号信息
     *
     * @param condition 查询条件
     * @return 跨站信号列表
     */
    List<AcrossMonitorUnitSignal> getCrossMonitorUnitSignalForSignal(java.util.Map<String, Object> condition);

    /**
     * 创建跨站信号
     *
     * @param signal 跨站信号
     * @return 是否创建成功
     */
    boolean createAcrossMonitorUnitSignalForSignal(AcrossMonitorUnitSignal signal);

    /**
     * 获取简化信号列表
     *
     * @return 简化信号列表
     */
    List<SimplifySignalDTO> getSimplifySignalsForSignal(Integer equipmentTemplateId);

    /**
     * 根据设备模板和设备ID获取信号列表
     *
     * @param equipmentTemplateId 设备模板ID
     * @param equipmentId         设备ID
     * @return 信号列表
     */
    List<SignalConfigItem> getSignalListForSignal(Integer equipmentTemplateId, Integer equipmentId);

    /**
     * 新增信号
     *
     * @param signalConfigItem 信号配置项
     * @return 创建的信号
     */
    Signal createForSignal(SignalConfigItem signalConfigItem);

    /**
     * 更新信号
     *
     * @param signalConfigItem 信号配置项
     * @return 更新的信号
     */
    Signal updateForSignal(SignalConfigItem signalConfigItem);

    /**
     * 批量删除信号
     *
     * @param equipmentTemplateId 设备模板ID
     * @param signalIds           信号ID列表
     * @return 是否删除成功
     */
    boolean batchDeleteForSignal(Integer equipmentTemplateId, List<Integer> signalIds);

    /**
     * 信号字段复制
     *
     * @param signalFieldCopyList 信号字段复制列表
     * @return 是否复制成功
     */
    boolean fieldCopyForSignal(List<SignalFieldCopyDTO> signalFieldCopyList);

    /**
     * 新增事件
     *
     * @param eventConfigItem 事件配置项
     * @return 创建的事件
     */
    Event createForEvent(EventConfigItem eventConfigItem);

    /**
     * 更新事件
     *
     * @param eventConfigItem 事件配置项
     * @return 更新的事件
     */
    Event updateForEvent(EventConfigItem eventConfigItem);

    /**
     * 批量更新事件
     *
     * @param batchEventConfigItem 批量事件配置项
     * @return 是否更新成功
     */
    boolean batchUpdateForEvent(BatchEventConfigItem batchEventConfigItem);

    /**
     * 删除事件
     *
     * @param equipmentTemplateId 设备模板ID
     * @param eventId             事件ID
     * @return 是否删除成功
     */
    boolean deleteForEvent(Integer equipmentTemplateId, Integer eventId);

    List<EventConfigItem> findEventItemByEquipmentTemplateIdForEvent(Integer equipmentTemplateId);

    /**
     * 事件字段复制
     *
     * @param eventFieldCopyList 事件字段复制列表
     * @return 是否复制成功
     */
    boolean fieldCopyForEvent(List<EventFieldCopyDTO> eventFieldCopyList);

    /**
     * 事件关联信号
     *
     * @param equipmentTemplateId 设备模板ID
     * @param signalId 信号ID
     * @return 是否关联成功
     */
    boolean linkEventForEvent(Integer equipmentTemplateId, Integer signalId);


    /**
     * 获取设备模板日志列表
     *
     * @param params 查询参数
     * @return 分页结果
     */
    Page<OperationDetailVO> getEquipmentTemplateLogPageForDeviceManagement(Map<String, Object> params);

    /**
     * 获取设备日志列表
     *
     * @param params 查询参数
     * @return 分页结果
     */
    Page<OperationDetailVO> getEquipmentLogPageForDeviceManagement(Map<String, Object> params);

    /**
     * 获取操作类型
     *
     * @return 操作类型列表
     */
    List<IdValueDTO<Integer, String>> getOperationTypesForDeviceManagement();


    EquipmentCategoryMapDTO findEquipmentCategoryMapDTOForEquipmentCategoryMap(Integer originCategory);

    // ==================== 控制管理相关方法 ====================

    /**
     * 创建控制
     *
     * @param control 控制配置项
     * @return 是否创建成功
     */
    boolean createForControl(ControlConfigItem control);

    /**
     * 更新控制
     *
     * @param control 控制配置项
     * @return 是否更新成功
     */
    boolean updateForControl(ControlConfigItem control);

    /**
     * 删除控制
     *
     * @param equipmentTemplateId 设备模板ID
     * @param controlId 控制ID
     * @return 是否删除成功
     */
    boolean deleteForControl(Integer equipmentTemplateId, Integer controlId);

    /**
     * 批量删除控制
     *
     * @param equipmentTemplateId 设备模板ID
     * @param controlIds 控制ID列表
     * @return 是否删除成功
     */
    boolean batchDeleteForControl(Integer equipmentTemplateId, List<Integer> controlIds);

    /**
     * 根据设备模板ID查询控制列表
     *
     * @param equipmentTemplateId 设备模板ID
     * @return 控制配置项列表
     */
    List<ControlConfigItem> findControlListForControl(Integer equipmentTemplateId);

    /**
     * 查询控制点
     *
     * @param equipmentTemplateId 设备模板ID
     * @return 控制配置点列表
     */
    List<ControlConfigPointDTO> findControlPointsForControl(Integer equipmentTemplateId);

    /**
     * 获取控制信息
     *
     * @param equipmentTemplateId 设备模板ID
     * @param controlId 控制ID
     * @return 控制配置项
     */
    ControlConfigItem getControlInfoForControl(Integer equipmentTemplateId, Integer controlId);

    /**
     * 获取设备控制信息
     *
     * @param stationId 站点ID（可选）
     * @param equipmentId 设备ID
     * @param controlId 控制ID
     * @return 控制配置项
     */
    ControlConfigItem getControlCategoryForControl(Integer stationId, Integer equipmentId, Integer controlId);

    /**
     * 字段复制
     *
     * @param commandFieldCopyList 命令字段复制列表
     * @return 是否复制成功
     */
    boolean fieldCopyForControl(List<CommandFieldCopyDTO> commandFieldCopyList);

    /**
     * 处理相似控制
     *
     * @param similarDataDTO 相似数据DTO
     */
    void disposeSimilarControlForControl(SimilarDataDTO similarDataDTO);

    // ==================== 操作日志相关方法（按tcs-config原始接口） ====================

    /**
     * 分页查询操作日志
     *
     * @param page 分页参数
     * @param operationDetailDTO 查询条件
     * @return 分页结果
     */
    Page<OperationDetailVO> findPageForOperationDetail(Page<OperationDetail> page, OperationDetailDTO operationDetailDTO);

    /**
     * 根据对象类型和对象ID分页查询操作日志
     *
     * @param page 分页参数
     * @param objectType 对象类型
     * @param objectId 对象ID
     * @return 分页结果
     */
    Page<OperationDetail> findPageByObjectTypeAndObjectIdForOperationDetail(Page<OperationDetail> page, String objectType, String objectId);

    /**
     * 分页查询设备模板操作日志
     *
     * @param page 分页参数
     * @param operationDetailDTO 查询条件
     * @return 分页结果
     */
    Page<OperationDetailVO> findEquipmentTemplateLogPageForOperationDetail(Page<OperationDetail> page, OperationDetailDTO operationDetailDTO);

    /**
     * 分页查询设备操作日志
     *
     * @param page 分页参数
     * @param operationDetailDTO 查询条件
     * @return 分页结果
     */
    Page<OperationDetailVO> findEquipmentLogPageForOperationDetail(Page<OperationDetail> page, OperationDetailDTO operationDetailDTO);


    // -- 代替websocket
    TaskStatusVO getTaskStatusForTaskStaus(String taskId);

    List<TaskStatusVO> getTaskHistoryForTaskStaus(String taskId);

    List<TaskStatusVO> getActiveTasksByTypeForTaskStaus(String taskType);

    List<TaskStatusVO> getTasksByMonitorUnitIdForTaskStaus(Integer monitorUnitId);

    void deleteTaskForTaskStaus(String taskId);

    void cleanupExpiredTasksForTaskStaus() ;

}
