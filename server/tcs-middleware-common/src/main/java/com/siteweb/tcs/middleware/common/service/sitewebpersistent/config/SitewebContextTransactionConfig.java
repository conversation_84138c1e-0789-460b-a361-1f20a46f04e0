package com.siteweb.tcs.middleware.common.service.sitewebpersistent.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

/**
 * Siteweb独立上下文专用的事务管理配置
 * 这个配置类只在SitewebPersistentService的独立ApplicationContext中使用
 *
 * 使用@ConditionalOnBean确保只有在sitewebDataSource存在时才激活
 * 这样可以避免主程序启动时因为缺少sitewebDataSource而失败
 *
 * 注意：虽然主程序会扫描到这个类，但由于条件注解的存在，
 * 只有在独立上下文中注册了sitewebDataSource后才会激活
 */
@Configuration
@EnableTransactionManagement(proxyTargetClass = true)
@ConditionalOnBean(name = "sitewebDataSource")
public class SitewebContextTransactionConfig {

    private static final Logger logger = LoggerFactory.getLogger(SitewebContextTransactionConfig.class);

    /**
     * 为独立上下文创建事务管理器
     * 使用sitewebDataSource作为数据源
     * 
     * 注意：这个Bean必须命名为"transactionManager"，这样Spring的@Transactional注解
     * 才能自动找到并使用它进行事务管理
     */
    @Bean(name = {"transactionManager", "sitewebTransactionManager"})
    @Primary
    public DataSourceTransactionManager transactionManager(@Qualifier("sitewebDataSource") DataSource dataSource) {
        logger.info("Creating sitewebTransactionManager with dataSource: {}", dataSource.getClass().getName());
        
        DataSourceTransactionManager transactionManager = new DataSourceTransactionManager(dataSource);
        // 设置事务管理器的一些属性
        transactionManager.setDefaultTimeout(100); // 默认事务超时时间30秒
        transactionManager.setRollbackOnCommitFailure(true); // 提交失败时回滚
        transactionManager.setValidateExistingTransaction(true); // 验证现有事务
        
        logger.info("SitewebTransactionManager created successfully with bean names: transactionManager, sitewebTransactionManager");
        return transactionManager;
    }
}
