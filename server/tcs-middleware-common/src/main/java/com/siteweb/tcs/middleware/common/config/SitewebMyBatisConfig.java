package com.siteweb.tcs.middleware.common.config;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import org.apache.ibatis.logging.slf4j.Slf4jImpl;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Siteweb MyBatis配置类
 * 用于为SitewebPersistentService创建独立的MyBatis配置
 */
public class SitewebMyBatisConfig {

    private static final Logger logger = LoggerFactory.getLogger(SitewebMyBatisConfig.class);

    /**
     * 创建SqlSessionFactory
     *
     * @param dataSource 数据源
     * @param serviceId 服务ID，用于标识不同的服务实例
     * @return SqlSessionFactory
     * @throws MiddlewareTechnicalException 创建失败时抛出异常
     */
    public static SqlSessionFactory createSqlSessionFactory(DataSource dataSource, String serviceId) 
            throws MiddlewareTechnicalException {
        try {
            logger.info("Creating SqlSessionFactory for SitewebPersistentService: {}", serviceId);

            MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
            bean.setDataSource(dataSource);
            String dbType;
            try (Connection conn = dataSource.getConnection()) {
                dbType = conn.getMetaData().getDatabaseProductName().toLowerCase();
            }

            // 扫描siteweb的mapper文件
            // 加载 mapper 文件
            PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            List<Resource> resources = new ArrayList<>();

            // 通用 mapper
            resources.addAll(Arrays.asList(resolver.getResources("classpath*:mapper/*.xml")));

            // 数据库类型专属 mapper（如：mapper/postgresql/*.xml）
            resources.addAll(Arrays.asList(resolver.getResources("classpath*:mapper/" + dbType + "/*.xml")));

            bean.setMapperLocations(resources.toArray(new Resource[0]));

            // 配置MyBatis-Plus
            GlobalConfig globalConfig = new GlobalConfig();
            globalConfig.setBanner(false);
            
            // 配置数据库配置
            GlobalConfig.DbConfig dbConfig = new GlobalConfig.DbConfig();
            dbConfig.setLogicDeleteField("deleted");
            dbConfig.setLogicDeleteValue("1");
            dbConfig.setLogicNotDeleteValue("0");
            globalConfig.setDbConfig(dbConfig);
            
            bean.setGlobalConfig(globalConfig);

            // 配置MyBatis设置
            MybatisConfiguration configuration = new MybatisConfiguration();
            configuration.setMapUnderscoreToCamelCase(true);
            configuration.setLogImpl(Slf4jImpl.class);
            bean.setConfiguration(configuration);

            SqlSessionFactory sqlSessionFactory = bean.getObject();
            logger.info("SqlSessionFactory created successfully for service: {}", serviceId);
            
            return sqlSessionFactory;

        } catch (Exception e) {
            logger.error("Failed to create SqlSessionFactory for service: {}", serviceId, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "Failed to create SqlSessionFactory for service: " + serviceId + ", error: " + e.getMessage(),
                e
            );
        }
    }

    /**
     * 创建SqlSessionTemplate
     *
     * @param sqlSessionFactory SqlSessionFactory
     * @param serviceId 服务ID
     * @return SqlSessionTemplate
     * @throws MiddlewareTechnicalException 创建失败时抛出异常
     */
    public static SqlSessionTemplate createSqlSessionTemplate(SqlSessionFactory sqlSessionFactory, String serviceId) 
            throws MiddlewareTechnicalException {
        try {
            logger.debug("Creating SqlSessionTemplate for service: {}", serviceId);
            
            SqlSessionTemplate sqlSessionTemplate = new SqlSessionTemplate(sqlSessionFactory);
            
            logger.debug("SqlSessionTemplate created successfully for service: {}", serviceId);
            return sqlSessionTemplate;
            
        } catch (Exception e) {
            logger.error("Failed to create SqlSessionTemplate for service: {}", serviceId, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "Failed to create SqlSessionTemplate for service: " + serviceId + ", error: " + e.getMessage(),
                e
            );
        }
    }

    /**
     * 验证数据源连接
     *
     * @param dataSource 数据源
     * @param serviceId 服务ID
     * @throws MiddlewareTechnicalException 验证失败时抛出异常
     */
    public static void validateDataSource(DataSource dataSource, String serviceId) 
            throws MiddlewareTechnicalException {
        try {
            logger.debug("Validating DataSource for service: {}", serviceId);
            
            if (dataSource == null) {
                throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                    "DataSource is null for service: " + serviceId
                );
            }

            // 测试连接
            try (var connection = dataSource.getConnection()) {
                if (!connection.isValid(5)) {
                    throw new MiddlewareTechnicalException(
                        MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                        "DataSource connection is invalid for service: " + serviceId
                    );
                }
            }
            
            logger.debug("DataSource validation successful for service: {}", serviceId);
            
        } catch (Exception e) {
            logger.error("DataSource validation failed for service: {}", serviceId, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "DataSource validation failed for service: " + serviceId + ", error: " + e.getMessage(),
                e
            );
        }
    }
}
