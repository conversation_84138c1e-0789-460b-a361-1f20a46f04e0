package com.siteweb.tcs.middleware.common.resource;

import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 资源基础抽象类
 * 提供Resource接口的基础实现
 */
public abstract class BaseResource implements Resource {

    protected final String id;
    protected final String type;
    protected final String name;
    protected final String description;
    protected final AtomicReference<ResourceStatus> status = new AtomicReference<>(ResourceStatus.INITIALIZED);

    /**
     * 构造函数
     *
     * @param id 资源ID
     * @param type 资源类型
     * @param name 资源名称
     * @param description 资源描述
     */
    protected BaseResource(String id, String type, String name, String description) {
        this.id = id;
        this.type = type;
        this.name = name;
        this.description = description;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getType() {
        return type;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public ResourceStatus getStatus() {
        return status.get();
    }

    @Override
    public void initialize() throws MiddlewareTechnicalException {
        if (status.compareAndSet(ResourceStatus.INITIALIZED, ResourceStatus.INITIALIZED)) {
            doInitialize();
        } else {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.OPERATION_NOT_ALLOWED,
                "Resource already initialized: " + id
            );
        }
    }

    @Override
    public void start() throws MiddlewareTechnicalException {
        if (status.compareAndSet(ResourceStatus.INITIALIZED, ResourceStatus.STARTED) ||
            status.compareAndSet(ResourceStatus.STOPPED, ResourceStatus.STARTED)) {
            doStart();
        } else {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.OPERATION_NOT_ALLOWED,
                "Resource cannot be started: " + id + ", current status: " + status.get()
            );
        }
    }

    @Override
    public void stop() throws MiddlewareTechnicalException {
        if (status.compareAndSet(ResourceStatus.STARTED, ResourceStatus.STOPPED)) {
            doStop();
        } else {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.OPERATION_NOT_ALLOWED,
                "Resource cannot be stopped: " + id + ", current status: " + status.get()
            );
        }
    }

    @Override
    public void destroy() throws MiddlewareTechnicalException {
        if (status.compareAndSet(ResourceStatus.STOPPED, ResourceStatus.DESTROYED) ||
            status.compareAndSet(ResourceStatus.INITIALIZED, ResourceStatus.DESTROYED)) {
            doDestroy();
        } else {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.OPERATION_NOT_ALLOWED,
                "Resource cannot be destroyed: " + id + ", current status: " + status.get()
            );
        }
    }

    @Override
    public boolean isHealthy() {
        return checkHealth().isUp();
    }

    /**
     * 执行初始化操作
     *
     * @throws MiddlewareTechnicalException 初始化失败时抛出异常
     */
    protected abstract void doInitialize() throws MiddlewareTechnicalException;

    /**
     * 执行启动操作
     *
     * @throws MiddlewareTechnicalException 启动失败时抛出异常
     */
    protected abstract void doStart() throws MiddlewareTechnicalException;

    /**
     * 执行停止操作
     *
     * @throws MiddlewareTechnicalException 停止失败时抛出异常
     */
    protected abstract void doStop() throws MiddlewareTechnicalException;

    /**
     * 执行销毁操作
     *
     * @throws MiddlewareTechnicalException 销毁失败时抛出异常
     */
    protected abstract void doDestroy() throws MiddlewareTechnicalException;
}
