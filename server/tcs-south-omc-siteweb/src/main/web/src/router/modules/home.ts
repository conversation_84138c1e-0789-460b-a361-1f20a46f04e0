const Layout = () => import("@/layout/index.vue");

export default {
  path: "/",
  name: "Plugin",
  component: Layout,
  redirect: "/siteweb-omc/protocol",
  meta: {
    icon: "mingcute:device-line",
    title: "SITEWEB-OMC",
    rank: 0
  },
  children: [
    {
      path: "/siteweb-omc/protocol",
      name: "SitewebOmcProtocol",
      component: () => import("@/views/protocol/index.vue"),
      meta: {
        icon: "mingcute:file-security-line",
        title: "协议管理",
        showParent: true
      }
    },
    {
      path: "/siteweb-omc/monitor-unit",
      name: "SitewebOmcMonitorUnit",
      component: () => import("@/views/monitor-unit/index.vue"),
      meta: {
        icon: "ic:outline-monitor-heart",
        title: "采集器",
        showParent: true
      }
    },
    {
      path: "/siteweb-omc/device-management/:id?",
      name: "SitewebOmcDeviceManagment",
      component: () => import("@/views/device-managment/index.vue"),
      meta: {
        icon: "ic:outline-monitor-heart",
        title: "设备管理",
        showLink: false
      }
    },
    {
      path: "/siteweb-omc/device-template",
      name: "SitewebOmcDeviceTemplate",
      component: () => import("@/views/device-template/index.vue"),
      meta: {
        icon: "fluent:calendar-template-24-regular",
        title: "设备模板管理",
        showParent: true
      }
    },
    {
      path: "/siteweb-omc/config-distribute-management",
      name: "SitewebOmcConfigDistributeManagement",
      component: () => import("@/views/config-distribute-management/index.vue"),
      meta: {
        icon: "ep:share",
        title: "配置分发管理",
        showParent: true
      }
    },
    {
      path: "/siteweb-omc/system-category-management",
      name: "SitewebOmcSystemCategoryManagement",
      component: () => import("@/views/system-category-management/index.vue"),
      meta: {
        icon: "ic:outline-category",
        title: "系统分类管理",
        showParent: true
      }
    }
  ]
} satisfies RouteConfigsTable;
