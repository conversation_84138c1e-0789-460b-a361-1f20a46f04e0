<template>
  <div class="device-control-container">
    <!-- 控制数据表格 -->
    <div class="table-container">
      <el-auto-resizer v-loading="loading">
        <template #default="{ height, width }">
          <el-table-v2
            ref="tableRef"
            :columns="columns"
            :data="filteredTableData"
            :width="width"
            :height="Math.max(height, 800)"
            row-key="controlId"
            fixed
          />
        </template>
      </el-auto-resizer>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, computed, onMounted, watch } from "vue";
import {
  ElMessage,
  ElButton,
  ElCheckbox,
  ElIcon,
  ElPopover,
  ElInput,
  ElSelect,
  ElOption
} from "element-plus";
import { Filter } from "@element-plus/icons-vue";
import {
  getDataDictionary,
  getTemplateControlById,
  getSignalListByTemplate
} from "@/api/device-management";
import type { Column, HeaderCellSlotProps } from "element-plus";

interface Props {
  equipmentId: string;
  active: boolean;
  tabIndex?: number;
  template?: any;
  muCategory?: number;
  buttonFlag?: boolean;
  searchText?: string;
}

interface ControlData {
  id: number | null;
  equipmentTemplateId: number;
  controlId: number;
  controlName: string;
  displayIndex: number;
  controlCategory: number;
  controlSeverity: number;
  cmdToken: string;
  timeOut: number | null;
  retry: number | null;
  commandType: number;
  controlType: number;
  dataType: number;
  maxValue: number | null;
  minValue: number | null;
  signalId: number | null;
  controlMeaningsList: any[];
  defaultValue: string;
  description: string;
  enable: boolean;
  visible: boolean;
  moduleNo: number;
  baseTypeId: number | null;
  baseTypeName: string;
  hasInstance: boolean;
}

// 过滤器状态接口
interface FilterState {
  [key: string]: {
    enabled: boolean;
    value?: any;
    options?: Array<{ label: string; value: any }>;
    type: "text" | "select" | "boolean" | "number";
  };
}

const props = withDefaults(defineProps<Props>(), {
  buttonFlag: false,
  muCategory: 0
});

// 响应式数据
const loading = ref(false);
const tableRef = ref();
const tableData = ref<ControlData[]>([]);

// 字典数据
const controlSeverityArr = ref<{ id: string; name: string }[]>([]);
const controlCategoryArr = ref<{ id: string; name: string }[]>([]);
const commandTypeArr = ref<{ id: string; name: string }[]>([]);
const controlTypeArr = ref<{ id: string; name: string }[]>([]);
const dataTypeArr = ref<{ id: string; name: string }[]>([]);
const signalList = ref<{ id: string; name: string }[]>([]);

// 过滤器状态
const filterState = ref<FilterState>({});

// 初始化过滤器状态
const initFilterState = () => {
  filterState.value = {
    controlName: { enabled: false, value: "", type: "text" },
    controlId: { enabled: false, value: "", type: "text" },
    displayIndex: { enabled: false, value: "", type: "number" },
    controlCategory: {
      enabled: false,
      value: "",
      type: "select",
      options: []
    },
    controlSeverity: {
      enabled: false,
      value: "",
      type: "select",
      options: []
    },
    cmdToken: { enabled: false, value: "", type: "text" },
    timeOut: { enabled: false, value: "", type: "number" },
    retry: { enabled: false, value: "", type: "number" },
    commandType: {
      enabled: false,
      value: "",
      type: "select",
      options: []
    },
    controlType: {
      enabled: false,
      value: "",
      type: "select",
      options: []
    },
    dataType: {
      enabled: false,
      value: "",
      type: "select",
      options: []
    },
    maxValue: { enabled: false, value: "", type: "number" },
    minValue: { enabled: false, value: "", type: "number" },
    signalId: {
      enabled: false,
      value: "",
      type: "select",
      options: []
    },
    defaultValue: { enabled: false, value: "", type: "text" },
    description: { enabled: false, value: "", type: "text" },
    enable: {
      enabled: false,
      value: "",
      type: "select",
      options: [
        { label: "是", value: true },
        { label: "否", value: false }
      ]
    },
    visible: {
      enabled: false,
      value: "",
      type: "select",
      options: [
        { label: "是", value: true },
        { label: "否", value: false }
      ]
    },
    moduleNo: { enabled: false, value: "", type: "number" },
    baseTypeName: { enabled: false, value: "", type: "text" },
    controlMeaningsList: { enabled: false, value: "", type: "text" }
  };
};

// 创建过滤器头部组件
const createFilterHeader = (column: any) => {
  const filterKey = column.dataKey;

  return (props: HeaderCellSlotProps) => {
    const popoverRef = ref();

    const onFilter = () => {
      popoverRef.value?.hide();
    };

    const onReset = () => {
      if (filterState.value[filterKey]) {
        filterState.value[filterKey].enabled = false;
        filterState.value[filterKey].value =
          filterState.value[filterKey].type === "select"
            ? ""
            : filterState.value[filterKey].type === "number"
              ? ""
              : "";
      }
    };

    const renderFilterControl = () => {
      const filter = filterState.value[filterKey];
      if (!filter) return null;

      switch (filter.type) {
        case "text":
          return (
            <>
              <ElCheckbox v-model={filter.enabled}>启用过滤</ElCheckbox>
              <ElInput
                v-model={filter.value}
                placeholder="输入过滤条件"
                size="small"
                style="margin-top: 8px"
                disabled={!filter.enabled}
              />
            </>
          );
        case "number":
          return (
            <>
              <ElCheckbox v-model={filter.enabled}>启用过滤</ElCheckbox>
              <ElInput
                v-model={filter.value}
                placeholder="输入数值"
                size="small"
                style="margin-top: 8px"
                disabled={!filter.enabled}
                type="number"
              />
            </>
          );
        case "select":
          return (
            <>
              <ElCheckbox v-model={filter.enabled}>启用过滤</ElCheckbox>
              <ElSelect
                v-model={filter.value}
                placeholder="选择过滤条件"
                size="small"
                style="margin-top: 8px"
                disabled={!filter.enabled}
                clearable
              >
                {filter.options?.map(option => (
                  <ElOption
                    key={option.value}
                    label={option.label}
                    value={option.value}
                  />
                ))}
              </ElSelect>
            </>
          );
        default:
          return null;
      }
    };

    return (
      <div class="flex items-center justify-center">
        <span class="mr-2 text-xs">{props.column.title}</span>
        <ElPopover ref={popoverRef} trigger="click" width={200}>
          {{
            default: () => (
              <div class="filter-wrapper">
                <div class="filter-group">{renderFilterControl()}</div>
                <div class="el-table-v2__demo-filter">
                  <ElButton text onClick={onFilter}>
                    确认
                  </ElButton>
                  <ElButton text onClick={onReset}>
                    重置
                  </ElButton>
                </div>
              </div>
            ),
            reference: () => (
              <ElIcon class="cursor-pointer">
                <Filter />
              </ElIcon>
            )
          }}
        </ElPopover>
      </div>
    );
  };
};

// 创建标签显示单元格
const createTagCell = (dataKey: string, options?: any[]) => {
  return ({ rowData }: { rowData: ControlData }) => {
    const cellStyle = getCellStyle(rowData);
    let displayValue = rowData[dataKey as keyof ControlData];

    // 处理布尔值显示 - 有效和可见字段
    if (dataKey === "enable" || dataKey === "visible") {
      const isTrue = displayValue as boolean;
      return (
        <div style={cellStyle}>
          <span class={isTrue ? "text-green-600" : "text-red-600"}>
            {isTrue ? "是" : "否"}
          </span>
        </div>
      );
    }

    // 处理各种分类字段
    if (options && typeof displayValue === "number") {
      const option = options.find(item => parseInt(item.id) === displayValue);
      const text = option?.name || displayValue;
      return (
        <div style={cellStyle}>
          <span>{text}</span>
        </div>
      );
    }

    // 处理关联信号
    if (dataKey === "signalId") {
      if (displayValue && signalList.value.length > 0) {
        const signal = signalList.value.find(
          item => parseInt(item.id) === displayValue
        );
        const text = signal?.name || displayValue;
        return (
          <div style={cellStyle}>
            <span>{text}</span>
          </div>
        );
      }
      return (
        <div style={cellStyle}>
          <span class="text-gray-400">-</span>
        </div>
      );
    }

    // 默认处理
    return <div style={cellStyle}>{displayValue || ""}</div>;
  };
};

// 创建参数含义显示单元格
const createControlMeaningsCell = () => {
  return ({ rowData }: { rowData: ControlData }) => {
    const cellStyle = getCellStyle(rowData);

    return (
      <div style={cellStyle}>
        {rowData.controlMeaningsList &&
        rowData.controlMeaningsList.length > 0 ? (
          <div class="meanings-container">
            <span class="meanings-text">
              {rowData.controlMeaningsList
                .map((item: any) => item.parameterValue)
                .join("/")}
            </span>
          </div>
        ) : (
          <span class="placeholder-text">未配置参数含义</span>
        )}
      </div>
    );
  };
};

// 创建只读显示单元格
const createReadOnlyCell = (dataKey: string, options?: any[]) => {
  return ({ rowData }: { rowData: ControlData }) => {
    const cellStyle = getCellStyle(rowData);
    let displayValue = rowData[dataKey as keyof ControlData];

    // 如果有选项数组，转换显示值
    if (options && typeof displayValue === "number") {
      const option = options.find(item => parseInt(item.id) === displayValue);
      displayValue = option?.name || displayValue;
    }

    return <div style={cellStyle}>{displayValue || ""}</div>;
  };
};

// 定义表格列
const columns = computed<Column<ControlData>[]>(() => {
  const baseColumns: Column<ControlData>[] = [
    // 控制名称
    {
      key: "controlName",
      dataKey: "controlName",
      title: "名称",
      width: 150,
      fixed: "left",
      cellRenderer: createReadOnlyCell("controlName"),
      headerCellRenderer: createFilterHeader({ dataKey: "controlName" })
    },
    // 控制ID
    {
      key: "controlId",
      dataKey: "controlId",
      title: "控制ID",
      width: 100,
      fixed: "left",
      cellRenderer: createReadOnlyCell("controlId"),
      headerCellRenderer: createFilterHeader({ dataKey: "controlId" })
    },
    // 显示顺序
    {
      key: "displayIndex",
      dataKey: "displayIndex",
      title: "显示顺序",
      width: 100,
      cellRenderer: createReadOnlyCell("displayIndex"),
      headerCellRenderer: createFilterHeader({ dataKey: "displayIndex" })
    },
    // 命令种类
    {
      key: "controlCategory",
      dataKey: "controlCategory",
      title: "命令种类",
      width: 120,
      cellRenderer: createTagCell("controlCategory", controlCategoryArr.value),
      headerCellRenderer: createFilterHeader({ dataKey: "controlCategory" })
    },
    // 重要度
    {
      key: "controlSeverity",
      dataKey: "controlSeverity",
      title: "重要度",
      width: 100,
      cellRenderer: createTagCell("controlSeverity", controlSeverityArr.value),
      headerCellRenderer: createFilterHeader({ dataKey: "controlSeverity" })
    },
    // 命令字符串
    {
      key: "cmdToken",
      dataKey: "cmdToken",
      title: "命令字符串",
      width: 120,
      cellRenderer: createReadOnlyCell("cmdToken"),
      headerCellRenderer: createFilterHeader({ dataKey: "cmdToken" })
    },
    // 超时
    {
      key: "timeOut",
      dataKey: "timeOut",
      title: "超时(秒)",
      width: 100,
      cellRenderer: createReadOnlyCell("timeOut"),
      headerCellRenderer: createFilterHeader({ dataKey: "timeOut" })
    },
    // 重试
    {
      key: "retry",
      dataKey: "retry",
      title: "重试",
      width: 80,
      cellRenderer: createReadOnlyCell("retry"),
      headerCellRenderer: createFilterHeader({ dataKey: "retry" })
    },
    // 控制命令类型
    {
      key: "commandType",
      dataKey: "commandType",
      title: "控制命令类型",
      width: 140,
      cellRenderer: createTagCell("commandType", commandTypeArr.value),
      headerCellRenderer: createFilterHeader({ dataKey: "commandType" })
    },
    // 控件分类
    {
      key: "controlType",
      dataKey: "controlType",
      title: "控件分类",
      width: 120,
      cellRenderer: createTagCell("controlType", controlTypeArr.value),
      headerCellRenderer: createFilterHeader({ dataKey: "controlType" })
    },
    // 数据类型
    {
      key: "dataType",
      dataKey: "dataType",
      title: "数据类型",
      width: 120,
      cellRenderer: createTagCell("dataType", dataTypeArr.value),
      headerCellRenderer: createFilterHeader({ dataKey: "dataType" })
    },
    // 参数上限
    {
      key: "maxValue",
      dataKey: "maxValue",
      title: "参数上限",
      width: 100,
      cellRenderer: createReadOnlyCell("maxValue"),
      headerCellRenderer: createFilterHeader({ dataKey: "maxValue" })
    },
    // 参数下限
    {
      key: "minValue",
      dataKey: "minValue",
      title: "参数下限",
      width: 100,
      cellRenderer: createReadOnlyCell("minValue"),
      headerCellRenderer: createFilterHeader({ dataKey: "minValue" })
    },
    // 关联信号
    {
      key: "signalId",
      dataKey: "signalId",
      title: "关联信号",
      width: 140,
      cellRenderer: createTagCell("signalId"),
      headerCellRenderer: createFilterHeader({ dataKey: "signalId" })
    },
    // 参数含义
    {
      key: "controlMeaningsList",
      dataKey: "controlMeaningsList",
      title: "参数含义",
      width: 150,
      cellRenderer: createControlMeaningsCell(),
      headerCellRenderer: createFilterHeader({ dataKey: "controlMeaningsList" })
    },
    // 默认值
    {
      key: "defaultValue",
      dataKey: "defaultValue",
      title: "默认值",
      width: 100,
      cellRenderer: createReadOnlyCell("defaultValue"),
      headerCellRenderer: createFilterHeader({ dataKey: "defaultValue" })
    },
    // 说明
    {
      key: "description",
      dataKey: "description",
      title: "说明",
      width: 120,
      cellRenderer: createReadOnlyCell("description"),
      headerCellRenderer: createFilterHeader({ dataKey: "description" })
    },
    // 有效
    {
      key: "enable",
      dataKey: "enable",
      title: "有效",
      width: 80,
      cellRenderer: createTagCell("enable"),
      headerCellRenderer: createFilterHeader({ dataKey: "enable" })
    },
    // 可见
    {
      key: "visible",
      dataKey: "visible",
      title: "可见",
      width: 80,
      cellRenderer: createTagCell("visible"),
      headerCellRenderer: createFilterHeader({ dataKey: "visible" })
    },
    // 所属模块
    {
      key: "moduleNo",
      dataKey: "moduleNo",
      title: "所属模块",
      width: 100,
      cellRenderer: createReadOnlyCell("moduleNo"),
      headerCellRenderer: createFilterHeader({ dataKey: "moduleNo" })
    },
    // 基类控制
    {
      key: "baseTypeName",
      dataKey: "baseTypeName",
      title: "基类控制",
      width: 150,
      cellRenderer: createReadOnlyCell("baseTypeName"),
      headerCellRenderer: createFilterHeader({ dataKey: "baseTypeName" })
    }
  ];

  return baseColumns;
});

// 应用过滤器的数据
const filteredTableData = computed(() => {
  let data = tableData.value;

  // 首先应用搜索文本过滤
  if (props.searchText && props.searchText.trim() !== "") {
    const searchTerm = props.searchText.toLowerCase();
    data = data.filter(item => {
      return (
        (item.controlName &&
          item.controlName.toLowerCase().includes(searchTerm)) ||
        (item.controlId && item.controlId.toString().includes(searchTerm)) ||
        (item.description &&
          item.description.toLowerCase().includes(searchTerm))
      );
    });
  }

  // 应用列过滤器
  Object.entries(filterState.value).forEach(([key, filter]) => {
    if (
      filter.enabled &&
      filter.value !== "" &&
      filter.value !== null &&
      filter.value !== undefined
    ) {
      data = data.filter(item => {
        const itemValue = item[key as keyof ControlData];

        switch (filter.type) {
          case "text":
            return (
              itemValue &&
              itemValue
                .toString()
                .toLowerCase()
                .includes(filter.value.toLowerCase())
            );
          case "number":
            return (
              itemValue !== null &&
              itemValue !== undefined &&
              itemValue.toString() === filter.value.toString()
            );
          case "select":
            return itemValue === filter.value;
          default:
            return true;
        }
      });
    }
  });

  return data;
});

// 方法
const initData = async () => {
  console.log("开始初始化控制数据，参数检查:", {
    equipmentId: props.equipmentId,
    template: props.template,
    active: props.active,
    tabIndex: props.tabIndex
  });

  if (!props.equipmentId) {
    console.warn("equipmentId为空，无法初始化数据");
    ElMessage.warning("设备ID为空，无法加载控制数据");
    return;
  }

  loading.value = true;
  try {
    console.log("开始并行加载字典数据、控制列表和信号列表");
    await Promise.all([
      loadDictionaryData(),
      loadControlList(),
      loadSignalList()
    ]);
    console.log("控制数据初始化完成");
  } catch (error) {
    console.error("初始化控制数据失败:", error);
    ElMessage.error("控制数据加载失败: " + (error.message || error));
  } finally {
    loading.value = false;
  }
};

const loadDictionaryData = async () => {
  // 字典数据映射
  const dictMapping = {
    28: { key: "controlSeverityArr", name: "重要度" },
    31: { key: "controlCategoryArr", name: "命令种类" },
    32: { key: "commandTypeArr", name: "控制命令类型" },
    68: { key: "controlTypeArr", name: "控件分类" },
    70: { key: "dataTypeArr", name: "数据类型" }
  };

  try {
    // 并行获取所有字典数据
    const promises = Object.entries(dictMapping).map(
      async ([entryId, config]) => {
        const result = await getDataDictionary(Number(entryId));

        if (result.code === 0 && result.data) {
          // 转换数据格式
          const formattedData = result.data.map((item: any) => ({
            id: item.itemId?.toString() || "",
            name: item.itemValue || ""
          }));

          return { key: config.key, data: formattedData };
        } else {
          console.warn(
            `获取${config.name}字典数据失败:`,
            result.msg || result.message
          );
          return { key: config.key, data: [] };
        }
      }
    );

    const results = await Promise.all(promises);

    // 将数据赋值给对应的响应式变量
    results.forEach(result => {
      switch (result.key) {
        case "controlSeverityArr":
          controlSeverityArr.value = result.data;
          // 更新过滤器选项
          if (filterState.value.controlSeverity) {
            filterState.value.controlSeverity.options = result.data.map(
              item => ({
                label: item.name,
                value: parseInt(item.id)
              })
            );
          }
          break;
        case "controlCategoryArr":
          controlCategoryArr.value = result.data;
          if (filterState.value.controlCategory) {
            filterState.value.controlCategory.options = result.data.map(
              item => ({
                label: item.name,
                value: parseInt(item.id)
              })
            );
          }
          break;
        case "commandTypeArr":
          commandTypeArr.value = result.data;
          if (filterState.value.commandType) {
            filterState.value.commandType.options = result.data.map(item => ({
              label: item.name,
              value: parseInt(item.id)
            }));
          }
          break;
        case "controlTypeArr":
          controlTypeArr.value = result.data;
          if (filterState.value.controlType) {
            filterState.value.controlType.options = result.data.map(item => ({
              label: item.name,
              value: parseInt(item.id)
            }));
          }
          break;
        case "dataTypeArr":
          dataTypeArr.value = result.data;
          if (filterState.value.dataType) {
            filterState.value.dataType.options = result.data.map(item => ({
              label: item.name,
              value: parseInt(item.id)
            }));
          }
          break;
      }
    });
  } catch (error) {
    console.error("获取字典数据失败:", error);
    ElMessage.warning("部分字典数据加载失败，可能影响下拉选项显示");

    // 设置默认数据
    controlSeverityArr.value = [
      { id: "1", name: "普通" },
      { id: "2", name: "重要" },
      { id: "3", name: "紧急" }
    ];

    controlCategoryArr.value = [
      { id: "1", name: "遥控" },
      { id: "2", name: "遥调" }
    ];

    commandTypeArr.value = [
      { id: "1", name: "开关控制" },
      { id: "2", name: "数值控制" }
    ];

    controlTypeArr.value = [
      { id: "1", name: "按钮" },
      { id: "2", name: "滑块" },
      { id: "3", name: "输入框" }
    ];

    dataTypeArr.value = [
      { id: "1", name: "REAL" },
      { id: "2", name: "INT" },
      { id: "3", name: "BOOL" }
    ];
  }
};

const loadSignalList = async () => {
  const templateId = props.template?.equipmentTemplateId || props.template?.id;
  if (!templateId) {
    console.warn("模板ID未提供，无法加载信号列表");
    return;
  }

  try {
    console.log("正在获取信号列表，模板ID:", templateId);

    const result = await getSignalListByTemplate(templateId);

    if (result.code === 0 && result.data) {
      const formattedSignals = result.data.map((item: any) => ({
        id: item.signalId?.toString() || "",
        name: item.signalName || ""
      }));

      signalList.value = formattedSignals;

      // 更新过滤器选项
      if (filterState.value.signalId) {
        filterState.value.signalId.options = formattedSignals.map(signal => ({
          label: signal.name,
          value: parseInt(signal.id)
        }));
      }
    }
  } catch (error) {
    console.error("获取信号列表失败:", error);
    signalList.value = [];
  }
};

const loadControlList = async () => {
  // 优先使用equipmentTemplateId，如果没有则使用id
  const templateId = props.template?.equipmentTemplateId || props.template?.id;
  if (!templateId) {
    console.warn("模板ID未提供，无法加载控制列表. template:", props.template);
    tableData.value = [];
    return;
  }

  try {
    console.log("正在获取控制列表，模板ID:", templateId);

    const result = await getTemplateControlById(templateId);

    console.log("控制列表API响应:", result);

    // 根据记忆，code: 0表示成功状态
    if (result.code === 0 && result.data) {
      const processedData = result.data.map((item: any) => {
        // 转换数据结构，处理null值和类型转换
        const controlData: ControlData = {
          id: item.id,
          equipmentTemplateId: item.equipmentTemplateId,
          controlId: item.controlId,
          controlName: item.controlName || "",
          displayIndex: item.displayIndex || 0,
          controlCategory: item.controlCategory || 0,
          controlSeverity: item.controlSeverity || 0,
          cmdToken: item.cmdToken || "",
          timeOut: item.timeOut,
          retry: item.retry,
          commandType: item.commandType || 0,
          controlType: item.controlType || 0,
          dataType: item.dataType || 0,
          maxValue: item.maxValue,
          minValue: item.minValue,
          signalId: item.signalId,
          controlMeaningsList: item.controlMeaningsList || [],
          defaultValue: item.defaultValue || "",
          description: item.description || "",
          enable: item.enable !== false, // 默认true
          visible: item.visible !== false, // 默认true
          moduleNo: item.moduleNo || 0,
          baseTypeId: item.baseTypeId,
          baseTypeName: item.baseTypeName || "",
          hasInstance: false // 初始化为false
        };

        return controlData;
      });

      tableData.value = processedData;
    } else {
      ElMessage.error(
        "获取控制列表失败: " + (result.msg || result.message || "未知错误")
      );
      tableData.value = [];
    }
  } catch (error) {
    console.error("获取控制列表失败:", error);
    ElMessage.error("获取控制列表失败，请检查网络连接");
    tableData.value = [];
  }
};

const getCellStyle = (row: ControlData) => {
  const style: any = {};

  // 已设置实例显示绿色背景
  if (row.hasInstance) {
    style.backgroundColor = "#B0F47D";
  }

  return style;
};

// 监听props变化
watch(
  () => props.active,
  newVal => {
    console.log(
      "DeviceControl active变化:",
      newVal,
      "tabIndex:",
      props.tabIndex,
      "template:",
      props.template
    );
    if (newVal && props.tabIndex === 3) {
      console.log("开始初始化控制数据 - active变化");
      initData();
    }
  },
  { immediate: true }
);

watch(
  () => props.tabIndex,
  newVal => {
    console.log(
      "DeviceControl tabIndex变化:",
      newVal,
      "active:",
      props.active,
      "template:",
      props.template
    );
    if (newVal === 3 && props.active) {
      console.log("开始初始化控制数据 - tabIndex变化");
      initData();
    }
  },
  { immediate: true }
);

watch(
  () => props.template,
  newVal => {
    console.log(
      "DeviceControl template变化:",
      newVal,
      "active:",
      props.active,
      "tabIndex:",
      props.tabIndex
    );
    const templateId = newVal?.equipmentTemplateId || newVal?.id;
    if (templateId && props.active && props.tabIndex === 3) {
      console.log("开始初始化控制数据 - template变化");
      initData();
    }
  }
);

// 综合监听：当所有必要条件都满足时，初始化数据
watch(
  [
    () => props.active,
    () => props.tabIndex,
    () => props.template,
    () => props.equipmentId
  ],
  ([active, tabIndex, template, equipmentId]) => {
    console.log("综合监听触发:", { active, tabIndex, template, equipmentId });
    const templateId = template?.equipmentTemplateId || template?.id;
    if (active && tabIndex === 3 && templateId && equipmentId) {
      console.log("所有条件满足，开始初始化控制数据");
      initData();
    }
  }
);

// 组件挂载
onMounted(() => {
  console.log("DeviceControl组件挂载:", {
    active: props.active,
    tabIndex: props.tabIndex,
    template: props.template,
    equipmentId: props.equipmentId
  });

  // 初始化过滤器状态
  initFilterState();

  if (props.active && props.tabIndex === 3) {
    console.log("开始初始化控制数据 - 组件挂载");
    initData();
  }
});
</script>

<style scoped>
.device-control-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-container {
  flex: 1;
  overflow: auto;
}

:deep(.el-table .el-table__cell) {
  padding: 4px 0;
}

/* 过滤器样式 */
.el-table-v2__demo-filter {
  border-top: var(--el-border);
  margin: 12px -12px -12px;
  padding: 0 12px;
  display: flex;
  justify-content: space-between;
}

.filter-wrapper {
  padding: 8px 0;
}

.filter-group {
  margin-bottom: 8px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.text-xs {
  font-size: 12px;
}

.mr-2 {
  margin-right: 8px;
}

.cursor-pointer {
  cursor: pointer;
}

.text-gray-400 {
  color: #9ca3af;
}

/* 文本颜色样式 */
.text-green-600 {
  color: #16a085;
  font-weight: 500;
}

.text-red-600 {
  color: #e74c3c;
  font-weight: 500;
}

.text-gray-400 {
  color: #ccc;
}

.mr-1 {
  margin-right: 4px;
}

.mb-1 {
  margin-bottom: 2px;
}

.meanings-text {
  font-size: 12px;
  line-height: 1.4;
  word-break: break-all;
  display: block;
  max-height: 40px;
  overflow: hidden;
}

.placeholder-text {
  color: #999;
  font-style: italic;
}

.meanings-container {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  max-width: 140px;
}
</style>
