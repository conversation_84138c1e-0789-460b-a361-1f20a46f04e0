<template>
  <div class="device-event-container">
    <!-- 事件数据表格 -->
    <div class="table-container">
      <el-auto-resizer v-loading="loading">
        <template #default="{ height, width }">
          <el-table-v2
            ref="tableRef"
            :columns="columns"
            :data="filteredTableData"
            :width="width"
            :height="Math.max(height, 800)"
            row-key="eventId"
            fixed
          />
        </template>
      </el-auto-resizer>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, computed, onMounted, watch } from "vue";
import {
  ElMessage,
  ElButton,
  ElCheckbox,
  ElIcon,
  ElPopover,
  ElInput,
  ElSelect,
  ElOption
} from "element-plus";
import { Filter } from "@element-plus/icons-vue";
import {
  getDataDictionary,
  getTemplateEventById,
  getSignalListByTemplate
} from "@/api/device-management";
import type { Column, HeaderCellSlotProps } from "element-plus";

interface Props {
  equipmentId: string;
  active: boolean;
  tabIndex?: number;
  template?: any;
  muCategory?: number;
  buttonFlag?: boolean;
  searchText?: string;
}

interface EventData {
  id: number | null;
  equipmentTemplateId: number;
  eventId: number;
  eventName: string;
  displayIndex: number;
  eventCategory: number;
  startType: number;
  endType: number;
  eventConditionListLabel: string;
  startExpression: string;
  suppressExpression: string;
  signalId: number | null;
  description: string;
  enable: boolean;
  visible: boolean;
  moduleNo: number;
  turnover: number | null;
  eventConditionList: any[];
  hasInstance: boolean;
}

// 过滤器状态接口
interface FilterState {
  [key: string]: {
    enabled: boolean;
    value?: any;
    options?: Array<{ label: string; value: any }>;
    type: "text" | "select" | "boolean" | "number";
  };
}

const props = withDefaults(defineProps<Props>(), {
  buttonFlag: false,
  muCategory: 0
});

// 响应式数据
const loading = ref(false);
const tableRef = ref();
const tableData = ref<EventData[]>([]);

// 字典数据
const eventCategoryArr = ref<{ id: string; name: string }[]>([]);
const startTypeArr = ref<{ id: string; name: string }[]>([]);
const endTypeArr = ref<{ id: string; name: string }[]>([]);
const signalList = ref<{ id: string; name: string }[]>([]);

// 过滤器状态
const filterState = ref<FilterState>({});

// 初始化过滤器状态
const initFilterState = () => {
  filterState.value = {
    eventName: { enabled: false, value: "", type: "text" },
    eventId: { enabled: false, value: "", type: "text" },
    displayIndex: { enabled: false, value: "", type: "number" },
    eventCategory: {
      enabled: false,
      value: "",
      type: "select",
      options: []
    },
    startType: {
      enabled: false,
      value: "",
      type: "select",
      options: []
    },
    endType: {
      enabled: false,
      value: "",
      type: "select",
      options: []
    },
    startExpression: { enabled: false, value: "", type: "text" },
    suppressExpression: { enabled: false, value: "", type: "text" },
    signalId: {
      enabled: false,
      value: "",
      type: "select",
      options: []
    },
    description: { enabled: false, value: "", type: "text" },
    enable: {
      enabled: false,
      value: "",
      type: "select",
      options: [
        { label: "是", value: true },
        { label: "否", value: false }
      ]
    },
    visible: {
      enabled: false,
      value: "",
      type: "select",
      options: [
        { label: "是", value: true },
        { label: "否", value: false }
      ]
    },
    moduleNo: { enabled: false, value: "", type: "number" },
    turnover: { enabled: false, value: "", type: "number" },
    eventConditionListLabel: { enabled: false, value: "", type: "text" }
  };
};

// 创建过滤器头部组件
const createFilterHeader = (column: any) => {
  const filterKey = column.dataKey;

  return (props: HeaderCellSlotProps) => {
    const popoverRef = ref();

    const onFilter = () => {
      popoverRef.value?.hide();
    };

    const onReset = () => {
      if (filterState.value[filterKey]) {
        filterState.value[filterKey].enabled = false;
        filterState.value[filterKey].value =
          filterState.value[filterKey].type === "select"
            ? ""
            : filterState.value[filterKey].type === "number"
              ? ""
              : "";
      }
    };

    const renderFilterControl = () => {
      const filter = filterState.value[filterKey];
      if (!filter) return null;

      switch (filter.type) {
        case "text":
          return (
            <>
              <ElCheckbox v-model={filter.enabled}>启用过滤</ElCheckbox>
              <ElInput
                v-model={filter.value}
                placeholder="输入过滤条件"
                size="small"
                style="margin-top: 8px"
                disabled={!filter.enabled}
              />
            </>
          );
        case "number":
          return (
            <>
              <ElCheckbox v-model={filter.enabled}>启用过滤</ElCheckbox>
              <ElInput
                v-model={filter.value}
                placeholder="输入数值"
                size="small"
                style="margin-top: 8px"
                disabled={!filter.enabled}
                type="number"
              />
            </>
          );
        case "select":
          return (
            <>
              <ElCheckbox v-model={filter.enabled}>启用过滤</ElCheckbox>
              <ElSelect
                v-model={filter.value}
                placeholder="选择过滤条件"
                size="small"
                style="margin-top: 8px"
                disabled={!filter.enabled}
                clearable
              >
                {filter.options?.map(option => (
                  <ElOption
                    key={option.value}
                    label={option.label}
                    value={option.value}
                  />
                ))}
              </ElSelect>
            </>
          );
        default:
          return null;
      }
    };

    return (
      <div class="flex items-center justify-center">
        <span class="mr-2 text-xs">{props.column.title}</span>
        <ElPopover ref={popoverRef} trigger="click" width={200}>
          {{
            default: () => (
              <div class="filter-wrapper">
                <div class="filter-group">{renderFilterControl()}</div>
                <div class="el-table-v2__demo-filter">
                  <ElButton text onClick={onFilter}>
                    确认
                  </ElButton>
                  <ElButton text onClick={onReset}>
                    重置
                  </ElButton>
                </div>
              </div>
            ),
            reference: () => (
              <ElIcon class="cursor-pointer">
                <Filter />
              </ElIcon>
            )
          }}
        </ElPopover>
      </div>
    );
  };
};

// 创建标签显示单元格
const createTagCell = (dataKey: string, options?: any[]) => {
  return ({ rowData }: { rowData: EventData }) => {
    const cellStyle = getCellStyle(rowData);
    let displayValue = rowData[dataKey as keyof EventData];

    // 处理布尔值显示 - 有效和可见字段
    if (dataKey === "enable" || dataKey === "visible") {
      const isTrue = displayValue as boolean;
      return (
        <div style={cellStyle}>
          <span class={isTrue ? "text-green-600" : "text-red-600"}>
            {isTrue ? "是" : "否"}
          </span>
        </div>
      );
    }

    // 处理事件类别
    if (dataKey === "eventCategory") {
      if (options && typeof displayValue === "number") {
        const option = options.find(item => parseInt(item.id) === displayValue);
        const text = option?.name || displayValue;
        return (
          <div style={cellStyle}>
            <span>{text}</span>
          </div>
        );
      }
    }

    // 处理启动类型
    if (dataKey === "startType") {
      if (options && typeof displayValue === "number") {
        const option = options.find(item => parseInt(item.id) === displayValue);
        const text = option?.name || displayValue;
        return (
          <div style={cellStyle}>
            <span>{text}</span>
          </div>
        );
      }
    }

    // 处理结束类型
    if (dataKey === "endType") {
      if (options && typeof displayValue === "number") {
        const option = options.find(item => parseInt(item.id) === displayValue);
        const text = option?.name || displayValue;
        return (
          <div style={cellStyle}>
            <span>{text}</span>
          </div>
        );
      }
    }

    // 处理关联信号
    if (dataKey === "signalId") {
      if (displayValue && signalList.value.length > 0) {
        const signal = signalList.value.find(
          item => parseInt(item.id) === displayValue
        );
        const text = signal?.name || displayValue;
        return (
          <div style={cellStyle}>
            <span>{text}</span>
          </div>
        );
      }
      return (
        <div style={cellStyle}>
          <span class="text-gray-400">-</span>
        </div>
      );
    }

    // 默认处理
    return <div style={cellStyle}>{displayValue || ""}</div>;
  };
};

// 创建只读显示单元格
const createReadOnlyCell = (dataKey: string, options?: any[]) => {
  return ({ rowData }: { rowData: EventData }) => {
    const cellStyle = getCellStyle(rowData);
    let displayValue = rowData[dataKey as keyof EventData];

    // 如果有选项数组，转换显示值
    if (options && typeof displayValue === "number") {
      const option = options.find(item => parseInt(item.id) === displayValue);
      displayValue = option?.name || displayValue;
    }

    return <div style={cellStyle}>{displayValue || ""}</div>;
  };
};

// 定义表格列
const columns = computed<Column<EventData>[]>(() => {
  const baseColumns: Column<EventData>[] = [
    // 事件名称
    {
      key: "eventName",
      dataKey: "eventName",
      title: "事件名称",
      width: 150,
      fixed: "left",
      cellRenderer: createReadOnlyCell("eventName"),
      headerCellRenderer: createFilterHeader({ dataKey: "eventName" })
    },
    // 事件ID
    {
      key: "eventId",
      dataKey: "eventId",
      title: "事件ID",
      width: 100,
      fixed: "left",
      cellRenderer: createReadOnlyCell("eventId"),
      headerCellRenderer: createFilterHeader({ dataKey: "eventId" })
    },
    // 显示顺序
    {
      key: "displayIndex",
      dataKey: "displayIndex",
      title: "显示顺序",
      width: 100,
      cellRenderer: createReadOnlyCell("displayIndex"),
      headerCellRenderer: createFilterHeader({ dataKey: "displayIndex" })
    },
    // 事件类别
    {
      key: "eventCategory",
      dataKey: "eventCategory",
      title: "事件类别",
      width: 120,
      cellRenderer: createTagCell("eventCategory", eventCategoryArr.value),
      headerCellRenderer: createFilterHeader({ dataKey: "eventCategory" })
    },
    // 启动类型
    {
      key: "startType",
      dataKey: "startType",
      title: "启动类型",
      width: 120,
      cellRenderer: createTagCell("startType", startTypeArr.value),
      headerCellRenderer: createFilterHeader({ dataKey: "startType" })
    },
    // 结束类型
    {
      key: "endType",
      dataKey: "endType",
      title: "结束类型",
      width: 120,
      cellRenderer: createTagCell("endType", endTypeArr.value),
      headerCellRenderer: createFilterHeader({ dataKey: "endType" })
    },
    // 条件
    {
      key: "eventConditionListLabel",
      dataKey: "eventConditionListLabel",
      title: "条件",
      width: 120,
      cellRenderer: createReadOnlyCell("eventConditionListLabel"),
      headerCellRenderer: createFilterHeader({
        dataKey: "eventConditionListLabel"
      })
    },
    // 启动表达式
    {
      key: "startExpression",
      dataKey: "startExpression",
      title: "启动表达式",
      width: 150,
      cellRenderer: createReadOnlyCell("startExpression"),
      headerCellRenderer: createFilterHeader({ dataKey: "startExpression" })
    },
    // 抑制表达式
    {
      key: "suppressExpression",
      dataKey: "suppressExpression",
      title: "抑制表达式",
      width: 150,
      cellRenderer: createReadOnlyCell("suppressExpression"),
      headerCellRenderer: createFilterHeader({ dataKey: "suppressExpression" })
    },
    // 关联信号
    {
      key: "signalId",
      dataKey: "signalId",
      title: "关联信号",
      width: 140,
      cellRenderer: createTagCell("signalId"),
      headerCellRenderer: createFilterHeader({ dataKey: "signalId" })
    },
    // 说明
    {
      key: "description",
      dataKey: "description",
      title: "说明",
      width: 120,
      cellRenderer: createReadOnlyCell("description"),
      headerCellRenderer: createFilterHeader({ dataKey: "description" })
    },
    // 有效
    {
      key: "enable",
      dataKey: "enable",
      title: "有效",
      width: 80,
      cellRenderer: createTagCell("enable"),
      headerCellRenderer: createFilterHeader({ dataKey: "enable" })
    },
    // 可见
    {
      key: "visible",
      dataKey: "visible",
      title: "可见",
      width: 80,
      cellRenderer: createTagCell("visible"),
      headerCellRenderer: createFilterHeader({ dataKey: "visible" })
    },
    // 模块
    {
      key: "moduleNo",
      dataKey: "moduleNo",
      title: "模块",
      width: 100,
      cellRenderer: createReadOnlyCell("moduleNo"),
      headerCellRenderer: createFilterHeader({ dataKey: "moduleNo" })
    },
    // 翻转时间
    {
      key: "turnover",
      dataKey: "turnover",
      title: "翻转时间",
      width: 120,
      cellRenderer: createReadOnlyCell("turnover"),
      headerCellRenderer: createFilterHeader({ dataKey: "turnover" })
    }
  ];

  return baseColumns;
});

// 应用过滤器的数据
const filteredTableData = computed(() => {
  let data = tableData.value;

  // 首先应用搜索文本过滤
  if (props.searchText && props.searchText.trim() !== "") {
    const searchTerm = props.searchText.toLowerCase();
    data = data.filter(item => {
      return (
        (item.eventName && item.eventName.toLowerCase().includes(searchTerm)) ||
        (item.eventId && item.eventId.toString().includes(searchTerm)) ||
        (item.description &&
          item.description.toLowerCase().includes(searchTerm))
      );
    });
  }

  // 应用列过滤器
  Object.entries(filterState.value).forEach(([key, filter]) => {
    if (
      filter.enabled &&
      filter.value !== "" &&
      filter.value !== null &&
      filter.value !== undefined
    ) {
      data = data.filter(item => {
        const itemValue = item[key as keyof EventData];

        switch (filter.type) {
          case "text":
            return (
              itemValue &&
              itemValue
                .toString()
                .toLowerCase()
                .includes(filter.value.toLowerCase())
            );
          case "number":
            return (
              itemValue !== null &&
              itemValue !== undefined &&
              itemValue.toString() === filter.value.toString()
            );
          case "select":
            return itemValue === filter.value;
          default:
            return true;
        }
      });
    }
  });

  return data;
});

// 方法
const initData = async () => {
  console.log("开始初始化事件数据，参数检查:", {
    equipmentId: props.equipmentId,
    template: props.template,
    active: props.active,
    tabIndex: props.tabIndex
  });

  if (!props.equipmentId) {
    console.warn("equipmentId为空，无法初始化数据");
    ElMessage.warning("设备ID为空，无法加载事件数据");
    return;
  }

  loading.value = true;
  try {
    console.log("开始并行加载字典数据、事件列表和信号列表");
    await Promise.all([
      loadDictionaryData(),
      loadEventList(),
      loadSignalList()
    ]);
    console.log("事件数据初始化完成");
  } catch (error) {
    console.error("初始化事件数据失败:", error);
    ElMessage.error("事件数据加载失败: " + (error.message || error));
  } finally {
    loading.value = false;
  }
};

const loadDictionaryData = async () => {
  // 字典数据映射
  const dictMapping = {
    24: { key: "eventCategoryArr", name: "事件类别" },
    25: { key: "startTypeArr", name: "启动类型" },
    26: { key: "endTypeArr", name: "结束类型" }
  };

  try {
    // 并行获取所有字典数据
    const promises = Object.entries(dictMapping).map(
      async ([entryId, config]) => {
        const result = await getDataDictionary(Number(entryId));

        if (result.code === 0 && result.data) {
          // 转换数据格式
          const formattedData = result.data.map((item: any) => ({
            id: item.itemId?.toString() || "",
            name: item.itemValue || ""
          }));

          return { key: config.key, data: formattedData };
        } else {
          console.warn(
            `获取${config.name}字典数据失败:`,
            result.msg || result.message
          );
          return { key: config.key, data: [] };
        }
      }
    );

    const results = await Promise.all(promises);

    // 将数据赋值给对应的响应式变量
    results.forEach(result => {
      switch (result.key) {
        case "eventCategoryArr":
          eventCategoryArr.value = result.data;
          // 更新过滤器选项
          if (filterState.value.eventCategory) {
            filterState.value.eventCategory.options = result.data.map(item => ({
              label: item.name,
              value: parseInt(item.id)
            }));
          }
          break;
        case "startTypeArr":
          startTypeArr.value = result.data;
          if (filterState.value.startType) {
            filterState.value.startType.options = result.data.map(item => ({
              label: item.name,
              value: parseInt(item.id)
            }));
          }
          break;
        case "endTypeArr":
          endTypeArr.value = result.data;
          if (filterState.value.endType) {
            filterState.value.endType.options = result.data.map(item => ({
              label: item.name,
              value: parseInt(item.id)
            }));
          }
          break;
      }
    });
  } catch (error) {
    console.error("获取字典数据失败:", error);
    ElMessage.warning("部分字典数据加载失败，可能影响下拉选项显示");

    // 设置默认数据
    eventCategoryArr.value = [
      { id: "1", name: "系统事件" },
      { id: "2", name: "告警事件" },
      { id: "3", name: "操作事件" }
    ];

    startTypeArr.value = [
      { id: "1", name: "条件启动" },
      { id: "2", name: "表达式启动" }
    ];

    endTypeArr.value = [
      { id: "1", name: "自动结束" },
      { id: "2", name: "手动结束" }
    ];
  }
};

const loadSignalList = async () => {
  const templateId = props.template?.equipmentTemplateId || props.template?.id;
  if (!templateId) {
    console.warn("模板ID未提供，无法加载信号列表");
    return;
  }

  try {
    console.log("正在获取信号列表，模板ID:", templateId);

    const result = await getSignalListByTemplate(templateId);

    if (result.code === 0 && result.data) {
      const formattedSignals = result.data.map((item: any) => ({
        id: item.signalId?.toString() || "",
        name: item.signalName || ""
      }));

      signalList.value = formattedSignals;

      // 更新过滤器选项
      if (filterState.value.signalId) {
        filterState.value.signalId.options = formattedSignals.map(signal => ({
          label: signal.name,
          value: parseInt(signal.id)
        }));
      }
    }
  } catch (error) {
    console.error("获取信号列表失败:", error);
    signalList.value = [];
  }
};

const loadEventList = async () => {
  // 优先使用equipmentTemplateId，如果没有则使用id
  const templateId = props.template?.equipmentTemplateId || props.template?.id;
  if (!templateId) {
    console.warn("模板ID未提供，无法加载事件列表. template:", props.template);
    tableData.value = [];
    return;
  }

  try {
    console.log("正在获取事件列表，模板ID:", templateId);

    const result = await getTemplateEventById(templateId);

    console.log("事件列表API响应:", result);

    // 根据记忆，code: 0表示成功状态
    if (result.code === 0 && result.data) {
      const processedData = result.data.map((item: any) => {
        // 处理事件条件标签
        let eventConditionListLabel = "";
        if (item.eventConditionList && item.eventConditionList.length > 0) {
          eventConditionListLabel = item.eventConditionList
            .map((condition: any) => condition.eventConditionId)
            .join("/");
        }

        // 转换数据结构，处理null值和类型转换
        const eventData: EventData = {
          id: item.id,
          equipmentTemplateId: item.equipmentTemplateId,
          eventId: item.eventId,
          eventName: item.eventName || "",
          displayIndex: item.displayIndex || 0,
          eventCategory: item.eventCategory || 0,
          startType: item.startType || 0,
          endType: item.endType || 0,
          eventConditionListLabel: eventConditionListLabel,
          startExpression: item.startExpression || "",
          suppressExpression: item.suppressExpression || "",
          signalId: item.signalId,
          description: item.description || "",
          enable: item.enable !== false, // 默认true
          visible: item.visible !== false, // 默认true
          moduleNo: item.moduleNo || 0,
          turnover: item.turnover,
          eventConditionList: item.eventConditionList || [],
          hasInstance: false // 初始化为false
        };

        return eventData;
      });

      tableData.value = processedData;
    } else {
      ElMessage.error(
        "获取事件列表失败: " + (result.msg || result.message || "未知错误")
      );
      tableData.value = [];
    }
  } catch (error) {
    console.error("获取事件列表失败:", error);
    ElMessage.error("获取事件列表失败，请检查网络连接");
    tableData.value = [];
  }
};

const getCellStyle = (row: EventData) => {
  const style: any = {};

  // 已设置实例显示绿色背景
  if (row.hasInstance) {
    style.backgroundColor = "#B0F47D";
  }

  return style;
};

// 监听props变化
watch(
  () => props.active,
  newVal => {
    console.log(
      "DeviceEvent active变化:",
      newVal,
      "tabIndex:",
      props.tabIndex,
      "template:",
      props.template
    );
    if (newVal && props.tabIndex === 2) {
      console.log("开始初始化事件数据 - active变化");
      initData();
    }
  },
  { immediate: true }
);

watch(
  () => props.tabIndex,
  newVal => {
    console.log(
      "DeviceEvent tabIndex变化:",
      newVal,
      "active:",
      props.active,
      "template:",
      props.template
    );
    if (newVal === 2 && props.active) {
      console.log("开始初始化事件数据 - tabIndex变化");
      initData();
    }
  },
  { immediate: true }
);

watch(
  () => props.template,
  newVal => {
    console.log(
      "DeviceEvent template变化:",
      newVal,
      "active:",
      props.active,
      "tabIndex:",
      props.tabIndex
    );
    const templateId = newVal?.equipmentTemplateId || newVal?.id;
    if (templateId && props.active && props.tabIndex === 2) {
      console.log("开始初始化事件数据 - template变化");
      initData();
    }
  }
);

// 综合监听：当所有必要条件都满足时，初始化数据
watch(
  [
    () => props.active,
    () => props.tabIndex,
    () => props.template,
    () => props.equipmentId
  ],
  ([active, tabIndex, template, equipmentId]) => {
    console.log("综合监听触发:", { active, tabIndex, template, equipmentId });
    const templateId = template?.equipmentTemplateId || template?.id;
    if (active && tabIndex === 2 && templateId && equipmentId) {
      console.log("所有条件满足，开始初始化事件数据");
      initData();
    }
  }
);

// 组件挂载
onMounted(() => {
  console.log("DeviceEvent组件挂载:", {
    active: props.active,
    tabIndex: props.tabIndex,
    template: props.template,
    equipmentId: props.equipmentId
  });

  // 初始化过滤器状态
  initFilterState();

  if (props.active && props.tabIndex === 2) {
    console.log("开始初始化事件数据 - 组件挂载");
    initData();
  }
});
</script>

<style scoped>
.device-event-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-container {
  flex: 1;
  overflow: auto;
}

:deep(.el-table .el-table__cell) {
  padding: 4px 0;
}

/* 过滤器样式 */
.el-table-v2__demo-filter {
  border-top: var(--el-border);
  margin: 12px -12px -12px;
  padding: 0 12px;
  display: flex;
  justify-content: space-between;
}

.filter-wrapper {
  padding: 8px 0;
}

.filter-group {
  margin-bottom: 8px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.text-xs {
  font-size: 12px;
}

.mr-2 {
  margin-right: 8px;
}

.cursor-pointer {
  cursor: pointer;
}

.text-gray-400 {
  color: #9ca3af;
}

/* 文本颜色样式 */
.text-green-600 {
  color: #16a085;
  font-weight: 500;
}

.text-red-600 {
  color: #e74c3c;
  font-weight: 500;
}

.text-gray-400 {
  color: #ccc;
}

.mr-1 {
  margin-right: 4px;
}

.mb-1 {
  margin-bottom: 2px;
}
</style>
