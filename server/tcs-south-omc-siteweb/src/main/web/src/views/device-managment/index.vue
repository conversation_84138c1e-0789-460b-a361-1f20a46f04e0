<template>
  <div class="device-management-container min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
    <!-- 页面头部 -->
    <div class="mb-8">


      <!-- 工具栏 -->
      <div
        class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
      >
        <!-- 设备信息和操作按钮 -->
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="w-10 h-10 bg-primary rounded-lg flex items-center justify-center flex-shrink-0">
              <el-icon size="20" class="text-white">
                <Monitor />
              </el-icon>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                {{ deviceName || '设备名称加载中...' }}
              </h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                设备ID: {{ equipmentId }}
              </p>
            </div>
          </div>
          <div class="flex items-center space-x-3">
            <el-button type="primary" @click="goToTemplate">
              <el-icon size="16" class="mr-2"><Setting /></el-icon>
              模板管理
            </el-button>
            <el-button @click="goBack">
              <el-icon size="16" class="mr-2"><ArrowLeft /></el-icon>
              返回
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- Tab页面容器 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <el-tabs
        v-model="activeTab"
        class="device-tabs"
        @tab-click="handleTabClick"
      >
        <el-tab-pane label="设备信息" name="info">
          <DeviceInfo 
            v-if="equipmentId"
            :equipment-id="equipmentId" 
            :active="activeTab === 'info'"
            @update-success="handleUpdateSuccess"
            @device-name-update="handleDeviceNameUpdate"
          />
        </el-tab-pane>
        
        <el-tab-pane label="信号" name="signal">
          <DeviceSignal 
            :equipment-id="equipmentId" 
            :active="activeTab === 'signal'"
            :mu-category="muCategory"
            :tab-index="activeTabIndex"
            :template="template"
          />
        </el-tab-pane>
        
        <el-tab-pane label="事件" name="event">
          <DeviceEvent 
            :equipment-id="equipmentId" 
            :active="activeTab === 'event'"
            :mu-category="muCategory"
            :tab-index="activeTabIndex"
            :template="template"
          />
        </el-tab-pane>
        
        <el-tab-pane label="控制" name="control">
          <DeviceControl 
            :equipment-id="equipmentId" 
            :active="activeTab === 'control'"
            :mu-category="muCategory"
            :tab-index="activeTabIndex"
            :template="template"
          />
        </el-tab-pane>
        
        <el-tab-pane label="变更记录" name="log">
          <DeviceLog 
            :equipment-id="equipmentId" 
            :active="activeTab === 'log'"
            :tab-index="activeTabIndex"
            :object-type="11"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Monitor, Setting } from '@element-plus/icons-vue'
import type { TabsPaneContext } from 'element-plus'
import DeviceInfo from './components/DeviceInfo.vue'
import DeviceSignal from './components/DeviceSignal.vue'
import DeviceEvent from './components/DeviceEvent.vue'
import DeviceControl from './components/DeviceControl.vue'
import DeviceLog from './components/DeviceLog.vue'
import { getDeviceInfo, getMonitorUnitInfo } from '@/api/device-management'

const route = useRoute()
const router = useRouter()

// 路由参数
const equipmentId = ref<string>('')
const deviceTitle = ref<string>('')
const deviceName = ref<string>('')
const activeTab = ref<string>('info')
const muCategory = ref<number>(18)
const template = ref<any>(null)

// 计算tab索引（用于兼容Angular组件的tabIndex参数）
const activeTabIndex = computed(() => {
  const tabMap = {
    'info': 0,
    'signal': 1,
    'event': 2,
    'control': 3,
    'log': 4
  }
  return tabMap[activeTab.value] || 0
})

// 从路由获取参数
const initializeParams = () => {
  equipmentId.value = route.params.id as string || '731000026'
  deviceTitle.value = route.query.title as string || ''
  
  if (!route.params.id) {
    ElMessage.info('未指定设备ID，使用默认设备: 731000026')
    deviceTitle.value = '默认设备'
  }
}

// 获取设备基础信息和监控单元类型
const getDeviceBasicInfo = async () => {
  try {
    if (!equipmentId.value) return
    
    // 获取设备信息
    const deviceResponse = await getDeviceInfo(equipmentId.value)
    if (deviceResponse.code === 0 && deviceResponse.data) {
      // 设置设备名称
      deviceName.value = deviceResponse.data.equipmentName || `设备${equipmentId.value}`
      
      // 设置模板信息
      if (deviceResponse.data.equipmentTemplateId) {
        template.value = {
          id: deviceResponse.data.equipmentTemplateId,
          equipmentTemplateId: deviceResponse.data.equipmentTemplateId, // 添加这个字段以兼容设备管理模式
          name: deviceResponse.data.equipmentTemplateName || '设备模板',
          parentId: deviceResponse.data.parentEquipmentId || 0,
          equipmentCategory: deviceResponse.data.equipmentCategory
        }
      }
      
      // 获取监控单元信息
      if (deviceResponse.data.monitorUnitId) {
        const muResponse = await getMonitorUnitInfo(deviceResponse.data.monitorUnitId)
        if (muResponse.code === 0 && muResponse.data) {
          muCategory.value = muResponse.data.monitorUnitCategory || 18
        }
      }
    }
    console.log('获取设备信息:', equipmentId.value, '名称:', deviceName.value, '监控单元类型:', muCategory.value, '模板:', template.value)
  } catch (error) {
    console.error('获取设备信息失败:', error)
    deviceName.value = `设备${equipmentId.value}`
    muCategory.value = 18
  }
}

// Tab点击处理
const handleTabClick = (tab: TabsPaneContext, event: Event) => {
  console.log('Tab点击:', tab.paneName, event)
  activeTab.value = tab.paneName as string
}

// Tab切换处理（保持兼容性）
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
  console.log('切换到Tab:', tabName, '索引:', activeTabIndex.value)
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 跳转到模板管理
const goToTemplate = async () => {
  try {
    if (!equipmentId.value) return
    
    // 获取设备信息，跳转到模板管理
    const deviceResponse = await getDeviceInfo(equipmentId.value)
    if (deviceResponse.code === 0 && deviceResponse.data?.equipmentTemplateId) {
      router.push({
        path: '/device-template',
        query: {
          id: deviceResponse.data.equipmentTemplateId,
          name: deviceResponse.data.equipmentTemplateName || '设备模板'
        }
      })
    } else {
      ElMessage.warning('设备模板信息不完整，无法跳转')
    }
  } catch (error) {
    console.error('跳转模板管理失败:', error)
    ElMessage.error('跳转失败')
  }
}

// 更新成功处理
const handleUpdateSuccess = () => {

}

// 设备名称更新处理
const handleDeviceNameUpdate = (name: string) => {
  deviceName.value = name
  console.log('设备名称已更新:', name)
}

onMounted(() => {
  initializeParams()
  getDeviceBasicInfo()
})
</script>

<style scoped>
.device-management-container {
  min-height: 100vh;
}

.device-tabs :deep(.el-tabs__header) {
  margin: 0;
  padding: 20px 20px 0 20px;
  border-bottom: 1px solid #e4e7ed;
}

.device-tabs :deep(.el-tabs__nav-wrap) {
  padding: 0;
}

.device-tabs :deep(.el-tabs__nav) {
  border: none;
}

.device-tabs :deep(.el-tabs__item) {
  height: 44px;
  line-height: 44px;
  padding: 0 24px;
  font-weight: 500;
  font-size: 14px;
  color: #606266;
  border: none;
  background: transparent;
  margin-right: 32px;
  border-radius: 0;
  position: relative;
}

.device-tabs :deep(.el-tabs__item.is-active) {
  color: #409eff;
  font-weight: 600;
}

.device-tabs :deep(.el-tabs__item:hover) {
  color: #409eff;
}

.device-tabs :deep(.el-tabs__active-bar) {
  height: 3px;
  background-color: #409eff;
  border-radius: 2px;
}

.device-tabs :deep(.el-tabs__content) {
  padding: 0;
  background: white;
  min-height: 500px;
}

.device-tabs :deep(.el-tab-pane) {
  padding: 0;
}
</style> 