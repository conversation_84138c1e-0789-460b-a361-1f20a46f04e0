<template>
  <div
    class="system-category-management-container bg-gray-50 dark:bg-gray-900 p-4 flex flex-col"
    style="height: calc(100vh - 48px)"
  >
    <!-- 页面头部 -->
    <div class="mb-4 flex-shrink-0">
      <div class="flex items-center justify-between mb-3">
        <div class="flex items-center">
          <div class="w-1 h-6 bg-primary rounded-full mr-3" />
          <h1 class="text-xl font-bold text-gray-900 dark:text-white">
            系统分类管理
          </h1>
        </div>
        <span class="text-sm text-gray-600 dark:text-gray-400">
          管理设备种类、设备厂商、局站种类的数据字典项
        </span>
      </div>

      <!-- 工具栏区域 -->
      <div
        class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-3"
      >
        <el-tabs
          v-model="activeTab"
          class="system-category-tabs"
          @tab-change="handleTabChange"
        >
          <el-tab-pane label="设备种类" name="device-type">
          </el-tab-pane>
          <el-tab-pane label="设备厂商" name="device-vendor">
          </el-tab-pane>
          <!-- <el-tab-pane label="局站种类" name="station-type">
          </el-tab-pane> -->
        </el-tabs>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="flex-1 min-h-0">
      <div v-if="activeTab === 'device-type'" class="h-full">
        <DeviceType :type-id="7" />
      </div>
      <div v-else-if="activeTab === 'device-vendor'" class="h-full">
        <DeviceType :type-id="14" />
      </div>
      <!-- <div v-else-if="activeTab === 'station-type'" class="h-full">
        <DeviceType :type-id="71" />
      </div> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import DeviceType from "./components/DeviceType.vue";

defineOptions({
  name: "SystemCategoryManagement"
});

// 当前活跃的标签页
const activeTab = ref<string>("device-type");

// Tab切换处理
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName;
};
</script>

<style scoped>
.system-category-management-container {
  height: calc(100vh - 48px);
}

.system-category-tabs {
  :deep(.el-tabs__header) {
    margin: 0 !important;
    padding: 0;
    background: transparent;
  }

  :deep(.el-tabs__nav-wrap::after) {
    display: none;
  }

  :deep(.el-tabs__item) {
    height: 40px;
    line-height: 40px;
    font-weight: 500;
    font-size: 14px;
    padding: 0 16px;
  }

  :deep(.el-tabs__content) {
    padding: 0;
    display: none;
  }

  :deep(.el-tabs__active-bar) {
    height: 2px;
  }
}
</style>
