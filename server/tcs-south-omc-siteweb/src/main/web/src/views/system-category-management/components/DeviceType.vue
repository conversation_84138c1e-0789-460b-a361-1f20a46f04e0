<template>
  <div class="device-type h-full flex flex-col">
    <!-- 工具栏 -->
    <div
      class="toolbar-container bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-3 mb-3 flex-shrink-0"
    >
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <el-button type="primary" size="small" @click="openModal()">
            <el-icon size="14" class="mr-1"><Plus /></el-icon>
            新增字典项
          </el-button>
        </div>
        <div class="flex items-center space-x-4">
          <span class="text-sm text-gray-600 dark:text-gray-400">
            共 {{ displayTableDataSource.length }} 个字典项
          </span>
        </div>
      </div>
    </div>

    <!-- 表格容器 -->
    <div
      class="table-container bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-3 flex-1 min-h-0 flex flex-col"
    >
      <!-- 固定的搜索行 -->
      <div class="fixed-search-row flex-shrink-0">
        <el-table
          :data="[{}]"
          style="width: 100%"
          border
          :header-cell-style="{ display: 'none' }"
          :show-header="false"
          class="search-table"
          size="small"
        >
          <el-table-column width="100" align="center">
            <template #default>
              <div class="filter-cell">
                <el-input
                  v-model="filterData.entryItemId"
                  placeholder="系统ID"
                  size="small"
                  clearable
                  @input="filterChange"
                />
              </div>
            </template>
          </el-table-column>

          <el-table-column width="100" align="center">
            <template #default>
              <div class="filter-cell">
                <el-input
                  v-model="filterData.entryId"
                  placeholder="类别ID"
                  size="small"
                  clearable
                  @input="filterChange"
                />
              </div>
            </template>
          </el-table-column>

          <el-table-column width="120">
            <template #default>
              <div class="filter-cell">
                <el-input
                  v-model="filterData.itemId"
                  placeholder="条目ID"
                  size="small"
                  clearable
                  @input="filterChange"
                />
              </div>
            </template>
          </el-table-column>

          <el-table-column min-width="150">
            <template #default>
              <div class="filter-cell">
                <el-input
                  v-model="filterData.itemValue"
                  placeholder="条目名称"
                  size="small"
                  clearable
                  @input="filterChange"
                />
              </div>
            </template>
          </el-table-column>

          <el-table-column min-width="150">
            <template #default>
              <div class="filter-cell">
                <el-input
                  v-model="filterData.itemAlias"
                  placeholder="英文名"
                  size="small"
                  clearable
                  @input="filterChange"
                />
              </div>
            </template>
          </el-table-column>

          <el-table-column min-width="200">
            <template #default>
              <div class="filter-cell">
                <el-input
                  v-model="filterData.description"
                  placeholder="描述"
                  size="small"
                  clearable
                  @input="filterChange"
                />
              </div>
            </template>
          </el-table-column>

          <el-table-column width="120" fixed="right" align="center">
            <template #default>
              <div class="filter-cell">
                <span class="text-xs text-gray-400" />
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 数据表格 -->
      <div class="flex-1 min-h-0">
        <el-table
          :data="displayTableDataSource"
          style="width: 100%; height: 100%"
          stripe
          border
          :default-sort="{ prop: 'entryItemId', order: 'ascending' }"
          @sort-change="onSortChange"
        >
          <el-table-column
            prop="entryItemId"
            label="系统ID"
            width="100"
            sortable
            align="center"
          />

          <el-table-column
            prop="entryId"
            label="类别ID"
            width="100"
            sortable
            align="center"
          />

          <el-table-column
            prop="itemId"
            label="条目ID"
            width="120"
            sortable
            show-overflow-tooltip
          />

          <el-table-column
            prop="itemValue"
            label="条目名称"
            min-width="150"
            sortable
            show-overflow-tooltip
          />

          <el-table-column
            prop="itemAlias"
            label="条目英文名"
            min-width="150"
            sortable
            show-overflow-tooltip
          />

          <el-table-column
            prop="description"
            label="描述"
            min-width="200"
            sortable
            show-overflow-tooltip
          />

          <el-table-column
            label="操作"
            width="120"
            fixed="right"
            align="center"
          >
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                text
                title="修改"
                @click="openModal(row)"
              >
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button
                type="danger"
                size="small"
                text
                title="删除"
                @click="deleteItem(row)"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </template>
          </el-table-column>

          <template #empty>
            <el-empty description="暂无数据" />
          </template>
        </el-table>
      </div>
    </div>

    <!-- 添加/编辑模态框 -->
    <AddEditModal
      v-model:visible="modalVisible"
      :data="modalData"
      :type-id="props.typeId"
      @confirm="handleModalConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus, Edit, Delete, Search } from "@element-plus/icons-vue";
import type { DictionaryItemData } from "@/api/system-category-management";
import { getList, deleteEntryItemIds } from "@/api/system-category-management";
import AddEditModal from "./AddEditModal.vue";

// Props
interface Props {
  typeId: number;
}
const props = defineProps<Props>();

// 原数据
const orgTableDataSource = ref<DictionaryItemData[]>([]);
// 过滤后的数据
const displayTableDataSource = ref<DictionaryItemData[]>([]);

// 过滤条件
const filterData = reactive({
  entryItemId: "",
  entryId: "",
  itemId: "",
  itemValue: "",
  itemAlias: "",
  description: ""
});

// 模态框相关
const modalVisible = ref(false);
const modalData = ref<any>(null);

// 移除了搜索行相关的逻辑，现在使用独立的搜索区域

// 获取表格数据
const requestTableDataSource = async () => {
  try {
    const response = await getList(props.typeId);
    if (response.state === true && response.data) {
      orgTableDataSource.value = response.data;
      filterTableDataSource();
    } else {
      ElMessage.error("获取数据失败");
    }
  } catch (error) {
    console.error("获取数据失败:", error);
    ElMessage.error("获取数据失败");
  }
};

// 过滤表格数据
const filterTableDataSource = () => {
  let filtered = [...orgTableDataSource.value];

  // 应用过滤条件
  Object.keys(filterData).forEach(key => {
    const filterValue = filterData[key as keyof typeof filterData];
    if (filterValue) {
      filtered = filtered.filter(item => {
        const itemValue = item[key as keyof DictionaryItemData];
        return (
          itemValue &&
          itemValue.toString().toLowerCase().includes(filterValue.toLowerCase())
        );
      });
    }
  });

  displayTableDataSource.value = filtered;
};

// 过滤条件变化
const filterChange = () => {
  filterTableDataSource();
};

// 排序变化（不影响搜索行）
const onSortChange = ({ prop, order }: { prop: string; order: string }) => {
  if (!order) {
    // 重新应用过滤，保持原始顺序
    filterTableDataSource();
    return;
  }

  displayTableDataSource.value.sort((a, b) => {
    const aVal = a[prop as keyof DictionaryItemData];
    const bVal = b[prop as keyof DictionaryItemData];

    if (aVal === undefined || bVal === undefined) return 0;

    if (order === "ascending") {
      return aVal > bVal ? 1 : -1;
    } else {
      return aVal < bVal ? 1 : -1;
    }
  });
};

// 打开模态框
const openModal = (item?: DictionaryItemData) => {
  modalData.value = item ? JSON.parse(JSON.stringify(item)) : props.typeId;
  modalVisible.value = true;
};

// 删除项目
const deleteItem = async (item: DictionaryItemData) => {
  try {
    await ElMessageBox.confirm(
      `请确认是否删除字典项：${item.itemValue}?`,
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true
      }
    );

    const response = await deleteEntryItemIds(item.entryItemId!);
    if (response.state === true) {
      ElMessage.success("操作成功");
      requestTableDataSource();
    } else {
      ElMessage.error("操作失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除失败:", error);
      ElMessage.error("操作失败");
    }
  }
};

// 模态框确认处理
const handleModalConfirm = () => {
  const text = modalData.value.entryId ? "修改" : "新增";
  ElMessage.success(`${text}成功`);
  requestTableDataSource();
};

// 监听typeId变化，重新获取数据
watch(
  () => props.typeId,
  () => {
    requestTableDataSource();
  },
  { immediate: true }
);

// 组件挂载后的处理
onMounted(() => {
  // requestTableDataSource 已在 watch immediate 中调用，无需重复调用
});
</script>

<style scoped>
.device-type {
  height: 100%;
  width: 100%;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header-wrapper) {
  background: #f8fafc;
}

:deep(.el-table th) {
  background: #f8fafc !important;
  color: #374151;
  font-weight: 600;
  border-bottom: 1px solid #e5e7eb;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f3f4f6;
}

:deep(.el-table tbody tr:hover > td) {
  background-color: #f9fafb !important;
}

/* 操作按钮样式 */
:deep(.el-button--text) {
  margin-right: 8px;
  padding: 4px 8px;
}

:deep(.el-button--text.el-button--primary) {
  color: #3b82f6;
}

:deep(.el-button--text.el-button--primary:hover) {
  background-color: #eff6ff;
  color: #1d4ed8;
}

:deep(.el-button--text.el-button--danger) {
  color: #ef4444;
}

:deep(.el-button--text.el-button--danger:hover) {
  background-color: #fef2f2;
  color: #dc2626;
}

/* 空数据样式 */
:deep(.el-empty) {
  padding: 40px 0;
}

/* 搜索过滤器样式 */
:deep(.el-input__wrapper) {
  border-radius: 4px;
  transition: all 0.2s ease;
}

:deep(.el-input__wrapper:hover) {
  border-color: #3b82f6;
}

:deep(.el-input__wrapper.is-focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* 工具栏容器样式 */
.toolbar-container {
  transition: all 0.2s ease;
}

/* 固定搜索行样式 */
.fixed-search-row {
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 2px;
}

.dark .fixed-search-row {
  background-color: #1f2937;
  border-bottom-color: #374151;
}

.search-table {
  background-color: #f8fafc;
}

.dark .search-table {
  background-color: #374151;
}

:deep(.search-table .el-table__body tr) {
  background-color: #f8fafc !important;
  height: 36px;
}

:deep(.dark .search-table .el-table__body tr) {
  background-color: #374151 !important;
  height: 36px;
}

:deep(.search-table .el-table__body tr:hover) {
  background-color: #f8fafc !important;
}

:deep(.dark .search-table .el-table__body tr:hover) {
  background-color: #374151 !important;
}

.filter-cell {
  padding: 2px;
}

/* 小尺寸输入框优化 */
:deep(.el-input--small .el-input__wrapper) {
  padding: 0 6px;
  height: 24px;
  border-radius: 4px;
}

:deep(.el-input--small .el-input__inner) {
  height: 24px;
  line-height: 24px;
  font-size: 12px;
}

.filter-cell .el-input__wrapper {
  background-color: #ffffff;
  border: 1px solid #dcdfe6;
}

.dark .filter-cell .el-input__wrapper {
  background-color: #374151;
  border-color: #4b5563;
  color: #e5e7eb;
}
</style>
