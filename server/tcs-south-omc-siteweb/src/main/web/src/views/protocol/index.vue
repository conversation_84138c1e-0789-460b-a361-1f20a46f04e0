<template>
  <div
    class="protocol-management-container bg-gray-50 dark:bg-gray-900 p-4 flex flex-col"
    style="height: calc(100vh - 48px)"
  >
    <!-- 页面头部 -->
    <div class="mb-4 flex-shrink-0">
      <div class="flex items-center justify-between mb-3">
        <div class="flex items-center">
          <div class="w-1 h-6 bg-primary rounded-full mr-3" />
          <h1 class="text-xl font-bold text-gray-900 dark:text-white">
            协议管理
          </h1>
        </div>
        <span class="text-sm text-gray-600 dark:text-gray-400">
          共 {{ filteredData.length }} 个协议
        </span>
      </div>

      <!-- 工具栏 -->
      <div
        class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4"
      >
        <!-- 操作按钮 -->
        <div class="flex items-center space-x-3">
          <el-button
            type="primary"
            size="small"
            :disabled="isLoading"
            @click="showImportDialog"
          >
            <el-icon size="14" class="mr-1"><Upload /></el-icon>
            增加协议
          </el-button>
          <el-button
            type="danger"
            plain
            size="small"
            :disabled="selectedIds.length === 0 || isLoading"
            @click="handleBatchDelete"
          >
            <el-icon size="14" class="mr-1"><Delete /></el-icon>
            删除协议
          </el-button>
        </div>
      </div>
    </div>

    <!-- 协议列表表格 -->
    <div
      class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-3 flex-1 min-h-0"
    >
      <div class="h-full">
        <el-auto-resizer>
          <template #default="{ height, width }">
            <el-table-v2
              v-loading="isLoading"
              :columns="tableColumns"
              :data="filteredData"
              :width="width"
              :height="height"
              fixed
              :row-height="48"
              @row-select="handleRowSelect"
            />
          </template>
        </el-auto-resizer>
      </div>
    </div>

    <!-- 协议导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="导入模板"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="space-y-3">
        <!-- 警告提示 -->
        <div
          class="bg-red-50 border border-red-200 text-red-700 px-2 py-1 rounded text-xs"
        >
          动态库上传后，下发配置时会连同动态库一起下发，可能覆盖原有动态库文件，请谨慎操作！
        </div>

        <!-- 协议模板上传 -->
        <div>
          <div class="mb-2">
            <span class="text-sm font-medium text-gray-700">选择模板文件</span>
            <span class="text-red-500 ml-1">*</span>
          </div>
          <el-upload
            class="w-full"
            drag
            action="#"
            :auto-upload="false"
            :on-change="handleTemplateFileChange"
            :show-file-list="false"
            accept=".xml"
          >
            <div class="py-6 px-4 text-center">
              <el-icon size="32" class="text-gray-400 mb-2">
                <UploadFilled />
              </el-icon>
              <div class="text-sm text-gray-600 dark:text-gray-400 mb-1">
                请选择文件...
              </div>
              <div class="text-xs text-gray-400">仅支持 .xml 文件</div>
            </div>
          </el-upload>
          <div
            v-if="importForm.templateFile"
            class="mt-2 flex items-center text-green-600 text-sm"
          >
            <el-icon class="mr-1"><Check /></el-icon>
            <span>已选择文件：{{ importForm.templateFile.name }}</span>
          </div>
        </div>

        <!-- 动态库上传区域 -->
        <div class="bg-gray-50 rounded-lg p-3">
          <div class="mb-3">
            <span class="text-sm font-medium text-gray-700"
              >选择动态库文件</span
            >
            <span class="text-sm text-gray-500 ml-2"
              >（可选，至少选择一个）</span
            >
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <!-- 335X动态库上传 -->
            <div class="space-y-2">
              <label class="text-xs font-medium text-gray-600"
                >335X动态库文件</label
              >
              <el-upload
                class="w-full"
                drag
                action="#"
                :auto-upload="false"
                :on-change="file => handleDllFileChange(file, '335X')"
                :show-file-list="false"
                accept=".dll,.so"
              >
                <div class="py-4 px-3 text-center">
                  <el-icon size="24" class="text-gray-400 mb-1">
                    <UploadFilled />
                  </el-icon>
                  <div class="text-xs text-gray-600 dark:text-gray-400">
                    请选择335X文件...
                  </div>
                </div>
              </el-upload>
              <div
                v-if="importForm.dll335XFile"
                class="text-xs text-green-600 flex items-center"
              >
                <el-icon class="mr-1"><Check /></el-icon>
                {{ importForm.dll335XFile.name }}
              </div>
            </div>

            <!-- 9200动态库上传 -->
            <div class="space-y-2">
              <label class="text-xs font-medium text-gray-600"
                >9200动态库文件</label
              >
              <el-upload
                class="w-full"
                drag
                action="#"
                :auto-upload="false"
                :on-change="file => handleDllFileChange(file, '9200')"
                :show-file-list="false"
                accept=".dll,.so"
              >
                <div class="py-4 px-3 text-center">
                  <el-icon size="24" class="text-gray-400 mb-1">
                    <UploadFilled />
                  </el-icon>
                  <div class="text-xs text-gray-600 dark:text-gray-400">
                    请选择9200文件...
                  </div>
                </div>
              </el-upload>
              <div
                v-if="importForm.dll9200File"
                class="text-xs text-green-600 flex items-center"
              >
                <el-icon class="mr-1"><Check /></el-icon>
                {{ importForm.dll9200File.name }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end gap-3">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="isUploading"
            :disabled="
              !importForm.templateFile ||
              (!importForm.dll335XFile && !importForm.dll9200File)
            "
            @click="confirmImport"
          >
            {{ isUploading ? "上传中..." : "确认" }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 动态库上传对话框 -->
    <el-dialog
      v-model="dllUploadDialogVisible"
      title="导入动态库"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="space-y-3">
        <!-- 协议信息提示 -->
        <div
          class="bg-blue-50 border border-blue-200 text-blue-700 px-2 py-2 rounded text-sm"
        >
          <div class="font-medium mb-1">当前选中协议：</div>
          <div class="text-sm">
            <span class="font-medium">协议名称：</span
            >{{ currentProtocolForDll?.samplerName || "未知" }}
            <span class="ml-4 font-medium">协议代码：</span
            >{{ currentProtocolForDll?.protocolCode || "未知" }}
          </div>
        </div>

        <!-- 警告提示 -->
        <div
          class="bg-orange-50 border border-orange-200 text-orange-700 px-4 py-3 rounded"
        >
          动态库上传后，下发配置时会连同动态库一起下发，可能覆盖原有动态库文件，请谨慎操作！
        </div>

        <!-- 动态库上传区域 -->
        <div>
          <div class="mb-4">
            <span class="text-sm font-medium text-gray-700"
              >选择动态库文件</span
            >
            <span class="text-sm text-gray-500 ml-2">（至少选择一个）</span>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- 335X动态库上传 -->
            <div class="space-y-2">
              <label class="text-sm font-medium text-gray-600"
                >335X动态库文件</label
              >
              <el-upload
                class="w-full"
                drag
                action="#"
                :auto-upload="false"
                :on-change="file => handleSingleDllFileChange(file, '1')"
                :show-file-list="false"
                accept=".dll,.so"
              >
                <div class="py-8 px-4 text-center">
                  <el-icon size="36" class="text-gray-400 mb-3">
                    <UploadFilled />
                  </el-icon>
                  <div class="text-sm text-gray-600 dark:text-gray-400 mb-1">
                    请选择335X文件...
                  </div>
                  <div class="text-xs text-gray-400">支持 .dll/.so 文件</div>
                </div>
              </el-upload>
              <div
                v-if="singleDllForm.dll335XFile"
                class="text-sm text-green-600 flex items-center"
              >
                <el-icon class="mr-1"><Check /></el-icon>
                {{ singleDllForm.dll335XFile.name }}
              </div>
            </div>

            <!-- 9200动态库上传 -->
            <div class="space-y-2">
              <label class="text-sm font-medium text-gray-600"
                >9200动态库文件</label
              >
              <el-upload
                class="w-full"
                drag
                action="#"
                :auto-upload="false"
                :on-change="file => handleSingleDllFileChange(file, '2')"
                :show-file-list="false"
                accept=".dll,.so"
              >
                <div class="py-8 px-4 text-center">
                  <el-icon size="36" class="text-gray-400 mb-3">
                    <UploadFilled />
                  </el-icon>
                  <div class="text-sm text-gray-600 dark:text-gray-400 mb-1">
                    请选择9200文件...
                  </div>
                  <div class="text-xs text-gray-400">支持 .dll/.so 文件</div>
                </div>
              </el-upload>
              <div
                v-if="singleDllForm.dll9200File"
                class="text-sm text-green-600 flex items-center"
              >
                <el-icon class="mr-1"><Check /></el-icon>
                {{ singleDllForm.dll9200File.name }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end gap-3">
          <el-button @click="dllUploadDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="isUploading"
            :disabled="!singleDllForm.dll335XFile && !singleDllForm.dll9200File"
            @click="confirmDllUpload"
          >
            {{ isUploading ? "上传中..." : "确认" }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 设备种类设置对话框 -->
    <el-dialog
      v-model="categoryDialogVisible"
      title="为协议中的模板设置标准设备种类"
      width="500px"
    >
      <el-form label-width="120px">
        <el-form-item :label="categoryTip">
          <el-select
            v-model="selectedStandardType"
            placeholder="请选择标准设备种类"
            style="width: 300px"
          >
            <el-option
              v-for="item in standardTypes"
              :key="item.equipmentCategory"
              :label="item.equipmentCategoryName"
              :value="item.equipmentCategory"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-end gap-3">
          <el-button @click="categoryDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmStandardType"
            >确认</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="tsx">
import {
  ref,
  computed,
  onMounted,
  reactive,
  nextTick,
  withKeys,
  type FunctionalComponent
} from "vue";
import {
  Plus,
  Search,
  Delete,
  Document,
  Upload,
  UploadFilled,
  Filter,
  Check
} from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox, ElSelect, ElOption, ElButton, ElPopover } from "element-plus";
import type { HeaderCellSlotProps, InputInstance, Column } from "element-plus";
import {
  getProtocolList,
  updateProtocol,
  deleteProtocolByIds,
  uploadProtocolTemplate,
  uploadDllFile,
  updateDeviceCategory,
  deleteDllFile,
  getDeviceModels,
  getDeviceCategoryMap,
  type ProtocolInfo,
  type DeviceModel,
  type DeviceCategoryMap
} from "@/api/protocol";

defineOptions({
  name: "ProtocolManagement"
});

// 状态定义
const isLoading = ref(false);
const isUploading = ref(false);
const selectedIds = ref<string[]>([]);
const tableData = ref<ProtocolInfo[]>([]);
const deviceModelList = ref<DeviceModel[]>([]);

// 过滤器状态接口
interface FilterState {
  [key: string]: {
    value: any[];
    options: Array<{ label: string; value: any }>;
  };
}

// 过滤器状态
const filterState = ref<FilterState>({});
const popoverRefs = reactive({});

// 编辑状态管理
const editingOriginalValues = reactive({});

// 导入对话框
const importDialogVisible = ref(false);
const importForm = reactive({
  templateFile: null as File | null,
  dll335XFile: null as File | null,
  dll9200File: null as File | null
});

// 单独动态库上传
const dllUploadDialogVisible = ref(false);
const currentProtocolForDll = ref<ProtocolInfo | null>(null);
const singleDllForm = reactive({
  dll335XFile: null as File | null,
  dll9200File: null as File | null
});

// 设备种类设置
const categoryDialogVisible = ref(false);
const standardTypes = ref<DeviceCategoryMap[]>([]);
const selectedStandardType = ref("");
const categoryTip = ref("");
const currentEquipmentTemplateId = ref("");

// 生命周期
onMounted(() => {
  getDeviceModelList();
  getProtocolListData();
  initFilterState();
});

// 初始化过滤器状态
const initFilterState = () => {
  filterState.value = {
    samplerName: { value: [], options: [] },
    dllPath: { value: [], options: [] },
    equipmentTemplateName: { value: [], options: [] },
    updateState: { value: [], options: [] },
    modal: { value: [], options: [] }
  };
};

// 更新过滤器选项
const updateFilterOptions = () => {
  const data = tableData.value;

  // 协议名称选项
  const nameOptions = [...new Set(data.map(item => item.samplerName).filter(Boolean))]
    .map(name => ({ label: name, value: name }));
  filterState.value.samplerName.options = nameOptions;

  // 动态库路径选项
  const pathOptions = [...new Set(data.map(item => item.dllPath).filter(Boolean))]
    .map(path => ({ label: path, value: path }));
  filterState.value.dllPath.options = pathOptions;

  // 关联模板选项
  const templateOptions = [...new Set(data.map(item => item.equipmentTemplateName).filter(Boolean))]
    .map(template => ({ label: template, value: template }));
  filterState.value.equipmentTemplateName.options = templateOptions;

  // 更新状态选项
  filterState.value.updateState.options = [
    { label: "已上传", value: "已上传" },
    { label: "未上传", value: "未上传" }
  ];

  // 设备型号选项
  const modalOptions = [...new Set(data.map(item => item.modal).filter(Boolean))]
    .map(modal => ({ label: modal, value: modal }));
  filterState.value.modal.options = modalOptions;
};

// 创建过滤器头部组件
const createFilterHeader = (column: any) => {
  const filterKey = column.dataKey;

  return (props: any) => {
    const popoverRef = ref();

    const onFilter = () => {
      popoverRef.value?.hide();
    };

    const onReset = () => {
      if (filterState.value[filterKey]) {
        filterState.value[filterKey].value = [];
      }
    };

    return (
      <div class="flex items-center justify-center">
        <span class="mr-2 text-xs">{props.column.title}</span>
        <ElPopover
          ref={popoverRef}
          trigger="click"
          width={250}
        >
          {{
            default: () => (
              <div class="filter-wrapper">
                <div class="filter-group">
                  <ElSelect
                    modelValue={filterState.value[filterKey]?.value || []}
                    onUpdate:modelValue={(value: any[]) => {
                      if (filterState.value[filterKey]) {
                        filterState.value[filterKey].value = value;
                      }
                    }}
                    placeholder="选择过滤条件"
                    size="small"
                    multiple
                    collapseTags
                    filterable
                    clearable
                    style={{ width: "100%" }}
                  >
                    {(filterState.value[filterKey]?.options || []).map((option: any) => (
                      <ElOption
                        key={option.value}
                        label={option.label}
                        value={option.value}
                      />
                    ))}
                  </ElSelect>
                </div>
                <div class="el-table-v2__demo-filter">
                  <ElButton text onClick={onFilter}>确认</ElButton>
                  <ElButton text onClick={onReset}>重置</ElButton>
                </div>
              </div>
            ),
            reference: () => (
              <ElIcon class="cursor-pointer">
                <Filter />
              </ElIcon>
            )
          }}
        </ElPopover>
      </div>
    );
  };
};

// 过滤后的数据
const filteredData = computed(() => {
  let data = tableData.value;

  // 应用列过滤器
  Object.entries(filterState.value).forEach(([key, filter]) => {
    if (filter.value && filter.value.length > 0) {
      data = data.filter(item => {
        const itemValue = item[key as keyof ProtocolInfo];
        return filter.value.includes(itemValue);
      });
    }
  });

  return data;
});

// 编辑单元格组件
type EditCellProps = {
  value: string;
  onChange: (value: string) => void;
  onBlur: () => void;
  onKeydownEnter: () => void;
  onKeydownEsc?: () => void;
  forwardRef: (el: InputInstance) => void;
};

const EditCell: FunctionalComponent<EditCellProps> = ({
  value,
  onChange,
  onBlur,
  onKeydownEnter,
  onKeydownEsc,
  forwardRef
}) => {
  return (
    <ElInput
      ref={forwardRef as any}
      modelValue={value}
      onInput={onChange}
      onBlur={onBlur}
      onKeydown={(e: KeyboardEvent) => {
        if (e.key === "Enter") {
          onKeydownEnter();
        } else if (e.key === "Escape" && onKeydownEsc) {
          onKeydownEsc();
        }
      }}
      size="small"
    />
  );
};



// 表格列配置
const tableColumns = computed<Column<ProtocolInfo>[]>(() => [
  // 选择列
  {
    key: "selection",
    width: 60,
    cellRenderer: ({ rowData }) => (
      <ElCheckbox
        modelValue={selectedIds.value.includes(rowData.samplerId)}
        onChange={(checked: boolean) => {
          if (checked) {
            if (!selectedIds.value.includes(rowData.samplerId)) {
              selectedIds.value.push(rowData.samplerId);
            }
          } else {
            const index = selectedIds.value.indexOf(rowData.samplerId);
            if (index > -1) {
              selectedIds.value.splice(index, 1);
            }
          }
        }}
      />
    ),
    headerCellRenderer: () => (
      <ElCheckbox
        modelValue={
          selectedIds.value.length === filteredData.value.length &&
          filteredData.value.length > 0
        }
        indeterminate={
          selectedIds.value.length > 0 &&
          selectedIds.value.length < filteredData.value.length
        }
        onChange={(checked: boolean) => {
          if (checked) {
            selectedIds.value = filteredData.value.map(item => item.samplerId);
          } else {
            selectedIds.value = [];
          }
        }}
      />
    )
  },
  // 协议名称
  {
    key: "samplerName",
    title: "协议名称",
    dataKey: "samplerName",
    width: 300,
    cellRenderer: ({ rowData }) => {
      const onChange = (value: string) => {
        rowData.samplerName = value;
      };
      const onEnterEditMode = () => {
        // 保存原始值
        if (!editingOriginalValues[rowData.samplerId]) {
          editingOriginalValues[rowData.samplerId] = {};
        }
        editingOriginalValues[rowData.samplerId].samplerName =
          rowData.samplerName;
        rowData.nameEdit = true;
      };
      const onExitEditMode = async () => {
        rowData.nameEdit = false;
        await confirmChange(rowData, "samplerName", "nameEdit");
      };
      const onCancelEdit = () => {
        // 恢复原始值
        if (editingOriginalValues[rowData.samplerId]) {
          rowData.samplerName =
            editingOriginalValues[rowData.samplerId].samplerName;
          delete editingOriginalValues[rowData.samplerId].samplerName;
        }
        rowData.nameEdit = false;
      };
      const input = ref();
      const setRef = (el: any) => {
        input.value = el;
        if (el) {
          el.focus?.();
        }
      };

      return rowData.nameEdit ? (
        <EditCell
          forwardRef={setRef}
          value={rowData.samplerName}
          onChange={onChange}
          onBlur={onExitEditMode}
          onKeydownEnter={onExitEditMode}
          onKeydownEsc={onCancelEdit}
        />
      ) : (
        <div
          class="flex items-center cursor-pointer table-v2-inline-editing-trigger"
          onClick={onEnterEditMode}
        >
          <div class="w-6 h-6 bg-primary rounded flex items-center justify-center mr-2 flex-shrink-0">
            <ElIcon size={12} class="text-white">
              <Document />
            </ElIcon>
          </div>
          <span class="font-medium text-sm">{rowData.samplerName}</span>
        </div>
      );
    },
    headerCellRenderer: createFilterHeader({ dataKey: "samplerName" })
  },
  // 动态库路径
  {
    key: "dllPath",
    title: "动态库路径",
    dataKey: "dllPath",
    width: 250,
    cellRenderer: ({ rowData }) => {
      const onChange = (value: string) => {
        rowData.dllPath = value;
      };
      const onEnterEditMode = () => {
        // 保存原始值
        if (!editingOriginalValues[rowData.samplerId]) {
          editingOriginalValues[rowData.samplerId] = {};
        }
        editingOriginalValues[rowData.samplerId].dllPath = rowData.dllPath;
        rowData.dllEdit = true;
      };
      const onExitEditMode = async () => {
        rowData.dllEdit = false;
        await confirmChange(rowData, "dllPath", "dllEdit");
      };
      const onCancelEdit = () => {
        // 恢复原始值
        if (editingOriginalValues[rowData.samplerId]) {
          rowData.dllPath = editingOriginalValues[rowData.samplerId].dllPath;
          delete editingOriginalValues[rowData.samplerId].dllPath;
        }
        rowData.dllEdit = false;
      };
      const input = ref();
      const setRef = (el: any) => {
        input.value = el;
        if (el) {
          el.focus?.();
        }
      };

      return rowData.dllEdit ? (
        <EditCell
          forwardRef={setRef}
          value={rowData.dllPath}
          onChange={onChange}
          onBlur={onExitEditMode}
          onKeydownEnter={onExitEditMode}
          onKeydownEsc={onCancelEdit}
        />
      ) : (
        <div
          class="cursor-pointer table-v2-inline-editing-trigger"
          onClick={onEnterEditMode}
          title={rowData.dllPath}
        >
          {rowData.dllPath}
        </div>
      );
    },
    headerCellRenderer: createFilterHeader({ dataKey: "dllPath" })
  },
  // 关联模板
  {
    key: "equipmentTemplateName",
    title: "关联模板",
    dataKey: "equipmentTemplateName",
    width: 250,
    cellRenderer: ({ rowData }) => <span>{rowData.equipmentTemplateName}</span>,
    headerCellRenderer: createFilterHeader({ dataKey: "equipmentTemplateName" })
  },
  // 更新状态
  {
    key: "updateState",
    title: "更新状态",
    dataKey: "updateState",
    width: 150,
    cellRenderer: ({ rowData }) => (
      <ElTag
        type={rowData.updateState === "已上传" ? "success" : "warning"}
        effect="light"
      >
        {rowData.updateState}
      </ElTag>
    ),
    headerCellRenderer: createFilterHeader({ dataKey: "updateState" })
  },
  // 设备型号
  {
    key: "modal",
    title: "设备型号",
    dataKey: "modal",
    width: 150,
    cellRenderer: ({ rowData }) => {
      const onChange = (value: string) => {
        rowData.samplerType = value;
        const modelIndex = deviceModelList.value.findIndex(
          model => model.itemId === value
        );
        rowData.modal =
          modelIndex >= 0 ? deviceModelList.value[modelIndex].itemValue : "";
      };
      const onEnterEditMode = () => {
        // 保存原始值
        if (!editingOriginalValues[rowData.samplerId]) {
          editingOriginalValues[rowData.samplerId] = {};
        }
        editingOriginalValues[rowData.samplerId].modal = rowData.modal;
        editingOriginalValues[rowData.samplerId].samplerType =
          rowData.samplerType;
        rowData.modalEdit = true;
      };
      const onExitEditMode = async () => {
        rowData.modalEdit = false;
        await confirmChange(rowData, "modal", "modalEdit");
      };
      const onCancelEdit = () => {
        // 恢复原始值
        if (editingOriginalValues[rowData.samplerId]) {
          rowData.modal = editingOriginalValues[rowData.samplerId].modal;
          rowData.samplerType =
            editingOriginalValues[rowData.samplerId].samplerType;
          delete editingOriginalValues[rowData.samplerId].modal;
          delete editingOriginalValues[rowData.samplerId].samplerType;
        }
        rowData.modalEdit = false;
      };

      return rowData.modalEdit ? (
        <ElSelect
          modelValue={rowData.samplerType}
          onChange={onChange}
          onBlur={onExitEditMode}
          onKeydown={(e: KeyboardEvent) => {
            if (e.key === "Escape") {
              onCancelEdit();
            }
          }}
          size="small"
          style="width: 120px"
        >
          {deviceModelList.value.map(item => (
            <ElOption
              key={item.itemId}
              label={item.itemValue}
              value={item.itemId}
            />
          ))}
        </ElSelect>
      ) : (
        <div
          class="cursor-pointer table-v2-inline-editing-trigger"
          onClick={onEnterEditMode}
        >
          {rowData.modal}
        </div>
      );
    },
    headerCellRenderer: createFilterHeader({ dataKey: "modal" })
  },
  // 操作列
  {
    key: "actions",
    title: "操作",
    width: 280,
    cellRenderer: ({ rowData }) => {
      // 解析soPath来判断当前有哪些动态库
      const getSoPathInfo = (soPath: string) => {
        if (!soPath) return { has335X: false, has9200: false };

        try {
          const soData = JSON.parse(soPath);
          return {
            has335X: !!soData["335X"],
            has9200: !!soData["9200"]
          };
        } catch (error) {
          // 如果不是JSON格式，检查是否包含关键字
          return {
            has335X: soPath.includes("335X"),
            has9200: soPath.includes("9200")
          };
        }
      };

      const { has335X, has9200 } = getSoPathInfo(rowData.soPath);

      return (
        <div class="flex items-center space-x-1">
          <ElButton
            size="small"
            type="primary"
            link
            onClick={() => uploadDLLorSO(rowData)}
          >
            上传
          </ElButton>
          {has335X && (
            <ElButton
              size="small"
              type="danger"
              link
              onClick={() => deleteDLLorSO(rowData, "1")}
            >
              删除335X
            </ElButton>
          )}
          {has9200 && (
            <ElButton
              size="small"
              type="danger"
              link
              onClick={() => deleteDLLorSO(rowData, "2")}
            >
              删除9200
            </ElButton>
          )}
        </div>
      );
    }
  }
]);

// 获取协议列表
const getProtocolListData = async () => {
  isLoading.value = true;
  try {
    const res = await getProtocolList();
    if (res.code === 0) {
      const protocols = res.data;
      // 处理数据，添加更新状态
      protocols.forEach((item: ProtocolInfo) => {
        const modelIndex = deviceModelList.value.findIndex(
          model => model.itemId === item.samplerType
        );
        item.modal =
          modelIndex >= 0 ? deviceModelList.value[modelIndex].itemValue : "";
        item.updateState = item.uploadProtocolFile ? "已上传" : "未上传";
      });

      // 按照接口返回值的倒序排序
      protocols.reverse();

      tableData.value = protocols;
      // 更新过滤器选项
      updateFilterOptions();
    } else {
      ElMessage.error(res.message || "获取协议列表失败");
    }
  } catch (error) {
    ElMessage.error("请求异常");
    console.error(error);
  } finally {
    isLoading.value = false;
  }
};

// 获取设备型号列表
const getDeviceModelList = async () => {
  try {
    const res = await getDeviceModels();
    if (res.code === 0) {
      deviceModelList.value = res.data;
    }
  } catch (error) {
    console.error("获取设备型号失败", error);
  }
};

// 行选择处理
const handleRowSelect = (selection: ProtocolInfo[]) => {
  selectedIds.value = selection.map(item => item.samplerId);
};

// 显示导入对话框
const showImportDialog = () => {
  importDialogVisible.value = true;
  importForm.templateFile = null;
  importForm.dll335XFile = null;
  importForm.dll9200File = null;
};

// 处理模板文件选择
const handleTemplateFileChange = (file: any) => {
  if (!file?.raw) return;

  const fileName = file.name.toLowerCase();
  if (!fileName.endsWith(".xml")) {
    ElMessage.error("只支持上传 .xml 格式的协议模板文件");
    return;
  }

  importForm.templateFile = file.raw;
};

// 处理动态库文件选择
const handleDllFileChange = (file: any, type: "335X" | "9200") => {
  if (!file?.raw) return;

  const fileName = file.name.toLowerCase();
  if (!fileName.endsWith(".dll") && !fileName.endsWith(".so")) {
    ElMessage.error("只支持上传 .dll 或 .so 格式的动态库文件");
    return;
  }

  if (type === "335X") {
    importForm.dll335XFile = file.raw;
  } else {
    importForm.dll9200File = file.raw;
  }
};

// 处理单独动态库文件选择
const handleSingleDllFileChange = (file: any, type: "1" | "2") => {
  if (!file?.raw) return;

  const fileName = file.name.toLowerCase();
  if (!fileName.endsWith(".dll") && !fileName.endsWith(".so")) {
    ElMessage.error("只支持上传 .dll 或 .so 格式的动态库文件");
    return;
  }

  if (type === "1") {
    singleDllForm.dll335XFile = file.raw;
  } else {
    singleDllForm.dll9200File = file.raw;
  }
};

// 确认导入
const confirmImport = async () => {
  if (!importForm.templateFile) {
    ElMessage.warning("请先上传模板文件！");
    return;
  }

  if (!importForm.dll335XFile && !importForm.dll9200File) {
    ElMessage.warning("请至少上传一个动态库文件！");
    return;
  }

  isUploading.value = true;
  try {
    // 1. 上传模板文件
    const templateFormData = new FormData();
    templateFormData.append("file", importForm.templateFile);

    const templateRes = await uploadProtocolTemplate(templateFormData);
    if (templateRes.code !== 0) {
      ElMessage.error(templateRes.message || "非法模板文件！");
      return;
    }

    ElMessage.success("模板文件上传成功！");
    const protocolCode = templateRes.data.protocolCode;

    // 2. 检查是否需要设置设备种类
    const equipmentCategory = templateRes.data.equipmentCategory;
    const haveEquipmentCategory =
      equipmentCategory &&
      equipmentCategory !== 0 &&
      equipmentCategory !== null &&
      equipmentCategory !== "";

    if (haveEquipmentCategory) {
      currentEquipmentTemplateId.value = templateRes.data.equipmentTemplateId;
      try {
        const categoryRes = await getDeviceCategoryMap(equipmentCategory);
        if (categoryRes.code === 0) {
          standardTypes.value = categoryRes.data.equipmentCategoryMapList;
          if (standardTypes.value && standardTypes.value.length > 0) {
            categoryTip.value = `选择${categoryRes.data.standardName}标准设备种类`;
            categoryDialogVisible.value = true;
            importDialogVisible.value = false;
          }
        }
      } catch (error) {
        console.error("获取设备种类失败", error);
      }
    }

    // 3. 上传动态库文件
    await uploadDllFiles(protocolCode, haveEquipmentCategory);

    if (!haveEquipmentCategory) {
      importDialogVisible.value = false;
      await getProtocolListData();
    }
  } catch (error) {
    ElMessage.error("导入过程中发生错误");
    console.error(error);
  } finally {
    isUploading.value = false;
  }
};

// 上传动态库文件
const uploadDllFiles = async (
  protocolCode: string,
  haveEquipmentCategory: boolean
) => {
  // 串行上传335X动态库
  if (importForm.dll335XFile) {
    try {
      const formData335X = new FormData();
      formData335X.append("file", importForm.dll335XFile);
      formData335X.append("protocolCode", protocolCode);
      formData335X.append("protocolType", "1");

      const res = await uploadDllFile(formData335X);
      if (res.code === 0) {
        ElMessage.success("335X动态库上传成功！");
      } else {
        ElMessage.error(res.message || "非法335X动态库文件！");
      }
    } catch (error) {
      console.error("335X动态库上传失败", error);
      ElMessage.error("335X动态库上传失败");
    }
  }

  // 串行上传9200动态库
  if (importForm.dll9200File) {
    try {
      const formData9200 = new FormData();
      formData9200.append("file", importForm.dll9200File);
      formData9200.append("protocolCode", protocolCode);
      formData9200.append("protocolType", "2");

      const res = await uploadDllFile(formData9200);
      if (res.code === 0) {
        ElMessage.success("9200动态库上传成功！");
      } else {
        ElMessage.error(res.message || "非法9200动态库文件！");
      }
    } catch (error) {
      console.error("9200动态库上传失败", error);
      ElMessage.error("9200动态库上传失败");
    }
  }

  // 如果没有设备种类设置需求，关闭对话框并刷新列表
  if (!haveEquipmentCategory) {
    await getProtocolListData();
  }
};

// 确认设备种类设置
const confirmStandardType = async () => {
  if (!selectedStandardType.value) {
    ElMessage.warning("请选择标准设备种类");
    return;
  }

  try {
    const res = await updateDeviceCategory(
      currentEquipmentTemplateId.value,
      selectedStandardType.value
    );
    if (res.code === 0) {
      ElMessage.success("设备种类设置成功");
      categoryDialogVisible.value = false;
      await getProtocolListData();
    } else {
      ElMessage.error(res.message || "设备种类设置失败");
    }
  } catch (error) {
    ElMessage.error("请求异常");
    console.error(error);
  }
};

// 批量删除
const handleBatchDelete = async () => {
  if (selectedIds.value.length === 0) {
    return;
  }

  try {
    await ElMessageBox.confirm("确认删除？", "提示", {
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      type: "warning"
    });

    const ids = selectedIds.value.join(",");
    const res = await deleteProtocolByIds(ids);
    if (res.code === 0) {
      ElMessage.success("删除成功！");
      selectedIds.value = [];
      await getProtocolListData();
    } else {
      ElMessage.error(res.message || "删除失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("请求异常");
      console.error(error);
    }
  }
};

// 确认修改
const confirmChange = async (row: any, field: string, editField: string) => {
  let originalValue = editingOriginalValues[row.samplerId]?.[field];
  let currentValue = row[field];

  // 对于设备型号字段，需要比较真实的值（samplerType）
  if (field === "modal") {
    originalValue = editingOriginalValues[row.samplerId]?.samplerType;
    currentValue = row.samplerType;
  }

  // 如果值没有变化，直接退出编辑模式
  if (originalValue === currentValue) {
    return;
  }

  try {
    const fieldMap = {
      samplerName: "协议名称",
      dllPath: "动态库路径",
      modal: "设备型号"
    };

    // 准备显示值
    let displayOriginalValue = originalValue;
    let displayCurrentValue = currentValue;

    // 对于设备型号，显示名称而不是ID
    if (field === "modal") {
      displayOriginalValue =
        editingOriginalValues[row.samplerId]?.modal || "无";
      displayCurrentValue = row.modal || "无";
    }

    // 显示修改确认对话框
    await ElMessageBox.confirm(
      `确认修改 ${row.samplerName} 的 ${fieldMap[field]} 吗？`,
      "确认修改",
      {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
        message: `
          <div>
            <p style="margin-bottom: 8px;"><strong>字段：</strong>${fieldMap[field]}</p>
            <p style="margin-bottom: 8px;"><strong>修改前：</strong><span style="color: #909399;">${displayOriginalValue}</span></p>
            <p style="margin-bottom: 0;"><strong>修改后：</strong><span style="color: #409EFF;">${displayCurrentValue}</span></p>
          </div>
        `
      }
    );

    // 更新协议
    const updateData = { ...row };
    delete updateData.modal;
    delete updateData.nameEdit;
    delete updateData.dllEdit;
    delete updateData.modalEdit;
    delete updateData.updateState;

    const res = await updateProtocol(updateData);
    if (res.code === 0) {
      ElMessage.success("更新成功！");
      // 清除编辑状态
      delete editingOriginalValues[row.samplerId];
      await getProtocolListData();
    } else {
      ElMessage.error(res.message || "更新失败");
      // 恢复原始值
      restoreOriginalValue(row, field);
    }
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("请求异常");
      console.error(error);
    }
    // 恢复原始值
    restoreOriginalValue(row, field);
  }
};

// 恢复原始值的辅助函数
const restoreOriginalValue = (row: any, field: string) => {
  if (!editingOriginalValues[row.samplerId]) return;

  if (field === "modal") {
    row.modal = editingOriginalValues[row.samplerId].modal;
    row.samplerType = editingOriginalValues[row.samplerId].samplerType;
  } else {
    row[field] = editingOriginalValues[row.samplerId][field];
  }
};

// 动态库上传
const uploadDLLorSO = (row: ProtocolInfo) => {
  if (!row.protocolCode) {
    ElMessage.error("该协议缺少protocolCode，无法上传动态库");
    return;
  }

  currentProtocolForDll.value = row;
  dllUploadDialogVisible.value = true;
  singleDllForm.dll335XFile = null;
  singleDllForm.dll9200File = null;

  console.log("选中协议信息：", {
    samplerName: row.samplerName,
    protocolCode: row.protocolCode,
    samplerId: row.samplerId
  });
};

// 确认动态库上传
const confirmDllUpload = async () => {
  if (!singleDllForm.dll335XFile && !singleDllForm.dll9200File) {
    ElMessage.warning("请至少上传一个动态库文件！");
    return;
  }

  if (
    !currentProtocolForDll.value ||
    !currentProtocolForDll.value.protocolCode
  ) {
    ElMessage.error("协议信息不存在或protocolCode为空");
    return;
  }

  isUploading.value = true;
  try {
    // 直接使用当前表格行中的protocolCode
    const protocolCode = currentProtocolForDll.value.protocolCode;
    console.log("使用的protocolCode：", protocolCode);

    // 串行上传335X动态库
    if (singleDllForm.dll335XFile) {
      try {
        const formData335X = new FormData();
        formData335X.append("file", singleDllForm.dll335XFile);
        formData335X.append("protocolCode", protocolCode);
        formData335X.append("protocolType", "1");

        const res = await uploadDllFile(formData335X);
        if (res.code === 0) {
          ElMessage.success("335X动态库上传成功！");
        } else {
          ElMessage.error(res.message || "非法335X动态库文件！");
        }
      } catch (error) {
        console.error("335X动态库上传失败", error);
        ElMessage.error("335X动态库上传失败");
      }
    }

    // 串行上传9200动态库
    if (singleDllForm.dll9200File) {
      try {
        const formData9200 = new FormData();
        formData9200.append("file", singleDllForm.dll9200File);
        formData9200.append("protocolCode", protocolCode);
        formData9200.append("protocolType", "2");

        const res = await uploadDllFile(formData9200);
        if (res.code === 0) {
          ElMessage.success("9200动态库上传成功！");
        } else {
          ElMessage.error(res.message || "非法9200动态库文件！");
        }
      } catch (error) {
        console.error("9200动态库上传失败", error);
        ElMessage.error("9200动态库上传失败");
      }
    }

    dllUploadDialogVisible.value = false;
    await getProtocolListData();
  } catch (error) {
    ElMessage.error("上传过程中发生错误");
    console.error(error);
  } finally {
    isUploading.value = false;
  }
};

// 删除动态库
const deleteDLLorSO = async (row: ProtocolInfo, type: "1" | "2") => {
  if (!row.protocolCode) {
    ElMessage.error("该协议缺少protocolCode，无法删除动态库");
    return;
  }

  const typeName = type === "1" ? "335X" : "9200";

  try {
    await ElMessageBox.confirm(
      `确认删除协议 "${row.samplerName}" 的 ${typeName} 动态库？`,
      "删除确认",
      {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    console.log("删除动态库：", {
      samplerName: row.samplerName,
      protocolCode: row.protocolCode,
      type: typeName
    });

    const res = await deleteDllFile(row.protocolCode, type);
    if (res.code === 0) {
      ElMessage.success(`${typeName}动态库删除成功！`);
      await getProtocolListData();
    } else {
      ElMessage.error(res.message || "删除失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("请求异常");
      console.error(error);
    }
  }
};
</script>

<style scoped>
.table-v2-inline-editing-trigger {
  border: 1px transparent dotted;
  padding: 4px;
  border-radius: 4px;
}

.table-v2-inline-editing-trigger:hover {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

.el-table-v2__demo-filter {
  border-top: var(--el-border);
  margin: 12px -12px -12px;
  padding: 0 12px;
  display: flex;
  justify-content: space-between;
}

.filter-wrapper {
  padding: 8px 0;
}

.filter-group {
  max-height: 200px;
  overflow-y: auto;
}
</style>
