# 配置分发管理功能需求文档

基于原Angular代码分析，配置分发管理模块需要实现以下功能：

## 1. 主界面结构 (mu-management)

### 功能描述
- 配置分发管理的主界面，采用标签页布局
- 包含三个主要功能模块的标签页切换

### 用户交互
- 标签页切换：非RMU、RMU、分发状态查看
- 每个标签页独立加载数据和功能

### 界面布局
```
标签页导航：
├── 非RMU (mu-list)
├── RMU (rmu-list) 
└── 分发状态查看 (mu-global-distribute-status)
```

## 2. 非RMU监控单元管理 (mu-list)

### 功能描述
- 管理非RMU类型的监控单元
- 支持批量操作和单个操作
- 提供配置分发和状态监控功能

### API接口
- `GET api/config/monitor-unit` - 获取监控单元列表
- `GET api/config/monitor-unit/types` - 获取监控单元类型
- `GET api/config/monitor-unit/log?monitorUnitId={id}` - 获取分发日志

### 用户交互
1. **列表显示**
   - 表格显示：监控单元ID、名称、类型、IP、配置同步状态、同步时间、下端口
   - 支持多列筛选搜索
   - 支持列排序

2. **批量操作**
   - 全选/取消全选复选框
   - 批量下发按钮（需要至少选择一个单元）

3. **行操作（右键菜单）**
   - 生成和分发监控单元配置
   - Telnet到监控单元
   - 重启（禁用状态）
   - 导出Excel（禁用状态）

4. **状态显示**
   - 不同颜色图标表示状态：
     - 红色：下发失败（可点击查看失败原因）
     - 橙色：正在下发
     - 绿色：下发成功
     - 蓝色：待下发
     - 灰色：无需下发

### 数据过滤
- 过滤条件：排除 `monitorUnitCategory` 为1和24的设备

## 3. RMU监控单元管理 (rmu-list)

### 功能描述
- 管理RMU类型的监控单元
- 支持批量生成和分发配置

### API接口
- `GET api/config/monitor-unit` - 获取监控单元列表
- `GET api/config/monitor-unit/types` - 获取监控单元类型

### 用户交互
1. **列表显示**
   - 表格显示：所属局站、监控单元ID、类型、IP、RMU名称、监控单元名称、下端口
   - 支持多列筛选搜索

2. **批量操作**
   - 批量生成和分发监控单元配置

3. **行操作（右键菜单）**
   - 生成和分发监控单元配置
   - 导出Excel（禁用状态）

### 数据过滤
- 过滤条件：仅显示 `monitorUnitCategory` 为1或24的设备

## 4. 监控单元配置分发流程 (mu-distribution)

### 功能描述
- 三步骤的配置分发向导
- 支持WebSocket实时通信
- 提供分发状态监控和日志查看

### API接口
- WebSocket连接：`/api/config/websocket/monitor`
- `GET api/config/monitor-unit/active?monitorUnitIds={ids}` - 获取活动状态
- `GET api/config/monitorunitxml/downloadMultipleMonitorUnitConfigXML?monitorUnitIds={ids}` - 下载配置文件

### 分发流程

#### 步骤1：选择监控单元
**用户交互：**
- 监控单元列表表格显示
- 全选/单选复选框
- 多列筛选：监控单元名称、类型、IP
- 显示已选择的监控单元数量

#### 步骤2：生成配置文件
**用户交互：**
- 生成按钮触发配置文件生成
- 下载生成的配置文件链接（生成成功后显示）

**WebSocket消息：**
```json
{
  "webSocketBusinessType": 1,
  "user": "用户名",
  "passWord": "密码", 
  "port": 端口,
  "monitorUnitIds": "监控单元ID列表"
}
```

#### 步骤3：配置分发及状态查看
**用户交互：**
- 分发参数设置表单：
  - 用户名（默认：root）
  - 密码（默认：hello）
  - 端口（默认：21）
  - 启用加密传输复选框（SFTP/FTP切换）
- 下发按钮
- 双标签页显示：
  - 日志标签：实时显示分发日志
  - 状态标签：显示各监控单元分发状态

**分发状态：**
- 0：无需下发
- 1：待下发  
- 2：正在下发
- 3：下发成功
- 4：下发失败

**WebSocket消息：**
```json
{
  "webSocketBusinessType": 2,
  "user": "用户名",
  "passWord": "密码",
  "port": 端口,
  "protocol": "ftp/sftp",
  "monitorUnitIds": "监控单元ID列表"
}
```

### 特殊逻辑
- 针对特定设备类型（monitorUnitCategory == 18 || == 17）自动设置特殊密码
- GFSU V3设备自动启用SFTP协议

## 5. 分发状态查看 (mu-global-distribute-status)

### 功能描述
- 全局查看所有监控单元的分发状态
- 实时刷新状态和日志
- 支持筛选和状态显示

### API接口
- `GET api/config/monitor-unit/active` - 获取活动监控单元状态
- `GET api/config/monitor-unit/types` - 获取监控单元类型
- `GET api/config/monitor-unit/log?monitorUnitId={id}` - 获取具体监控单元日志

### 用户交互
1. **左侧状态列表**
   - 表格显示：所属局站、监控单元名称、类型、IP、状态
   - 多列筛选搜索
   - 点击行选择监控单元

2. **右侧日志显示**
   - 显示选中监控单元的分发日志
   - 实时刷新（2秒间隔）
   - 日志标题显示监控单元名称

3. **状态图标**
   - 使用不同颜色和图标表示分发状态
   - 状态含义与监控单元列表一致

### 自动刷新机制
- 列表状态：2秒间隔自动刷新
- 日志内容：2秒间隔自动刷新（当有选中监控单元时）

## 6. 配置生成和下载 (generate-distribution-mu)

### 功能描述
- 显示配置文件生成进度
- 自动触发文件下载
- 进度条动画效果

### API接口
- `GET api/config/monitorunitxml/createAndDownloadMonitorUnitConfigXML?workStationIds={ids}` - 生成并下载配置文件

### 用户交互
1. **进度显示**
   - 进度条显示生成进度（0-100%）
   - 进度动画（300ms间隔递增）
   - 88%时暂停等待服务器响应

2. **文件下载**
   - 自动解析响应头获取文件名
   - 自动触发浏览器下载
   - 成功提示消息

3. **错误处理**
   - 生成失败时进度条变红色
   - 显示错误提示消息

## 7. RMU监控单元详情 (rmu-mulist)

### 功能描述
- 显示特定RMU下的监控单元详细信息
- 支持端口冲突检查
- 支持配置生成

### API接口
- `GET api/config/monitor-unit/rmu/{workStationId}` - 获取RMU下的监控单元
- `GET api/config/port/checkrmuportconflict/{workStationId}` - 检查端口冲突

### 用户交互
1. **列表显示**
   - 局站ID、局站名称、监控单元ID、监控单元名称、监控单元IP、RMU名称、RMU ID

2. **操作功能**
   - 生成配置按钮
   - 检查端口冲突功能
   - 确认操作

## 8. 技术要求

### WebSocket通信
- 连接地址：`/api/config/websocket/monitor`
- 支持心跳检测（ping/pong）
- 自动重连机制
- 会话ID和用户ID验证

### 定时任务
- 状态刷新：2-3秒间隔
- WebSocket重连：10秒间隔
- 心跳检测：根据配置间隔

### 错误处理
- 网络异常重连
- API调用失败提示
- WebSocket连接异常处理

### 数据筛选
- 支持多列同时筛选
- 实时筛选结果更新
- 筛选条件持久化

## 9. 页面路由设计建议

```
/config-distribute-management
├── /mu-list (非RMU列表)
├── /rmu-list (RMU列表)
└── /distribute-status (分发状态)
```

## 10. 组件设计建议

```
ConfigDistributeManagement (主组件)
├── MuList (非RMU列表)
├── RmuList (RMU列表)  
├── DistributeStatus (分发状态)
├── MuDistribution (分发流程弹窗)
├── GenerateProgress (生成进度弹窗)
└── RmuMuList (RMU详情弹窗)
```
