<template>
  <div class="mu-list-container">
    <!-- 操作工具栏 -->
    <div class="mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <el-button 
            type="primary" 
            :disabled="selectedRows.length === 0 || isLoading"
            @click="handleBatchDistribute"
          >
            <el-icon size="16" class="mr-2"><Download /></el-icon>
            批量下发
          </el-button>
          <el-button 
            type="default" 
            :loading="isLoading"
            @click="refreshList"
          >
            <el-icon size="16" class="mr-2"><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
        <span class="text-sm text-gray-600 dark:text-gray-400">
          共 {{ filteredTableData.length }} 个监控单元
        </span>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
      <div style="height: 800px">
        <el-auto-resizer>
          <template #default="{ height, width }">
            <el-table-v2
              v-loading="isLoading"
              :columns="tableColumns"
              :data="filteredTableData"
              :width="width"
              :height="height"
              fixed
              :row-height="60"
              @row-select="handleRowSelect"
            />
          </template>
        </el-auto-resizer>
      </div>
    </div>

    <!-- 监控单元分发配置对话框 -->
    <MuDistribution
      v-model:visible="distributionDialogVisible"
      :mu-list="selectedRows"
      :monitor-unit-categories="muTypes"
      @close="handleDistributionClose"
      :key="distributionDialogKey"
    />

    <!-- 失败日志对话框 -->
    <el-dialog
      v-model="logDialogVisible"
      :title="logDialogTitle"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg max-h-96 overflow-auto">
        <div v-html="logContent" class="text-sm font-mono whitespace-pre-wrap text-red-600 dark:text-red-400" />
      </div>
      <template #footer>
        <div class="flex justify-end">
          <el-button @click="logDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="tsx">
import { ref, computed, onMounted, watch } from "vue";
import {
  Download,
  Refresh,
  Check,
  Warning,
  Loading,
  CircleCheck,
  CircleClose,
  Monitor,
  Setting
} from "@element-plus/icons-vue";
import { 
  ElMessage, 
  ElMessageBox,
  ElButton,
  ElIcon,
  ElTag,
  ElInput,
  ElSelect,
  ElOption,
  ElCheckbox
} from "element-plus";
import type { Column } from "element-plus";
import ContextMenu from '@imengyu/vue3-context-menu';
import MuDistribution from './MuDistribution.vue';
import { 
  getMonitorUnitList as getMonitorUnitListAPI,
  getMonitorUnitTypes as getMonitorUnitTypesAPI,
  getMonitorUnitLog as getMonitorUnitLogAPI,
  batchDistributeConfig,
  distributeConfig,
  type MonitorUnit,
  type MonitorUnitType,
  type ApiResponse
} from '@/api/config-distribute';

// Props
interface Props {
  tabIndex: number;
}

const props = defineProps<Props>();

defineOptions({
  name: "MuList"
});

// 状态定义
const isLoading = ref(false);
const selectedRows = ref<MonitorUnit[]>([]);
const tableData = ref<MonitorUnit[]>([]);
const distributionDialogVisible = ref(false);
const logDialogVisible = ref(false);
const logDialogTitle = ref('');
const logContent = ref('');

// 分发配置弹框
const distributionDialogKey = ref(0);

// 输入框筛选状态
const filterInputs = ref({
  monitorUnitId: '',
  monitorUnitName: '',
  ipAddress: ''
});

// 下拉筛选状态
const filterSelects = ref({
  typeName: '',
  statusTxt: ''
});

// 监控单元类型映射
const muTypes = ref<MonitorUnitType[]>([]);
const muTypeObj = ref<Record<number, string>>({});

// 监听tab切换，激活时刷新数据
watch(() => props.tabIndex, (newIndex) => {
  if (newIndex === 0) {
    refreshList();
  }
});

// 过滤后的数据 (过滤RMU类型 + 输入框筛选)
const filteredTableData = computed(() => {
  let data = tableData.value.filter(item => 
    // 过滤掉RMU类型的设备 (monitorUnitCategory 1和24)
    item.monitorUnitCategory !== 1 && item.monitorUnitCategory !== 24
  );

  // 应用输入框筛选
  if (filterInputs.value.monitorUnitId) {
    data = data.filter(item => 
      item.monitorUnitId.toLowerCase().includes(filterInputs.value.monitorUnitId.toLowerCase())
    );
  }
  
  if (filterInputs.value.monitorUnitName) {
    data = data.filter(item => 
      item.monitorUnitName.toLowerCase().includes(filterInputs.value.monitorUnitName.toLowerCase())
    );
  }
  
  if (filterInputs.value.ipAddress) {
    data = data.filter(item => 
      item.ipAddress.toLowerCase().includes(filterInputs.value.ipAddress.toLowerCase())
    );
  }
  
  // 应用下拉筛选
  if (filterSelects.value.typeName) {
    data = data.filter(item => item.typeName === filterSelects.value.typeName);
  }
  
  if (filterSelects.value.statusTxt) {
    data = data.filter(item => item.statusTxt === filterSelects.value.statusTxt);
  }

  return data;
});

// 获取唯一值用于筛选器
const getUniqueValues = (prop: string) => {
  const values = [...new Set(filteredTableData.value.map(item => item[prop as keyof MonitorUnit]?.toString()).filter(Boolean))];
  return values.map(value => ({ text: value, value }));
};

// 获取状态筛选器选项
const getStatusFilters = () => {
  return [
    { text: '无需下发', value: '无需下发' },
    { text: '待下发', value: '待下发' },
    { text: '正在下发', value: '正在下发' },
    { text: '下发成功', value: '下发成功' },
    { text: '下发失败', value: '下发失败' }
  ];
};

// 表格列配置
const tableColumns = computed<Column<MonitorUnit>[]>(() => [
  // 选择列
  {
    key: 'selection',
    width: 60,
    cellRenderer: ({ rowData }) => (
      <ElCheckbox
        modelValue={selectedRows.value.some(row => row.monitorUnitId === rowData.monitorUnitId)}
        onChange={(checked: boolean) => {
          if (checked) {
            if (!selectedRows.value.some(row => row.monitorUnitId === rowData.monitorUnitId)) {
              selectedRows.value.push(rowData);
            }
          } else {
            const index = selectedRows.value.findIndex(row => row.monitorUnitId === rowData.monitorUnitId);
            if (index > -1) {
              selectedRows.value.splice(index, 1);
            }
          }
        }}
      />
    ),
    headerCellRenderer: () => (
      <ElCheckbox
        modelValue={selectedRows.value.length === filteredTableData.value.length && filteredTableData.value.length > 0}
        indeterminate={selectedRows.value.length > 0 && selectedRows.value.length < filteredTableData.value.length}
        onChange={(checked: boolean) => {
          if (checked) {
            selectedRows.value = [...filteredTableData.value];
          } else {
            selectedRows.value = [];
          }
        }}
      />
    )
  },
  // 监控单元ID
  {
    key: 'monitorUnitId',
    title: '监控单元ID',
    dataKey: 'monitorUnitId',
    width: 220,
    cellRenderer: ({ rowData }) => (
      <span class="font-medium">{rowData.monitorUnitId}</span>
    ),
    headerCellRenderer: () => (
      <div class="flex items-center justify-between">
        <span class="mr-2 text-sm font-medium">监控单元ID</span>
        <ElInput
          modelValue={filterInputs.value.monitorUnitId}
          onInput={(value: string) => filterInputs.value.monitorUnitId = value}
          placeholder="搜索..."
          size="small"
          style="width: 120px"
          clearable
        />
      </div>
    )
  },
  // 监控单元名称
  {
    key: 'monitorUnitName',
    title: '监控单元名称',
    dataKey: 'monitorUnitName',
    width: 300,
    cellRenderer: ({ rowData }) => (
      <div class="flex items-center">
        <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center mr-3 flex-shrink-0">
          <ElIcon size={14} class="text-white">
            <Monitor />
          </ElIcon>
        </div>
        <span class="font-medium truncate" title={rowData.monitorUnitName}>
          {rowData.monitorUnitName}
        </span>
      </div>
    ),
    headerCellRenderer: () => (
      <div class="flex items-center justify-between">
        <span class="mr-2 text-sm font-medium">监控单元名称</span>
        <ElInput
          modelValue={filterInputs.value.monitorUnitName}
          onInput={(value: string) => filterInputs.value.monitorUnitName = value}
          placeholder="搜索..."
          size="small"
          style="width: 120px"
          clearable
        />
      </div>
    )
  },
  // 类型
  {
    key: 'typeName',
    title: '类型',
    dataKey: 'typeName',
    width: 180,
    cellRenderer: ({ rowData }) => (
      <ElTag type="info" effect="light">
        {rowData.typeName}
      </ElTag>
    ),
    headerCellRenderer: () => (
      <div class="flex items-center justify-between">
        <span class="mr-2 text-sm font-medium">类型</span>
        <ElSelect
          modelValue={filterSelects.value.typeName}
          onChange={(value: string) => filterSelects.value.typeName = value}
          placeholder="全部"
          size="small"
          style="width: 100px"
          clearable
        >
          {getUniqueValues('typeName').map(type => (
            <ElOption
              key={type.value}
              label={type.value}
              value={type.value}
            />
          ))}
        </ElSelect>
      </div>
    )
  },
  // IP地址
  {
    key: 'ipAddress',
    title: 'IP地址',
    dataKey: 'ipAddress',
    width: 200,
    cellRenderer: ({ rowData }) => (
      <span class="font-mono">{rowData.ipAddress}</span>
    ),
    headerCellRenderer: () => (
      <div class="flex items-center justify-between">
        <span class="mr-2 text-sm font-medium">IP地址</span>
        <ElInput
          modelValue={filterInputs.value.ipAddress}
          onInput={(value: string) => filterInputs.value.ipAddress = value}
          placeholder="搜索..."
          size="small"
          style="width: 120px"
          clearable
        />
      </div>
    )
  },
  // 配置同步状态
  {
    key: 'statusTxt',
    title: '配置同步状态',
    dataKey: 'statusTxt',
    width: 220,
    cellRenderer: ({ rowData }) => (
      <div 
        class="flex items-center cursor-pointer hover:opacity-80" 
        style={{ color: getStatusColor(rowData.state) }}
        onClick={() => rowData.state === 4 ? handleShowFailLog(rowData) : undefined}
      >
        <ElIcon size={14} class={["mr-2", { 'animate-spin': rowData.state === 2 }]}>
          <component is={getStatusIcon(rowData.state)} />
        </ElIcon>
        <span>{rowData.statusTxt}</span>
      </div>
    ),
    headerCellRenderer: () => (
      <div class="flex items-center justify-between">
        <span class="mr-2 text-sm font-medium">配置同步状态</span>
        <ElSelect
          modelValue={filterSelects.value.statusTxt}
          onChange={(value: string) => filterSelects.value.statusTxt = value}
          placeholder="全部"
          size="small"
          style="width: 100px"
          clearable
        >
          {getStatusFilters().map(status => (
            <ElOption
              key={status.value}
              label={status.text}
              value={status.value}
            />
          ))}
        </ElSelect>
      </div>
    )
  },
  // 配置同步时间
  {
    key: 'syncTime',
    title: '配置同步时间',
    dataKey: 'syncTime',
    width: 200,
    cellRenderer: ({ rowData }) => (
      <span class="text-gray-600 dark:text-gray-400">
        {rowData.syncTime || '--'}
      </span>
    )
  },
  // 监控单元下端口
  {
    key: 'portNos',
    title: '监控单元下端口',
    dataKey: 'portNos',
    width: 250,
    cellRenderer: ({ rowData }) => (
      <span class="text-gray-600 dark:text-gray-400 truncate" title={rowData.portNos}>
        {rowData.portNos || '--'}
      </span>
    )
  }
]);

// 行选择处理
const handleRowSelect = (selection: MonitorUnit[]) => {
  selectedRows.value = selection;
};

// 获取状态颜色
const getStatusColor = (state: number) => {
  const statusConfig = {
    0: '#909399',
    1: '#409EFF',
    2: '#FF7700',
    3: '#067306',
    4: '#F56C6C'
  };
  return statusConfig[state] || statusConfig[0];
};

// 获取状态图标
const getStatusIcon = (state: number) => {
  const statusConfig = {
    0: Check,
    1: Loading,
    2: Loading,
    3: CircleCheck,
    4: CircleClose
  };
  return statusConfig[state] || statusConfig[0];
};

// 获取监控单元类型
const getMonitorUnitTypes = async () => {
  try {
    const response = await getMonitorUnitTypesAPI();
    if (response.code === 0 && response.data) {
      muTypes.value = response.data;
      muTypeObj.value = {};
      response.data.forEach((element: MonitorUnitType) => {
        muTypeObj.value[element.typeId] = element.typeName;
      });
    } else {
      // 模拟数据作为后备方案
      muTypes.value = [
        { typeId: 18, typeName: 'Type-A', order: 1 },
        { typeId: 19, typeName: 'Type-B', order: 2 },
        { typeId: 20, typeName: 'Type-C', order: 3 }
      ];
      muTypeObj.value = {
        18: 'Type-A',
        19: 'Type-B', 
        20: 'Type-C'
      };
    }
  } catch (error) {
    console.error('获取监控单元类型失败:', error);
    // 使用模拟数据
    muTypes.value = [
      { typeId: 18, typeName: 'Type-A', order: 1 },
      { typeId: 19, typeName: 'Type-B', order: 2 },
      { typeId: 20, typeName: 'Type-C', order: 3 }
    ];
    muTypeObj.value = {
      18: 'Type-A',
      19: 'Type-B', 
      20: 'Type-C'
    };
  }
};

// 获取监控单元列表
const getMonitorUnitList = async () => {
  try {
    const response = await getMonitorUnitListAPI();
    if (response.code === 0 && response.data) {
      const orgData = response.data.filter((s: MonitorUnit) => 
        s.monitorUnitCategory !== 1 && s.monitorUnitCategory !== 24
      );
      
      // 处理状态文本和类型名称
      orgData.forEach((mu: MonitorUnit) => {
        switch (mu.state) {
          case 0: mu.statusTxt = "无需下发"; break;
          case 1: mu.statusTxt = "待下发"; break;
          case 2: mu.statusTxt = "正在下发"; break;
          case 3: mu.statusTxt = "下发成功"; break;
          case 4: mu.statusTxt = "下发失败"; break;
          default: mu.statusTxt = "无需下发"; break;
        }
        mu.typeName = muTypeObj.value[mu.monitorUnitCategory] || '未知类型';
      });
      
      tableData.value = orgData;
    } else {
      // 使用模拟数据作为后备方案
      throw new Error('API返回数据格式错误');
    }
  } catch (error) {
    console.error('获取监控单元列表失败:', error);
    ElMessage.error('获取监控单元列表失败，使用模拟数据');
    
    // 模拟数据
    tableData.value = [
      {
        monitorUnitId: 'MU001',
        monitorUnitName: '监控单元001',
        monitorUnitCategory: 18,
        typeName: 'Type-A',
        ipAddress: '*************',
        state: 1,
        statusTxt: '待下发',
        syncTime: '2024-01-15 14:30:00',
        portNos: 'Port1, Port2, Port3'
      },
      {
        monitorUnitId: 'MU002',
        monitorUnitName: '监控单元002',
        monitorUnitCategory: 19,
        typeName: 'Type-B',
        ipAddress: '*************',
        state: 3,
        statusTxt: '下发成功',
        syncTime: '2024-01-15 14:25:00',
        portNos: 'Port4, Port5'
      },
      {
        monitorUnitId: 'MU003',
        monitorUnitName: '监控单元003',
        monitorUnitCategory: 20,
        typeName: 'Type-C',
        ipAddress: '*************',
        state: 4,
        statusTxt: '下发失败',
        syncTime: '2024-01-15 14:20:00',
        portNos: 'Port6, Port7'
      },
      {
        monitorUnitId: 'MU004',
        monitorUnitName: '监控单元004',
        monitorUnitCategory: 18,
        typeName: 'Type-A',
        ipAddress: '*************',
        state: 2,
        statusTxt: '正在下发',
        syncTime: '2024-01-15 14:15:00',
        portNos: 'Port8'
      },
      {
        monitorUnitId: 'MU005',
        monitorUnitName: '监控单元005',
        monitorUnitCategory: 19,
        typeName: 'Type-B',
        ipAddress: '*************',
        state: 0,
        statusTxt: '无需下发',
        syncTime: '',
        portNos: 'Port9, Port10, Port11'
      }
    ];
  }
};

// 生命周期
onMounted(async () => {
  await getMonitorUnitTypes();
  if (props.tabIndex === 0) {
    refreshList();
  }
});

// 刷新列表
const refreshList = async () => {
  isLoading.value = true;
  try {
    await getMonitorUnitList();
  } finally {
    isLoading.value = false;
  }
};



// 右键菜单处理
const handleRowContextMenu = ({ event, rowData }: { event: MouseEvent; rowData: MonitorUnit }) => {
  event.preventDefault();
  
  ContextMenu.showContextMenu({
    x: event.x,
    y: event.y,
    items: [
      {
        label: "生成和分发监控单元配置",
        icon: "h:connection",
        onClick: () => {
          handleDistribute(rowData);
        }
      },
      {
        label: "Telnet到监控单元",
        icon: "h:monitor",
        onClick: () => {
          handleTelnet(rowData);
        }
      },
      {
        label: "查看分发日志",
        icon: "h:view",
        onClick: () => {
          handleViewLog(rowData);
        }
      }
    ]
  });
};

// 显示分发配置对话框
const showDistributionDialog = () => {
  distributionDialogKey.value++; // 强制重新创建组件
  distributionDialogVisible.value = true;
};

// 处理分发配置对话框关闭
const handleDistributionClose = () => {
  distributionDialogVisible.value = false;
  // 刷新列表数据
  refreshList();
};

// 批量分发处理 - 改为打开分发对话框
const handleBatchDistribute = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要分发的监控单元');
    return;
  }
  
  showDistributionDialog();
};

// 单个分发
const handleDistribute = async (rowData: MonitorUnit) => {
  try {
    await ElMessageBox.confirm(
      `确认要分发 "${rowData.monitorUnitName}" 的配置吗？`,
      '分发确认',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    const response = await distributeConfig(rowData.monitorUnitId);
    if (response.code === 0) {
      ElMessage.success(`成功启动 ${rowData.monitorUnitName} 的配置分发`);
      // 更新状态为"正在下发"
      rowData.state = 2;
      rowData.statusTxt = '正在下发';
    } else {
      ElMessage.error(response.msg || '分发失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('分发失败:', error);
      ElMessage.error('分发失败');
    }
  }
};

// Telnet连接
const handleTelnet = (rowData: MonitorUnit) => {
  ElMessage.info(`连接到 ${rowData.monitorUnitName} (${rowData.ipAddress})`);
  // TODO: 实现Telnet连接逻辑
};

// 查看日志
const handleViewLog = (rowData: MonitorUnit) => {
  ElMessage.info(`查看 ${rowData.monitorUnitName} 的分发日志`);
  // TODO: 实现日志查看功能
};

// 显示失败日志
const handleShowFailLog = async (rowData: MonitorUnit) => {
  try {
    const response = await getMonitorUnitLogAPI(rowData.monitorUnitId);
    if (response.code === 0 && response.data) {
      logDialogTitle.value = `${rowData.monitorUnitName}—下发失败日志`;
      logContent.value = response.data.replaceAll('\n', '<br>');
      logDialogVisible.value = true;
    } else {
      // 使用模拟日志作为后备方案
      throw new Error('API返回数据格式错误');
    }
  } catch (error) {
    console.error('获取失败日志失败:', error);
    ElMessage.warning('获取失败日志失败，显示模拟日志');
    
    // 模拟失败日志
    logDialogTitle.value = `${rowData.monitorUnitName}—下发失败日志`;
    logContent.value = `连接失败: 无法连接到 ${rowData.ipAddress}:22
SSH连接超时
错误码: ECONNREFUSED
时间: 2024-01-15 14:20:15

重试3次后仍然失败
建议检查网络连接和目标设备状态`;
    logDialogVisible.value = true;
  }
};
</script>

<style scoped>
.mu-list-container {
  height: 100%;
  overflow: hidden;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 表格样式 */
:deep(.el-table-v2__header-cell) {
  border-bottom: 1px solid var(--el-border-color);
}

:deep(.el-table-v2__row-cell) {
  border-bottom: 1px solid var(--el-border-color-lighter);
}

:deep(.el-table-v2__row:hover) {
  background-color: var(--el-bg-color-page);
}

/* 筛选输入框样式 */
:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-border-color) inset;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset;
}
</style> 