<template>
  <div class="distribute-status-container">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 h-full">
      <!-- 左侧：状态列表 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            分发状态列表
          </h3>
          <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
            点击行查看详细日志
          </p>
        </div>
        
        <div style="height: 500px">
          <el-auto-resizer>
            <template #default="{ height, width }">
              <el-table-v2
                v-loading="isLoading"
                :columns="statusTableColumns"
                :data="filteredStatusData"
                :width="width"
                :height="height"
                fixed
                :row-height="50"
                @row-click="handleRowClick"
              />
            </template>
          </el-auto-resizer>
        </div>
      </div>

      <!-- 右侧：日志显示 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            {{ currentLogTitle }}
          </h3>
          <div class="flex items-center mt-2">
            <el-button 
              size="small" 
              type="primary" 
              :loading="isRefreshingLog"
              @click="refreshCurrentLog"
            >
              <el-icon size="14" class="mr-1"><Refresh /></el-icon>
              刷新日志
            </el-button>
            <span class="text-xs text-gray-500 dark:text-gray-400 ml-3">
              自动刷新间隔：2秒
            </span>
          </div>
        </div>
        
        <div class="p-4">
          <el-input
            v-model="logContent"
            type="textarea"
            readonly
            :rows="20"
            resize="none"
            placeholder="请点击左侧监控单元查看分发日志..."
            class="log-textarea"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, computed, onMounted, onUnmounted, watch } from "vue";
import {
  Refresh,
  Check,
  Loading,
  CircleCheck,
  CircleClose,
  Warning
} from "@element-plus/icons-vue";
import { 
  ElMessage,
  ElIcon,
  ElTag,
  ElInput,
  ElButton
} from "element-plus";
import type { Column } from "element-plus";
import { 
  getGlobalDistributeStatus,
  getDistributeStatusLog,
  createWebSocketConnection,
  setupWebSocketHandlers,
  type DistributeStatus,
  type WebSocketMessage,
  type ApiResponse
} from '@/api/config-distribute';

// Props
interface Props {
  tabIndex: number;
}

const props = defineProps<Props>();

defineOptions({
  name: "DistributeStatus"
});

// 状态定义
const isLoading = ref(false);
const isRefreshingLog = ref(false);
const statusData = ref<DistributeStatus[]>([]);
const selectedMonitorUnit = ref<DistributeStatus | null>(null);
const logContent = ref("");
const currentLogTitle = ref("分发日志");

// 定时器和WebSocket
let statusRefreshTimer: any = null;
let logRefreshTimer: any = null;
let webSocket: WebSocket | null = null;

// 监听tab切换，激活时开始刷新
watch(() => props.tabIndex, (newIndex) => {
  if (newIndex === 1) {
    startAutoRefresh();
  } else {
    stopAutoRefresh();
  }
});

// 过滤后的状态数据
const filteredStatusData = computed(() => {
  return statusData.value;
});

// 状态图标组件
const StatusIcon = ({ state }: { state: number }) => {
  const statusConfig = {
    0: { icon: <Check />, color: '#909399', text: '无需下发' },
    1: { icon: <Loading />, color: '#409EFF', text: '待下发' },
    2: { icon: <Loading class="animate-spin" />, color: '#FF7700', text: '正在下发' },
    3: { icon: <CircleCheck />, color: '#067306', text: '下发成功' },
    4: { icon: <CircleClose />, color: '#F56C6C', text: '下发失败' }
  };
  
  const config = statusConfig[state] || statusConfig[0];
  
  return (
    <div class="flex items-center" style={{ color: config.color }}>
      <ElIcon size={14} class="mr-2">
        {config.icon}
      </ElIcon>
      <span>{config.text}</span>
    </div>
  );
};

// 状态表格列配置
const statusTableColumns = computed<Column<DistributeStatus>[]>(() => [
  // 所属局站
  {
    key: 'stationName',
    title: '所属局站',
    dataKey: 'stationName',
    width: 120,
    cellRenderer: ({ rowData }) => (
      <span class="text-gray-700 dark:text-gray-300" title={rowData.stationName}>
        {rowData.stationName}
      </span>
    )
  },
  // 监控单元名称
  {
    key: 'unitName',
    title: '监控单元名称',
    dataKey: 'unitName',
    width: 180,
    cellRenderer: ({ rowData }) => (
      <div class="flex items-center">
        <div class="w-6 h-6 bg-primary rounded flex items-center justify-center mr-2 flex-shrink-0">
          <ElIcon size={12} class="text-white">
            <Warning />
          </ElIcon>
        </div>
        <span class="font-medium truncate" title={rowData.unitName}>
          {rowData.unitName}
        </span>
      </div>
    )
  },
  // 类型
  {
    key: 'unitType',
    title: '类型',
    dataKey: 'unitType',
    width: 100,
    cellRenderer: ({ rowData }) => (
      <ElTag size="small" type="info">
        {rowData.unitType}
      </ElTag>
    )
  },
  // IP地址
  {
    key: 'ipAddress',
    title: 'IP',
    dataKey: 'ipAddress',
    width: 120,
    cellRenderer: ({ rowData }) => (
      <span class="font-mono text-sm">{rowData.ipAddress}</span>
    )
  },
  // 状态
  {
    key: 'status',
    title: '状态',
    dataKey: 'status',
    width: 120,
    cellRenderer: ({ rowData }) => (
      <StatusIcon state={rowData.status || 0} />
    )
  }
]);

// 生命周期
onMounted(() => {
  if (props.tabIndex === 1) {
    startAutoRefresh();
  }
});

onUnmounted(() => {
  stopAutoRefresh();
});

// 开始自动刷新
const startAutoRefresh = () => {
  refreshStatusList();
  setupWebSocketConnection();
  
  // 状态列表定时刷新（作为WebSocket的后备方案）
  if (statusRefreshTimer) {
    clearInterval(statusRefreshTimer);
  }
  statusRefreshTimer = setInterval(() => {
    refreshStatusList();
  }, 10000); // 减少轮询频率，主要依赖WebSocket
};

// 停止自动刷新
const stopAutoRefresh = () => {
  if (statusRefreshTimer) {
    clearInterval(statusRefreshTimer);
    statusRefreshTimer = null;
  }
  if (logRefreshTimer) {
    clearInterval(logRefreshTimer);
    logRefreshTimer = null;
  }
  if (webSocket) {
    webSocket.close();
    webSocket = null;
  }
};

// 设置WebSocket连接
const setupWebSocketConnection = () => {
  try {
    // TODO: 替换为实际的WebSocket地址
    const wsUrl = '/ws/config-distribute-status';
    webSocket = createWebSocketConnection(wsUrl);
    
    setupWebSocketHandlers(
      webSocket,
      (message: WebSocketMessage) => {
        handleWebSocketMessage(message);
      },
      (error: Event) => {
        console.error('WebSocket连接错误:', error);
        ElMessage.warning('实时连接断开，使用定时刷新模式');
      },
      (event: CloseEvent) => {
        console.log('WebSocket连接关闭:', event);
        if (event.code !== 1000) {
          // 非正常关闭，尝试重连
          setTimeout(() => {
            if (props.tabIndex === 1) {
              setupWebSocketConnection();
            }
          }, 5000);
        }
      }
    );
  } catch (error) {
    console.error('WebSocket连接失败:', error);
  }
};

// 处理WebSocket消息
const handleWebSocketMessage = (message: WebSocketMessage) => {
  switch (message.type) {
    case 'status':
      // 更新状态数据
      if (Array.isArray(message.data)) {
        statusData.value = message.data;
      }
      break;
    case 'log':
      // 更新日志内容
      if (selectedMonitorUnit.value && message.data.unitName === selectedMonitorUnit.value.unitName) {
        logContent.value = message.data.logContent || '';
      }
      break;
    case 'progress':
      // 更新进度信息
      const progressUpdate = message.data;
      const targetUnit = statusData.value.find(unit => 
        unit.unitName === progressUpdate.unitName && unit.stationName === progressUpdate.stationName
      );
      if (targetUnit) {
        targetUnit.status = progressUpdate.status;
        targetUnit.statusText = progressUpdate.statusText;
        targetUnit.progress = progressUpdate.progress;
        targetUnit.lastUpdate = new Date().toLocaleString();
      }
      break;
  }
};

// 刷新状态列表
const refreshStatusList = async () => {
  if (isLoading.value) return;
  
  isLoading.value = true;
  try {
    const response = await getGlobalDistributeStatus();
    if (response.code === 0 && response.data) {
      statusData.value = response.data;
    } else {
      // 使用模拟数据作为后备方案
      throw new Error('API返回数据格式错误');
    }
  } catch (error) {
    console.error("获取状态列表失败", error);
    
    // 模拟数据
    statusData.value = [
      {
        stationName: '局站A',
        unitName: '监控单元001',
        unitType: 'Type-A',
        ipAddress: '*************',
        status: 1,
        statusText: '待下发',
        progress: 0,
        lastUpdate: new Date().toLocaleString()
      },
      {
        stationName: '局站B',
        unitName: '监控单元002',
        unitType: 'Type-B',
        ipAddress: '*************',
        status: 3,
        statusText: '下发成功',
        progress: 100,
        lastUpdate: new Date().toLocaleString()
      },
      {
        stationName: '局站C',
        unitName: '监控单元003',
        unitType: 'Type-A',
        ipAddress: '*************',
        status: 4,
        statusText: '下发失败',
        progress: 0,
        lastUpdate: new Date().toLocaleString()
      },
      {
        stationName: '局站D',
        unitName: '监控单元004',
        unitType: 'Type-C',
        ipAddress: '*************',
        status: 2,
        statusText: '正在下发',
        progress: 45,
        lastUpdate: new Date().toLocaleString()
      }
    ];
  } finally {
    isLoading.value = false;
  }
};

// 行点击事件
const handleRowClick = (rowData: DistributeStatus) => {
  selectedMonitorUnit.value = rowData;
  currentLogTitle.value = `${rowData.unitName} — 分发日志`;
  
  // 停止之前的日志刷新
  if (logRefreshTimer) {
    clearInterval(logRefreshTimer);
  }
  
  // 立即获取日志
  refreshCurrentLog();
  
  // 开始日志定时刷新
  logRefreshTimer = setInterval(() => {
    refreshCurrentLog();
  }, 2000);
};

// 刷新当前选中监控单元的日志
const refreshCurrentLog = async () => {
  if (!selectedMonitorUnit.value) {
    ElMessage.warning("请先选择一个监控单元");
    return;
  }
  
  isRefreshingLog.value = true;
  try {
    const response = await getDistributeStatusLog(
      selectedMonitorUnit.value.stationName, 
      selectedMonitorUnit.value.unitName
    );
    
    if (response.code === 0 && response.data) {
      logContent.value = response.data.join('\n');
    } else {
      // 使用模拟日志作为后备方案
      throw new Error('API返回数据格式错误');
    }
  } catch (error) {
    console.error("获取日志失败", error);
    
    // 模拟日志数据
    const timestamp = new Date().toLocaleString();
    const mockLogs = [
      `[${timestamp}] 开始配置分发流程...`,
      `[${timestamp}] 连接监控单元 ${selectedMonitorUnit.value.ipAddress}`,
      `[${timestamp}] 验证连接状态...`,
      `[${timestamp}] 开始传输配置文件...`,
      `[${timestamp}] 配置文件传输完成`,
      `[${timestamp}] 应用配置设置...`,
    ];
    
    // 根据状态添加不同的日志内容
    if (selectedMonitorUnit.value.status === 2) {
      mockLogs.push(`[${timestamp}] 正在处理配置，请稍候...`);
    } else if (selectedMonitorUnit.value.status === 3) {
      mockLogs.push(`[${timestamp}] 配置分发成功完成`);
    } else if (selectedMonitorUnit.value.status === 4) {
      mockLogs.push(`[${timestamp}] 错误：配置分发失败 - 连接超时`);
    }
    
    logContent.value = mockLogs.join('\n');
  } finally {
    isRefreshingLog.value = false;
  }
};
</script>

<style scoped>
.distribute-status-container {
  height: 100%;
}

.log-textarea {
  :deep(.el-textarea__inner) {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    background-color: var(--el-bg-color-page);
    border: 1px solid var(--el-border-color);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 表格行点击样式 */
:deep(.el-table-v2__row:hover) {
  background-color: var(--el-color-primary-light-9);
  cursor: pointer;
}
</style> 