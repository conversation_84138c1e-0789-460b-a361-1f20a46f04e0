<template>
  <div class="distribute-status-container h-full flex flex-col">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 h-full flex-1 min-h-0">
      <!-- 左侧：状态列表 -->
      <div
        class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"
      >
        <div class="p-3 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-base font-semibold text-gray-900 dark:text-white">
            分发状态列表
          </h3>
        </div>

        <div class="table-container" style="height: calc(100% - 60px)">
          <el-table
            v-loading="isLoading"
            :data="filteredStatusData"
            stripe
            :row-class-name="getRowClassName"
            highlight-current-row
            @row-click="handleRowClick"
          >
            <el-table-column
              prop="stationName"
              label="所属局站"
              width="150"
              show-overflow-tooltip
            />
            <el-table-column
              prop="monitorUnitName"
              label="监控单元名称"
              width="200"
              show-overflow-tooltip
            />
            <el-table-column
              prop="typeName"
              label="类型"
              width="120"
              show-overflow-tooltip
            />
            <el-table-column
              prop="ipAddress"
              label="IP"
              width="130"
              show-overflow-tooltip
            />
            <el-table-column prop="statusText" label="状态" width="120">
              <template #default="{ row }">
                <div class="flex items-center">
                  <el-icon
                    :size="12"
                    class="mr-1"
                    :class="getStatusIconClass(row.state)"
                  >
                    <component :is="getStatusIcon(row.state)" />
                  </el-icon>
                  <span :class="getStatusTextClass(row.state)">{{
                    row.statusText
                  }}</span>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 右侧：日志显示 -->
      <div
        class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"
      >
        <div class="p-3 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-base font-semibold text-gray-900 dark:text-white">
            {{ currentLogTitle }}
          </h3>
        </div>

        <div class="p-3" style="height: calc(100% - 60px)">
          <el-input
            v-model="logContent"
            type="textarea"
            readonly
            placeholder="请点击左侧监控单元查看分发日志..."
            class="log-textarea"
            :rows="20"
            resize="none"
            style="height: 100%"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from "vue";
import {
  Check,
  Loading,
  CircleCheck,
  CircleClose,
  Warning
} from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import {
  getActiveMonitorUnits,
  getMonitorUnitTypes,
  getMonitorUnitLog,
  type MonitorUnit,
  type MonitorUnitType,
  type ApiResponse
} from "@/api/config-distribute";

// Props
interface Props {
  tabIndex?: number;
}

const props = withDefaults(defineProps<Props>(), {
  tabIndex: 0
});

defineOptions({
  name: "DistributeStatus"
});

// 扩展监控单元接口，添加状态文本字段
interface ExtendedMonitorUnit extends MonitorUnit {
  statusText: string;
  typeName: string;
}

// 状态定义
const isLoading = ref(false);
const statusData = ref<ExtendedMonitorUnit[]>([]);
const selectedMonitorUnit = ref<ExtendedMonitorUnit | null>(null);
const logContent = ref("");
const currentLogTitle = ref("分发日志");
const muTypeObj = ref<Record<number, string>>({});

// 定时器
let statusRefreshTimer: any = null;
let logRefreshTimer: any = null;

// 监听tab切换，激活时开始刷新
watch(
  () => props.tabIndex,
  newIndex => {
    if (newIndex === 2) {
      // 分发状态是第3个tab，索引为2
      startAutoRefresh();
    } else {
      stopAutoRefresh();
    }
  }
);

// 过滤后的状态数据
const filteredStatusData = computed(() => {
  return statusData.value;
});

// 获取监控单元类型
const getMonitorUnitTypesData = async () => {
  try {
    const response = await getMonitorUnitTypes();
    if (response.code === 0 && response.data) {
      const typeObj: Record<number, string> = {};
      response.data.forEach((element: MonitorUnitType) => {
        typeObj[element.typeId] = element.typeName;
      });
      muTypeObj.value = typeObj;

      // 更新现有数据的类型名称
      statusData.value.forEach(item => {
        item.typeName = typeObj[item.monitorUnitCategory] || "未知类型";
      });
    }
  } catch (error) {
    console.error("获取监控单元类型失败", error);
  }
};

// 刷新状态列表
const refreshStatusList = async () => {
  if (isLoading.value) return;

  isLoading.value = true;
  try {
    const response = await getActiveMonitorUnits();
    if (response.code === 0 && response.data) {
      // 处理状态文本
      const processedData = response.data.map(item => {
        let statusText = "";
        switch (item.state) {
          case 0:
            statusText = "无需下发";
            break;
          case 1:
            statusText = "待下发";
            break;
          case 2:
            statusText = "正在下发";
            break;
          case 3:
            statusText = "下发成功";
            break;
          case 4:
            statusText = "下发失败";
            break;
          default:
            statusText = "未知状态";
            break;
        }
        return {
          ...item,
          statusText,
          typeName: muTypeObj.value[item.monitorUnitCategory] || "未知类型"
        } as ExtendedMonitorUnit;
      });

      statusData.value = processedData;

      // 如果当前没有数据，清空日志显示
      if (processedData.length === 0) {
        logContent.value = "";
        currentLogTitle.value = "分发日志";
        selectedMonitorUnit.value = null;
      }
    } else {
      ElMessage.error(response.msg || "获取分发状态失败");
    }
  } catch (error) {
    console.error("获取状态列表失败", error);
    ElMessage.error("获取分发状态失败");
  } finally {
    isLoading.value = false;

    // 设置下次轮询
    if (statusRefreshTimer) {
      clearTimeout(statusRefreshTimer);
    }
    statusRefreshTimer = setTimeout(() => {
      refreshStatusList();
    }, 2000);
  }
};

// 刷新当前选中监控单元的日志
const refreshCurrentLog = async () => {
  if (!selectedMonitorUnit.value) {
    return;
  }

  try {
    const response = await getMonitorUnitLog(
      selectedMonitorUnit.value.monitorUnitId
    );

    if (response.code === 0) {
      logContent.value = response.data || "";
    } else {
      console.error("获取日志失败:", response.msg);
    }
  } catch (error) {
    console.error("获取日志失败", error);
  } finally {
    // 设置下次日志轮询
    if (logRefreshTimer) {
      clearTimeout(logRefreshTimer);
    }
    logRefreshTimer = setTimeout(() => {
      refreshCurrentLog();
    }, 2000);
  }
};

// 行点击事件
const handleRowClick = (row: ExtendedMonitorUnit) => {
  selectedMonitorUnit.value = row;
  currentLogTitle.value = `${row.monitorUnitName} — 分发日志`;

  // 停止之前的日志刷新
  if (logRefreshTimer) {
    clearTimeout(logRefreshTimer);
  }

  // 立即获取日志
  refreshCurrentLog();
};

// 获取状态图标
const getStatusIcon = (state: number) => {
  switch (state) {
    case 0:
      return Check;
    case 1:
      return Loading;
    case 2:
      return Loading;
    case 3:
      return CircleCheck;
    case 4:
      return CircleClose;
    default:
      return Warning;
  }
};

// 获取状态图标样式类
const getStatusIconClass = (state: number) => {
  switch (state) {
    case 0:
      return "text-gray-500";
    case 1:
      return "text-blue-500";
    case 2:
      return "text-orange-500 animate-spin";
    case 3:
      return "text-green-600";
    case 4:
      return "text-red-500";
    default:
      return "text-gray-500";
  }
};

// 获取状态文字样式类
const getStatusTextClass = (state: number) => {
  switch (state) {
    case 0:
      return "text-gray-500";
    case 1:
      return "text-blue-500";
    case 2:
      return "text-orange-500";
    case 3:
      return "text-green-600";
    case 4:
      return "text-red-500";
    default:
      return "text-gray-500";
  }
};

// 获取行样式类名
const getRowClassName = ({ row }: { row: ExtendedMonitorUnit }) => {
  if (
    selectedMonitorUnit.value &&
    selectedMonitorUnit.value.monitorUnitId === row.monitorUnitId
  ) {
    return "selected-row";
  }
  return "";
};

// 开始自动刷新
const startAutoRefresh = () => {
  getMonitorUnitTypesData();
  refreshStatusList();
};

// 停止自动刷新
const stopAutoRefresh = () => {
  if (statusRefreshTimer) {
    clearTimeout(statusRefreshTimer);
    statusRefreshTimer = null;
  }
  if (logRefreshTimer) {
    clearTimeout(logRefreshTimer);
    logRefreshTimer = null;
  }
};

// 生命周期
onMounted(() => {
  if (props.tabIndex === 2) {
    startAutoRefresh();
  }
});

onUnmounted(() => {
  stopAutoRefresh();
});
</script>

<style scoped>
.distribute-status-container {
  height: 100%;
}

.table-container {
  overflow: hidden;
  height: calc(100vh - 200px);
}

.log-textarea :deep(.el-textarea__inner) {
  font-family: "Courier New", Consolas, monospace;
  font-size: 12px;
  background-color: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color);
  height: 100% !important;
  resize: none;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 表格行点击样式 */
:deep(.el-table__body tr:hover) {
  cursor: pointer;
}

:deep(.el-table__body tr.selected-row) {
  background-color: var(--el-color-primary-light-9) !important;
}

:deep(.el-table__body tr.selected-row:hover) {
  background-color: var(--el-color-primary-light-8) !important;
}

/* 表格行状态样式 */
:deep(.el-table__body tr.current-row) {
  background-color: var(--el-color-primary-light-9) !important;
}
</style>
