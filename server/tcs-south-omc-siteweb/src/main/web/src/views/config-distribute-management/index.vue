<template>
  <div
    class="config-distribute-management-container bg-gray-50 dark:bg-gray-900 p-4 flex flex-col"
    style="height: calc(100vh - 48px)"
  >
    <!-- 页面头部 -->
    <div class="mb-4 flex-shrink-0">
      <div class="flex items-center justify-between mb-3">
        <div class="flex items-center">
          <div class="w-1 h-6 bg-primary rounded-full mr-3" />
          <h1 class="text-xl font-bold text-gray-900 dark:text-white">
            配置分发管理
          </h1>
        </div>
      </div>

      <!-- 工具栏区域 -->
      <div
        class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-3"
      >
        <el-tabs
          v-model="activeTab"
          class="config-tabs"
          @tab-change="handleTabChange"
        >
          <!-- 非RMU监控单元管理 -->
          <el-tab-pane label="非RMU" name="mu-list">
          </el-tab-pane>

          <!-- 分发状态查看 -->
          <el-tab-pane label="分发状态查看" name="distribute-status">
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="flex-1 min-h-0">
      <div v-if="activeTab === 'mu-list'" class="h-full">
        <MuList :tab-index="0" />
      </div>
      <div v-else-if="activeTab === 'distribute-status'" class="h-full">
        <DistributeStatus :tab-index="2" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { Plus, Delete, Document, Share } from "@element-plus/icons-vue";
import MuList from "./components/MuList.vue";
import DistributeStatus from "./components/DistributeStatus.vue";

defineOptions({
  name: "ConfigDistributeManagement"
});

// 状态定义
const isLoading = ref(false);
const activeTab = ref("mu-list");

// Tab切换处理
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName;
};
</script>

<style scoped>
.config-distribute-management-container {
  height: calc(100vh - 48px);
}

.config-tabs {
  :deep(.el-tabs__header) {
    margin: 0 !important;
    padding: 0;
    background: transparent;
  }

  :deep(.el-tabs__nav-wrap::after) {
    display: none;
  }

  :deep(.el-tabs__item) {
    height: 40px;
    line-height: 40px;
    font-weight: 500;
    font-size: 14px;
    padding: 0 16px;
  }

  :deep(.el-tabs__content) {
    padding: 0;
    display: none;
  }

  :deep(.el-tabs__active-bar) {
    height: 2px;
  }
}
</style>
