<template>
  <div class="config-distribute-management-container min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex items-center mb-4">
        <div class="w-1 h-8 bg-primary rounded-full mr-4" />
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            配置分发管理
          </h1>
          <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
            管理配置文件的分发、下发以及监控配置分发状态
          </p>
        </div>
      </div>


    </div>

    <!-- 标签页内容区域 -->
    <div
      class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4"
    >
      <el-tabs v-model="activeTab" class="config-tabs" @tab-change="handleTabChange">
        <!-- 非RMU监控单元管理 -->
        <el-tab-pane label="非RMU" name="mu-list">
          <div class="tab-content">
            <MuList :tab-index="activeTab === 'mu-list' ? 0 : -1" />
          </div>
        </el-tab-pane>
        
        <!-- 分发状态查看 -->
        <el-tab-pane label="分发状态查看" name="distribute-status">
          <div class="tab-content">
            <DistributeStatus :tab-index="activeTab === 'distribute-status' ? 1 : -1" />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import {
  Plus,
  Delete,
  Document,
  Share
} from "@element-plus/icons-vue";
import MuList from "./components/MuList.vue";
import DistributeStatus from "./components/DistributeStatus.vue";

defineOptions({
  name: "ConfigDistributeManagement"
});

// 状态定义
const isLoading = ref(false);
const activeTab = ref("mu-list");

// Tab切换处理
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName;
};
</script>

<style scoped>
.config-distribute-management-container {
  min-h: 100vh;
}

.config-tabs {
  :deep(.el-tabs__header) {
    margin: 0 0 15px 15px !important;
    padding: 0 24px 0 48px;
    background: transparent;
  }

  :deep(.el-tabs__nav-wrap::after) {
    display: none;
  }

  :deep(.el-tabs__item) {
    height: 48px;
    line-height: 48px;
    font-weight: 500;
  }

  :deep(.el-tabs__content) {
    padding: 0;
  }
}

.tab-content {
  min-height: 600px;
}
</style> 