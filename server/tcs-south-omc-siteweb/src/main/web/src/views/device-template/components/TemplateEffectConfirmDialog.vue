<template>
  <el-dialog
    v-model="dialogVisible"
    title="模板更换影响"
    width="1000px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
  >
    <!-- 表格容器 -->
    <div class="effect-table-container">
      <el-table
        v-loading="loading"
        :data="filteredEffectData"
        :height="450"
        stripe
        border
      >
        <!-- 站点列 -->
        <el-table-column prop="stationName" label="站点" width="180">
          <template #header>
            <div class="column-header">
              <div class="column-title">站点</div>
              <el-input
                v-model="searchText.stationName"
                placeholder="请输入关键字..."
                size="small"
                clearable
                :suffix-icon="Search"
                @input="handleSearch"
              />
            </div>
          </template>
          <template #default="{ row }">
            <span class="table-cell-text" :title="row.stationName">
              {{ row.stationName }}
            </span>
          </template>
        </el-table-column>

        <!-- 设备列 -->
        <el-table-column prop="equipmentName" label="设备" width="150">
          <template #header>
            <div class="column-header">
              <div class="column-title">设备</div>
              <el-input
                v-model="searchText.equipmentName"
                placeholder="请输入关键字..."
                size="small"
                clearable
                :suffix-icon="Search"
                @input="handleSearch"
              />
            </div>
          </template>
          <template #default="{ row }">
            <span class="table-cell-text" :title="row.equipmentName">
              {{ row.equipmentName }}
            </span>
          </template>
        </el-table-column>

        <!-- 配置类型列 -->
        <el-table-column prop="objectType" label="配置类型" width="130">
          <template #header>
            <div class="column-header">
              <div class="column-title">配置类型</div>
              <el-input
                v-model="searchText.objectType"
                placeholder="请输入关键字..."
                size="small"
                clearable
                :suffix-icon="Search"
                @input="handleSearch"
              />
            </div>
          </template>
          <template #default="{ row }">
            <span class="table-cell-text" :title="row.objectType">
              {{ row.objectType }}
            </span>
          </template>
        </el-table-column>

        <!-- 配置名称列 -->
        <el-table-column prop="objectName" label="配置名称" width="200">
          <template #header>
            <div class="column-header">
              <div class="column-title">配置名称</div>
              <el-input
                v-model="searchText.objectName"
                placeholder="请输入关键字..."
                size="small"
                clearable
                :suffix-icon="Search"
                @input="handleSearch"
              />
            </div>
          </template>
          <template #default="{ row }">
            <span class="table-cell-text" :title="row.objectName">
              {{ row.objectName }}
            </span>
          </template>
        </el-table-column>

        <!-- 配置描述列 -->
        <el-table-column prop="description" label="配置描述" min-width="200">
          <template #header>
            <div class="column-header">
              <div class="column-title">配置描述</div>
              <el-input
                v-model="searchText.description"
                placeholder="请输入关键字..."
                size="small"
                clearable
                :suffix-icon="Search"
                @input="handleSearch"
              />
            </div>
          </template>
          <template #default="{ row }">
            <span class="table-cell-text" :title="row.description">
              {{ row.description }}
            </span>
          </template>
        </el-table-column>

        <!-- 空状态 -->
        <template #empty>
          <div class="text-center py-8">
            <el-icon size="48" class="text-gray-400 mb-4">
              <Document />
            </el-icon>
            <p class="text-gray-500">暂无影响数据</p>
          </div>
        </template>
      </el-table>
    </div>

    <template #footer>
      <div class="flex justify-between">
        <el-button type="primary" @click="exportData"> 导出 </el-button>
        <div class="flex space-x-3">
          <el-button @click="closeDialog"> 取消 </el-button>
          <el-button type="primary" :loading="updating" @click="confirmSwitch">
            {{ updating ? "切换中..." : "确定" }}
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { ElMessage } from "element-plus";
import { Search, Document } from "@element-plus/icons-vue";
import {
  getTemplateChangeEffect,
  exportTemplateChangeEffect,
  switchTemplate,
  type TemplateChangeEffect,
  type TemplateChangeEffectParams,
  type TemplateTreeNode
} from "@/api/device-template";
import { debounce } from "lodash";

// Props
interface Props {
  visible: boolean;
  originTemplate: TemplateTreeNode | null;
  newTemplate: TemplateTreeNode | null;
  selectedDeviceIds: string[];
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  originTemplate: null,
  newTemplate: null,
  selectedDeviceIds: () => []
});

// Emits
const emit = defineEmits<{
  "update:visible": [visible: boolean];
  confirm: [];
}>();

// 响应式数据
const loading = ref(false);
const updating = ref(false);
const effectData = ref<TemplateChangeEffect[]>([]);
const searchText = ref({
  stationName: "",
  equipmentName: "",
  objectType: "",
  objectName: "",
  description: ""
});

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit("update:visible", value)
});

// 过滤后的影响数据
const filteredEffectData = computed(() => {
  let filtered = [...effectData.value];

  // 根据搜索条件过滤
  if (searchText.value.stationName) {
    filtered = filtered.filter(item =>
      item.stationName?.includes(searchText.value.stationName)
    );
  }
  if (searchText.value.equipmentName) {
    filtered = filtered.filter(item =>
      item.equipmentName?.includes(searchText.value.equipmentName)
    );
  }
  if (searchText.value.objectType) {
    filtered = filtered.filter(item =>
      item.objectType?.includes(searchText.value.objectType)
    );
  }
  if (searchText.value.objectName) {
    filtered = filtered.filter(item =>
      item.objectName?.includes(searchText.value.objectName)
    );
  }
  if (searchText.value.description) {
    filtered = filtered.filter(item =>
      item.description?.includes(searchText.value.description)
    );
  }

  return filtered;
});

// 监听对话框显示状态
watch(
  () => props.visible,
  visible => {
    if (visible) {
      loadEffectData();
      resetSearch();
    }
  }
);

// 重置搜索
const resetSearch = () => {
  searchText.value = {
    stationName: "",
    equipmentName: "",
    objectType: "",
    objectName: "",
    description: ""
  };
};

// 加载影响数据
const loadEffectData = async () => {
  if (!props.originTemplate || !props.newTemplate) {
    return;
  }

  try {
    loading.value = true;

    const params: TemplateChangeEffectParams = {
      destTemplateId: props.newTemplate.id,
      originTemplateId: props.originTemplate.id,
      equipmentIds: props.selectedDeviceIds
    };

    const response = await getTemplateChangeEffect(params);

    if (response.code === 0) {
      effectData.value = response.data || [];
    } else {
      ElMessage.error(response.msg || "获取影响数据失败");
      effectData.value = [];
    }
  } catch (error) {
    console.error("获取影响数据失败:", error);
    ElMessage.error("获取影响数据失败");
    effectData.value = [];
  } finally {
    loading.value = false;
  }
};

// 搜索处理（防抖）
const handleSearch = debounce(() => {
  // 过滤逻辑已在计算属性中处理
}, 300);

// 导出数据
const exportData = async () => {
  if (!props.originTemplate || !props.newTemplate) {
    return;
  }

  try {
    const params: TemplateChangeEffectParams = {
      destTemplateId: props.newTemplate.id,
      originTemplateId: props.originTemplate.id,
      equipmentIds: props.selectedDeviceIds
    };

    const response = await exportTemplateChangeEffect(params);

    // 创建下载链接
    const url = URL.createObjectURL(response);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${props.newTemplate.name}到${props.originTemplate.name}-切换影响.xlsx`;
    a.click();
    URL.revokeObjectURL(url);

    ElMessage.success("导出成功！");
  } catch (error) {
    console.error("导出失败:", error);
    ElMessage.error("导出失败");
  }
};

// 确认切换
const confirmSwitch = async () => {
  if (!props.originTemplate || !props.newTemplate) {
    return;
  }

  try {
    updating.value = true;

    const params: TemplateChangeEffectParams = {
      destTemplateId: props.newTemplate.id,
      originTemplateId: props.originTemplate.id,
      equipmentIds: props.selectedDeviceIds
    };

    const response = await switchTemplate(params);

    if (response.code === 0) {
      ElMessage.success(
        `成功将 ${props.selectedDeviceIds.length} 个设备从模板 "${props.originTemplate.name}" 切换到 "${props.newTemplate.name}"`
      );
      emit("confirm");
      closeDialog();
    } else {
      ElMessage.error(response.msg || "模板切换失败");
    }
  } catch (error) {
    console.error("模板切换失败:", error);
    ElMessage.error("模板切换失败");
  } finally {
    updating.value = false;
  }
};

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false;
};
</script>

<style scoped>
.effect-table-container {
  border-radius: 6px;
  overflow: hidden;
}

.column-header {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.column-title {
  height: 32px;
  line-height: 32px;
  font-weight: 600;
}

.table-cell-text {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 表格样式 */
:deep(.el-table th.el-table__cell) {
  background-color: var(--el-fill-color-lighter);
  padding: 12px 0;
}

:deep(.el-table td.el-table__cell) {
  padding: 8px 12px;
}

:deep(.el-table--border) {
  border: 1px solid var(--el-border-color);
}

:deep(.el-table--border th) {
  border-right: 1px solid var(--el-border-color);
}

:deep(.el-table--border td) {
  border-right: 1px solid var(--el-border-color);
}

/* 搜索输入框样式 */
:deep(.el-input--small .el-input__inner) {
  height: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .effect-table-container {
    font-size: 12px;
  }

  .column-header {
    gap: 4px;
  }
}
</style>
