<template>
  <div class="device-template-management dark:bg-gray-900 p-4" style="height: calc(100vh - 48px)">
    <!-- 主内容区域 -->
    <div class="flex gap-4 h-full">
      <!-- 左侧模板树 -->
      <div class="w-80 flex-shrink-0">
        <div
          class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 h-full flex flex-col"
        >
          <!-- 调整宽度的分割线 -->
          <div class="resize-handle" @mousedown="startResize">
            <div class="resize-line" />
          </div>

          <!-- 工具栏 -->
          <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center space-x-2">
              <el-tooltip content="删除模板" placement="top">
                <el-button
                  type="primary"
                  text
                  size="small"
                  :disabled="!selectedNode || !selectedNode.template"
                  title="删除模板"
                  @click="deleteSelectedTemplate"
                >
                  <el-icon size="16"><Delete /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="导出模板" placement="top">
                <el-button
                  type="primary"
                  text
                  size="small"
                  :disabled="!selectedNode || !selectedNode.template"
                  title="导出模板"
                  @click="exportSelectedTemplate"
                >
                  <el-icon size="16"><Download /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="复制模板" placement="top">
                <el-button
                  type="primary"
                  text
                  size="small"
                  :disabled="!selectedNode || !selectedNode.template"
                  title="复制模板"
                  @click="copySelectedTemplate"
                >
                  <el-icon size="16"><DocumentCopy /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="刷新模板" placement="top">
                <el-button
                  type="primary"
                  text
                  size="small"
                  :loading="loading"
                  title="刷新模板"
                  @click="refreshTemplate"
                >
                  <el-icon size="16"><Refresh /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip
                :content="showDynamic ? '隐藏动态配置模板' : '显示动态配置模板'"
                placement="top"
              >
                <el-button
                  type="primary"
                  text
                  size="small"
                  :title="showDynamic ? '隐藏动态配置模板' : '显示动态配置模板'"
                  @click="toggleDynamicTemplate"
                >
                  <el-icon size="16">
                    <Folder v-if="showDynamic" />
                    <FolderOpened v-else />
                  </el-icon>
                </el-button>
              </el-tooltip>
            </div>
          </div>

          <!-- 搜索框 -->
          <div class="px-4 pt-3 pb-4">
            <el-input
              v-model="searchText"
              placeholder="搜索模板..."
              clearable
              :prefix-icon="Search"
              size="default"
              class="mb-3"
              @input="onSearchChange"
              @keyup.enter="selectSearchNode"
            />

            <div class="text-xs text-gray-500 dark:text-gray-400">
              共 {{ countTotalTemplates(treeData) }} 个模板
            </div>
          </div>

          <!-- 树形结构 -->
          <div class="flex-1 overflow-auto p-2">
            <div v-if="loading" class="flex justify-center items-center h-32">
              <el-icon size="20" class="text-primary animate-spin">
                <Loading />
              </el-icon>
            </div>

            <div v-else-if="treeData.length === 0" class="text-center py-12">
              <div
                class="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4"
              >
                <el-icon size="20" class="text-gray-400"><Files /></el-icon>
              </div>
              <h3
                class="text-lg font-medium text-gray-900 dark:text-white mb-2"
              >
                {{ searchText ? "未找到相关模板" : "暂无模板" }}
              </h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ searchText ? "请尝试其他搜索条件" : "请联系管理员添加模板" }}
              </p>
            </div>

            <div v-else>
              <el-tree
                ref="treeRef"
                :data="treeData"
                :props="treeProps"
                :filter-node-method="filterNode"
                :highlight-current="true"
                :expand-on-click-node="false"
                :check-on-click-node="false"
                node-key="id"
                class="template-tree"
                @node-click="handleNodeClick"
                @node-contextmenu="handleContextMenu"
              >
                <template #default="{ node, data }">
                  <div class="flex items-center space-x-2 flex-1 py-1">
                    <el-icon
                      v-if="!data.template"
                      size="18"
                      class="text-orange-500"
                    >
                      <Folder />
                    </el-icon>
                    <el-icon v-else size="18" class="text-blue-500">
                      <Document />
                    </el-icon>
                    <span
                      class="flex-1 overflow-hidden text-ellipsis whitespace-nowrap"
                      :title="data.name"
                      v-html="highlightSearchText(data.name)"
                    />
                  </div>
                </template>
              </el-tree>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧内容区 -->
      <div class="flex-1 min-w-0">
        <div
          v-if="selectedNode"
          class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 h-full flex flex-col"
        >
          <!-- 使用 el-tabs 但自定义样式 -->
          <div class="flex-1 flex flex-col relative">
            <el-tabs v-model="activeTabName" class="custom-tabs flex-1">
              <el-tab-pane label="设备模板" name="template" lazy>
                <DeviceTemplateInfo
                  :template-data="selectedNode"
                  :active="activeTabName === 'template'"
                  @refresh="refreshTemplate"
                  @update-template-name="handleUpdateTemplateName"
                />
              </el-tab-pane>

              <el-tab-pane label="信号" name="signal">
                <DeviceTemplateSignal
                  :template-data="selectedNode"
                  :active="activeTabName === 'signal'"
                  :tab-index="activeTabName === 'signal' ? 1 : 0"
                  :mu-category="0"
                  :table-search-text="searchText"
                  :equipment-id="''"
                  :button-flag="false"
                  :is-root-template="isRootTemplate"
                  @refresh="handleChildRefresh"
                  @select-tab="handleTabSelect"
                />
              </el-tab-pane>

              <el-tab-pane label="事件" name="event">
                <DeviceTemplateEvent
                  :template-data="selectedNode"
                  :active="activeTabName === 'event'"
                  :tab-index="activeTabName === 'event' ? 2 : 0"
                  :mu-category="0"
                  :table-search-text="searchText"
                  :equipment-id="''"
                  :button-flag="false"
                  :is-root-template="isRootTemplate"
                  @refresh="handleChildRefresh"
                  @select-tab="handleTabSelect"
                />
              </el-tab-pane>

              <el-tab-pane label="控制" name="control">
                <DeviceTemplateControl
                  :template-data="selectedNode"
                  :active="activeTabName === 'control'"
                  :tab-index="activeTabName === 'control' ? 3 : 0"
                  :mu-category="0"
                  :table-search-text="searchText"
                  :equipment-id="''"
                  :button-flag="false"
                  :is-root-template="isRootTemplate"
                  @refresh="handleChildRefresh"
                  @select-tab="handleTabSelect"
                />
              </el-tab-pane>

              <el-tab-pane label="变更记录" name="changelog">
                <DeviceTemplateLog
                  :template-data="selectedNode"
                  :active="activeTabName === 'changelog'"
                  :tab-index="activeTabName === 'changelog' ? 4 : 0"
                  :mu-category="0"
                  :table-search-text="searchText"
                  :equipment-id="''"
                  :button-flag="false"
                  :is-root-template="isRootTemplate"
                  @refresh="handleChildRefresh"
                  @select-tab="handleTabSelect"
                />
              </el-tab-pane>
            </el-tabs>

            <!-- 模板信息显示在右上角 -->
            <div
              class="absolute top-3 right-6 flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 z-10"
            >
              <span>当前模板：</span>
              <span class="font-medium text-gray-900 dark:text-white">
                {{ selectedNode.name }}#{{ selectedNode.id }}
              </span>
              <span
                v-if="isRootTemplate"
                class="px-2 py-1 text-xs bg-orange-100 text-orange-600 rounded-full"
              >
                根模板（只读）
              </span>
            </div>
          </div>
        </div>

        <!-- 未选择状态 -->
        <div
          v-else
          class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 h-full flex items-center justify-center"
        >
          <div class="text-center">
            <div
              class="w-20 h-20 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-6"
            >
              <el-icon size="24" class="text-gray-400"><Files /></el-icon>
            </div>
            <h3 class="text-xl font-medium text-gray-900 dark:text-white mb-3">
              请选择一个模板
            </h3>
            <p class="text-gray-500 dark:text-gray-400 max-w-md">
              从左侧列表中选择要查看的设备模板，查看其详细信息和配置
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- 复制模板对话框 -->
    <CopyTemplateDialog
      v-model:visible="copyDialogVisible"
      :template-name="selectedNode?.name"
      @confirm="handleCopyConfirm"
    />

    <!-- 设备关联对话框 -->
    <DeviceAssociatedDialog
      v-model:visible="deviceAssociatedDialogVisible"
      :template-id="selectedTemplateForDevices?.id || ''"
      :template-name="selectedTemplateForDevices?.name || ''"
      :equipment-category="selectedTemplateForDevices?.equipmentCategory || ''"
      @batch-change-template="handleBatchChangeTemplate"
      @batch-distribute="handleBatchDistribute"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, watch, computed } from "vue";
import { useRoute } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import type { ElTree } from "element-plus";
import {
  Delete,
  Download,
  DocumentCopy,
  Refresh,
  Folder,
  FolderOpened,
  Search,
  Document,
  Files,
  Monitor,
  Top,
  Loading,
  Connection,
  Bell,
  Setting
} from "@element-plus/icons-vue";
import ContextMenu from "@imengyu/vue3-context-menu";

import CopyTemplateDialog from "./components/CopyTemplateDialog.vue";
import DeviceAssociatedDialog from "./components/DeviceAssociatedDialog.vue";
import DeviceTemplateInfo from "./components/DeviceTemplateInfo.vue";
import DeviceTemplateSignal from "./components/DeviceTemplateSignal.vue";
import DeviceTemplateEvent from "./components/DeviceTemplateEvent.vue";
import DeviceTemplateControl from "./components/DeviceTemplateControl.vue";
import DeviceTemplateLog from "./components/DeviceTemplateLog.vue";
import {
  getTemplateTree,
  getTemplateTreeShowHide,
  deleteTemplate,
  copyTemplate,
  exportTemplate,
  upgradeTemplate,
  getDevicesByTemplateId,
  type TemplateTreeNode
} from "@/api/device-template";

// 页面状态
const loading = ref(false);
const siderWidth = ref(300);
const showDynamic = ref(false);
const searchText = ref("");
const treeData = ref<TemplateTreeNode[]>([]);
const selectedNode = ref<TemplateTreeNode | null>(null);
const contextMenuNode = ref<TemplateTreeNode | null>(null);
const copyDialogVisible = ref(false);
const activeTabName = ref("template");

// 设备关联对话框
const deviceAssociatedDialogVisible = ref(false);
const selectedTemplateForDevices = ref<TemplateTreeNode | null>(null);

// 搜索相关
const searchChanged = ref(false);
const matchNodes = ref<TemplateTreeNode[]>([]);
const matchIndex = ref(0);

// DOM引用
const treeRef = ref<InstanceType<typeof ElTree>>();

// 拖拽调整宽度相关
const isResizing = ref(false);

// 路由相关
const route = useRoute();

// 计算是否为根模板 - 当 parentId < 1000000 时为根模板，只读状态
const isRootTemplate = computed(() => {
  return (
    selectedNode.value?.template &&
    selectedNode.value?.parentId &&
    Number(selectedNode.value.parentId) < 1000000
  );
});

// 树配置
const treeProps = {
  children: "children",
  label: "name",
  isLeaf: "isLeaf"
};

// 生命周期
onMounted(() => {
  initTree();
});

// 监听搜索文本变化
watch(searchText, val => {
  if (treeRef.value) {
    treeRef.value.filter(val);
  }
});

// 监听路由查询参数变化
watch(
  () => route.query.templateId,
  templateId => {
    if (templateId && treeData.value.length > 0) {
      nextTick(() => {
        selectNodeByTemplateId(templateId as string);
      });
    }
  }
);

// 初始化树数据
const initTree = async () => {
  loading.value = true;
  try {
    const api = showDynamic.value ? getTemplateTreeShowHide : getTemplateTree;
    const res = await api();

    if (res.code === 0) {
      treeData.value = buildTreeData(res.data);

      // 检查是否有指定的模板ID需要选中
      nextTick(() => {
        const templateId = route.query.templateId as string;
        if (templateId) {
          // 如果有指定的模板ID，则选中该模板
          selectNodeByTemplateId(templateId);
        } else if (!selectedNode.value) {
          // 如果没有选中的节点，则选中第一个叶子节点
          selectFirstLeafNode();
        }
      });
    } else {
      ElMessage.error(res.msg || "获取模板树失败");
    }
  } catch (error) {
    ElMessage.error("获取模板树异常");
    console.error(error);
  } finally {
    loading.value = false;
  }
};

// 构建树数据
const buildTreeData = (nodes: TemplateTreeNode[]): TemplateTreeNode[] => {
  return nodes.map((node, index) => {
    const newNode = {
      ...node,
      title: node.name,
      key: node.id
    };

    if (node.children && node.children.length > 0) {
      newNode.children = buildTreeData(node.children);
    } else {
      newNode.isLeaf = true;
    }

    return newNode;
  });
};

// 计算总模板数
const countTotalTemplates = (nodes: TemplateTreeNode[]): number => {
  let count = 0;
  nodes.forEach(node => {
    if (node.template) count++;
    if (node.children && node.children.length > 0) {
      count += countTotalTemplates(node.children);
    }
  });
  return count;
};

// 选中第一个叶子节点
const selectFirstLeafNode = () => {
  const findFirstLeaf = (
    nodes: TemplateTreeNode[]
  ): TemplateTreeNode | null => {
    for (const node of nodes) {
      if (node.isLeaf) {
        return node;
      }
      if (node.children && node.children.length > 0) {
        const leaf = findFirstLeaf(node.children);
        if (leaf) return leaf;
      }
    }
    return null;
  };

  const firstLeaf = findFirstLeaf(treeData.value);
  if (firstLeaf && treeRef.value) {
    treeRef.value.setCurrentKey(firstLeaf.id);
    selectedNode.value = firstLeaf;
  }
};

// 根据模板ID查找并选中节点
const selectNodeByTemplateId = (templateId: string) => {
  const findNodeById = (
    nodes: TemplateTreeNode[],
    id: string
  ): TemplateTreeNode | null => {
    for (const node of nodes) {
      if (node.id == id) {
        return node;
      }
      if (node.children && node.children.length > 0) {
        const found = findNodeById(node.children, id);
        if (found) return found;
      }
    }
    return null;
  };

  const targetNode = findNodeById(treeData.value, templateId);
  if (targetNode && treeRef.value) {
    // 展开父节点
    treeRef.value.setCurrentKey(targetNode.id);
    selectedNode.value = targetNode;

    // 确保节点可见
    nextTick(() => {
      const nodeElement = document.querySelector(
        `[data-key="${targetNode.id}"]`
      );
      if (nodeElement) {
        nodeElement.scrollIntoView({ behavior: "smooth", block: "center" });
      }
    });

    ElMessage.success(`已自动选择模板: ${targetNode.name}`);
  } else {
    ElMessage.warning(`未找到指定的模板 ID: ${templateId}`);
    // 如果找不到指定模板，则选择第一个叶子节点
    selectFirstLeafNode();
  }
};

// 过滤节点
const filterNode = (value: string, data: TemplateTreeNode) => {
  if (!value) return true;
  return data.name.toLowerCase().includes(value.toLowerCase());
};

// 高亮搜索文本
const highlightSearchText = (text: string) => {
  if (!searchText.value.trim()) {
    return text;
  }
  const regex = new RegExp(`(${searchText.value.trim()})`, "gi");
  return text.replace(regex, '<span class="highlight-text">$1</span>');
};

// 搜索变化处理
const onSearchChange = () => {
  searchChanged.value = true;
};

// 选择搜索节点
const selectSearchNode = () => {
  if (searchChanged.value) {
    searchChanged.value = false;
    matchNodes.value = [];
    matchIndex.value = 0;
    findMatchingNodes(searchText.value.trim(), treeData.value);

    if (matchNodes.value.length > 0) {
      selectMatchNode(0);
    }
  } else {
    matchIndex.value++;
    if (matchIndex.value >= matchNodes.value.length) {
      matchIndex.value = 0;
    }
    if (matchIndex.value < matchNodes.value.length) {
      selectMatchNode(matchIndex.value);
    }
  }
};

// 查找匹配节点
const findMatchingNodes = (searchValue: string, nodes: TemplateTreeNode[]) => {
  nodes.forEach(node => {
    if (node.name.toLowerCase().includes(searchValue.toLowerCase())) {
      matchNodes.value.push(node);
    }
    if (node.children && node.children.length > 0) {
      findMatchingNodes(searchValue, node.children);
    }
  });
};

// 选择匹配节点
const selectMatchNode = (index: number) => {
  const node = matchNodes.value[index];
  if (node && treeRef.value) {
    treeRef.value.setCurrentKey(node.id);
    selectedNode.value = node;
  }
};

// 处理节点点击
const handleNodeClick = (data: TemplateTreeNode, node: any) => {
  // 如果是文件夹节点，只处理展开/折叠，不选择
  if (!data.template) {
    // 切换节点的展开状态
    if (treeRef.value) {
      const nodeKey = data.id;
      if (node.expanded) {
        treeRef.value.getNode(nodeKey).collapse();
      } else {
        treeRef.value.getNode(nodeKey).expand();
      }
    }
    return;
  }
  
  // 只有模板节点才执行选择操作
  selectedNode.value = data;
};

// 处理右键菜单
const handleContextMenu = (event: MouseEvent, data: TemplateTreeNode) => {
  event.preventDefault();
  contextMenuNode.value = data;

  // 只对模板节点显示右键菜单
  if (!data.template) return;

  // 显示右键菜单
  ContextMenu.showContextMenu({
    x: event.x,
    y: event.y,
    items: [
      {
        label: "删除模板",
        icon: "h:delete",
        onClick: () => {
          deleteTemplateAction(data);
        }
      },
      {
        label: "导出模板",
        icon: "h:download",
        onClick: () => {
          exportTemplateAction(data);
        }
      },
      {
        label: "复制模板",
        icon: "h:document-copy",
        onClick: () => {
          copyTemplateAction(data);
        }
      },
      {
        label: "查看引用该模板的设备",
        icon: "h:monitor",
        onClick: () => {
          viewAssociatedDevices(data);
        }
      },
      // 只有当模板不是根模板时才显示升级选项
      ...(data.equipmentCategory !== data.parentId
        ? [
            {
              label: "升级成根模板",
              icon: "h:top",
              onClick: () => {
                upgradeTemplateAction(data);
              }
            }
          ]
        : [])
    ]
  });
};

// 删除选中的模板
const deleteSelectedTemplate = () => {
  if (selectedNode.value && selectedNode.value.template) {
    deleteTemplateAction(selectedNode.value);
  }
};

// 导出选中的模板
const exportSelectedTemplate = () => {
  if (selectedNode.value && selectedNode.value.template) {
    exportTemplateAction(selectedNode.value);
  }
};

// 复制选中的模板
const copySelectedTemplate = () => {
  if (selectedNode.value && selectedNode.value.template) {
    copyTemplateAction(selectedNode.value);
  }
};

// 刷新模板
const refreshTemplate = () => {
  initTree();
};

// 切换动态配置显示
const toggleDynamicTemplate = () => {
  showDynamic.value = !showDynamic.value;
  initTree();
};

// 删除模板操作
const deleteTemplateAction = async (node: TemplateTreeNode) => {
  try {
    await ElMessageBox.confirm(
      `确认删除模板 "${node.name}" 吗？此操作不可撤销！`,
      "删除确认",
      {
        confirmButtonText: "确认删除",
        cancelButtonText: "取消",
        type: "warning",
        confirmButtonClass: "el-button--danger"
      }
    );

    const res = await deleteTemplate(node.id);
    if (res.code === 0) {
      ElMessage.success("删除成功");
      // 如果删除的是当前选中的节点，清空选中状态
      if (selectedNode.value?.id === node.id) {
        selectedNode.value = null;
      }
      // 删除操作会从树中移除节点，需要重新加载树
      refreshTemplate();
    } else {
      ElMessage.error(res.msg || "删除失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("删除异常");
      console.error(error);
    }
  }
};

// 导出模板操作
const exportTemplateAction = async (node: TemplateTreeNode) => {
  try {
    ElMessage.info("正在导出模板，请稍候...");
    const blob = await exportTemplate(node.id);

    if (!blob || blob.size === 0) {
      ElMessage.error("导出的文件为空");
      return;
    }

    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${node.name}设备模板.xml`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    ElMessage.success("导出成功");
  } catch (error) {
    ElMessage.error("导出失败");
    console.error(error);
  }
};

// 复制模板操作
const copyTemplateAction = (node: TemplateTreeNode) => {
  selectedNode.value = node;
  copyDialogVisible.value = true;
};

// 处理复制确认
const handleCopyConfirm = async (data: { name: string; reason: string }) => {
  if (!selectedNode.value) return;

  try {
    const params = {
      originEquipmentTemplateId: Number(selectedNode.value.id),
      newEquipmentTemplateName: data.name,
      reason: data.reason
    };

    const res = await copyTemplate(params);
    if (res.code === 0) {
      ElMessage.success("复制成功");
      copyDialogVisible.value = false;
      // 复制操作会创建新的模板，需要重新加载树
      refreshTemplate();
    } else {
      ElMessage.error(res.msg || "复制失败");
    }
  } catch (error) {
    ElMessage.error("复制异常");
    console.error(error);
  }
};

// 查看关联设备
const viewAssociatedDevices = (node: TemplateTreeNode) => {
  selectedTemplateForDevices.value = node;
  deviceAssociatedDialogVisible.value = true;
};

// 处理批量切换模板
const handleBatchChangeTemplate = (deviceIds: string[]) => {
  ElMessage.success(`已启动模板选择器，选择了${deviceIds.length}个设备`);
};

// 处理批量分发配置
const handleBatchDistribute = (deviceIds: string[]) => {
  ElMessage.info(`选择了${deviceIds.length}个设备，批量分发MU配置功能待实现`);
  // 这里可以集成分发配置对话框
};

// 处理标签页选择
const handleTabSelect = (data: { index: number }) => {
  const tabNames = ["template", "signal", "event", "control", "changelog"];
  if (data.index >= 0 && data.index < tabNames.length) {
    activeTabName.value = tabNames[data.index];
  }
};

// 升级模板操作
const upgradeTemplateAction = async (node: TemplateTreeNode) => {
  try {
    await ElMessageBox.confirm(
      `确认将模板 "${node.name}" 升级为根模板吗？升级后该模板将成为独立的根模板。`,
      "升级确认",
      {
        confirmButtonText: "确认升级",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    const res = await upgradeTemplate(node.id);
    if (res.code === 0) {
      ElMessage.success("升级成功");
      // 升级操作会改变模板的层级结构，需要重新加载树
      refreshTemplate();
    } else {
      ElMessage.error(res.msg || "升级失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("升级异常");
      console.error(error);
    }
  }
};

// 处理模板名称更新
const handleUpdateTemplateName = (newName: string) => {
  if (selectedNode.value) {
    // 更新当前选中节点的名称
    selectedNode.value.name = newName;
    selectedNode.value.title = newName;

    // 更新树中对应节点的名称
    updateNodeNameInTree(treeData.value, selectedNode.value.id, newName);
  }
};

// 在树数据中更新节点名称
const updateNodeNameInTree = (
  nodes: TemplateTreeNode[],
  nodeId: string,
  newName: string
): boolean => {
  for (const node of nodes) {
    if (node.id === nodeId) {
      node.name = newName;
      node.title = newName;
      return true;
    }
    if (node.children && node.children.length > 0) {
      if (updateNodeNameInTree(node.children, nodeId, newName)) {
        return true;
      }
    }
  }
  return false;
};

// 处理子组件的刷新事件（信号、事件、控制等子组件）
const handleChildRefresh = () => {
  // 对于信号、事件、控制等子组件的刷新，通常不需要重新加载整个树
  // 这些操作主要是增删改信号、事件、控制项，不会影响模板树结构
  //
  // 需要重新加载树的情况：
  // 1. 删除模板 - 会从树中移除节点
  // 2. 复制模板 - 会在树中添加新节点
  // 3. 升级模板 - 会改变模板的层级结构
  // 4. 切换动态配置显示 - 会改变树的显示内容
  //
  // 不需要重新加载树的情况：
  // 1. 更新模板基本信息 - 只需要刷新当前模板数据和更新树中的显示名称
  // 2. 增删改信号、事件、控制 - 不影响树结构
  // 3. 查看关联设备等操作 - 不影响树结构
  console.log("子组件请求刷新，但不重新加载模板树");
};

// 开始调整宽度
const startResize = (e: MouseEvent) => {
  isResizing.value = true;
  const startX = e.clientX;
  const startWidth = siderWidth.value;

  const handleMouseMove = (e: MouseEvent) => {
    if (!isResizing.value) return;

    const deltaX = e.clientX - startX;
    const newWidth = startWidth + deltaX;

    // 限制最小和最大宽度
    if (newWidth >= 200 && newWidth <= 600) {
      siderWidth.value = newWidth;
    }
  };

  const handleMouseUp = () => {
    isResizing.value = false;
    document.removeEventListener("mousemove", handleMouseMove);
    document.removeEventListener("mouseup", handleMouseUp);
  };

  document.addEventListener("mousemove", handleMouseMove);
  document.addEventListener("mouseup", handleMouseUp);
};
</script>

<style scoped>
.device-template-management {
}

.resize-handle {
  position: absolute;
  top: 0;
  right: -2px;
  width: 4px;
  height: 100%;
  cursor: ew-resize;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
}

.resize-handle:hover {
  background-color: rgba(175, 184, 193, 0.2);
}

.resize-line {
  width: 1px;
  height: 100%;
  background-color: var(--el-border-color);
}

.template-tree {
  padding: 0;
}

.template-tree :deep(.el-tree-node__content) {
  height: 36px;
  padding: 0 16px;
  border-radius: 6px;
  margin-bottom: 4px;
}

.template-tree :deep(.el-tree-node__content:hover) {
  background-color: var(--el-fill-color-light);
}

.template-tree :deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

:deep(.highlight-text) {
  background-color: var(--el-color-warning-light-8);
  color: var(--el-color-warning-dark-2);
  font-weight: bold;
}

/* 自定义标签页样式 */
.custom-tabs {
  height: 100%;
}

.custom-tabs :deep(.el-tabs__header) {
  margin: 0;
  padding-left: 24px; /* 添加左边距 */
  position: relative;
}

.custom-tabs :deep(.el-tabs__nav-wrap) {
  border-bottom: 1px solid var(--el-border-color);
}

.custom-tabs :deep(.el-tabs__content) {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.custom-tabs :deep(.el-tab-pane) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .device-template-management {
    padding: 16px;
  }

  .device-template-management .flex {
    flex-direction: column;
    height: auto;
  }

  .device-template-management .w-80 {
    width: 100%;
    height: 400px;
    margin-bottom: 24px;
  }
}
</style>
