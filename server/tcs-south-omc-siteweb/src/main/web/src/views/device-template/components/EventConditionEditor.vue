<template>
  <el-dialog
    v-model="visible"
    title="事件条件设置"
    width="900px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="() => handleClose(true)"
  >
    <div class="event-condition-editor">
      <!-- 表格区域 -->
      <div class="table-container">
        <el-table
          ref="tableRef"
          v-loading="loading"
          :data="conditionList"
          border
          stripe
          height="300"
          row-key="eventConditionId"
          @row-contextmenu="handleContextMenu"
        >
          <el-table-column prop="eventConditionId" label="ID" width="80" />

          <el-table-column prop="eventSeverity" label="事件严重度" width="120">
            <template #default="{ row }">
              <el-select
                v-model="row.eventSeverity"
                size="small"
                :disabled="disabled"
                @change="handleFieldChange(row, 'eventSeverity')"
              >
                <el-option
                  v-for="item in eventSeverityList"
                  :key="item.itemId"
                  :label="item.itemValue"
                  :value="item.itemId"
                />
              </el-select>
            </template>
          </el-table-column>

          <el-table-column prop="startOperation" label="开始操作符" width="100">
            <template #default="{ row }">
              <el-select
                v-model="row.startOperation"
                size="small"
                :disabled="disabled"
                @change="handleFieldChange(row, 'startOperation')"
              >
                <el-option
                  v-for="item in operatorList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </template>
          </el-table-column>

          <el-table-column
            prop="startCompareValue"
            label="开始比较值"
            width="100"
          >
            <template #default="{ row }">
              <el-input
                v-model="row.startCompareValue"
                size="small"
                :disabled="disabled"
                @change="handleFieldChange(row, 'startCompareValue')"
              />
            </template>
          </el-table-column>

          <el-table-column prop="startDelay" label="开始延迟" width="80">
            <template #default="{ row }">
              <el-input
                v-model="row.startDelay"
                size="small"
                :disabled="disabled"
                @change="handleFieldChange(row, 'startDelay')"
              />
            </template>
          </el-table-column>

          <el-table-column prop="endOperation" label="结束操作符" width="100">
            <template #default="{ row }">
              <el-select
                v-model="row.endOperation"
                size="small"
                :disabled="disabled"
                clearable
                @change="handleFieldChange(row, 'endOperation')"
              >
                <el-option
                  v-for="item in operatorList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </template>
          </el-table-column>

          <el-table-column
            prop="endCompareValue"
            label="结束比较值"
            width="100"
          >
            <template #default="{ row }">
              <el-input
                v-model="row.endCompareValue"
                size="small"
                :disabled="disabled"
                @change="handleFieldChange(row, 'endCompareValue')"
              />
            </template>
          </el-table-column>

          <el-table-column prop="endDelay" label="结束延迟" width="80">
            <template #default="{ row }">
              <el-input
                v-model="row.endDelay"
                size="small"
                :disabled="disabled"
                @change="handleFieldChange(row, 'endDelay')"
              />
            </template>
          </el-table-column>

          <el-table-column prop="frequency" label="事件持续时间" width="120">
            <template #default="{ row }">
              <el-input
                v-model="row.frequency"
                size="small"
                :disabled="disabled"
                @change="handleFieldChange(row, 'frequency')"
              />
            </template>
          </el-table-column>

          <el-table-column
            prop="frequencyThreshold"
            label="事件阈值"
            width="100"
          >
            <template #default="{ row }">
              <el-input
                v-model="row.frequencyThreshold"
                size="small"
                :disabled="disabled"
                @change="handleFieldChange(row, 'frequencyThreshold')"
              />
            </template>
          </el-table-column>

          <el-table-column prop="meanings" label="含义" width="120">
            <template #default="{ row }">
              <el-input
                v-model="row.meanings"
                size="small"
                :disabled="disabled"
                @change="handleFieldChange(row, 'meanings')"
              />
            </template>
          </el-table-column>

          <el-table-column prop="baseTypeIdLabel" label="基础事件" width="140">
            <template #default="{ row }">
              <div
                class="base-type-cell"
                :class="{ clickable: !disabled }"
                :title="row.baseTypeName || '点击设置基类事件'"
                @click="editBaseType(row)"
              >
                {{
                  row.baseTypeName || row.baseTypeIdLabel || "点击设置基类事件"
                }}
              </div>
            </template>
          </el-table-column>

          <!-- 电池状态列（仅电池设备显示） -->
          <el-table-column
            v-if="isBattery"
            prop="equipmentState"
            label="电池状态"
            width="100"
          >
            <template #default="{ row }">
              <el-select
                v-model="row.equipmentState"
                size="small"
                :disabled="disabled"
                @change="handleFieldChange(row, 'equipmentState')"
              >
                <el-option
                  v-for="item in batteryStateList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 右键菜单 -->
      <div
        v-show="showContextMenu"
        ref="contextMenuRef"
        class="context-menu"
        :style="{
          left: contextMenuPosition.x + 'px',
          top: contextMenuPosition.y + 'px'
        }"
      >
        <div class="menu-item" @click="handleMenuAction('add')">
          <i class="el-icon-plus" />
          新增
        </div>
        <div class="menu-item" @click="handleMenuAction('delete')">
          <i class="el-icon-delete" />
          删除
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="() => handleClose(true)">关闭</el-button>
      </div>
    </template>

    <!-- 模板修改确认对话框 -->
    <DeviceTemplateConfirmDialog
      v-model="showConfirmDialog"
      :template-id="eventData?.equipmentTemplateId"
      :template-data="templateData"
      @confirm="handleConfirmDialogResult"
      @cancel="handleConfirmCancel"
    />

    <!-- 基类选择器对话框 -->
    <BaseClassSelectorDialog
      v-model="showBaseClassSelector"
      :signal-data="{
        eventName: eventData?.eventName,
        eventCondition: currentEditingCondition?.eventConditionId,
        baseTypeId: currentEditingCondition?.baseTypeId,
        baseTypeName: currentEditingCondition?.baseTypeName,
        equipmentTemplateId: eventData?.equipmentTemplateId,
        eventConditionList: eventData?.eventConditionList
      }"
      :template-id="eventData?.equipmentTemplateId"
      :type="BaseClassSelectorType.EVENT"
      @confirm="handleBaseClassConfirm"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import DeviceTemplateConfirmDialog from "./DeviceTemplateConfirmDialog.vue";
import BaseClassSelectorDialog from "./BaseClassSelectorDialog.vue";
import {
  getEventSeverityList,
  getBatteryStateList,
  updateTemplateEventRow,
  deviceTemplateService,
  BaseClassSelectorType,
  type DataDictionaryItem,
  type EventInfo
} from "@/api/device-template";

interface EventCondition {
  id?: number;
  eventConditionId: number;
  equipmentTemplateId: number;
  eventId: number;
  startOperation: string;
  startCompareValue: number;
  startDelay: number;
  endOperation?: string;
  endCompareValue?: number;
  endDelay?: number;
  frequency?: number;
  frequencyThreshold?: number;
  meanings?: string;
  equipmentState?: string;
  baseTypeId?: number;
  eventSeverity: number;
  standardName?: string;
  baseTypeName?: string;
  baseTypeIdLabel?: string;
}

interface Props {
  modelValue: boolean;
  eventData?: EventInfo | null;
  templateData?: any;
  disabled?: boolean;
  isBattery?: boolean;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
  (e: "confirm"): void;
  (e: "cancel"): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  eventData: null,
  templateData: null,
  disabled: false,
  isBattery: false
});

const emit = defineEmits<Emits>();

// 状态变量
const loading = ref(false);
const conditionList = ref<EventCondition[]>([]);
const originalConditionList = ref<EventCondition[]>([]);
const selectedCondition = ref<EventCondition | null>(null);

// 字典数据
const eventSeverityList = ref<DataDictionaryItem[]>([]);
const batteryStateList = ref<any[]>([]);

// 操作符列表
const operatorList = [
  { id: "=", name: "=" },
  { id: ">", name: ">" },
  { id: "<", name: "<" },
  { id: "!=", name: "!=" },
  { id: ">=", name: ">=" },
  { id: "<=", name: "<=" }
];

// 右键菜单相关
const showContextMenu = ref(false);
const contextMenuPosition = ref({ x: 0, y: 0 });
const contextMenuRef = ref<HTMLElement>();

// 模板确认对话框相关
const showConfirmDialog = ref(false);
const pendingAction = ref<string>("");
const pendingCondition = ref<EventCondition | null>(null);

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit("update:modelValue", value)
});

// 监听事件数据变化
watch(
  () => props.eventData,
  newData => {
    if (newData && visible.value) {
      initConditionData();
    }
  },
  { immediate: true }
);

// 监听事件数据中的条件列表变化
watch(
  () => props.eventData?.eventConditionList,
  newConditionList => {
    if (newConditionList && visible.value) {
      initConditionData();
    }
  },
  { deep: true }
);

// 监听对话框显示状态
watch(visible, newVisible => {
  if (newVisible) {
    loadDictionaryData();
    initConditionData();
  }
});

// 初始化条件数据
const initConditionData = () => {
  if (!props.eventData?.eventConditionList) {
    conditionList.value = [];
    originalConditionList.value = [];
    return;
  }

  // 深拷贝条件列表
  const conditions = props.eventData.eventConditionList.map(
    (condition: any) => ({
      ...condition,
      baseTypeIdLabel: condition.baseTypeName || condition.baseTypeIdLabel || ""
    })
  );

  conditionList.value = conditions;
  originalConditionList.value = JSON.parse(JSON.stringify(conditions));
};

// 加载字典数据
const loadDictionaryData = async () => {
  try {
    loading.value = true;

    const [severityRes, batteryRes] = await Promise.all([
      getEventSeverityList(),
      getBatteryStateList()
    ]);

    if (severityRes.code === 0) {
      eventSeverityList.value = severityRes.data;
    }

    if (batteryRes.code === 0) {
      batteryStateList.value = batteryRes.data.map((item: any) => ({
        id: item.itemId.toString(),
        name: item.itemValue
      }));
    }
  } catch (error) {
    console.error("加载字典数据失败:", error);
  } finally {
    loading.value = false;
  }
};

// 处理字段变化
const handleFieldChange = (row: EventCondition, field: string) => {
  if (props.disabled) return;

  // 更新原始事件数据中的条件列表
  if (props.eventData?.eventConditionList) {
    const index = props.eventData.eventConditionList.findIndex(
      (item: any) => item.eventConditionId === row.eventConditionId
    );
    if (index !== -1) {
      props.eventData.eventConditionList[index] = { ...row };
    }
  }

  // 更新本地条件列表
  const localIndex = conditionList.value.findIndex(
    item => item.eventConditionId === row.eventConditionId
  );
  if (localIndex !== -1) {
    conditionList.value[localIndex] = { ...row };
  }

  // 检查是否需要确认对话框
  changeConfirm("change");
};

// 处理右键菜单
const handleContextMenu = (
  row: EventCondition,
  column: any,
  event: MouseEvent
) => {
  if (props.disabled) return;

  event.preventDefault();

  // 右键时自动选中当前行
  selectedCondition.value = row;

  contextMenuPosition.value = { x: event.clientX, y: event.clientY };
  showContextMenu.value = true;

  // 点击外部关闭菜单
  nextTick(() => {
    document.addEventListener("click", handleClickOutside);
  });
};

// 处理点击外部关闭菜单
const handleClickOutside = (event: MouseEvent) => {
  if (
    contextMenuRef.value &&
    !contextMenuRef.value.contains(event.target as Node)
  ) {
    showContextMenu.value = false;
    document.removeEventListener("click", handleClickOutside);
  }
};

// 处理菜单操作
const handleMenuAction = (action: string) => {
  showContextMenu.value = false;
  document.removeEventListener("click", handleClickOutside);

  changeConfirm(action);
};

// 变更确认
const changeConfirm = (type: string) => {
  if (deviceTemplateService.getNotShowState()) {
    executeAction(type, false);
  } else {
    pendingAction.value = type;
    showConfirmDialog.value = true;
  }
};

// 执行操作
const executeAction = async (type: string, isNewTemplate: boolean) => {
  try {
    switch (type) {
      case "add":
        await addNewCondition(isNewTemplate);
        break;
      case "delete":
        await deleteCondition(isNewTemplate);
        break;
      case "change":
        await updateCondition(isNewTemplate);
        break;
    }
  } catch (error) {
    console.error("执行操作失败:", error);
    ElMessage.error("操作失败");
  }
};

// 新增条件
const addNewCondition = async (isNewTemplate: boolean) => {
  if (!props.eventData) return;

  const maxId =
    conditionList.value.length > 0
      ? Math.max(...conditionList.value.map(item => item.eventConditionId))
      : -1;

  const newCondition: EventCondition = {
    id: null,
    eventConditionId: maxId + 1,
    equipmentTemplateId: props.eventData.equipmentTemplateId,
    eventId: props.eventData.eventId,
    startOperation: "=",
    startCompareValue: 0,
    startDelay: 0,
    endOperation: null,
    endCompareValue: null,
    endDelay: null,
    frequency: null,
    frequencyThreshold: null,
    meanings: null,
    equipmentState: null,
    baseTypeId: null,
    eventSeverity: 0,
    standardName: null,
    baseTypeName: null,
    baseTypeIdLabel: ""
  };

  // 添加到事件条件列表
  if (!props.eventData.eventConditionList) {
    props.eventData.eventConditionList = [];
  }
  props.eventData.eventConditionList.push(newCondition);

  // 更新事件
  await updateTemplateEventRow(props.eventData);

  ElMessage.success("增加成功！");

  // 刷新数据
  conditionList.value.push(newCondition);

  if (isNewTemplate) {
    handleClose(true);
  }
};

// 删除条件
const deleteCondition = async (isNewTemplate: boolean) => {
  if (!props.eventData || !selectedCondition.value) {
    ElMessage.warning("请先选择要删除的条件");
    return;
  }

  const selectedId = selectedCondition.value.eventConditionId;

  // 从事件条件列表中移除
  if (props.eventData.eventConditionList) {
    props.eventData.eventConditionList =
      props.eventData.eventConditionList.filter(
        (item: any) => item.eventConditionId !== selectedId
      );
  }

  // 更新事件
  await updateTemplateEventRow(props.eventData);

  ElMessage.success("删除成功！");

  // 刷新数据
  conditionList.value = conditionList.value.filter(
    item => item.eventConditionId !== selectedId
  );
  selectedCondition.value = null;

  if (isNewTemplate) {
    handleClose(true);
  }
};

// 更新条件
const updateCondition = async (isNewTemplate: boolean) => {
  if (!props.eventData) return;

  // 更新事件
  await updateTemplateEventRow(props.eventData);

  ElMessage.success("更新成功！");

  if (isNewTemplate) {
    handleClose(true);
  } else {
    // 如果不是新模板，也需要通知父组件刷新
    handleClose(true);
  }
};

// 基类选择器相关
const showBaseClassSelector = ref(false);
const currentEditingCondition = ref<EventCondition | null>(null);

// 编辑基类事件
const editBaseType = (row: EventCondition) => {
  if (props.disabled) return;

  currentEditingCondition.value = row;
  showBaseClassSelector.value = true;
};

// 处理基类选择器确认
const handleBaseClassConfirm = (result: {
  action: string;
  baseTypeId: number | null;
  baseTypeName?: string;
  moduleNumber?: number | null;
}) => {
  if (!currentEditingCondition.value) return;

  const condition = currentEditingCondition.value;
  const conditionId = condition.eventConditionId;

  switch (result.action) {
    case "confirm":
      // 设置基类信息
      condition.baseTypeId = result.baseTypeId;
      condition.baseTypeName = result.baseTypeName || "";
      condition.baseTypeIdLabel = result.baseTypeName || "";

      // 更新表格显示的数据
      const conditionIndex = conditionList.value.findIndex(
        item => item.eventConditionId === conditionId
      );
      if (conditionIndex !== -1) {
        conditionList.value[conditionIndex].baseTypeId = result.baseTypeId;
        conditionList.value[conditionIndex].baseTypeName =
          result.baseTypeName || "";
        conditionList.value[conditionIndex].baseTypeIdLabel =
          result.baseTypeName || "";
      }

      // 更新事件数据中对应的条件
      if (props.eventData?.eventConditionList) {
        const eventConditionIndex =
          props.eventData.eventConditionList.findIndex(
            (item: any) => item.eventConditionId === conditionId
          );
        if (eventConditionIndex !== -1) {
          props.eventData.eventConditionList[eventConditionIndex].baseTypeId =
            result.baseTypeId;
          props.eventData.eventConditionList[eventConditionIndex].baseTypeName =
            result.baseTypeName || "";
        }
      }

      // 更新原始数据
      const originalIndex = originalConditionList.value.findIndex(
        item => item.eventConditionId === conditionId
      );
      if (originalIndex !== -1) {
        originalConditionList.value[originalIndex].baseTypeId =
          result.baseTypeId;
        originalConditionList.value[originalIndex].baseTypeName =
          result.baseTypeName || "";
        originalConditionList.value[originalIndex].baseTypeIdLabel =
          result.baseTypeName || "";
      }

      changeConfirm("change");
      break;

    case "delete":
      // 取消基类关联
      condition.baseTypeId = null;
      condition.baseTypeName = "";
      condition.baseTypeIdLabel = "";

      // 更新表格显示的数据
      const deleteConditionIndex = conditionList.value.findIndex(
        item => item.eventConditionId === conditionId
      );
      if (deleteConditionIndex !== -1) {
        conditionList.value[deleteConditionIndex].baseTypeId = null;
        conditionList.value[deleteConditionIndex].baseTypeName = "";
        conditionList.value[deleteConditionIndex].baseTypeIdLabel = "";
      }

      // 更新事件数据中对应的条件
      if (props.eventData?.eventConditionList) {
        const eventConditionIndex =
          props.eventData.eventConditionList.findIndex(
            (item: any) => item.eventConditionId === conditionId
          );
        if (eventConditionIndex !== -1) {
          props.eventData.eventConditionList[eventConditionIndex].baseTypeId =
            null;
          props.eventData.eventConditionList[eventConditionIndex].baseTypeName =
            "";
        }
      }

      // 更新原始数据
      const deleteOriginalIndex = originalConditionList.value.findIndex(
        item => item.eventConditionId === conditionId
      );
      if (deleteOriginalIndex !== -1) {
        originalConditionList.value[deleteOriginalIndex].baseTypeId = null;
        originalConditionList.value[deleteOriginalIndex].baseTypeName = "";
        originalConditionList.value[deleteOriginalIndex].baseTypeIdLabel = "";
      }

      changeConfirm("change");
      break;
  }

  currentEditingCondition.value = null;

  // 强制刷新表格显示
  nextTick(() => {
    conditionList.value = [...conditionList.value];
  });
};

// 处理确认对话框结果
const handleConfirmDialogResult = async (
  action: "update" | "copy",
  data: any
) => {
  try {
    // 保存"不再提醒"状态
    if (data.notShowAgain) {
      deviceTemplateService.setNotShowState(true);
    }

    if (action === "update") {
      // 更新原模板
      await executeAction(pendingAction.value, false);
    } else if (action === "copy") {
      // 另存为新模板
      if (data.newTemplateId && props.eventData) {
        // 更新模板ID
        props.eventData.equipmentTemplateId = data.newTemplateId;
        if (props.eventData.eventConditionList) {
          props.eventData.eventConditionList.forEach((condition: any) => {
            condition.equipmentTemplateId = data.newTemplateId;
          });
        }
        await executeAction(pendingAction.value, true);
      }
    }
  } catch (error) {
    console.error("处理确认对话框结果失败:", error);
    ElMessage.error("操作失败");
  } finally {
    pendingAction.value = "";
    pendingCondition.value = null;
    showConfirmDialog.value = false;
  }
};

// 处理确认取消
const handleConfirmCancel = () => {
  // 恢复原始数据
  if (
    props.eventData?.eventConditionList &&
    originalConditionList.value.length > 0
  ) {
    const restoredData = JSON.parse(
      JSON.stringify(originalConditionList.value)
    );
    props.eventData.eventConditionList = restoredData;
    conditionList.value = restoredData;

    // 强制触发响应式更新
    nextTick(() => {
      conditionList.value = [...conditionList.value];
    });
  }

  pendingAction.value = "";
  pendingCondition.value = null;
  showConfirmDialog.value = false;
};

// 关闭对话框
const handleClose = (refresh = false) => {
  visible.value = false;

  if (refresh) {
    emit("confirm");
  } else {
    emit("cancel");
  }

  // 清理状态
  conditionList.value = [];
  originalConditionList.value = [];
  selectedCondition.value = null;
  showContextMenu.value = false;
};

// 组件卸载时清理事件监听器
onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
});
</script>

<style scoped>
.event-condition-editor {
  width: 100%;
}

.table-container {
  height: 300px;
  overflow: auto;
}

.base-type-cell {
  padding: 4px 8px;
  min-height: 24px;
  line-height: 24px;
  border-radius: 4px;
  transition: all 0.2s;
}

.base-type-cell.clickable {
  cursor: pointer;
  border: 1px solid transparent;
}

.base-type-cell.clickable:hover {
  background-color: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary-light-5);
}

.context-menu {
  position: fixed;
  background: white;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 9999;
  padding: 4px 0;
  min-width: 100px;
}

.menu-item {
  padding: 8px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: var(--el-text-color-primary);
  transition: background-color 0.2s;
}

.menu-item:hover {
  background-color: var(--el-color-primary-light-9);
}

.menu-item i {
  margin-right: 8px;
  font-size: 16px;
}

.dialog-footer {
  text-align: right;
}

/* 表格样式优化 */
:deep(.el-table) {
  font-size: 12px;
}

:deep(.el-table .cell) {
  padding: 4px 8px;
}

:deep(.el-input--small) {
  font-size: 12px;
}

:deep(.el-input--small .el-input__inner) {
  height: 28px;
  line-height: 28px;
}

:deep(.el-select--small) {
  font-size: 12px;
}

:deep(.el-input-number--small) {
  width: 100%;
}

:deep(.el-input-number--small .el-input__inner) {
  height: 28px;
  line-height: 28px;
}
</style>
