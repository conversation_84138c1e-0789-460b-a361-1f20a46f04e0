<template>
  <div class="device-template-info">
    <el-scrollbar class="h-full">
      <div class="form-container">
        <!-- 第一行：设备模板ID 和 名称 -->
        <div class="form-row">
          <div class="form-item">
            <label class="form-label">设备模板ID</label>
            <el-input
              v-model="formData.equipmentTemplateId"
              class="form-input"
              readonly
              disabled
              type="number"
            />
          </div>
          <div class="form-item">
            <label class="form-label">名称</label>
            <el-input
              v-model="formData.equipmentTemplateName"
              class="form-input"
              maxlength="128"
            />
          </div>
        </div>

        <!-- 第二行：类型 和 分类 -->
        <div class="form-row">
          <div class="form-item">
            <label class="form-label">类型</label>
            <el-select
              v-model="formData.equipmentCategory"
              class="form-select"
              filterable
              placeholder="请选择类型"
              :disabled="!isOriginTemp"
            >
              <el-option
                v-for="item in deviceCategoryList"
                :key="item.itemId"
                :value="item.itemId"
                :label="item.itemValue"
              />
            </el-select>
          </div>
          <div class="form-item">
            <label class="form-label">分类</label>
            <el-select
              v-model="formData.equipmentType"
              class="form-select"
              placeholder="请选择分类"
            >
              <el-option
                v-for="item in deviceTypeList"
                :key="item.itemId"
                :value="item.itemId"
                :label="item.itemValue"
              />
            </el-select>
          </div>
        </div>

        <!-- 第三行：厂商 和 单位 -->
        <div class="form-row">
          <div class="form-item">
            <label class="form-label">厂商</label>
            <el-select
              v-model="formData.vendor"
              class="form-select"
              filterable
              placeholder="请选择厂商"
            >
              <el-option
                v-for="item in vendorList"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
          </div>
          <div class="form-item">
            <label class="form-label">单位</label>
            <el-input
              v-model="formData.unit"
              class="form-input"
            />
          </div>
        </div>

        <!-- 第四行：协议编码 和 设备型号 -->
        <div class="form-row">
          <div class="form-item">
            <label class="form-label">协议编码</label>
            <el-input
              v-model="formData.protocolCode"
              class="form-input"
              readonly
              disabled
              :title="formData.protocolCode"
            />
          </div>
          <div class="form-item">
            <label class="form-label">设备型号</label>
            <el-input
              v-model="formData.equipmentStyle"
              class="form-input"
            />
          </div>
        </div>

        <!-- 第五行：采集器 和 属性 -->
        <div class="form-row">
          <div class="form-item">
            <label class="form-label">采集器</label>
            <el-input
              v-model="formData.samplerName"
              class="form-input"
              readonly
              disabled
            />
          </div>
          <div class="form-item">
            <label class="form-label">属性</label>
            <el-select
              v-model="formData.propertyList"
              class="form-select"
              multiple
              placeholder="请选择属性"
            >
              <el-option
                v-for="item in propertyList"
                :key="item.itemId"
                :value="item.itemId"
                :label="item.itemValue"
              />
            </el-select>
          </div>
        </div>

        <!-- 第六行：设备基类 和 局站类型 -->


        <!-- 第七行：说明 -->
        <div class="form-row">
          <div class="form-item full-width">
            <label class="form-label">说明</label>
            <el-input
              v-model="formData.description"
              class="form-textarea"
              type="textarea"
              :rows="4"
            />
          </div>
        </div>
      </div>

      <!-- 按钮区域 -->
      <div class="button-container">
        <el-button
          type="primary"
          :disabled="!isInit"
          :loading="updating"
          @click="beforeSubmit"
        >
          更新
        </el-button>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  getTemplateInfoById,
  getDeviceTypeList,
  getDeviceCategoryList,
  getPropertyList,
  getVendorList,
  // getDeviceBaseTypeList,
  // getStationCategoryList,
  updateTemplate,
  updateTemplateEquipmentType,
  type DeviceTemplateInfo,
  type DataDictionaryItem,
  type BaseTypeItem,
  // type StationCategoryItem,
  type VendorItem
} from "@/api/device-template";

interface Props {
  templateData?: any;
}

interface Emits {
  (e: 'refresh'): void;
}

const props = withDefaults(defineProps<Props>(), {
  templateData: null
});

const emit = defineEmits<Emits>();

// 表单数据
const formData = reactive<DeviceTemplateInfo>({
  equipmentTemplateId: null,
  equipmentTemplateName: '',
  equipmentType: null,
  equipmentCategory: null,
  vendor: null,
  unit: null,
  protocolCode: null,
  equipmentStyle: null,
  memo: null,
  propertyList: [],
  // equipmentBaseType: null,
  // stationCategory: null,
  samplerName: '',
  description: '',
});

// 状态变量
const isInit = ref(false);
const updating = ref(false);
const isChangeEqBaseType = ref(false);
const isOriginTemp = ref(false);
const originEquipmentType = ref<number | null>(null);

// 下拉选项数据
const deviceTypeList = ref<DataDictionaryItem[]>([]);
const deviceCategoryList = ref<DataDictionaryItem[]>([]);
const propertyList = ref<DataDictionaryItem[]>([]);
// const baseTypeList = ref<BaseTypeItem[]>([]);
// const stationCategoryList = ref<StationCategoryItem[]>([]);
const vendorList = ref<VendorItem[]>([]);

// 组件初始化
onMounted(() => {
  initializeDictionaryData();
});

// 初始化字典数据
const initializeDictionaryData = async () => {
  try {
    await Promise.all([
      loadDeviceTypeList(),
      loadDeviceCategoryList(),
      loadPropertyList(),
      loadVendorList(),
      // loadDeviceBaseTypeList(),
      // loadStationCategoryList(),
    ]);
  } catch (error) {
    console.error('初始化字典数据失败:', error);
  }
};

// 加载设备类型列表
const loadDeviceTypeList = async () => {
  try {
    const res = await getDeviceTypeList();
    if (res.code === 0) {
      deviceTypeList.value = res.data;
    }
  } catch (error) {
    console.error('获取设备类型列表失败:', error);
  }
};

// 加载设备分类列表
const loadDeviceCategoryList = async () => {
  try {
    const res = await getDeviceCategoryList();
    if (res.code === 0) {
      deviceCategoryList.value = res.data;
    }
  } catch (error) {
    console.error('获取设备分类列表失败:', error);
  }
};

// 加载属性列表
const loadPropertyList = async () => {
  try {
    const res = await getPropertyList();
    if (res.code === 0) {
      propertyList.value = res.data;
    }
  } catch (error) {
    console.error('获取属性列表失败:', error);
  }
};

// 加载厂商列表
const loadVendorList = async () => {
  try {
    const res = await getVendorList();
    if (res.code === 0 && Array.isArray(res.data)) {
      vendorList.value = res.data.map(item => ({
        label: item.itemValue,
        value: item.itemValue,
      }));
    }
  } catch (error) {
    console.error('获取厂商列表失败:', error);
  }
};

// // 加载设备基类列表
// const loadDeviceBaseTypeList = async () => {
//   try {
//     const res = await getDeviceBaseTypeList();
//     if (res.code === 0) {
//       baseTypeList.value = res.data;
//     }
//   } catch (error) {
//     console.error('获取设备基类列表失败:', error);
//   }
// };

// // 加载局站类型列表
// const loadStationCategoryList = async () => {
//   try {
//     const res = await getStationCategoryList();
//     if (res.code === 0) {
//       stationCategoryList.value = res.data;
//     }
//   } catch (error) {
//     console.error('获取局站类型列表失败:', error);
//   }
// };

// 加载模板信息
const loadTemplateInfo = async (templateData: any) => {
  if (!templateData || !templateData.id) return;
  
  try {
    const res = await getTemplateInfoById(templateData.id);
    if (res.code === 0 && res.data) {
      Object.assign(formData, res.data);
      isInit.value = true;
      originEquipmentType.value = formData.equipmentCategory;
      isChangeEqBaseType.value = false;
      
      // 判断是否为原始模板（根据parentId小于1000000）
      if (templateData.parentId && templateData.parentId < 1000000) {
        isOriginTemp.value = true;
      } else {
        isOriginTemp.value = false;
      }
    } else {
      ElMessage.error(res.msg || '获取模板信息失败');
    }
  } catch (error) {
    console.error('获取模板信息失败:', error);
    ElMessage.error('获取模板信息异常');
  }
};

// 监听模板数据变化
watch(() => props.templateData, (newData) => {
  if (newData && newData.template) {
    loadTemplateInfo(newData);
  }
}, { immediate: true });

// 设备基类变更处理
const changeEqBaseType = () => {
  isChangeEqBaseType.value = true;
};

// 提交前确认
const beforeSubmit = async () => {
  if (isChangeEqBaseType.value) {
    try {
      await ElMessageBox.confirm(
        '模板切换设备基类，会清除模板所有信号，事件，控制配置的基类ID。<br />请确认是否要切换设备基类！',
        '提示',
        {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: true
        }
      );
      await submit();
    } catch {
      // 用户取消
    }
  } else {
    await submit();
  }
};

// 提交更新
const submit = async () => {
  updating.value = true;
  
  try {
    const res = await updateTemplate(formData);
    if (res.code === 0) {
      ElMessage.success('更新成功！');
      isChangeEqBaseType.value = false;
      
      // 如果是原始模板且修改了设备类型，需要调用特殊API
      if (isOriginTemp.value && originEquipmentType.value !== formData.equipmentCategory) {
        const updateParams = {
          equipmentTemplateId: formData.equipmentTemplateId,
          equipmentCategory: formData.equipmentCategory
        };
        
        try {
          const updateTypeRes = await updateTemplateEquipmentType(updateParams);
          if (updateTypeRes.code === 0) {
            originEquipmentType.value = formData.equipmentCategory;
            emit('refresh');
          }
        } catch (error) {
          console.error('更新设备类型失败:', error);
        }
      } else {
        setTimeout(() => {
          emit('refresh');
        }, 1000);
      }
    } else {
      ElMessage.error(res.msg || '更新失败');
    }
  } catch (error) {
    console.error('更新模板失败:', error);
    ElMessage.error('更新模板异常');
  } finally {
    updating.value = false;
  }
};
</script>

<style scoped>
.device-template-info {
  width: 100%;
  height: 100%;
  padding: 24px;
}

.form-container {
  max-width: 800px;
}

.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 60px;
}

.form-item {
  display: flex;
  align-items: center;
  min-width: 0;
}

.form-item.full-width {
  width: 100%;
}

.form-label {
  width: 120px;
  text-align: right;
  margin-right: 12px;
  color: var(--el-text-color-regular);
  font-size: 14px;
  flex-shrink: 0;
}

.form-input,
.form-select {
  width: 250px;
}

.form-textarea {
  width: 635px;
}

.button-container {
  margin-top: 32px;
  margin-left: 132px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .form-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .form-item {
    width: 100%;
  }
  
  .form-input,
  .form-select,
  .form-textarea {
    width: 100%;
    max-width: 400px;
  }
  
  .button-container {
    margin-left: 0;
  }
}

@media (max-width: 768px) {
  .device-template-info {
    padding: 16px;
  }
  
  .form-label {
    width: 100px;
    margin-right: 8px;
  }
  
  .form-input,
  .form-select,
  .form-textarea {
    max-width: 300px;
  }
}
</style> 