<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="监控单元配置分发"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
    class="mu-distribution-dialog"
  >
    <div class="min-h-[480px] bg-gray-50 dark:bg-gray-900 p-4 ">
      <!-- 页面头部 -->
      <div class="mb-4">
        <div class="flex items-center mb-3">
          <div class="w-1 h-6 bg-primary rounded-full mr-3" />
          <div>
            <h2 class="text-lg font-bold text-gray-900 dark:text-white">
              监控单元配置分发
            </h2>
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              选择监控单元，生成配置文件并进行配置分发
            </p>
          </div>
        </div>
      </div>

      <!-- 步骤条 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-4">
        <el-steps :active="current" align-center>
          <el-step>
            <template #title>
              <div class="flex items-center space-x-2">
                <el-icon size="16"><Connection /></el-icon>
                <span>选择监控单元</span>
              </div>
            </template>
          </el-step>
          <el-step>
            <template #title>
              <div class="flex items-center space-x-2">
                <el-icon size="16"><Document /></el-icon>
                <span>生成配置文件</span>
              </div>
            </template>
          </el-step>
          <el-step>
            <template #title>
              <div class="flex items-center space-x-2">
                <el-icon size="16"><Upload /></el-icon>
                <span>配置分发及状态查看</span>
              </div>
            </template>
          </el-step>
        </el-steps>
      </div>

      <!-- 步骤内容 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 flex flex-col" style="min-height: 400px">
        <!-- 第一步：选择监控单元 -->
        <div v-if="current === 0" class="flex flex-col h-full">
          <!-- 搜索工具栏 -->
          <div class="p-4 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center space-x-4">
              <el-input
                v-model="filters.name"
                placeholder="监控单元名称"
                clearable
                @input="filterChange"
                :prefix-icon="Search"
                style="width: 300px"
              />
              <el-input
                v-model="filters.type"
                placeholder="类型"
                clearable
                @input="filterChange"
                style="width: 200px"
              />
              <el-input
                v-model="filters.address"
                placeholder="IP地址"
                clearable
                @input="filterChange"
                style="width: 200px"
              />
              <div class="text-sm text-gray-500 dark:text-gray-400 ml-auto">
                已选择 {{ allSelectedItems.length }} / {{ showMuList.length }} 个监控单元
              </div>
            </div>
          </div>
          
          <!-- 表格区域 -->
          <div class="flex-1 p-4">
            <el-table
              ref="tableRef"
              :data="showMuList"
              height="320"
              row-key="monitorUnitId"
              @selection-change="handleSelectionChange"
              @select-all="handleSelectAll"
              class="modern-table"
            >
              <el-table-column
                type="selection"
                width="55"
                :reserve-selection="true"
                :selectable="() => true"
              />
              <el-table-column
                prop="monitorUnitName"
                label="监控单元名称"
                width="500"
                show-overflow-tooltip
              >
                <template #default="{ row }">
                  <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                      <el-icon size="14" class="text-primary"><Monitor /></el-icon>
                    </div>
                    <span class="font-medium text-gray-900 dark:text-white">{{ row.monitorUnitName }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                prop="typeName"
                label="类型"
                width="150"
              >
                <template #default="{ row }">
                  <el-tag size="small" effect="light">{{ row.typeName }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column
                prop="ipAddress"
                label="IP"
                width="150"
              >
                <template #default="{ row }">
                  <span class="text-sm text-gray-600 dark:text-gray-400">{{ row.ipAddress }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <!-- 第二步：生成配置文件 -->
        <div v-else-if="current === 1" class="flex flex-col h-full">
          <!-- 操作工具栏 -->
          <div class="p-4 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center space-x-4">
              <el-button type="primary" @click="onGenerate" :loading="generating" size="default">
                <el-icon class="mr-2"><Cpu /></el-icon>
                {{ generating ? '生成中...' : '生成配置文件' }}
              </el-button>
              <el-button 
                v-if="canGenerate" 
                type="success" 
                @click="onDownFile"
                size="default"
              >
                <el-icon class="mr-2"><Download /></el-icon>
                下载生成的配置文件
              </el-button>
              <div class="text-sm text-gray-500 dark:text-gray-400 ml-auto">
                {{ allSelectedItems.length }} 个监控单元待生成配置
              </div>
            </div>
          </div>
          
          <!-- 日志区域 -->
          <div class="flex-1 p-4">
            <div class="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
              <div class="flex items-center mb-3">
                <el-icon size="16" class="text-primary mr-2"><DataLine /></el-icon>
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">生成日志</span>
              </div>
              <el-input
                v-model="logDetail"
                type="textarea"
                :rows="14"
                readonly
                placeholder="生成日志将在这里显示..."
                class="log-textarea"
              />
            </div>
          </div>
        </div>

        <!-- 第三步：配置分发及状态查看 -->
        <div v-else-if="current === 2" class="flex flex-col h-full">
          <!-- 分发配置表单 -->
          <div class="p-4 border-b border-gray-200 dark:border-gray-700">
            <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 mb-4">
              <div class="flex items-center mb-3">
                <el-icon size="16" class="text-blue-600 mr-2"><Setting /></el-icon>
                <span class="text-sm font-medium text-blue-800 dark:text-blue-200">分发配置</span>
              </div>
              <el-form 
                ref="distributeFormRef"
                :model="distributeForm" 
                :rules="distributeRules"
                label-width="120px" 
                class="distribute-form"
              >
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <el-form-item label="用户名" prop="userName">
                    <el-input
                      v-model="distributeForm.userName"
                      placeholder="请输入用户名"
                      :prefix-icon="User"
                    />
                  </el-form-item>
                  
                  <el-form-item label="密码" prop="passWord">
                    <el-input
                      v-model="distributeForm.passWord"
                      :type="passwordVisible ? 'text' : 'password'"
                      placeholder="请输入密码"
                      :prefix-icon="Lock"
                    >
                      <template #suffix>
                        <el-icon 
                          @click="passwordVisible = !passwordVisible" 
                          class="cursor-pointer"
                        >
                          <View v-if="passwordVisible" />
                          <Hide v-else />
                        </el-icon>
                      </template>
                    </el-input>
                  </el-form-item>
                  
                  <el-form-item label="端口" prop="port">
                    <el-input-number
                      v-model="distributeForm.port"
                      :min="0"
                      :max="65535"
                      style="width: 100%"
                    />
                  </el-form-item>
                </div>
                
                <div class="flex items-center justify-between">
                  <el-form-item label="启用加密传输" class="mb-0">
                    <div class="flex items-center space-x-2">
                      <el-checkbox v-model="distributeForm.protocol" />
                      <span class="text-sm text-gray-500">(采集器需支持SSH/SFTP)</span>
                    </div>
                  </el-form-item>
                  
                  <el-button
                    type="primary"
                    @click="onDistribute"
                    :disabled="!canDistribute"
                    :loading="distributing"
                    size="default"
                  >
                    <el-icon class="mr-2"><Upload /></el-icon>
                    {{ distributing ? '下发中...' : '开始下发' }}
                  </el-button>
                </div>
              </el-form>
            </div>
          </div>

          <!-- Tab 区域 -->
          <div class="flex-1 p-4">
            <el-tabs type="card" class="distribution-tabs">
              <el-tab-pane label="分发日志">
                <template #label>
                  <div class="flex items-center space-x-2">
                    <el-icon size="14"><DataLine /></el-icon>
                    <span>分发日志</span>
                  </div>
                </template>
                <div class="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
                  <el-input
                    v-model="logDetail"
                    type="textarea"
                    :rows="12"
                    readonly
                    placeholder="分发日志将在这里显示..."
                    class="log-textarea"
                  />
                </div>
              </el-tab-pane>
              
              <el-tab-pane v-if="allSelectedItems.length > 0" label="状态监控">
                <template #label>
                  <div class="flex items-center space-x-2">
                    <el-icon size="14"><Monitor /></el-icon>
                    <span>状态监控</span>
                  </div>
                </template>
                <div class="space-y-4">
                  <!-- 状态筛选 -->
                  <div class="flex items-center space-x-4">
                    <el-input
                      v-model="statusFilters.name"
                      placeholder="监控单元名称"
                      clearable
                      @input="filterChangeStatus"
                      :prefix-icon="Search"
                      style="width: 300px"
                    />
                    <el-input
                      v-model="statusFilters.type"
                      placeholder="类型"
                      clearable
                      @input="filterChangeStatus"
                      style="width: 150px"
                    />
                    <el-input
                      v-model="statusFilters.address"
                      placeholder="IP地址"
                      clearable
                      @input="filterChangeStatus"
                      style="width: 150px"
                    />
                    <el-input
                      v-model="statusFilters.status"
                      placeholder="状态"
                      clearable
                      @input="filterChangeStatus"
                      style="width: 150px"
                    />
                  </div>
                  
                  <!-- 状态表格 -->
                  <el-table
                    :data="showAllSelectedItems"
                    height="220"
                    row-key="monitorUnitId"
                    class="modern-table"
                  >
                    <el-table-column
                      prop="monitorUnitName"
                      label="监控单元名称"
                      width="400"
                      show-overflow-tooltip
                    >
                      <template #default="{ row }">
                        <div class="flex items-center space-x-3">
                          <div class="w-6 h-6 bg-primary/10 rounded flex items-center justify-center flex-shrink-0">
                            <el-icon size="12" class="text-primary"><Monitor /></el-icon>
                          </div>
                          <span class="text-sm font-medium">{{ row.monitorUnitName }}</span>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="typeName"
                      label="类型"
                      width="150"
                    >
                      <template #default="{ row }">
                        <el-tag size="small" effect="light">{{ row.typeName }}</el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="ipAddress"
                      label="IP"
                      width="150"
                    >
                      <template #default="{ row }">
                        <span class="text-sm text-gray-600 dark:text-gray-400">{{ row.ipAddress }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="statusTxt"
                      label="状态"
                      width="150"
                    >
                      <template #default="{ row }">
                        <el-tag
                          :type="getStatusType(row.statusTxt)"
                          size="small"
                          effect="light"
                        >
                          {{ row.statusTxt || '未知' }}
                        </el-tag>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="flex items-center justify-between bg-gray-50 dark:bg-gray-900 p-4">
        <div class="text-sm text-gray-500 dark:text-gray-400">
          <span v-if="current === 0">请选择要配置的监控单元</span>
          <span v-else-if="current === 1">生成配置文件后可进行下一步</span>
          <span v-else-if="current === 2">配置下发完成后点击完成</span>
        </div>
        <div class="flex items-center space-x-3">
          <el-button
            v-if="current > 0"
            @click="pre"
            :disabled="(current === 1 && canGenerate)"
            size="default"
          >
            <el-icon class="mr-2"><ArrowLeft /></el-icon>
            上一步
          </el-button>
          
          <el-button
            v-if="current < 2"
            type="primary"
            @click="next"
            :disabled="(current === 0 && allSelectedItems.length === 0) || (current === 1 && !canGenerate)"
            size="default"
          >
            下一步
            <el-icon class="ml-2"><ArrowRight /></el-icon>
          </el-button>
          
          <el-button
            v-if="current === 2"
            type="success"
            @click="done"
            :disabled="current === 2 && !completed"
            size="default"
          >
            <el-icon class="mr-2"><Check /></el-icon>
            完成
          </el-button>
          
          <el-button @click="handleClose" size="default">
            取消
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  User, 
  Lock, 
  View, 
  Hide, 
  Search, 
  Monitor, 
  Connection, 
  Document, 
  Upload, 
  Download, 
  DataLine, 
  Setting, 
  Cpu,
  ArrowLeft,
  ArrowRight,
  Check
} from '@element-plus/icons-vue'
import _ from 'lodash'

// Props
interface Props {
  visible: boolean
  muList: any[]
  monitorUnitCategories: any[]
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  muList: () => [],
  monitorUnitCategories: () => []
})

// Emits
const emit = defineEmits<{
  'update:visible': [visible: boolean]
  close: []
}>()

// 响应式数据
const current = ref(0)
const tableRef = ref()
const distributeFormRef = ref()
const passwordVisible = ref(false)
const generating = ref(false)
const distributing = ref(false)
const canGenerate = ref(false)
const completed = ref(false)
const logDetail = ref('')

// WebSocket 相关
let websocket: WebSocket | null = null
const websocketId = 'monitor'
let websocketMsg: string[] = []
let websocketDownMsg: string[] = []
let errorTimer: NodeJS.Timeout | null = null
let webSocketTimer: NodeJS.Timeout | null = null
let heartBeatTimer: NodeJS.Timeout | null = null
let refreshTimer: NodeJS.Timeout | null = null
const wsRefreshSpan = 10000
let pingCount = 0

// 筛选条件
const filters = reactive({
  name: '',
  type: '',
  address: ''
})

const statusFilters = reactive({
  name: '',
  type: '',
  address: '',
  status: ''
})

// 分发表单
const distributeForm = reactive({
  userName: 'root',
  passWord: 'hello',
  port: 21,
  protocol: false
})

// 表单验证规则
const distributeRules = {
  userName: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  passWord: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口', trigger: 'blur' }
  ]
}

// 数据
const showMuList = ref<any[]>([])
const allSelectedItems = ref<any[]>([])
const showAllSelectedItems = ref<any[]>([])
const muStatusList = ref<any[]>([])

// 计算属性
const canDistribute = computed(() => {
  return distributeForm.userName && 
         distributeForm.passWord && 
         distributeForm.port !== 0 &&
         allSelectedItems.value.length > 0
})

// 方法
const initData = () => {
  if (props.muList && props.muList.length > 0) {
    const muListWithType = props.muList.map(item => ({
      ...item,
      typeName: props.monitorUnitCategories?.find(cat => cat.typeId === item.monitorUnitCategory)?.typeName || '未知'
    }))
    showMuList.value = _.cloneDeep(muListWithType)
  } else {
    showMuList.value = []
  }
}

const filterChange = () => {
  if (!props.muList) return
  
  const muListWithType = props.muList.map(item => ({
    ...item,
    typeName: props.monitorUnitCategories.find(cat => cat.typeId === item.monitorUnitCategory)?.typeName || '未知'
  }))
  
  showMuList.value = muListWithType.filter(item => {
    const nameMatch = !filters.name || item.monitorUnitName?.toLowerCase().includes(filters.name.toLowerCase())
    const typeMatch = !filters.type || item.typeName?.toLowerCase().includes(filters.type.toLowerCase())
    const addressMatch = !filters.address || item.ipAddress?.toLowerCase().includes(filters.address.toLowerCase())
    return nameMatch && typeMatch && addressMatch
  })
}

const filterChangeStatus = () => {
  if (!allSelectedItems.value) return
  
  showAllSelectedItems.value = allSelectedItems.value.filter(item => {
    const nameMatch = !statusFilters.name || item.monitorUnitName?.toLowerCase().includes(statusFilters.name.toLowerCase())
    const typeMatch = !statusFilters.type || item.typeName?.toLowerCase().includes(statusFilters.type.toLowerCase())
    const addressMatch = !statusFilters.address || item.ipAddress?.toLowerCase().includes(statusFilters.address.toLowerCase())
    const statusMatch = !statusFilters.status || item.statusTxt?.toLowerCase().includes(statusFilters.status.toLowerCase())
    return nameMatch && typeMatch && addressMatch && statusMatch
  })
}

const handleSelectionChange = (selection: any[]) => {
  allSelectedItems.value = selection
}

const handleSelectAll = (selection: any[]) => {
  allSelectedItems.value = selection
}

const getStatusType = (status: string) => {
  switch (status) {
    case '无需下发':
      return 'info'
    case '待下发':
      return 'warning'
    case '正在下发':
      return 'primary'
    case '下发成功':
      return 'success'
    case '下发失败':
      return 'danger'
    default:
      return 'info'
  }
}

// WebSocket 方法
const initWebSocket = () => {
  if (websocket && websocket.readyState !== WebSocket.CLOSED) {
    websocket.close()
  }
  
  console.log('websocket init')
  const origin = window.location.origin.replace('http', 'ws')
  const wsuri = `${origin}/api/config/websocket/${websocketId}?sessionid=620291d8-60ad-4388-8e45-6a0fad5b487b&userid=-1`
  
  websocket = new WebSocket(wsuri)
  websocket.onmessage = websocketOnMessage
  websocket.onerror = websocketOnError
  websocket.onclose = websocketOnClose
  websocket.onopen = websocketOnOpen
}

const websocketOnOpen = () => {
  console.log('websocket open')
}

const websocketOnMessage = (e: MessageEvent) => {
  if (e.data === 'pong') return
  
  try {
    const msg = JSON.parse(e.data)
    if (msg.webSocketBusinessType === 1) {
      websocketMsg.push(msg.msg)
      canGenerate.value = msg.result
      logDetail.value = websocketMsg.join('\r\n')
      generating.value = false
    } else if (msg.webSocketBusinessType === 2) {
      websocketDownMsg.push(msg.msg)
      completed.value = msg.result
      logDetail.value = websocketDownMsg.join('\r\n')
      distributing.value = false
    }
  } catch (error) {
    console.error('WebSocket message parsing error:', error)
  }
}

const websocketOnError = () => {
  console.log('websocket error and reconnect')
  if (errorTimer) {
    clearTimeout(errorTimer)
  }
  errorTimer = setTimeout(() => {
    initWebSocket()
  }, wsRefreshSpan)
}

const websocketOnClose = () => {
  console.log('websocket close')
}

const heartBeat = () => {
  pingCount++
  if (websocket && websocket.readyState === WebSocket.OPEN) {
    websocket.send('ping')
  }
  if (heartBeatTimer) {
    clearTimeout(heartBeatTimer)
  }
  heartBeatTimer = setTimeout(() => {
    heartBeat()
  }, wsRefreshSpan)
}

const connectWebSocket = () => {
  if (websocket && websocket.readyState === WebSocket.CLOSED) {
    initWebSocket()
  }
  if (webSocketTimer) {
    clearTimeout(webSocketTimer)
  }
  webSocketTimer = setTimeout(() => {
    connectWebSocket()
  }, wsRefreshSpan)
}

// 业务方法
const onGenerate = () => {
  if (allSelectedItems.value.length === 0) {
    ElMessage.warning('请先选择监控单元')
    return
  }
  
  generating.value = true
  websocketMsg = []
  logDetail.value = ''
  
  const ids = allSelectedItems.value.map(item => item.monitorUnitId)
  
  // 如果包含特定类型的监控单元，自动设置密码
  allSelectedItems.value.forEach(item => {
    if (item.monitorUnitCategory === 18 || item.monitorUnitCategory === 17) {
      distributeForm.passWord = '0202@smsP'
    }
  })
  
  const msg = {
    webSocketBusinessType: 1,
    user: distributeForm.userName,
    passWord: distributeForm.passWord,
    port: distributeForm.port,
    monitorUnitIds: ids.join(',')
  }
  
  if (websocket && websocket.readyState === WebSocket.OPEN) {
    websocket.send(JSON.stringify(msg))
    
    // 3秒后开始刷新状态
    if (refreshTimer) {
      clearTimeout(refreshTimer)
    }
    refreshTimer = setTimeout(() => {
      refreshStatus(ids)
    }, 3000)
  } else {
    ElMessage.error('WebSocket连接未建立')
    generating.value = false
  }
}

const onDownFile = async () => {
  if (allSelectedItems.value.length === 0) return
  
  try {
    const ids = allSelectedItems.value.map(item => item.monitorUnitId)
    const response = await fetch(
      `/api/config/monitorunitxml/downloadMultipleMonitorUnitConfigXML?monitorUnitIds=${ids.join(',')}`,
      {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
        }
      }
    )
    
    if (!response.ok) {
      throw new Error('下载失败')
    }
    
    const blob = await response.blob()
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    
    const name = ids.join(',')
    a.download = `${name.length <= 30 ? name : name.substring(0, 29) + '+'}监控单元配置文件.zip`
    a.click()
    
    URL.revokeObjectURL(url)
    ElMessage.success('下载成功！')
  } catch (error) {
    console.error('下载失败:', error)
    ElMessage.error('下载失败')
  }
}

const onDistribute = async () => {
  try {
    await distributeFormRef.value?.validate()
    
    if (allSelectedItems.value.length === 0) {
      ElMessage.warning('请先选择监控单元')
      return
    }
    
    distributing.value = true
    websocketDownMsg = []
    logDetail.value = ''
    
    const ids = allSelectedItems.value.map(item => item.monitorUnitId)
    const msg = {
      webSocketBusinessType: 2,
      user: distributeForm.userName,
      passWord: distributeForm.passWord,
      port: distributeForm.port,
      protocol: distributeForm.protocol ? 'sftp' : 'ftp',
      monitorUnitIds: ids.join(',')
    }
    
    if (websocket && websocket.readyState === WebSocket.OPEN) {
      websocket.send(JSON.stringify(msg))
    } else {
      ElMessage.error('WebSocket连接未建立')
      distributing.value = false
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const refreshStatus = async (ids: number[]) => {
  try {
    const response = await fetch(`/api/config/monitor-unit/active?monitorUnitIds=${ids.join(',')}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
      }
    })
    
    if (!response.ok) {
      throw new Error('获取状态失败')
    }
    
    const result = await response.json()
    muStatusList.value = result.data || []
    
    // 更新状态
    allSelectedItems.value.forEach(mu => {
      const status = muStatusList.value.find(s => s.monitorUnitId === mu.monitorUnitId)
      if (status) {
        switch (status.state) {
          case 0:
            mu.statusTxt = '无需下发'
            break
          case 1:
            mu.statusTxt = '待下发'
            break
          case 2:
            mu.statusTxt = '正在下发'
            break
          case 3:
            mu.statusTxt = '下发成功'
            break
          case 4:
            mu.statusTxt = '下发失败'
            break
          default:
            mu.statusTxt = '未知'
            break
        }
      }
    })
    
    // 继续轮询
    if (refreshTimer) {
      clearTimeout(refreshTimer)
    }
    refreshTimer = setTimeout(() => {
      refreshStatus(ids)
    }, 3000)
  } catch (error) {
    console.error('刷新状态失败:', error)
  }
}

// 步骤控制
const pre = () => {
  current.value -= 1
  if (current.value === 1) {
    logDetail.value = websocketMsg.join('\r\n')
  }
}

const next = () => {
  current.value += 1
  if (current.value === 1) {
    showAllSelectedItems.value = _.cloneDeep(allSelectedItems.value)
  }
  if (current.value === 2) {
    logDetail.value = websocketDownMsg.join('\r\n')
    
    // 检查是否全部是GFSU V3类型，如果是则启用SFTP
    const isAllGfsuV3 = allSelectedItems.value.every(item => item.monitorUnitCategory === 18)
    if (isAllGfsuV3) {
      distributeForm.protocol = true
    }
  }
}

const done = () => {
  handleClose()
}

const handleClose = () => {
  // 清理定时器
  if (errorTimer) {
    clearTimeout(errorTimer)
    errorTimer = null
  }
  if (webSocketTimer) {
    clearTimeout(webSocketTimer)
    webSocketTimer = null
  }
  if (heartBeatTimer) {
    clearTimeout(heartBeatTimer)
    heartBeatTimer = null
  }
  if (refreshTimer) {
    clearTimeout(refreshTimer)
    refreshTimer = null
  }
  
  // 关闭WebSocket
  if (websocket) {
    websocket.close()
    websocket = null
  }
  
  // 重置所有状态
  current.value = 0
  allSelectedItems.value = []
  showAllSelectedItems.value = []
  websocketMsg = []
  websocketDownMsg = []
  logDetail.value = ''
  canGenerate.value = false
  completed.value = false
  generating.value = false
  distributing.value = false
  passwordVisible.value = false
  
  // 重置筛选条件
  filters.name = ''
  filters.type = ''
  filters.address = ''
  statusFilters.name = ''
  statusFilters.type = ''
  statusFilters.address = ''
  statusFilters.status = ''
  
  // 重置表格选择
  if (tableRef.value) {
    tableRef.value.clearSelection()
  }
  
  // 重置表单
  if (distributeFormRef.value) {
    distributeFormRef.value.clearValidate()
  }
  
  // 最后发送关闭事件
  emit('update:visible', false)
  emit('close')
}

// 监听props变化
watch(
  () => props.visible,
  (newVal, oldVal) => {
    if (newVal && !oldVal) {
      // 弹框打开时初始化
      nextTick(() => {
        initData()
        initWebSocket()
        
        // 启动WebSocket重连定时器
        if (webSocketTimer) {
          clearTimeout(webSocketTimer)
        }
        webSocketTimer = setTimeout(() => {
          connectWebSocket()
        }, 10000)
        
        // 启动心跳定时器
        if (heartBeatTimer) {
          clearTimeout(heartBeatTimer)
        }
        heartBeatTimer = setTimeout(() => {
          heartBeat()
        }, 3000)
      })
    }
  },
  { immediate: false }
)

// 监听数据变化，确保数据加载后能够正确显示
watch(
  () => [props.muList, props.monitorUnitCategories],
  ([newMuList, newCategories]) => {
    if (props.visible && newMuList && newMuList.length > 0) {
      initData()
    }
  },
  { deep: true, immediate: true }
)

// 组件挂载时初始化
onMounted(() => {
  if (props.visible) {
    initData()
    initWebSocket()
    
    // 启动WebSocket重连定时器
    if (webSocketTimer) {
      clearTimeout(webSocketTimer)
    }
    webSocketTimer = setTimeout(() => {
      connectWebSocket()
    }, 10000)
    
    // 启动心跳定时器
    if (heartBeatTimer) {
      clearTimeout(heartBeatTimer)
    }
    heartBeatTimer = setTimeout(() => {
      heartBeat()
    }, 3000)
  }
})

// 组件卸载时清理
onUnmounted(() => {
  if (errorTimer) clearTimeout(errorTimer)
  if (webSocketTimer) clearTimeout(webSocketTimer)
  if (heartBeatTimer) clearTimeout(heartBeatTimer)
  if (refreshTimer) clearTimeout(refreshTimer)
  
  if (websocket) {
    websocket.close()
  }
})
</script>

<style scoped>
.mu-distribution-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.mu-distribution-dialog :deep(.el-dialog__footer) {
  padding: 0;
}

.modern-table {
  border-radius: 8px;
  overflow: hidden;
}

.modern-table :deep(.el-table__header) {
  background-color: var(--el-fill-color-lighter);
}

.modern-table :deep(.el-table th) {
  background-color: var(--el-fill-color-lighter);
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.modern-table :deep(.el-table td) {
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.modern-table :deep(.el-table tr:hover td) {
  background-color: var(--el-fill-color-light);
}

.log-textarea :deep(.el-textarea__inner) {
  background-color: var(--el-fill-color-darker);
  border: none;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  color: var(--el-text-color-primary);
}

.distribute-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--el-text-color-regular);
}

.distribution-tabs :deep(.el-tabs__header) {
  margin-bottom: 16px;
}

.distribution-tabs :deep(.el-tabs__nav-wrap) {
  background-color: transparent;
}

.distribution-tabs :deep(.el-tabs__item) {
  font-weight: 500;
}

.distribution-tabs :deep(.el-tabs__content) {
  padding: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .mu-distribution-dialog {
    width: 90% !important;
  }
}

@media (max-width: 992px) {
  .mu-distribution-dialog {
    width: 95% !important;
  }
}

@media (max-width: 768px) {
  .grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}
</style> 