<template>
  <el-dialog
    v-model="dialogVisible"
    title="参数含义设置"
    width="750px"
    :before-close="handleClose"
    @closed="$emit('update:modelValue', false)"
  >
    <div class="control-meaning-modal">
      <!-- 表格 -->
      <el-table
        :data="meaningsList"
        border
        style="width: 100%"
        max-height="400px"
      >
        <!-- 序号列 -->
        <el-table-column label="序号" width="120">
          <template #default="{ row, $index }">
            <div class="edit-container">
              <el-input
                v-model="row.stateValue"
                size="small"
                :class="{ 'error-input': row.error }"
                :disabled="disabled"
                placeholder="请输入序号"
                @blur="validateStateValue(row)"
                @input="validateStateValue(row)"
                @keydown.enter="validateStateValue(row)"
              />
              <el-icon v-if="row.error" class="error-icon" color="#eb2f96">
                <WarningFilled />
              </el-icon>
            </div>
          </template>
        </el-table-column>

        <!-- 含义列 -->
        <el-table-column label="含义" min-width="200">
          <template #default="{ row }">
            <div class="edit-container">
              <el-input
                v-model="row.meanings"
                size="small"
                :disabled="disabled"
              />
            </div>
          </template>
        </el-table-column>

        <!-- 基类状态含义列 -->
        <el-table-column label="基类状态含义" width="200">
          <template #default="{ row }">
            <el-select
              v-model="row.baseCondId"
              size="small"
              style="width: 100%"
              clearable
              placeholder="请选择基类状态含义"
              :disabled="disabled"
            >
              <el-option-group :label="baseStatusName || '基类状态含义'">
                <el-option
                  v-for="item in basetypeMeaningsArr"
                  :key="item.value"
                  :label="item.meaning"
                  :value="item.value"
                />
              </el-option-group>
            </el-select>
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column v-if="!disabled" label="操作" width="80">
          <template #default="{ $index }">
            <el-button
              type="danger"
              size="small"
              :icon="Delete"
              title="删除"
              @click="deleteRow($index)"
            />
          </template>
        </el-table-column>
      </el-table>

      <!-- 添加按钮 -->
      <el-button
        v-if="!disabled"
        type="primary"
        style="margin-top: 10px"
        @click="addRow"
      >
        添加
      </el-button>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :disabled="disabled" @click="handleConfirm">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { ElMessage } from "element-plus";
import { Delete, WarningFilled } from "@element-plus/icons-vue";
import { http } from "@/utils/http";

// 控制含义接口
export interface ControlMeaning {
  id?: number;
  equipmentTemplateId?: number;
  controlId?: number;
  parameterValue?: number;
  stateValue?: number | string;
  meanings?: string;
  baseCondId?: number;
  editing?: boolean;
  meaningEdit?: boolean;
  error?: boolean;
}

// 基类状态含义接口
export interface BaseTypeMeaning {
  value: number;
  meaning: string;
}

// 基类状态响应接口
export interface BaseStatusInfo {
  baseStatusName?: string;
  statusMeanings?: BaseTypeMeaning[];
}

interface Props {
  modelValue: boolean;
  controlData?: any;
  disabled?: boolean;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
  (e: "confirm", data: { action: string; data: ControlMeaning[] }): void;
  (e: "cancel"): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  controlData: null,
  disabled: false
});

const emit = defineEmits<Emits>();

// 响应式数据
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit("update:modelValue", value)
});

const meaningsList = ref<ControlMeaning[]>([]);
const basetypeMeaningsArr = ref<BaseTypeMeaning[]>([]);
const baseStatusName = ref<string>("");

// 默认行数据
const createDefaultRow = (): ControlMeaning => ({
  stateValue: "",
  parameterValue: 0,
  meanings: "含义",
  baseCondId: undefined, // 默认为空，不选择任何基类状态含义
  equipmentTemplateId: props.controlData?.equipmentTemplateId || null,
  controlId: props.controlData?.controlId || null,
  editing: false,
  meaningEdit: false,
  error: false
});

// 监听弹框打开
watch(
  () => props.modelValue,
  newVal => {
    if (newVal) {
      initData();
    }
  }
);

// 初始化数据
const initData = () => {
  if (props.controlData?.controlMeaningsList) {
    meaningsList.value = props.controlData.controlMeaningsList.map(
      (item: any) => ({
        ...item,
        stateValue: item.parameterValue,
        editing: false,
        meaningEdit: false,
        error: false
      })
    );
  } else {
    meaningsList.value = [];
  }

  loadBaseTypeMeanings();
};

// 加载基类状态含义
const loadBaseTypeMeanings = async () => {
  if (
    !props.controlData?.equipmentTemplateId ||
    !props.controlData?.controlId
  ) {
    basetypeMeaningsArr.value = [{ value: -1, meaning: "无" }];
    return;
  }

  try {
    const res = await http.request<any>(
      "get",
      `/api/config/control/basetypemeanings?equipmentTemplateId=${props.controlData.equipmentTemplateId}&controlId=${props.controlData.controlId}`
    );

    if (res.code === 0 && res.data) {
      baseStatusName.value = res.data.baseStatusName || "";
      basetypeMeaningsArr.value = [
        { value: -1, meaning: "无" },
        ...(res.data.statusMeanings || [])
      ];
    } else {
      basetypeMeaningsArr.value = [{ value: -1, meaning: "无" }];
    }
  } catch (error) {
    console.error("获取基类状态含义失败:", error);
    basetypeMeaningsArr.value = [{ value: -1, meaning: "无" }];
  }
};

// 验证状态值
const validateStateValue = (row: ControlMeaning) => {
  if (props.disabled) return;

  // 验证数值（允许0和正整数）
  const reg = /^(0|[1-9]\d*)$/;
  const valueStr = row.stateValue?.toString().trim() || "";

  if (valueStr === "") {
    // 允许为空，默认设为0
    row.error = false;
    row.parameterValue = 0;
    row.stateValue = 0;
  } else if (reg.test(valueStr)) {
    // 是有效的非负整数（包括0）
    row.error = false;
    row.parameterValue = parseInt(valueStr);
    row.stateValue = parseInt(valueStr);
  } else {
    // 不是有效的非负整数
    row.error = true;
  }
};

// 添加行
const addRow = () => {
  // 检查是否有错误的行
  if (meaningsList.value.find(item => item.error)) {
    ElMessage.warning("请先修复错误的数据");
    return;
  }

  // 计算新的序号值
  let maxId = Math.max(
    ...meaningsList.value.map(item => Number(item.stateValue) || 0)
  );
  maxId = maxId === -Infinity ? 0 : maxId + 1;

  const newRow = createDefaultRow();
  newRow.stateValue = maxId;
  newRow.parameterValue = maxId;
  newRow.meanings = ""; // 新行的含义默认为空，让用户填写

  meaningsList.value.push(newRow);
};

// 删除行
const deleteRow = (index: number) => {
  meaningsList.value.splice(index, 1);
};

// 确认
const handleConfirm = () => {
  // 检查是否有错误
  if (meaningsList.value.find(item => item.error)) {
    ElMessage.error("请先修复错误的数据");
    return;
  }

  // 验证所有状态值并确保 parameterValue 与 stateValue 同步
  meaningsList.value.forEach(item => {
    validateStateValue(item);
    if (!item.error) {
      item.parameterValue = Number(item.stateValue) || 0;
    }
  });

  // 再次检查是否有验证错误
  if (meaningsList.value.find(item => item.error)) {
    ElMessage.error("请输入正确的序号（0或正整数）");
    return;
  }

  emit("confirm", {
    action: "confirm",
    data: meaningsList.value
  });

  dialogVisible.value = false;
};

// 取消
const handleCancel = () => {
  emit("cancel");
  dialogVisible.value = false;
};

// 关闭前处理
const handleClose = () => {
  handleCancel();
};
</script>

<style scoped>
.control-meaning-modal {
  padding: 0;
}

.edit-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.error-input {
  border-color: #eb2f96 !important;
}

.error-icon {
  font-size: 16px;
  flex-shrink: 0;
}

:deep(.el-table .cell) {
  padding: 4px 8px;
}

:deep(.el-input--small) {
  font-size: 12px;
}

:deep(.el-select) {
  width: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>
