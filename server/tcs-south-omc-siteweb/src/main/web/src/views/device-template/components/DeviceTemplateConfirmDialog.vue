<template>
  <el-dialog
    v-model="visible"
    title="模板修改确认"
    width="900px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="confirm-container">
      <div class="confirm-tips">
        您正在修改的设备模板已被下面局站中的设备所引用。设备数：{{
          deviceCount
        }}
      </div>

      <div class="device-table">
        <el-table
          v-loading="loading"
          :data="deviceList"
          height="400px"
          stripe
          border
        >
          <el-table-column prop="stationId" label="局站ID" width="80" />
          <el-table-column prop="stationName" label="局站名称" width="120" />
          <el-table-column
            prop="monitorUnitId"
            label="监控单元ID"
            width="100"
          />
          <el-table-column
            prop="monitorUnitName"
            label="监控单元名称"
            width="120"
          />
          <el-table-column prop="ipAddress" label="监控单元IP" width="120" />
          <el-table-column prop="equipmentName" label="设备名称" width="120" />
        </el-table>
      </div>

      <div class="show-tips">
        <el-checkbox v-model="notShowAgain">
          下次更新原模板时不再提醒我
        </el-checkbox>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleCopyTemplate"
          >另存为新模板</el-button
        >
        <el-button type="primary" @click="handleUpdateOriginal"
          >更新原模板</el-button
        >
      </div>
    </template>

    <!-- 复制模板对话框 -->
    <DeviceTemplateCopyDialog
      v-model="showCopyDialog"
      :template-id="templateId"
      @confirm="handleCopyConfirm"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { ElMessage } from "element-plus";
import DeviceTemplateCopyDialog from "./DeviceTemplateCopyDialog.vue";
import { getDevicesByTemplateId } from "@/api/device-template";

interface DeviceInfo {
  stationId: string;
  stationName: string;
  monitorUnitId: string;
  monitorUnitName: string;
  ipAddress: string;
  equipmentName: string;
  equipmentId: string;
}

interface Props {
  modelValue: boolean;
  templateId?: number;
  templateData?: any;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
  (e: "confirm", action: "update" | "copy", data?: any): void;
  (e: "cancel"): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  templateId: 0,
  templateData: null
});

const emit = defineEmits<Emits>();

// 状态变量
const loading = ref(false);
const deviceList = ref<DeviceInfo[]>([]);
const notShowAgain = ref(false);
const showCopyDialog = ref(false);

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit("update:modelValue", value)
});

const deviceCount = computed(() => deviceList.value.length);

// 监听对话框显示状态
watch(visible, newValue => {
  if (newValue && props.templateId) {
    loadDeviceList();
  }
});

// 加载受影响的设备列表
const loadDeviceList = async () => {
  if (!props.templateId) return;

  loading.value = true;
  try {
    const res = await getDevicesByTemplateId(props.templateId);
    if (res.code === 0) {
      deviceList.value = res.data || [];
    } else {
      ElMessage.error(res.msg || "获取设备列表失败");
      deviceList.value = [];
    }
  } catch (error) {
    console.error("获取设备列表失败:", error);
    ElMessage.error("获取设备列表失败");
    deviceList.value = [];
  } finally {
    loading.value = false;
  }
};

// 处理对话框关闭
const handleClose = () => {
  // 关闭对话框时也触发取消事件
  emit("cancel");
  visible.value = false;
  deviceList.value = [];
  notShowAgain.value = false;
};

// 处理取消
const handleCancel = () => {
  emit("cancel");
  handleClose();
};

// 处理更新原模板
const handleUpdateOriginal = () => {
  emit("confirm", "update", {
    notShowAgain: notShowAgain.value,
    refresh: true
  });
  handleClose();
};

// 处理另存为新模板
const handleCopyTemplate = () => {
  showCopyDialog.value = true;
};

// 处理复制确认
const handleCopyConfirm = (result: {
  newTemplateId: number;
  name: string;
  reason: string;
}) => {
  showCopyDialog.value = false;
  emit("confirm", "copy", {
    newTemplateId: result.newTemplateId,
    newTemplateName: result.name,
    reason: result.reason,
    notShowAgain: notShowAgain.value,
    refresh: true
  });
  handleClose();
};
</script>

<style scoped>
.confirm-container {
  padding: 0;
}

.confirm-tips {
  margin-bottom: 16px;
  padding: 12px;
  background-color: var(--el-color-warning-light-9);
  border: 1px solid var(--el-color-warning-light-5);
  border-radius: 4px;
  color: var(--el-color-warning-dark-2);
  font-size: 14px;
}

.device-table {
  margin-bottom: 16px;
}

.show-tips {
  display: flex;
  align-items: center;
  padding-top: 8px;
  border-top: 1px solid var(--el-border-color-light);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>
