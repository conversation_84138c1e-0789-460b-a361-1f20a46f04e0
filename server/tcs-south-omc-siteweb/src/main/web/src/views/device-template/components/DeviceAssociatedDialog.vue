<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`查看引用该模板的设备 - ${templateName}`"
    width="1200px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
  >
    <!-- 提示信息 -->
    <div class="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
      <p class="text-sm text-blue-700 dark:text-blue-300">
        该设备模板已被下面局站中的设备所引用。设备数：{{ filteredDevices.length }}
      </p>
    </div>

    <!-- 表格 -->
    <div class="device-table-container">
      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="filteredDevices"
        :height="560"
        stripe
        border
        @selection-change="handleSelectionChange"
      >
        <!-- 选择列 -->
        <el-table-column
          type="selection"
          width="50"
          :selectable="() => true"
        />
        
        <!-- 局站ID -->
        <el-table-column
          prop="stationId"
          label="局站ID"
          width="120"
          
        >
          <template #header>
            <div class="table-header">
              <div class="header-title">局站ID</div>
              <el-input
                v-model="searchText.stationId"
                size="small"
                placeholder="请输入关键字..."
                :suffix-icon="Search"
                clearable
                @input="handleSearch"
                class="header-search"
              />
            </div>
          </template>
          <template #default="{ row }">
            <span :title="row.stationId">{{ row.stationId }}</span>
          </template>
        </el-table-column>

        <!-- 局站名称 -->
        <el-table-column
          prop="stationName"
          label="局站名称"
          width="200"
        >
          <template #header>
            <div class="table-header">
              <div class="header-title">局站名称</div>
              <el-input
                v-model="searchText.stationName"
                size="small"
                placeholder="请输入关键字..."
                :suffix-icon="Search"
                clearable
                @input="handleSearch"
                class="header-search"
              />
            </div>
          </template>
          <template #default="{ row }">
            <span :title="row.stationName">{{ row.stationName }}</span>
          </template>
        </el-table-column>

        <!-- 监控单元ID -->
        <el-table-column
          prop="monitorUnitId"
          label="监控单元ID"
          width="120"
        >
          <template #header>
            <div class="table-header">
              <div class="header-title">监控单元ID</div>
              <el-input
                v-model="searchText.monitorUnitId"
                size="small"
                placeholder="请输入关键字..."
                :suffix-icon="Search"
                clearable
                @input="handleSearch"
                class="header-search"
              />
            </div>
          </template>
          <template #default="{ row }">
            <span :title="row.monitorUnitId">{{ row.monitorUnitId }}</span>
          </template>
        </el-table-column>

        <!-- 监控单元名称 -->
        <el-table-column
          prop="monitorUnitName"
          label="监控单元名称"
          width="200"
          
        >
          <template #header>
            <div class="table-header">
              <div class="header-title">监控单元名称</div>
              <el-input
                v-model="searchText.monitorUnitName"
                size="small"
                placeholder="请输入关键字..."
                :suffix-icon="Search"
                clearable
                @input="handleSearch"
                class="header-search"
              />
            </div>
          </template>
          <template #default="{ row }">
            <span :title="row.monitorUnitName">{{ row.monitorUnitName }}</span>
          </template>
        </el-table-column>

        <!-- 监控单元IP -->
        <el-table-column
          prop="ipAddress"
          label="监控单元IP"
          width="150"
          
        >
          <template #header>
            <div class="table-header">
              <div class="header-title">监控单元IP</div>
              <el-input
                v-model="searchText.ipAddress"
                size="small"
                placeholder="请输入关键字..."
                :suffix-icon="Search"
                clearable
                @input="handleSearch"
                class="header-search"
              />
            </div>
          </template>
          <template #default="{ row }">
            <span :title="row.ipAddress">{{ row.ipAddress }}</span>
          </template>
        </el-table-column>

        <!-- 设备名称 -->
        <el-table-column
          prop="equipmentName"
          label="设备名称"
          min-width="150"
          
        >
          <template #header>
            <div class="table-header">
              <div class="header-title">设备名称</div>
              <el-input
                v-model="searchText.equipmentName"
                size="small"
                placeholder="请输入关键字..."
                :suffix-icon="Search"
                clearable
                @input="handleSearch"
                class="header-search"
              />
            </div>
          </template>
          <template #default="{ row }">
            <span :title="row.equipmentName">{{ row.equipmentName }}</span>
          </template>
        </el-table-column>

        <!-- 空状态 -->
        <template #empty>
          <div class="text-center py-8">
            <el-icon size="48" class="text-gray-400 mb-4">
              <Monitor />
            </el-icon>
            <p class="text-gray-500">暂无关联设备数据</p>
          </div>
        </template>
      </el-table>
    </div>

    <template #footer>
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-500">
          已选择 {{ selectedDevices.length }} 个设备
        </div>
        <div class="flex space-x-3">
          <el-button
            type="primary"
            :disabled="selectedDevices.length === 0"
            @click="exportDevices"
            :loading="exportLoading"
          >
            <el-icon><Download /></el-icon>
            导出
          </el-button>
          <el-button
            type="primary"
            :disabled="selectedDevices.length === 0"
            @click="batchChangeTemplate"
          >
            <el-icon><DocumentCopy /></el-icon>
            批量切换模板
          </el-button>
          <el-button
            type="primary"
            :disabled="selectedDevices.length === 0"
            @click="batchDistributeConfig"
          >
            <el-icon><Connection /></el-icon>
            批量分发MU配置
          </el-button>
          <el-button @click="closeDialog">
            关闭
          </el-button>
        </div>
      </div>
    </template>
    
    <!-- 模板选择器对话框 -->
    <TemplateSelectorDialog
      v-model:visible="templateSelectorVisible"
      :origin-template="currentOriginTemplate"
      :selected-device-ids="selectedDevices.map(device => device.equipmentId)"
      :equipment-category="currentOriginTemplate?.equipmentCategory || ''"
      @confirm="handleTemplateChangeConfirm"
      @show-effect-dialog="handleShowEffectDialog"
    />

    <!-- 模板切换影响确认对话框 -->
    <TemplateEffectConfirmDialog
      v-model:visible="effectDialogVisible"
      :origin-template="effectDialogData.originTemplate"
      :new-template="effectDialogData.newTemplate"
      :selected-device-ids="effectDialogData.deviceIds"
      @confirm="handleEffectConfirm"
    />

    <!-- 监控单元配置分发对话框 -->
    <MuDistribution
      v-model:visible="muDistributionVisible"
      :mu-list="muList"
      :monitor-unit-categories="monitorUnitCategories"
      @close="handleMuDistributionClose"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Monitor, Download, DocumentCopy, Connection } from '@element-plus/icons-vue'
  import { 
  getDevicesByTemplateId, 
  exportAssociatedDevice, 
  batchChangeTemplate as apiBatchChangeTemplate,
  getMonitorUnitsByIds,
  getMonitorUnitCategories,
  type TemplateTreeNode,
  type BatchChangeTemplateParams 
} from '@/api/device-template'
  import TemplateSelectorDialog from './TemplateSelectorDialog.vue'
  import TemplateEffectConfirmDialog from './TemplateEffectConfirmDialog.vue'
  import MuDistribution from './MuDistribution.vue'
  import { debounce } from 'lodash'
  
  // 组件注册
  defineOptions({
    components: {
      TemplateSelectorDialog
    }
  })

// Props
interface Props {
  visible: boolean
  templateId: string
  templateName: string
  equipmentCategory?: string
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  templateId: '',
  templateName: '',
  equipmentCategory: ''
})

// Emits
const emit = defineEmits<{
  'update:visible': [visible: boolean]
  'batch-change-template': [deviceIds: string[]]
}>()

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const devices = ref<any[]>([])
const selectedDevices = ref<any[]>([])
const tableRef = ref()

// 模板选择器状态
const templateSelectorVisible = ref(false)
const currentOriginTemplate = ref<TemplateTreeNode | null>(null)

// 模板切换影响对话框状态
const effectDialogVisible = ref(false)
const effectDialogData = ref({
  originTemplate: null as TemplateTreeNode | null,
  newTemplate: null as TemplateTreeNode | null,
  deviceIds: [] as string[]
})

// 监控单元分发对话框状态
const muDistributionVisible = ref(false)
const muList = ref<any[]>([])
const monitorUnitCategories = ref<any[]>([])

// 搜索文本
const searchText = reactive({
  stationId: '',
  stationName: '',
  monitorUnitId: '',
  monitorUnitName: '',
  ipAddress: '',
  equipmentName: ''
})

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 过滤后的设备列表
const filteredDevices = computed(() => {
  let result = [...devices.value]
  
  Object.entries(searchText).forEach(([key, value]) => {
    if (value && value.trim()) {
      result = result.filter(device => {
        const fieldValue = device[key]
        return fieldValue && fieldValue.toString().toLowerCase().includes(value.toLowerCase())
      })
    }
  })
  
  return result
})

// 监听对话框显示状态
watch(() => props.visible, (visible) => {
  if (visible && props.templateId) {
    loadDevices()
  } else {
    resetData()
  }
})

// 重置数据
const resetData = () => {
  devices.value = []
  selectedDevices.value = []
  Object.keys(searchText).forEach(key => {
    searchText[key] = ''
  })
}

// 加载设备数据
const loadDevices = async () => {
  try {
    loading.value = true
    const response = await getDevicesByTemplateId(props.templateId)
    
    if (response.code === 0) {
      devices.value = response.data || []
    } else {
      ElMessage.error(response.msg || '获取关联设备失败')
      devices.value = []
    }
  } catch (error) {
    console.error('获取关联设备失败:', error)
    ElMessage.error('获取关联设备信息失败')
    devices.value = []
  } finally {
    loading.value = false
  }
}

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedDevices.value = selection
}

// 搜索处理（防抖）
const handleSearch = debounce(() => {
  // 搜索逻辑在计算属性中处理
  nextTick(() => {
    // 清空当前选择，因为搜索后数据可能发生变化
    tableRef.value?.clearSelection()
  })
}, 300)

// 导出设备
const exportDevices = async () => {
  try {
    exportLoading.value = true
    const blob = await exportAssociatedDevice(props.templateId)
    
    if (!blob || blob.size === 0) {
      ElMessage.error('导出的文件为空')
      return
    }
    
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${props.templateName}-关联设备.xlsx`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

// 批量切换模板
const batchChangeTemplate = () => {
  if (selectedDevices.value.length === 0) {
    ElMessage.warning('请先选择设备')
    return
  }
  
  // 创建原始模板对象
  currentOriginTemplate.value = {
    id: props.templateId,
    name: props.templateName,
    template: true,
    title: props.templateName,
    key: props.templateId,
    equipmentCategory: props.equipmentCategory
  }
  
  templateSelectorVisible.value = true
}

// 批量分发MU配置
const batchDistributeConfig = async () => {
  if (selectedDevices.value.length === 0) {
    ElMessage.warning('请先选择要分发的设备')
    return
  }
  
  try {
    // 收集监控单元ID
    const muIds: number[] = []
    selectedDevices.value.forEach(device => {
      if (device.monitorUnitId && !muIds.includes(device.monitorUnitId)) {
        muIds.push(device.monitorUnitId)
      }
    })
    
    if (muIds.length === 0) {
      ElMessage.warning('选中的设备没有关联的监控单元')
      return
    }
    
    // 获取监控单元列表和类型
    const [muResponse, categoriesResponse] = await Promise.all([
      getMonitorUnitsByIds(muIds.join(',')),
      getMonitorUnitCategories()
    ])
    
    if (muResponse.code === 0 && categoriesResponse.code === 0) {
      muList.value = muResponse.data || []
      monitorUnitCategories.value = categoriesResponse.data || []
      muDistributionVisible.value = true
    } else {
      ElMessage.error('获取监控单元信息失败')
    }
  } catch (error) {
    console.error('获取监控单元信息失败:', error)
    ElMessage.error('获取监控单元信息失败')
  }
}

// 处理模板切换确认（直接切换，不再使用）
const handleTemplateChangeConfirm = async (templateData: { 
  originTemplate: TemplateTreeNode, 
  newTemplate: TemplateTreeNode, 
  deviceIds: string[] 
}) => {
  try {
    const params: BatchChangeTemplateParams = {
      newEquipmentTemplateId: templateData.newTemplate.id,
      equipmentIds: templateData.deviceIds
    }
    
    const response = await apiBatchChangeTemplate(params)
    
    if (response.code === 0) {
      ElMessage.success(`成功将 ${templateData.deviceIds.length} 个设备从模板 "${templateData.originTemplate.name}" 切换到 "${templateData.newTemplate.name}"`)
      
      templateSelectorVisible.value = false
      
      // 刷新设备列表
      if (props.templateId) {
        loadDevices()
      }
      
      // 清空选中状态
      selectedDevices.value = []
    } else {
      ElMessage.error(response.msg || '模板切换失败')
    }
  } catch (error) {
    console.error('模板切换失败:', error)
    ElMessage.error('模板切换失败')
  }
}

// 处理显示模板切换影响对话框
const handleShowEffectDialog = (templateData: { 
  originTemplate: TemplateTreeNode, 
  newTemplate: TemplateTreeNode, 
  deviceIds: string[] 
}) => {
  // 隐藏模板选择器对话框
  templateSelectorVisible.value = false
  
  // 设置影响对话框数据
  effectDialogData.value = {
    originTemplate: templateData.originTemplate,
    newTemplate: templateData.newTemplate,
    deviceIds: templateData.deviceIds
  }
  
  // 显示影响对话框
  effectDialogVisible.value = true
}

// 处理影响确认（最终执行模板切换）
const handleEffectConfirm = () => {
  // 关闭影响对话框
  effectDialogVisible.value = false
  
  // 刷新设备列表
  if (props.templateId) {
    loadDevices()
  }
  
  // 清空选中状态
  selectedDevices.value = []
}

// 处理监控单元分发对话框关闭
const handleMuDistributionClose = () => {
  muDistributionVisible.value = false
  // 清理数据
  muList.value = []
  monitorUnitCategories.value = []
}

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.device-table-container {
  border-radius: 6px;
  overflow: hidden;
}

.table-header {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.header-title {
  font-weight: 600;
  color: var(--el-text-color-primary);
  white-space: nowrap;
}

.header-search {
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .device-table-container {
    font-size: 12px;
  }
  
  .header-search {
    font-size: 12px;
  }
}
</style> 