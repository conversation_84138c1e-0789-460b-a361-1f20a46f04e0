<template>
  <el-dialog
    v-model="dialogVisible"
    title="复制模板"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
    >
      <el-form-item label="模板名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入模板名称"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="复制原因" prop="reason">
        <el-input
          v-model="form.reason"
          type="textarea"
          :rows="3"
          placeholder="请输入复制原因（可选）"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleConfirm"
        >
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from "vue";
import type { FormInstance } from "element-plus";

// Props
interface Props {
  visible: boolean;
  templateName?: string;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  templateName: ""
});

// Emits
const emit = defineEmits<{
  "update:visible": [value: boolean];
  confirm: [data: { name: string; reason: string }];
}>();

// 响应式数据
const dialogVisible = ref(false);
const loading = ref(false);
const formRef = ref<FormInstance>();

const form = reactive({
  name: "",
  reason: ""
});

const rules = {
  name: [
    { required: true, message: "请输入模板名称", trigger: "blur" },
    { min: 1, max: 100, message: "模板名称长度在1到100个字符", trigger: "blur" }
  ]
};

// 监听显示状态
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal;
    if (newVal && props.templateName) {
      // 生成随机数后缀
      const randomNumber = Math.floor(Math.random() * 100000) + 1;
      form.name = `${props.templateName}_${randomNumber}#`;
      form.reason = "";
    }
  },
  { immediate: true }
);

watch(dialogVisible, (newVal) => {
  emit("update:visible", newVal);
});

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 确认操作
const handleConfirm = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    loading.value = true;
    
    emit("confirm", {
      name: form.name.trim(),
      reason: form.reason.trim()
    });
    
  } catch (error) {
    console.error("表单验证失败:", error);
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style> 