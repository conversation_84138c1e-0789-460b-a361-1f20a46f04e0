<template>
  <el-dialog
    v-model="visible"
    title="基类设置"
    width="750px"
    :before-close="handleClose"
  >
    <div class="base-class-selector">
      <el-form :model="form" label-width="120px" :rules="formRules" ref="formRef">
        <el-form-item :label="nameLabel">
          <el-input v-model="form.nameValue" disabled />
        </el-form-item>
        
        <el-form-item 
          v-if="selectorType === BaseClassSelectorType.event" 
          label="事件条件"
        >
          <el-input v-model="form.eventCondition" disabled />
        </el-form-item>
        
        <el-form-item label="设备类型" prop="equipmentSubType">
          <el-cascader
            v-model="equipmentTypeValue"
            :options="equipmentTypeOptions"
            :props="cascaderProps"
            placeholder="请选择设备类型"
            clearable
            @change="onEquipmentTypeChange"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item :label="bindBaseClassLabel">
          <div class="base-class-table-container">
            <el-table
              ref="tableRef"
              :data="displayTableDataSource"
              border
              stripe
              size="small"
              height="260"
              row-key="baseTypeId"
              @selection-change="onSelectionChange"
            >
              <el-table-column
                type="selection"
                width="55"
                :selectable="() => true"
                :reserve-selection="false"
              />
              <el-table-column :label="tableHeaderId" prop="baseTypeId" width="120" />
              <el-table-column :label="tableHeaderName" prop="baseTypeName" width="180" />
              <el-table-column label="扩展表达式" prop="baseNameExt" />
            </el-table>
          </div>
        </el-form-item>
        
        <el-form-item 
          v-if="selectedRow.baseNameExt" 
          label="模块号" 
          prop="moduleNumber"
        >
          <el-input 
            v-model="form.moduleNumber" 
            placeholder="请输入模块号"
            type="number"
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button 
          type="danger" 
          ghost
          @click="handleDeleteAssociation"
          :disabled="!hasExistingBaseType"
        >
          取消关联基类
        </el-button>
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleConfirm"
          :loading="confirmLoading"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from "vue";
import { 
  ElDialog, 
  ElForm, 
  ElFormItem, 
  ElInput, 
  ElButton, 
  ElTable, 
  ElTableColumn, 
  ElCascader,
  ElMessage,
  ElMessageBox,
  type FormInstance,
  type FormRules
} from "element-plus";
import { 
  BaseClassSelectorType,
  getBaseClassEquipmentTree,
  getBaseClassList,
  getEquipmentTypeByBaseTypeId,
  getEquipmentTypeByBaseEquipmentId,
  getTemplateInfoById,
  updateTableRow,
  addBaseType,
  type EquipmentTypeNode,
  type BaseClassInfo
} from "@/api/device-template";

interface Props {
  modelValue: boolean;
  signalData?: any;
  templateId?: string | number;
  type?: BaseClassSelectorType;
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'confirm', result: { 
    action: string; 
    baseTypeId: number | null; 
    baseTypeName?: string; 
    moduleNumber?: number | null;
  }): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  signalData: null,
  templateId: '',
  type: BaseClassSelectorType.signal
});

const emit = defineEmits<Emits>();

// 响应式数据
const visible = ref(false);
const confirmLoading = ref(false);
const formRef = ref<FormInstance>();
const tableRef = ref();
const selectorType = ref(props.type);

// 表单数据
const form = ref({
  nameValue: '',
  eventCondition: '',
  equipmentSubType: '',
  moduleNumber: ''
});

// 表单验证规则
const formRules: FormRules = {
  moduleNumber: [
    { pattern: /^[1-9]\d*$/, message: '模块号必须为正整数', trigger: 'blur' }
  ]
};

// 设备类型相关
const equipmentTypeOptions = ref<EquipmentTypeNode[]>([]);
const equipmentTypeValue = ref<number[]>([]);

// 级联选择器配置
const cascaderProps = {
  value: 'value',
  label: 'label',
  children: 'children',
  checkStrictly: false
};

// 基类表格数据
const orgTableDataSource = ref<BaseClassInfo[]>([]);
const displayTableDataSource = ref<BaseClassInfo[]>([]);
const selectedRow = ref<BaseClassInfo>({ baseTypeId: 0, baseTypeName: '', baseNameExt: '', description: '' });
const selectedRows = ref<BaseClassInfo[]>([]);

// 计算属性
const nameLabel = computed(() => {
  switch (selectorType.value) {
    case BaseClassSelectorType.signal:
      return '信号名称';
    case BaseClassSelectorType.event:
      return '事件名称';
    case BaseClassSelectorType.control:
      return '控制名称';
    default:
      return '名称';
  }
});

const bindBaseClassLabel = computed(() => {
  switch (selectorType.value) {
    case BaseClassSelectorType.signal:
      return '关联基类信号';
    case BaseClassSelectorType.event:
      return '关联基类事件';
    case BaseClassSelectorType.control:
      return '关联基类控制';
    default:
      return '关联基类';
  }
});

const tableHeaderId = computed(() => {
  switch (selectorType.value) {
    case BaseClassSelectorType.signal:
      return '基类信号ID';
    case BaseClassSelectorType.event:
      return '基类事件ID';
    case BaseClassSelectorType.control:
      return '基类控制ID';
    default:
      return '基类ID';
  }
});

const tableHeaderName = computed(() => {
  switch (selectorType.value) {
    case BaseClassSelectorType.signal:
      return '基类信号名称';
    case BaseClassSelectorType.event:
      return '基类事件名称';
    case BaseClassSelectorType.control:
      return '基类控制名称';
    default:
      return '基类名称';
  }
});

const assistApiOfEquipmentType = computed(() => {
  switch (selectorType.value) {
    case BaseClassSelectorType.signal:
      return 'signalbasedic';
    case BaseClassSelectorType.event:
      return 'eventbasedic';
    case BaseClassSelectorType.control:
      return 'commandbasedic';
    default:
      return 'signalbasedic';
  }
});

const assistApiOfTable = computed(() => {
  switch (selectorType.value) {
    case BaseClassSelectorType.signal:
      return 'signal';
    case BaseClassSelectorType.event:
      return 'event';
    case BaseClassSelectorType.control:
      return 'control';
    default:
      return 'signal';
  }
});

const hasExistingBaseType = computed(() => {
  return props.signalData?.baseTypeId;
});

// 监听显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal;
  if (newVal) {
    initializeData();
  }
});

// 监听内部状态变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal);
});

// 初始化数据
const initializeData = async () => {
  if (!props.signalData) return;
  
  // 设置表单数据
  form.value.nameValue = props.signalData.signalName || props.signalData.eventName || props.signalData.controlName || '';
  form.value.eventCondition = props.signalData.eventCondition || '';
  form.value.moduleNumber = '';
  
  // 重置选择状态
  selectedRow.value = { baseTypeId: 0, baseTypeName: '', baseNameExt: '', description: '' };
  selectedRows.value = [];
  equipmentTypeValue.value = [];
  
  // 加载设备类型树
  await loadEquipmentTypeTree();
  
  // 设置默认设备类型
  await setDefaultEquipmentType();
};

// 加载设备类型树
const loadEquipmentTypeTree = async () => {
  try {
    const res = await getBaseClassEquipmentTree();
    if (res.code === 0 && res.data) {
      // 处理数据格式，确保正确映射API返回的数据结构
      equipmentTypeOptions.value = res.data.map(item => ({
        value: item.value,
        label: item.label,
        children: item.children?.map(sub => ({
          value: sub.value,
          label: sub.label,
          isLeaf: true
        })) || []
      }));
    }
  } catch (error) {
    console.error('获取设备类型树失败:', error);
  }
};

// 设置默认设备类型
const setDefaultEquipmentType = async () => {
  if (!props.signalData || !props.templateId) return;
  
  try {
    if (props.signalData.baseTypeId) {
      // 如果已有基类ID，根据基类ID获取设备类型
      const res = await getEquipmentTypeByBaseTypeId(assistApiOfEquipmentType.value, props.signalData.baseTypeId);
      if (res.code === 0 && res.data) {
        const baseEquipmentId = res.data.baseEquipmentId;
        equipmentTypeValue.value = [
          Math.floor(baseEquipmentId / 100),
          baseEquipmentId % 100
        ];
        await loadBaseClassList();
      }
    } else {
      // 根据模板信息获取设备类型
      const templateRes = await getTemplateInfoById(props.templateId.toString());
      if (templateRes.code === 0 && templateRes.data?.equipmentBaseType) {
        const typeRes = await getEquipmentTypeByBaseEquipmentId(templateRes.data.equipmentBaseType);
        if (typeRes.code === 0 && typeRes.data?.length > 0) {
          equipmentTypeValue.value = [
            typeRes.data[0].equipmentTypeId,
            typeRes.data[0].equipmentSubTypeId
          ];
          await loadBaseClassList();
        }
      }
    }
  } catch (error) {
    console.error('设置默认设备类型失败:', error);
  }
};

// 设备类型变化处理
const onEquipmentTypeChange = () => {
  // 清空之前的选择
  selectedRow.value = { baseTypeId: 0, baseTypeName: '', baseNameExt: '', description: '' };
  selectedRows.value = [];
  if (tableRef.value) {
    tableRef.value.clearSelection();
  }
  loadBaseClassList();
};

// 加载基类列表
const loadBaseClassList = async () => {
  if (!equipmentTypeValue.value || equipmentTypeValue.value.length !== 2) {
    orgTableDataSource.value = [];
    displayTableDataSource.value = [];
    return;
  }
  
  try {
    const params = {
      eqTypeId: equipmentTypeValue.value[0],
      eqSubTypeId: equipmentTypeValue.value[1]
    };
    
    const res = await getBaseClassList(assistApiOfEquipmentType.value, params);
    if (res.code === 0 && res.data) {
      orgTableDataSource.value = res.data;
      displayTableDataSource.value = [...res.data];
      
      // 如果有现有的基类ID，自动选中对应行
      if (props.signalData?.baseTypeId) {
        const targetRow = displayTableDataSource.value.find(item => 
          item.baseTypeId === props.signalData.baseTypeId
        );
        if (targetRow) {
          selectedRow.value = targetRow;
          selectedRows.value = [targetRow];
          // 设置表格选中状态
          await nextTick();
          if (tableRef.value) {
            tableRef.value.clearSelection();
            tableRef.value.toggleRowSelection(targetRow, true);
          }
        }
      }
    }
  } catch (error) {
    console.error('获取基类列表失败:', error);
  }
};



// 选择变化处理（单选模式）
const onSelectionChange = (selection: BaseClassInfo[]) => {
  if (selection.length > 1) {
    // 如果选择了多行，只保留最后选中的一行
    const lastSelected = selection[selection.length - 1];
    if (tableRef.value) {
      tableRef.value.clearSelection();
      tableRef.value.toggleRowSelection(lastSelected, true);
    }
    selectedRows.value = [lastSelected];
    selectedRow.value = lastSelected;
  } else if (selection.length === 1) {
    selectedRows.value = selection;
    selectedRow.value = selection[0];
  } else {
    selectedRows.value = [];
    selectedRow.value = { baseTypeId: 0, baseTypeName: '', baseNameExt: '', description: '' };
  }
};

// 表单验证
const validateForm = async (): Promise<boolean> => {
  if (!formRef.value) return false;
  
  try {
    await formRef.value.validate();
    return true;
  } catch (error) {
    return false;
  }
};

// 处理取消关联基类
const handleDeleteAssociation = async () => {
  if (!hasExistingBaseType.value) {
    ElMessage.info('当前没有关联的基类数据');
    return;
  }
  
  try {
    await ElMessageBox.confirm(
      '确认要取消关联基类吗？',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    emit('confirm', {
      action: 'delete',
      baseTypeId: null,
      moduleNumber: null
    });
    visible.value = false;
  } catch (error) {
    // 用户取消操作
  }
};

// 处理关闭
const handleClose = () => {
  visible.value = false;
};

// 处理确认
const handleConfirm = async () => {
  // 表单验证
  const isValid = await validateForm();
  if (!isValid) {
    return;
  }
  
  // 检查是否选择了基类
  if (!selectedRow.value.baseTypeId) {
    ElMessage.warning('请选择一条基类数据');
    return;
  }
  
  confirmLoading.value = true;
  
  try {
    const moduleNumber = form.value.moduleNumber ? parseInt(form.value.moduleNumber) : null;
    
    // 如果有模块号，需要更新数据
    if (moduleNumber) {
      const baseTypeId = Math.floor(selectedRow.value.baseTypeId / 1000) * 1000 + moduleNumber;
      const baseTypeName = selectedRow.value.baseNameExt?.replace('{0}', moduleNumber.toString()) || '';
      
      // 更新行数据
      const rowData = { ...props.signalData };
      
      switch (selectorType.value) {
        case BaseClassSelectorType.signal:
          rowData.baseTypeId = baseTypeId;
          rowData.baseTypeName = baseTypeName;
          break;
        case BaseClassSelectorType.event:
          // 事件的处理逻辑
          const conditionIndex = rowData.eventConditionList?.findIndex((item: any) => 
            item.eventConditionId === props.signalData.eventCondition
          );
          if (conditionIndex >= 0) {
            rowData.eventConditionList[conditionIndex].baseTypeId = baseTypeId;
            rowData.eventConditionList[conditionIndex].baseTypeName = baseTypeName;
          }
          break;
        case BaseClassSelectorType.control:
          rowData.baseTypeId = baseTypeId;
          rowData.baseTypeName = baseTypeName;
          break;
      }
      
      // 更新到后端
      const updateRes = await updateTableRow(assistApiOfTable.value, rowData);
      if (updateRes.code === 0) {
        await addBaseType(assistApiOfEquipmentType.value, rowData.equipmentTemplateId);
        
        emit('confirm', {
          action: 'confirm',
          baseTypeId: baseTypeId,
          baseTypeName: baseTypeName,
          moduleNumber: moduleNumber
        });
      } else {
        ElMessage.error('更新失败');
        return;
      }
    } else {
      // 没有模块号，直接返回选中的基类信息
      emit('confirm', {
        action: 'confirm',
        baseTypeId: selectedRow.value.baseTypeId,
        baseTypeName: selectedRow.value.baseTypeName,
        moduleNumber: null
      });
    }
    
    visible.value = false;
  } catch (error) {
    console.error('确认操作失败:', error);
    ElMessage.error('操作失败');
  } finally {
    confirmLoading.value = false;
  }
};

// 组件挂载时初始化
onMounted(() => {
  if (visible.value) {
    initializeData();
  }
});
</script>

<style scoped>
.base-class-selector {
  padding: 16px 0;
}

.base-class-table-container {
  width: 100%;
}

.dialog-footer {
  text-align: right;
}

/* 表格样式优化 */
:deep(.el-table) {
  font-size: 12px;
}

:deep(.el-table .cell) {
  padding: 8px 12px;
}

:deep(.el-table td) {
  padding: 8px 0;
}

/* 表单样式 */
:deep(.el-form-item__label) {
  font-size: 12px;
}

:deep(.el-input--small) {
  font-size: 12px;
}

:deep(.el-cascader) {
  width: 100%;
}

/* 按钮样式 */
:deep(.el-button--small) {
  padding: 5px 8px;
  font-size: 12px;
}
</style> 