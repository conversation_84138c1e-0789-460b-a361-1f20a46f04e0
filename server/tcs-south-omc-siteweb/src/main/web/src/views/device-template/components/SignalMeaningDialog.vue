<template>
  <el-dialog
    v-model="visible"
    title="信号含义设置"
    width="750px"
    :before-close="handleClose"
  >
    <div class="signal-meaning-modal">
      <el-table 
        :data="dataSource" 
        border 
        stripe 
        size="small"
        :max-height="400"
      >
        <el-table-column label="值" width="120">
          <template #default="{ row, $index }">
            <div v-if="!row.indexEdit" class="cell-content" @dblclick="enterEdit(row, 'indexEdit')">
              {{ row.stateValue }}
            </div>
            <div v-else class="edit-padding-fix">
              <div class="error-wrapper">
                <el-icon 
                  v-if="row.error" 
                  class="error-icon"
                  color="#eb2f96"
                  size="16"
                >
                  <Warning />
                </el-icon>
                <el-input 
                  v-model="row.stateValue" 
                  size="small"
                  :disabled="disabled"
                  @blur="validateStateValue(row, $index)"
                  @keyup.enter="exitEdit(row, 'indexEdit')"
                  ref="stateValueInput"
                />
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="含义">
          <template #default="{ row }">
            <div v-if="!row.meaningEdit" class="cell-content" @dblclick="enterEdit(row, 'meaningEdit')">
              {{ row.meanings }}
            </div>
            <div v-else class="edit-padding-fix">
              <el-input 
                v-model="row.meanings" 
                size="small"
                :disabled="disabled"
                @blur="exitEdit(row, 'meaningEdit')"
                @keyup.enter="exitEdit(row, 'meaningEdit')"
                ref="meaningInput"
              />
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="基类状态含义" width="200">
          <template #default="{ row }">
            <el-select 
              v-model="row.baseCondId" 
              placeholder="请选择"
              size="small"
              :disabled="disabled"
            >
              <el-option-group :label="baseStatusName">
                <el-option
                  v-for="item in baseTypeMeaningsArr"
                  :key="item.value"
                  :label="item.meaning"
                  :value="item.value"
                />
              </el-option-group>
            </el-select>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="80" v-if="!disabled">
          <template #default="{ $index }">
            <el-button
              type="danger"
              size="small"
              :icon="Delete"
              @click="deleteClick($index)"
              title="删除"
            />
          </template>
        </el-table-column>
      </el-table>
      
      <div class="table-actions" v-if="!disabled">
        <el-button 
          type="primary" 
          size="small"
          :icon="Plus"
          @click="addClick"
        >
          添加
        </el-button>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleConfirm"
          :disabled="disabled || hasValidationErrors"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from "vue";
import { 
  ElDialog, 
  ElTable, 
  ElTableColumn, 
  ElInput, 
  ElButton, 
  ElSelect, 
  ElOption, 
  ElOptionGroup,
  ElIcon,
  ElMessage 
} from "element-plus";
import { Plus, Delete, Warning } from '@element-plus/icons-vue';
import { 
  getSignalBaseTypeMeanings, 
  getControlBaseTypeMeanings,
  type SignalMeaning,
  type BaseStatusMeaning 
} from "@/api/device-template";

// 扩展的信号含义接口，包含编辑状态
interface ExtendedSignalMeaning extends Partial<SignalMeaning> {
  stateValue: number | string;
  meanings: string;
  baseCondId: number;
  parameterValue?: number | string;
  indexEdit?: boolean;
  meaningEdit?: boolean;
  error?: boolean;
}

// 组件类型枚举
enum MeaningSelectorType {
  signal = 0,
  event = 1,
  control = 2
}

interface Props {
  modelValue: boolean;
  signalData?: any;
  disabled?: boolean;
  type?: MeaningSelectorType;
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'confirm', data: ExtendedSignalMeaning[]): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  signalData: null,
  disabled: false,
  type: MeaningSelectorType.signal
});

const emit = defineEmits<Emits>();

// 响应式数据
const visible = ref(false);
const dataSource = ref<ExtendedSignalMeaning[]>([]);
const baseTypeMeaningsArr = ref<BaseStatusMeaning[]>([]);
const baseStatusName = ref<string>('');

// 默认行数据
const defaultRowData: ExtendedSignalMeaning = {
  stateValue: '',
  meanings: '含义',
  baseCondId: -1,
  equipmentTemplateId: 0,
  signalId: 0
};

// 计算属性：是否有验证错误
const hasValidationErrors = computed(() => {
  return dataSource.value.some(item => item.error);
});

// 监听显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal;
  if (newVal) {
    initializeData();
  }
});

// 监听内部状态变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal);
});

// 初始化数据
const initializeData = () => {
  if (!props.signalData) return;
  
  // 初始化数据源
  if (props.type === MeaningSelectorType.control) {
    dataSource.value = props.signalData.controlMeaningsList?.map((item: any) => ({
      ...item,
      stateValue: item.parameterValue,
      indexEdit: false,
      meaningEdit: false,
      error: false
    })) || [];
  } else {
    dataSource.value = props.signalData.signalMeaningsList?.map((item: any) => ({
      ...item,
      indexEdit: false,
      meaningEdit: false,
      error: false
    })) || [];
  }
  
  // 获取基类状态含义
  loadBaseTypeMeanings();
};

// 加载基类状态含义
const loadBaseTypeMeanings = async () => {
  if (!props.signalData) return;
  
  const templateId = props.signalData.equipmentTemplateId;
  const id = props.type === MeaningSelectorType.control 
    ? props.signalData.controlId 
    : props.signalData.signalId;
  
  // 重置基类含义数组
  baseTypeMeaningsArr.value = [{ value: -1, meaning: '无' }];
  
  try {
    const res = props.type === MeaningSelectorType.control
      ? await getControlBaseTypeMeanings(templateId, id)
      : await getSignalBaseTypeMeanings(templateId, id);
      
    if (res.code === 0 && res.data) {
      baseStatusName.value = res.data.baseStatusName;
      baseTypeMeaningsArr.value = baseTypeMeaningsArr.value.concat(res.data.statusMeanings);
    }
  } catch (error) {
    console.error('获取基类状态含义失败:', error);
  }
};

// 编辑前验证
const beforeEditEnd = (rowItem: ExtendedSignalMeaning, field: string) => {
  const reg = /^[0-9]+?$/;
  if (rowItem && reg.test(rowItem[field as keyof ExtendedSignalMeaning] as string)) {
    rowItem.error = false;
    return true;
  } else {
    rowItem.error = true;
    return false;
  }
};

// 验证状态值
const validateStateValue = (rowItem: ExtendedSignalMeaning, index: number) => {
  const isValid = beforeEditEnd(rowItem, 'stateValue');
  if (isValid) {
    exitEdit(rowItem, 'indexEdit');
  }
};

// 进入编辑模式
const enterEdit = (rowItem: ExtendedSignalMeaning, field: 'indexEdit' | 'meaningEdit') => {
  if (props.disabled) return;
  rowItem[field] = true;
};

// 退出编辑模式
const exitEdit = (rowItem: ExtendedSignalMeaning, field: 'indexEdit' | 'meaningEdit') => {
  rowItem[field] = false;
};

// 添加行
const addClick = () => {
  // 检查是否有验证错误
  if (dataSource.value.find(item => item.error)) {
    ElMessage.warning('请先修正现有错误');
    return;
  }
  
  // 计算新的最大ID
  let maxId = Math.max(...dataSource.value.map(item => Number(item.stateValue) || 0));
  maxId = (maxId === -Infinity) ? 0 : ++maxId;
  
  const newData: ExtendedSignalMeaning = { 
    ...defaultRowData, 
    stateValue: maxId,
    indexEdit: false,
    meaningEdit: false,
    error: false
  };
  
  // 设置关联ID
  if (props.type !== MeaningSelectorType.control && props.signalData) {
    newData.signalId = props.signalData.signalId;
    newData.equipmentTemplateId = props.signalData.equipmentTemplateId;
  }
  
  dataSource.value.push(newData);
};

// 删除行
const deleteClick = (index: number) => {
  dataSource.value.splice(index, 1);
};

// 处理关闭
const handleClose = () => {
  visible.value = false;
};

// 处理确认
const handleConfirm = () => {
  // 检查验证错误
  if (dataSource.value.find(item => item.error)) {
    ElMessage.warning('请先修正现有错误');
    return;
  }
  
  // 处理控制类型的数据转换
  if (props.type === MeaningSelectorType.control) {
    dataSource.value.forEach(item => {
      item.parameterValue = item.stateValue;
    });
  }
  
  emit('confirm', dataSource.value);
  visible.value = false;
};

// 组件挂载时的初始化
onMounted(() => {
  if (visible.value) {
    initializeData();
  }
});
</script>

<style scoped>
.signal-meaning-modal {
  padding: 16px 0;
}

.cell-content {
  padding: 4px 0;
}

.edit-padding-fix {
  margin: -8px;
}

.error-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.error-icon {
  flex-shrink: 0;
}

.table-actions {
  margin-top: 16px;
  text-align: left;
}

.dialog-footer {
  text-align: right;
}

/* 表格样式优化 */
:deep(.el-table) {
  font-size: 12px;
}

:deep(.el-table .cell) {
  padding: 8px 12px;
}

:deep(.el-table td) {
  padding: 8px 0;
}

/* 输入框样式 */
:deep(.el-input--small) {
  font-size: 12px;
}

:deep(.el-input--small .el-input__inner) {
  height: 28px;
  line-height: 28px;
}

/* 选择框样式 */
:deep(.el-select--small) {
  font-size: 12px;
}

/* 按钮样式 */
:deep(.el-button--small) {
  padding: 5px 8px;
  font-size: 12px;
}
</style> 