<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="900px"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="expression-config">
      <!-- 信号选择区域 -->
      <div class="signal-section">
        <div class="signal-search">
          <label class="section-label">信号</label>
          <el-input
            v-model="searchText"
            placeholder="搜索信号..."
            clearable
            size="small"
            :disabled="disabled"
            @input="handleSearch"
          >
            <template #suffix>
              <el-icon class="el-input__icon">
                <Search />
              </el-icon>
            </template>
          </el-input>
        </div>
        <div class="signal-list">
          <div
            v-for="signal in filteredSignals"
            :key="signal.signalId"
            class="signal-item"
            :class="{ disabled: disabled }"
            @dblclick="selectSignal(signal)"
          >
            {{ `[${signal.signalId}, ${signal.signalName}]` }}
          </div>
        </div>
      </div>

      <!-- 表达式编辑区域 -->
      <div class="expression-section">
        <div class="expression-input">
          <label class="section-label">信号表达式</label>
          <div class="expression-textarea-wrapper">
            <el-input
              ref="expressionTextareaRef"
              v-model="expression"
              type="textarea"
              :rows="4"
              :maxlength="1024"
              :disabled="disabled"
              :class="{
                correct: expression && isValid === true,
                error: expression && isValid === false
              }"
              placeholder="请输入表达式"
              @input="handleExpressionChange"
              @click="handleTextareaClick"
              @keyup="handleTextareaKeyup"
              @focus="handleTextareaFocus"
            />
            <el-icon
              v-if="expression && !disabled"
              class="clear-icon"
              @click="clearExpression"
            >
              <Close />
            </el-icon>
          </div>

          <!-- 验证状态提示 -->
          <div class="validation-status">
            <div v-if="!expression.trim()" class="status-item info">
              <el-icon><InfoFilled /></el-icon>
              <span
                >请输入表达式，可通过双击左侧信号或点击运算符按钮来构建</span
              >
            </div>
            <div v-else-if="isValidating" class="status-item validating">
              <el-icon class="loading-icon"><Loading /></el-icon>
              <span>正在验证表达式...</span>
            </div>
            <div v-else-if="isValid === true" class="status-item success">
              <el-icon><CircleCheck /></el-icon>
              <span>表达式格式正确</span>
            </div>
            <div v-else-if="isValid === false" class="status-item error">
              <el-icon><CircleClose /></el-icon>
              <span>表达式格式错误，请检查语法</span>
            </div>
          </div>

          <!-- 字符计数 -->
          <div class="char-count">
            <span
              :class="{
                'count-warning': expression.length > 900,
                'count-error': expression.length >= 1024
              }"
            >
              {{ expression.length }}/1024
            </span>
          </div>
        </div>

        <!-- 表达式描述 -->
        <div class="expression-description">
          <label class="section-label">信号描述</label>
          <el-input
            v-model="description"
            type="textarea"
            :rows="4"
            readonly
            :class="{
              correct: expression && isValid === true,
              error: expression && isValid === false
            }"
            placeholder="表达式描述将自动生成"
          />
        </div>

        <!-- 运算符按钮组 -->
        <div class="operators-section">
          <label class="section-label">运算符</label>
          <div class="operator-buttons">
            <el-button
              v-for="operator in operators"
              :key="operator.name"
              size="small"
              :disabled="disabled"
              :title="operator.description"
              @click="insertOperator(operator.name)"
            >
              {{ operator.name }}
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :disabled="disabled || (expression && !isValid)"
          @click="handleConfirm"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed, nextTick } from "vue";
import { ElDialog, ElInput, ElButton, ElIcon, ElMessage } from "element-plus";
import {
  Search,
  Close,
  Loading,
  CircleCheck,
  CircleClose,
  InfoFilled
} from "@element-plus/icons-vue";
import {
  getSignalList,
  validateExpression,
  getExpressionDescription,
  type SignalInfo
} from "@/api/device-template";

interface Props {
  modelValue: boolean;
  signalData?: any;
  templateId?: string | number;
  disabled?: boolean;
  expressionField?: string; // 指定当前编辑的表达式字段
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
  (e: "confirm", expression: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  signalData: null,
  templateId: "",
  disabled: false,
  expressionField: "expression"
});

const emit = defineEmits<Emits>();

// 响应式数据
const visible = ref(false);
const expression = ref("");
const description = ref("");
const searchText = ref("");
const isValid = ref<boolean | null>(null);
const isValidating = ref(false);
const cursorPosition = ref(0);
const signals = ref<SignalInfo[]>([]);
const selectedSignals = ref<
  Array<{ expressionName: string; descriptionName: string }>
>([]);

// DOM引用
const expressionTextareaRef = ref();

// 防抖定时器
const validateTimer = ref<NodeJS.Timeout>();

// 运算符配置
const operators = [
  { name: "+", description: "加" },
  { name: "-", description: "减" },
  { name: "*", description: "乘" },
  { name: "/", description: "除" },
  { name: ">", description: "大于" },
  { name: "==", description: "等于" },
  { name: ">=", description: "大于等于" },
  { name: "<", description: "小于" },
  { name: "<=", description: "小于等于" },
  { name: "AND", description: "逻辑与" },
  { name: "OR", description: "逻辑或" },
  { name: "NOT", description: "逻辑非" },
  { name: "(", description: "左括号" },
  { name: ")", description: "右括号" },
  { name: "max()", description: "最大值" },
  { name: "min()", description: "最小值" },
  { name: "avg()", description: "平均值" },
  { name: ":", description: "延时" },
  { name: "?", description: "设备预警持续时间" },
  { name: "#", description: "分割表达式" },
  { name: "$", description: "后面跟事件条件ID，高频告警屏蔽" },
  { name: "@", description: "后面跟单次告警有效时间，告警屏蔽" },
  { name: "%", description: "后面跟统计周期，配置高频次告警屏蔽" },
  { name: "^", description: "后面跟统计周期数，用于高频次告警屏蔽" },
  { name: ";", description: "后面跟周期内告警频次阀值，用于高频次告警屏蔽" }
];

// 过滤后的信号列表
const filteredSignals = computed(() => {
  if (!searchText.value) return signals.value;
  const searchLower = searchText.value.toLowerCase();
  return signals.value.filter(
    signal =>
      signal.signalId.toString().includes(searchLower) ||
      signal.signalName.toLowerCase().includes(searchLower)
  );
});

// 对话框标题
const dialogTitle = computed(() => {
  switch (props.expressionField) {
    case "startExpression":
      return "开始表达式配置";
    case "suppressExpression":
      return "抑制表达式配置";
    default:
      return "表达式配置";
  }
});

// 监听显示状态
watch(
  () => props.modelValue,
  async newVal => {
    visible.value = newVal;
    if (newVal) {
      await initializeDialog();
    }
  }
);

// 监听内部状态变化
watch(visible, newVal => {
  emit("update:modelValue", newVal);
});

// 初始化对话框
const initializeDialog = async () => {
  try {
    // 重置状态
    searchText.value = "";
    selectedSignals.value = [];
    isValid.value = null;
    isValidating.value = false;

    // 设置初始表达式
    if (props.signalData && props.signalData[props.expressionField]) {
      expression.value = props.signalData[props.expressionField];
      // 设置光标位置到表达式末尾
      cursorPosition.value = expression.value.length;
    } else {
      expression.value = "";
      cursorPosition.value = 0;
    }

    // 加载信号列表
    await loadSignals();

    // 验证初始表达式
    if (expression.value) {
      await validateCurrentExpression();
    }

    // 确保文本框获得焦点并设置光标位置
    nextTick(() => {
      const textarea = expressionTextareaRef.value?.textarea;
      if (textarea) {
        textarea.focus();
        textarea.setSelectionRange(cursorPosition.value, cursorPosition.value);
      }
    });
  } catch (error) {
    console.error("初始化表达式对话框失败:", error);
    ElMessage.error("初始化失败");
  }
};

// 加载信号列表
const loadSignals = async () => {
  if (!props.templateId) return;

  try {
    const res = await getSignalList(props.templateId);
    if (res.code === 0 && res.data) {
      signals.value = res.data;
    }
  } catch (error) {
    console.error("加载信号列表失败:", error);
    ElMessage.error("加载信号列表失败");
  }
};

// 搜索处理
const handleSearch = () => {
  // 搜索逻辑已通过计算属性实现
};

// 选择信号
const selectSignal = (signal: SignalInfo) => {
  if (props.disabled) return;

  const expressionName = `[-1,${signal.signalId}]`;
  const descriptionName = `[${signal.signalName}]`;

  // 检查表达式长度
  if (expression.value.length + expressionName.length > 1024) {
    ElMessage.error("表达式长度不能超过1024字符");
    return;
  }

  // 记录选择的信号
  selectedSignals.value.push({
    expressionName,
    descriptionName
  });

  // 在光标位置插入信号
  insertAtCursor(expressionName);

  // 更新描述
  updateDescription();
};

// 在光标位置插入文本
const insertAtCursor = (text: string) => {
  const textarea = expressionTextareaRef.value?.textarea;
  if (textarea) {
    const start = cursorPosition.value;
    const before = expression.value.substring(0, start);
    const after = expression.value.substring(start);

    expression.value = before + text + after;
    const newCursorPosition = start + text.length;
    cursorPosition.value = newCursorPosition;

    // 设置光标位置到插入内容的后面
    nextTick(() => {
      textarea.focus();
      textarea.setSelectionRange(newCursorPosition, newCursorPosition);

      // 再次确保光标位置正确（某些情况下需要延迟）
      setTimeout(() => {
        if (textarea === document.activeElement) {
          textarea.setSelectionRange(newCursorPosition, newCursorPosition);
        }
      }, 10);
    });
  } else {
    // 如果没有textarea引用，直接在末尾追加
    expression.value += text;
    cursorPosition.value = expression.value.length;

    // 确保下次能正确设置光标位置
    nextTick(() => {
      const textarea = expressionTextareaRef.value?.textarea;
      if (textarea) {
        textarea.focus();
        textarea.setSelectionRange(cursorPosition.value, cursorPosition.value);

        // 再次确保光标位置正确
        setTimeout(() => {
          if (textarea === document.activeElement) {
            textarea.setSelectionRange(
              cursorPosition.value,
              cursorPosition.value
            );
          }
        }, 10);
      }
    });
  }

  handleExpressionChange();
};

// 插入运算符
const insertOperator = (operator: string) => {
  if (props.disabled) return;

  // 检查表达式长度
  if (expression.value.length + operator.length > 1024) {
    ElMessage.error("表达式长度不能超过1024字符");
    return;
  }

  insertAtCursor(operator);
};

// 处理文本框点击，获取光标位置
const handleTextareaClick = (event: Event) => {
  updateCursorPosition(event.target as HTMLTextAreaElement);
};

// 处理键盘事件，更新光标位置
const handleTextareaKeyup = (event: Event) => {
  updateCursorPosition(event.target as HTMLTextAreaElement);
};

// 处理焦点事件，更新光标位置
const handleTextareaFocus = (event: Event) => {
  updateCursorPosition(event.target as HTMLTextAreaElement);
};

// 统一的光标位置更新函数
const updateCursorPosition = (textarea: HTMLTextAreaElement | null) => {
  if (textarea) {
    const newPosition = textarea.selectionStart || 0;
    cursorPosition.value = newPosition;
    // console.log('光标位置更新:', newPosition); // 调试日志，可在需要时启用
  }
};

// 处理表达式变化
const handleExpressionChange = () => {
  // 延迟获取光标位置，确保DOM已更新
  nextTick(() => {
    const textarea = expressionTextareaRef.value?.textarea;
    if (textarea) {
      cursorPosition.value = textarea.selectionStart || 0;
    }
  });

  validateCurrentExpression();
};

// 更新描述
const updateDescription = () => {
  let desc = expression.value;
  selectedSignals.value.forEach(item => {
    const regex = new RegExp(
      item.expressionName.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"),
      "g"
    );
    desc = desc.replace(regex, item.descriptionName);
  });
  description.value = desc;
};

// 验证表达式
const validateCurrentExpression = async () => {
  if (!expression.value.trim()) {
    isValid.value = null;
    description.value = "";
    isValidating.value = false;
    return;
  }

  // 清除之前的定时器
  if (validateTimer.value) {
    clearTimeout(validateTimer.value);
  }

  // 设置验证状态
  isValidating.value = true;

  // 500ms后执行验证
  validateTimer.value = setTimeout(async () => {
    try {
      // 验证表达式
      const validateRes = await validateExpression(expression.value);
      if (validateRes.code === 0) {
        isValid.value = validateRes.data;
      } else {
        isValid.value = false;
      }

      // 获取表达式描述
      if (props.templateId) {
        const descRes = await getExpressionDescription(
          props.templateId,
          expression.value
        );
        if (descRes.code === 0) {
          description.value = descRes.data || "";
        }
      }
    } catch (error) {
      console.error("表达式验证失败:", error);
      isValid.value = false;
    } finally {
      isValidating.value = false;
    }
  }, 500);
};

// 清空表达式
const clearExpression = () => {
  // 清除验证定时器
  if (validateTimer.value) {
    clearTimeout(validateTimer.value);
  }

  expression.value = "";
  description.value = "";
  selectedSignals.value = [];
  isValid.value = null;
  isValidating.value = false;
  cursorPosition.value = 0;

  // 设置焦点并确保光标在开头
  nextTick(() => {
    const textarea = expressionTextareaRef.value?.textarea;
    if (textarea) {
      textarea.focus();
      textarea.setSelectionRange(0, 0);
    }
  });
};

// 处理关闭
const handleClose = () => {
  // 清除验证定时器
  if (validateTimer.value) {
    clearTimeout(validateTimer.value);
  }

  visible.value = false;
};

// 处理确认
const handleConfirm = () => {
  if (expression.value && !isValid.value) {
    ElMessage.warning("请输入有效的表达式");
    return;
  }

  emit("confirm", expression.value);
  visible.value = false;
};
</script>

<style scoped>
.expression-config {
  display: flex;
  gap: 20px;
  height: 500px;
}

/* 信号选择区域 */
.signal-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.signal-search {
  margin-bottom: 10px;
}

.section-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.signal-list {
  flex: 1;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  padding: 8px 0;
  overflow-y: auto;
  background: var(--el-bg-color);
}

.signal-item {
  padding: 6px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 12px;
}

.signal-item:hover {
  background-color: var(--el-color-primary-light-9);
}

.signal-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 表达式编辑区域 */
.expression-section {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.expression-input,
.expression-description {
  display: flex;
  flex-direction: column;
}

.expression-textarea-wrapper {
  position: relative;
}

.clear-icon {
  position: absolute;
  top: 8px;
  right: 8px;
  cursor: pointer;
  color: var(--el-color-info);
  z-index: 10;
}

.clear-icon:hover {
  color: var(--el-color-primary);
}

/* 表达式验证状态样式 */
:deep(.el-textarea__inner.correct) {
  border-color: var(--el-color-success);
}

:deep(.el-textarea__inner.error) {
  border-color: var(--el-color-danger);
}

/* 运算符按钮组 */
.operators-section {
  display: flex;
  flex-direction: column;
}

.operator-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.operator-buttons .el-button {
  min-width: 45px;
  margin: 0;
}

/* 对话框底部 */
.dialog-footer {
  text-align: right;
}

/* 验证状态提示样式 */
.validation-status {
  margin-top: 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.status-item {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.status-item .el-icon {
  margin-right: 8px;
  font-size: 16px;
}

.status-item.validating {
  background: var(--el-color-info-light-9);
  border: 1px solid var(--el-color-info-light-5);
  color: var(--el-color-info);
}

.status-item.validating .loading-icon {
  animation: spin 1s linear infinite;
}

.status-item.info {
  background: var(--el-color-info-light-9);
  border: 1px solid var(--el-color-info-light-5);
  color: var(--el-color-info);
}

.status-item.success {
  background: var(--el-color-success-light-9);
  border: 1px solid var(--el-color-success-light-5);
  color: var(--el-color-success);
}

.status-item.error {
  background: var(--el-color-danger-light-9);
  border: 1px solid var(--el-color-danger-light-5);
  color: var(--el-color-danger);
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 字符计数样式 */
.char-count {
  margin-top: 8px;
  text-align: right;
}

.count-warning {
  color: var(--el-color-warning);
}

.count-error {
  color: var(--el-color-danger);
}
</style>
