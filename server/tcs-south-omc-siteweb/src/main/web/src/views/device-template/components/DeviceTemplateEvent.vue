<template>
  <div class="device-template-event">
    <!-- 表格区域 -->
    <div class="event-table-container">
      <el-table-v2
        ref="tableRef"
        v-loading="loading"
        :columns="tableColumns"
        :data="filteredData"
        :width="tableWidth"
        :height="tableHeight"
        :row-height="40"
        :header-height="40"
        :row-class="getRowClass"
        :row-event-handlers="rowEventHandlers"
        fixed
      />
    </div>

    <!-- 表达式配置对话框 -->
    <ExpressionConfigDialog
      v-model="showExpressionDialog"
      :signal-data="currentEditingEvent"
      :template-id="templateData?.id"
      :disabled="isRootTemplate"
      :expression-field="currentExpressionField"
      @confirm="handleExpressionConfirm"
    />

    <!-- 模板修改确认对话框 -->
    <DeviceTemplateConfirmDialog
      v-model="showConfirmDialog"
      :template-id="templateData?.id"
      :template-data="templateData"
      @confirm="handleConfirmDialogResult"
      @cancel="handleConfirmCancel"
    />

    <!-- 从模板选择事件对话框 -->
    <DeviceTemplateSecSelector
      v-model="showTemplateSelector"
      :type="1"
      :equipment-category="
        templateData?.template?.equipmentCategory ||
        templateData?.equipmentCategory ||
        0
      "
      :origin-template-id="templateData?.template?.id || templateData?.id || 0"
      :origin-signal-list="tableData"
      :protocol-code="
        templateData?.template?.protocolCode || templateData?.protocolCode || ''
      "
      @confirm="handleTemplateSelectorConfirm"
      @cancel="handleTemplateSelectorCancel"
    />

    <!-- 事件条件编辑对话框 -->
    <EventConditionEditor
      v-model="showConditionEditor"
      :event-data="currentEditingEvent"
      :template-data="templateData"
      :disabled="isRootTemplate"
      :is-battery="isBatteryDevice"
      @confirm="handleConditionEditorConfirm"
      @cancel="handleConditionEditorCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, h, withKeys } from "vue";
import {
  ElMessage,
  ElButton,
  ElSelect,
  ElOption,
  ElIcon,
  ElPopover,
  ElInput,
  ElCheckbox,
  type InputInstance,
  type SelectInstance
} from "element-plus";
import { Filter } from "@element-plus/icons-vue";
import type { FunctionalComponent } from "vue";
import ContextMenu from "@imengyu/vue3-context-menu";
import ExpressionConfigDialog from "./ExpressionConfigDialog.vue";
import DeviceTemplateConfirmDialog from "./DeviceTemplateConfirmDialog.vue";
import DeviceTemplateSecSelector from "./DeviceTemplateSecSelector.vue";
import EventConditionEditor from "./EventConditionEditor.vue";
import {
  getTemplateEventById,
  getSignalList,
  getEventCategoryList,
  getStartTypeList,
  getEndTypeList,
  updateTemplateEventRow,
  addEvent,
  deleteEvent,
  batchEventFieldCopy,
  getTemplateInfoById,
  getTemplateTreeByCategory,
  BaseClassSelectorType,
  deviceTemplateService,
  type DataDictionaryItem,
  type TemplateTreeNode,
  type EventInfo as BaseEventInfo
} from "@/api/device-template";

// 扩展事件信息接口，添加编辑状态字段
export interface EventInfo extends BaseEventInfo {
  // 编辑状态字段
  eventName_editing?: boolean;
  displayIndex_editing?: boolean;
  eventCategory_editing?: boolean;
  startType_editing?: boolean;
  endType_editing?: boolean;
  signalId_editing?: boolean;
  description_editing?: boolean;
  enable_editing?: boolean;
  visible_editing?: boolean;
  moduleNo_editing?: boolean;
  turnover_editing?: boolean;
  [key: string]: any; // 允许动态属性
}

interface Props {
  templateData?: any;
  tabIndex?: number;
  muCategory?: number;
  tableSearchText?: string;
  equipmentId?: string | number;
  buttonFlag?: boolean;
  isRootTemplate?: boolean;
}

interface Emits {
  (e: "refresh"): void;
  (e: "selectTab", data: { index: number }): void;
}

const props = withDefaults(defineProps<Props>(), {
  templateData: null,
  tabIndex: 0,
  muCategory: 0,
  tableSearchText: "",
  equipmentId: "",
  buttonFlag: false,
  isRootTemplate: false
});

const emit = defineEmits<Emits>();

// 过滤器状态接口
interface FilterState {
  [key: string]: {
    value: any[];
    options: Array<{ label: string; value: any }>;
  };
}

// 编辑单元格组件类型定义
type EditCellProps = {
  value: any;
  onChange: (value: any) => void;
  onBlur: () => void;
  onKeydownEnter: () => void;
  forwardRef: (el: any) => void;
  options?: Array<{ label: string; value: any }>;
  type?: "input" | "number" | "select" | "checkbox";
  placeholder?: string;
};

// 输入框编辑组件
const InputCell: FunctionalComponent<EditCellProps> = ({
  value,
  onChange,
  onBlur,
  onKeydownEnter,
  forwardRef,
  placeholder = ""
}) => {
  return h(ElInput, {
    ref: forwardRef,
    modelValue: value,
    placeholder,
    size: "small",
    onInput: onChange,
    onBlur,
    onKeydown: withKeys(onKeydownEnter, ["enter"])
  });
};

// 下拉选择编辑组件
const SelectCell: FunctionalComponent<EditCellProps> = ({
  value,
  onChange,
  onBlur,
  onKeydownEnter,
  forwardRef,
  options = [],
  placeholder = "请选择"
}) => {
  return h(
    ElSelect,
    {
      ref: forwardRef,
      modelValue: value,
      placeholder,
      size: "small",
      clearable: true,
      filterable: true, // 支持搜索
      "onUpdate:modelValue": onChange,
      onBlur,
      onKeydown: withKeys(onKeydownEnter, ["enter"])
    },
    {
      default: () =>
        options.map(option =>
          h(ElOption, {
            key: option.value,
            label: option.label,
            value: option.value
          })
        )
    }
  );
};

// 复选框编辑组件
const CheckboxCell: FunctionalComponent<EditCellProps> = ({
  value,
  onChange,
  onBlur,
  onKeydownEnter,
  forwardRef
}) => {
  return h(ElCheckbox, {
    ref: forwardRef,
    modelValue: !!value,
    "onUpdate:modelValue": onChange,
    onBlur,
    onKeydown: withKeys(onKeydownEnter, ["enter"])
  });
};

// 状态变量
const loading = ref(false);
const tableData = ref<EventInfo[]>([]);

// 表格选中相关
const selectedRowIndexes = ref<number[]>([]);
const selectedRows = ref<EventInfo[]>([]);

// 下拉选项数据
const eventCategoryList = ref<DataDictionaryItem[]>([]);
const startTypeList = ref<DataDictionaryItem[]>([]);
const endTypeList = ref<DataDictionaryItem[]>([]);
const signalList = ref<any[]>([]);

// 过滤器状态
const filterState = ref<FilterState>({});

// 存储原始数据用于对比
const originalData = ref<Map<string, any>>(new Map());

// 处理单元格值变化（编辑过程中）- 只保存值，不提交
const handleCellValueChange = (
  rowData: EventInfo,
  fieldKey: string,
  value: any
) => {
  // 直接更新值，不触发提交
  rowData[fieldKey] = value;
};

// 处理编辑完成（失去焦点或按回车）- 这时才提交变更
const handleCellEditComplete = (rowData: EventInfo, fieldKey: string) => {
  // 获取原始值
  const originalKey = `${rowData.eventId}_${fieldKey}`;
  const originalValue = originalData.value.get(originalKey);
  const currentValue = rowData[fieldKey];

  // 检查值是否真的发生了变化
  if (originalValue !== currentValue) {
    // 值确实发生了变化，提交更改
    submitEventChange(rowData, fieldKey);
  }
};

// 开始编辑时保存原始值
const handleCellEditStart = (rowData: EventInfo, fieldKey: string) => {
  const originalKey = `${rowData.eventId}_${fieldKey}`;
  if (!originalData.value.has(originalKey)) {
    originalData.value.set(originalKey, rowData[fieldKey]);
  }
};

// 提交事件变更 - 需要前置声明
const submitEventChange = async (rowData: EventInfo, fieldKey: string = "") => {
  // 防止重复确认
  if (isConfirming.value) return;

  // 检查是否需要显示确认对话框
  if (!deviceTemplateService.getNotShowState()) {
    // 需要显示确认对话框
    pendingEventData.value = { ...rowData };
    pendingFieldKey.value = fieldKey;
    showConfirmDialog.value = true;
    return;
  }

  // 直接更新
  await performEventUpdate(rowData);
};

// 执行实际的事件更新
const performEventUpdate = async (
  rowData: EventInfo,
  newTemplateId?: number
) => {
  try {
    loading.value = true;

    // 处理特殊字段
    const submitData = { ...rowData };

    // 如果是新模板，更新模板ID
    if (newTemplateId) {
      submitData.equipmentTemplateId = newTemplateId;
      // 处理事件条件列表的模板ID
      if (submitData.eventConditionList) {
        submitData.eventConditionList.forEach((condition: any) => {
          condition.equipmentTemplateId = newTemplateId;
        });
      }
    }

    const res = await updateTemplateEventRow(submitData);
    if (res.code === 0) {
      ElMessage.success("更新成功！");

      // 不管是否为新模板，都重新加载事件列表以保证数据一致性
      await loadEventList();

      // 只有在新模板的情况下才通知父组件刷新（用于更新模板树）
      if (newTemplateId) {
        emit("refresh");
      }
    } else {
      ElMessage.error(res.msg || "更新失败");
      loadEventList(); // 失败时重新加载数据
    }
  } catch (error) {
    console.error("更新事件失败:", error);
    ElMessage.error("更新失败");
    loadEventList(); // 失败时重新加载数据
  } finally {
    loading.value = false;
  }
};

// 创建可编辑单元格渲染器
const createEditableCell = (column: any, cellType: string = "input") => {
  return ({ rowData }: any) => {
    const fieldKey = column.dataKey;
    const editingKey = `${fieldKey}_editing`;

    // 获取选项数据（需要在前面定义，以便只读状态也能使用）
    const getOptions = () => {
      switch (fieldKey) {
        case "eventCategory":
          return eventCategoryList.value.map(item => ({
            label: item.itemValue,
            value: item.itemId
          }));
        case "startType":
          return startTypeList.value.map(item => ({
            label: item.itemValue,
            value: item.itemId
          }));
        case "endType":
          return endTypeList.value.map(item => ({
            label: item.itemValue,
            value: item.itemId
          }));
        case "signalId":
          return signalList.value.map(item => ({
            label: item.name,
            value: item.id
          }));
        case "enable":
        case "visible":
          return [
            { label: "是", value: true },
            { label: "否", value: false }
          ];
        default:
          return [];
      }
    };

    // 获取显示值（用于只读和编辑状态）
    const getDisplayValue = () => {
      const value = rowData[fieldKey];
      if (cellType === "select") {
        const options = getOptions();
        const option = options.find(opt => opt.value === value);
        return option ? option.label : value || "";
      } else if (cellType === "checkbox") {
        return value ? "是" : "否";
      }
      return value || "";
    };

    // 检查是否为只读模板
    if (props.isRootTemplate) {
      return h("div", { class: "read-only-cell" }, getDisplayValue());
    }

    const onChange = (value: any) => {
      handleCellValueChange(rowData, fieldKey, value);
    };

    const onEnterEditMode = () => {
      // 开始编辑时保存原始值
      handleCellEditStart(rowData, fieldKey);
      rowData[editingKey] = true;
    };

    const onExitEditMode = () => {
      // 退出编辑时检查并提交变更
      handleCellEditComplete(rowData, fieldKey);
      rowData[editingKey] = false;
    };

    const inputRef = ref();
    const setRef = (el: any) => {
      inputRef.value = el;
      if (el) {
        el.focus?.();
      }
    };

    if (rowData[editingKey]) {
      const cellProps = {
        forwardRef: setRef,
        value: rowData[fieldKey],
        onChange,
        onBlur: onExitEditMode,
        onKeydownEnter: onExitEditMode,
        options: getOptions()
      };

      switch (cellType) {
        case "select":
          return h(SelectCell, cellProps);
        case "checkbox":
          return h(CheckboxCell, cellProps);
        default:
          return h(InputCell, cellProps);
      }
    } else {
      const displayValue = getDisplayValue();
      return h(
        "div",
        {
          class: "table-v2-inline-editing-trigger",
          onClick: onEnterEditMode
        },
        displayValue || "\u00A0"
      ); // 使用不间断空格确保有内容可点击
    }
  };
};

// 创建只读单元格渲染器（用于不可编辑的字段）
const createReadOnlyCell = (
  column: any,
  clickHandler?: Function,
  customTitle?: string
) => {
  return ({ rowData }: any) => {
    const fieldKey = column.dataKey;
    const value = rowData[fieldKey];

    if (clickHandler) {
      // 检查是否为只读模板
      if (props.isRootTemplate) {
        return h(
          "div",
          {
            class: "read-only-cell"
          },
          value || ""
        );
      }

      // 默认可点击单元格
      const title = customTitle || "点击配置";
      const placeholder =
        fieldKey === "startExpression"
          ? "点击配置开始表达式"
          : fieldKey === "suppressExpression"
            ? "点击配置抑制表达式"
            : fieldKey === "eventConditionListLabel"
              ? "点击配置条件"
              : "点击配置";

      return h(
        "div",
        {
          class: "read-only-clickable-cell",
          onClick: () => clickHandler(rowData),
          title
        },
        [h("span", { class: "cell-content" }, value || placeholder)]
      );
    }

    return h(
      "div",
      {
        class: "read-only-cell"
      },
      value || ""
    );
  };
};

// 处理表达式点击
const handleExpressionClick = (rowData: EventInfo) => {
  if (props.isRootTemplate) return;

  // 保存原始值
  const originalKey = `${rowData.eventId}_startExpression`;
  if (!originalData.value.has(originalKey)) {
    originalData.value.set(originalKey, rowData.startExpression);
  }

  // 设置当前编辑的字段
  currentExpressionField.value = "startExpression";

  // 打开表达式配置对话框
  showExpressionDialog.value = true;
  currentEditingEvent.value = rowData;
};

// 处理抑制表达式点击
const handleSuppressExpressionClick = (rowData: EventInfo) => {
  if (props.isRootTemplate) return;

  // 保存原始值
  const originalKey = `${rowData.eventId}_suppressExpression`;
  if (!originalData.value.has(originalKey)) {
    originalData.value.set(originalKey, rowData.suppressExpression);
  }

  // 打开表达式配置对话框
  showExpressionDialog.value = true;
  currentEditingEvent.value = rowData;
  // 标记当前编辑的字段
  currentExpressionField.value = "suppressExpression";
};

// 处理条件点击
const handleConditionClick = (rowData: EventInfo) => {
  if (props.isRootTemplate) return;

  // 保存原始值
  const originalKey = `${rowData.eventId}_eventConditionList`;
  if (!originalData.value.has(originalKey)) {
    originalData.value.set(
      originalKey,
      JSON.stringify(rowData.eventConditionList || [])
    );
  }

  // 打开条件编辑对话框
  showConditionEditor.value = true;
  currentEditingEvent.value = rowData;
};

// 当前编辑的表达式字段
const currentExpressionField = ref<string>("startExpression");

// 表达式对话框相关
const showExpressionDialog = ref(false);
const currentEditingEvent = ref<EventInfo | null>(null);

// 模板修改确认对话框相关
const showConfirmDialog = ref(false);
const pendingEventData = ref<EventInfo | null>(null);
const pendingFieldKey = ref<string>("");
const pendingActionType = ref<string>("");
const isConfirming = ref(false);

// 从模板选择事件对话框相关
const showTemplateSelector = ref(false);

// 事件条件编辑对话框相关
const showConditionEditor = ref(false);

// 检查是否为电池设备
const isBatteryDevice = ref(false);

// 处理表达式配置确认
const handleExpressionConfirm = (expression: string) => {
  if (currentEditingEvent.value) {
    const fieldKey = currentExpressionField.value;
    // 保存原始值（如果尚未保存）
    const originalKey = `${currentEditingEvent.value.eventId}_${fieldKey}`;
    if (!originalData.value.has(originalKey)) {
      originalData.value.set(
        originalKey,
        currentEditingEvent.value[fieldKey as keyof EventInfo]
      );
    }

    // 更新值
    (currentEditingEvent.value as any)[fieldKey] = expression;

    // 检查是否有变化
    const originalValue = originalData.value.get(originalKey);
    if (originalValue !== expression) {
      submitEventChange(currentEditingEvent.value, fieldKey);
    }
  }
  showExpressionDialog.value = false;
  currentEditingEvent.value = null;
  currentExpressionField.value = "startExpression";
};

// 处理表达式配置取消
const handleExpressionCancel = () => {
  // 表达式对话框取消时不需要特殊处理，因为我们没有修改原始数据
  showExpressionDialog.value = false;
  currentEditingEvent.value = null;
  currentExpressionField.value = "startExpression";
};

// 处理确认对话框结果
const handleConfirmDialogResult = async (
  action: "update" | "copy",
  data: any
) => {
  isConfirming.value = true;

  try {
    // 保存"不再提醒"状态
    if (data.notShowAgain) {
      deviceTemplateService.setNotShowState(true);
    }

    if (pendingEventData.value && pendingFieldKey.value) {
      // 处理事件更新操作
      const eventId = pendingEventData.value.eventId;
      const fieldKey = pendingFieldKey.value;

      if (action === "update") {
        // 更新原模板
        await performEventUpdate(pendingEventData.value);
      } else if (action === "copy") {
        // 另存为新模板
        if (data.newTemplateId) {
          await performEventUpdate(pendingEventData.value, data.newTemplateId);
          ElMessage.success("已在新模板中更新成功！");
        }
      }

      // 更新成功后，清理对应的原始数据缓存
      const originalKey = `${eventId}_${fieldKey}`;
      originalData.value.delete(originalKey);
    } else if (pendingActionType.value) {
      // 处理右键菜单操作
      if (action === "update") {
        // 在原模板上执行操作
        await executeAction(pendingActionType.value);
      } else if (action === "copy") {
        // 在新模板上执行操作
        if (data.newTemplateId) {
          await executeAction(pendingActionType.value, data.newTemplateId);
          ElMessage.success("已在新模板中操作成功！");
        }
      }
    }
  } catch (error) {
    console.error("处理确认对话框结果失败:", error);
    ElMessage.error("操作失败");
    // 操作失败时也重新加载数据以确保界面一致性
    loadEventList();
  } finally {
    // 重置状态
    isConfirming.value = false;
    pendingEventData.value = null;
    pendingFieldKey.value = "";
    pendingActionType.value = "";
    showConfirmDialog.value = false;
  }
};

// 处理确认取消
const handleConfirmCancel = () => {
  // 恢复修改前的值（仅对事件更新操作有效）
  if (pendingEventData.value && pendingFieldKey.value) {
    const originalKey = `${pendingEventData.value.eventId}_${pendingFieldKey.value}`;
    const originalValue = originalData.value.get(originalKey);
    if (originalValue !== undefined) {
      // 恢复到原始值
      pendingEventData.value[pendingFieldKey.value] = originalValue;

      // 更新对应的表格行数据
      const tableRowIndex = tableData.value.findIndex(
        item => item.eventId === pendingEventData.value!.eventId
      );
      if (tableRowIndex !== -1) {
        tableData.value[tableRowIndex][pendingFieldKey.value] = originalValue;
      }

      // 清理原始数据缓存
      originalData.value.delete(originalKey);
    }
  }

  // 重置状态
  isConfirming.value = false;
  pendingEventData.value = null;
  pendingFieldKey.value = "";
  pendingActionType.value = "";
  showConfirmDialog.value = false;
};

// 初始化过滤器状态
const initFilterState = () => {
  filterState.value = {
    eventName: {
      value: [],
      options: []
    },
    eventId: {
      value: [],
      options: []
    },
    displayIndex: {
      value: [],
      options: []
    },
    eventCategory: {
      value: [],
      options: []
    },
    startType: {
      value: [],
      options: []
    },
    endType: {
      value: [],
      options: []
    },
    signalId: {
      value: [],
      options: []
    },
    startExpression: {
      value: [],
      options: []
    },
    suppressExpression: {
      value: [],
      options: []
    },
    eventConditionListLabel: {
      value: [],
      options: []
    },
    enable: {
      value: [],
      options: []
    },
    visible: {
      value: [],
      options: []
    },
    description: {
      value: [],
      options: []
    },
    moduleNo: {
      value: [],
      options: []
    },
    turnover: {
      value: [],
      options: []
    }
  };
};

// 更新动态选项
const updateDynamicOptions = () => {
  if (tableData.value.length === 0) return;

  // 更新事件名称选项
  const eventNames = [
    ...new Set(tableData.value.map(item => item.eventName).filter(Boolean))
  ];
  filterState.value.eventName.options = eventNames.map(name => ({
    label: name,
    value: name
  }));

  // 更新事件ID选项
  const eventIds = [
    ...new Set(
      tableData.value
        .map(item => item.eventId)
        .filter(id => id !== null && id !== undefined)
    )
  ];
  filterState.value.eventId.options = eventIds.map(id => ({
    label: id.toString(),
    value: id
  }));

  // 更新显示顺序选项
  const displayIndexes = [
    ...new Set(
      tableData.value
        .map(item => item.displayIndex)
        .filter(index => index !== null && index !== undefined)
    )
  ];
  filterState.value.displayIndex.options = displayIndexes.map(index => ({
    label: index.toString(),
    value: index
  }));

  // 更新事件种类选项
  const eventCategories = [
    ...new Set(tableData.value.map(item => item.eventCategory).filter(Boolean))
  ];
  filterState.value.eventCategory.options = eventCategories.map(category => ({
    label: category.toString(),
    value: category
  }));

  // 更新开始类型选项
  const startTypes = [
    ...new Set(tableData.value.map(item => item.startType).filter(Boolean))
  ];
  filterState.value.startType.options = startTypes.map(type => ({
    label: type.toString(),
    value: type
  }));

  // 更新结束类型选项
  const endTypes = [
    ...new Set(tableData.value.map(item => item.endType).filter(Boolean))
  ];
  filterState.value.endType.options = endTypes.map(type => ({
    label: type.toString(),
    value: type
  }));

  // 更新信号ID选项
  const signalIds = [
    ...new Set(
      tableData.value
        .map(item => item.signalId)
        .filter(id => id !== null && id !== undefined)
    )
  ];
  filterState.value.signalId.options = signalIds.map(id => ({
    label: id.toString(),
    value: id
  }));

  // 更新开始表达式选项
  const startExpressions = [
    ...new Set(
      tableData.value.map(item => item.startExpression).filter(Boolean)
    )
  ];
  filterState.value.startExpression.options = startExpressions.map(expr => ({
    label: expr,
    value: expr
  }));

  // 更新抑制表达式选项
  const suppressExpressions = [
    ...new Set(
      tableData.value.map(item => item.suppressExpression).filter(Boolean)
    )
  ];
  filterState.value.suppressExpression.options = suppressExpressions.map(
    expr => ({ label: expr, value: expr })
  );

  // 更新事件条件列表标签选项
  const eventConditionListLabels = [
    ...new Set(
      tableData.value.map(item => item.eventConditionListLabel).filter(Boolean)
    )
  ];
  filterState.value.eventConditionListLabel.options =
    eventConditionListLabels.map(label => ({ label: label, value: label }));

  // 更新说明选项
  const descriptions = [
    ...new Set(tableData.value.map(item => item.description).filter(Boolean))
  ];
  filterState.value.description.options = descriptions.map(desc => ({
    label: desc,
    value: desc
  }));

  // 从字典更新选项
  if (eventCategoryList.value.length > 0) {
    filterState.value.eventCategory.options = eventCategoryList.value.map(
      item => ({
        label: item.itemValue,
        value: item.itemId
      })
    );
  }

  if (startTypeList.value.length > 0) {
    filterState.value.startType.options = startTypeList.value.map(item => ({
      label: item.itemValue,
      value: item.itemId
    }));
  }

  if (endTypeList.value.length > 0) {
    filterState.value.endType.options = endTypeList.value.map(item => ({
      label: item.itemValue,
      value: item.itemId
    }));
  }

  if (signalList.value.length > 0) {
    filterState.value.signalId.options = signalList.value.map(item => ({
      label: item.name,
      value: item.id
    }));
  }

  // 设置布尔值字段的过滤选项
  filterState.value.enable.options = [
    { label: "是", value: true },
    { label: "否", value: false }
  ];

  filterState.value.visible.options = [
    { label: "是", value: true },
    { label: "否", value: false }
  ];
};

// 创建过滤器头部组件
const createFilterHeader = (column: any) => {
  const filterKey = column.dataKey;

  return (props: any) => {
    const popoverRef = ref();

    const onFilter = () => {
      popoverRef.value?.hide();
    };

    const onReset = () => {
      if (filterState.value[filterKey]) {
        filterState.value[filterKey].value = [];
      }
    };

    return h("div", { class: "flex items-center justify-center" }, [
      h("span", { class: "mr-2 text-xs" }, props.column.title),
      h(
        ElPopover,
        {
          ref: popoverRef,
          trigger: "click",
          width: 250
        },
        {
          default: () =>
            h("div", { class: "filter-wrapper" }, [
              h("div", { class: "filter-group" }, [
                h(
                  ElSelect,
                  {
                    modelValue: filterState.value[filterKey]?.value || [],
                    "onUpdate:modelValue": (value: any[]) => {
                      if (filterState.value[filterKey]) {
                        filterState.value[filterKey].value = value;
                      }
                    },
                    placeholder: "选择过滤条件",
                    size: "small",
                    multiple: true,
                    collapseTags: true,
                    filterable: true,
                    clearable: true,
                    style: { width: "100%" }
                  },
                  {
                    default: () =>
                      (filterState.value[filterKey]?.options || []).map(
                        (option: any) =>
                          h(ElOption, {
                            key: option.value,
                            label: option.label,
                            value: option.value
                          })
                      )
                  }
                )
              ]),
              h("div", { class: "el-table-v2__demo-filter" }, [
                h(ElButton, { text: true, onClick: onFilter }, () => "确认"),
                h(ElButton, { text: true, onClick: onReset }, () => "重置")
              ])
            ]),
          reference: () =>
            h(ElIcon, { class: "cursor-pointer" }, () => [h(Filter)])
        }
      )
    ]);
  };
};

// 过滤后的数据
const filteredData = computed(() => {
  let data = tableData.value;

  // 首先应用搜索文本过滤
  if (props.tableSearchText && props.tableSearchText.trim() !== "") {
    const searchText = props.tableSearchText.toLowerCase();
    data = data.filter(
      item =>
        item.eventName?.toLowerCase().includes(searchText) ||
        item.eventId?.toString().includes(searchText) ||
        item.description?.toLowerCase().includes(searchText)
    );
  }

  // 应用列过滤器
  Object.entries(filterState.value).forEach(([key, filter]) => {
    if (filter.value && filter.value.length > 0) {
      data = data.filter(item => {
        const itemValue = item[key as keyof EventInfo];
        return filter.value.includes(itemValue);
      });
    }
  });

  return data;
});

// 虚拟表格配置
const tableWidth = ref(1200);
const tableHeight = ref(500);

// 获取容器尺寸并更新表格尺寸
const updateTableSize = () => {
  // 查找 el-tabs__content 容器
  const tabsContent = document.querySelector(
    ".el-tabs__content"
  ) as HTMLElement;
  if (tabsContent) {
    const rect = tabsContent.getBoundingClientRect();
    tableWidth.value = rect.width - 32; // 减去内边距
    tableHeight.value = rect.height - 32; // 减去内边距
  }
};

// 监听窗口尺寸变化
let resizeObserver: ResizeObserver | null = null;

// 表格列配置
const tableColumns: any[] = [
  {
    key: "eventName",
    title: "名称",
    dataKey: "eventName",
    width: 150,
    cellRenderer: createEditableCell({ dataKey: "eventName" }, "input"),
    headerCellRenderer: createFilterHeader({ dataKey: "eventName" })
  },
  {
    key: "eventId",
    title: "事件ID",
    dataKey: "eventId",
    width: 100,
    cellRenderer: createReadOnlyCell({ dataKey: "eventId" }),
    headerCellRenderer: createFilterHeader({ dataKey: "eventId" })
  },
  {
    key: "displayIndex",
    title: "显示顺序",
    dataKey: "displayIndex",
    width: 100,
    cellRenderer: createEditableCell({ dataKey: "displayIndex" }, "input"),
    headerCellRenderer: createFilterHeader({ dataKey: "displayIndex" })
  },
  {
    key: "eventCategory",
    title: "种类",
    dataKey: "eventCategory",
    width: 120,
    cellRenderer: createEditableCell({ dataKey: "eventCategory" }, "select"),
    headerCellRenderer: createFilterHeader({ dataKey: "eventCategory" })
  },
  {
    key: "startType",
    title: "开始类型",
    dataKey: "startType",
    width: 120,
    cellRenderer: createEditableCell({ dataKey: "startType" }, "select"),
    headerCellRenderer: createFilterHeader({ dataKey: "startType" })
  },
  {
    key: "endType",
    title: "结束类型",
    dataKey: "endType",
    width: 120,
    cellRenderer: createEditableCell({ dataKey: "endType" }, "select"),
    headerCellRenderer: createFilterHeader({ dataKey: "endType" })
  },
  {
    key: "eventConditionListLabel",
    title: "条件",
    dataKey: "eventConditionListLabel",
    width: 120,
    cellRenderer: createReadOnlyCell(
      { dataKey: "eventConditionListLabel" },
      (rowData: EventInfo) => {
        handleConditionClick(rowData);
      },
      "点击配置条件"
    ),
    headerCellRenderer: createFilterHeader({
      dataKey: "eventConditionListLabel"
    })
  },
  {
    key: "startExpression",
    title: "开始表达式",
    dataKey: "startExpression",
    width: 150,
    cellRenderer: createReadOnlyCell(
      { dataKey: "startExpression" },
      (rowData: EventInfo) => {
        handleExpressionClick(rowData);
      },
      "点击配置开始表达式"
    ),
    headerCellRenderer: createFilterHeader({ dataKey: "startExpression" })
  },
  {
    key: "suppressExpression",
    title: "抑制表达式",
    dataKey: "suppressExpression",
    width: 150,
    cellRenderer: createReadOnlyCell(
      { dataKey: "suppressExpression" },
      (rowData: EventInfo) => {
        handleSuppressExpressionClick(rowData);
      },
      "点击配置抑制表达式"
    ),
    headerCellRenderer: createFilterHeader({ dataKey: "suppressExpression" })
  },
  {
    key: "signalId",
    title: "关联信号",
    dataKey: "signalId",
    width: 150,
    cellRenderer: createEditableCell({ dataKey: "signalId" }, "select"),
    headerCellRenderer: createFilterHeader({ dataKey: "signalId" })
  },
  {
    key: "description",
    title: "说明",
    dataKey: "description",
    width: 150,
    cellRenderer: createEditableCell({ dataKey: "description" }, "input"),
    headerCellRenderer: createFilterHeader({ dataKey: "description" })
  },
  {
    key: "enable",
    title: "有效",
    dataKey: "enable",
    width: 80,
    align: "center",
    cellRenderer: createEditableCell({ dataKey: "enable" }, "select"),
    headerCellRenderer: createFilterHeader({ dataKey: "enable" })
  },
  {
    key: "visible",
    title: "可见",
    dataKey: "visible",
    width: 80,
    align: "center",
    cellRenderer: createEditableCell({ dataKey: "visible" }, "select"),
    headerCellRenderer: createFilterHeader({ dataKey: "visible" })
  },
  {
    key: "moduleNo",
    title: "所属模块",
    dataKey: "moduleNo",
    width: 100,
    cellRenderer: createEditableCell({ dataKey: "moduleNo" }, "input"),
    headerCellRenderer: createFilterHeader({ dataKey: "moduleNo" })
  },
  {
    key: "turnover",
    title: "翻转时间",
    dataKey: "turnover",
    width: 100,
    cellRenderer: createEditableCell({ dataKey: "turnover" }, "input"),
    headerCellRenderer: createFilterHeader({ dataKey: "turnover" })
  }
];

// 组件初始化
onMounted(() => {
  // 初始化过滤器状态
  initFilterState();

  initializeDictionaryData();

  // 检查是否为电池设备
  checkIsBatteryDevice();

  // 初始化表格尺寸
  updateTableSize();

  // 设置 ResizeObserver 监听容器尺寸变化
  const tabsContent = document.querySelector(
    ".el-tabs__content"
  ) as HTMLElement;
  if (tabsContent && window.ResizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      updateTableSize();
    });
    resizeObserver.observe(tabsContent);
  }

  // 监听窗口尺寸变化作为备选方案
  window.addEventListener("resize", updateTableSize);
});

// 监听模板数据变化
watch(
  () => props.templateData,
  newData => {
    if (newData && newData.template) {
      if (props.tabIndex === 2) {
        // 事件标签页的索引是2
        loadEventList();
      }
    }
  },
  { immediate: true }
);

// 监听标签页变化
watch(
  () => props.tabIndex,
  newIndex => {
    if (newIndex === 2 && props.templateData) {
      // 事件标签页的索引是2
      loadEventList();
    }
  }
);

// 加载事件列表
const loadEventList = async () => {
  if (!props.templateData?.id) return;

  try {
    loading.value = true;
    const res = await getTemplateEventById(props.templateData.id);
    if (res.code === 0 && res.data) {
      processEventData(res.data);
    } else {
      ElMessage.error(res.msg || "获取事件列表失败");
      tableData.value = [];
    }
  } catch (error) {
    console.error("获取事件列表失败:", error);
    ElMessage.error("获取事件列表失败");
    tableData.value = [];
  } finally {
    loading.value = false;
  }
};

// 初始化字典数据
const initializeDictionaryData = async () => {
  try {
    await Promise.all([
      loadEventCategoryList(),
      loadStartTypeList(),
      loadEndTypeList(),
      loadSignalList()
    ]);
  } catch (error) {
    console.error("初始化字典数据失败:", error);
  }
};

// 加载事件种类列表
const loadEventCategoryList = async () => {
  try {
    const res = await getEventCategoryList();
    if (res.code === 0) {
      eventCategoryList.value = res.data;
    }
  } catch (error) {
    console.error("获取事件种类列表失败:", error);
  }
};

// 加载开始类型列表
const loadStartTypeList = async () => {
  try {
    const res = await getStartTypeList();
    if (res.code === 0) {
      startTypeList.value = res.data;
    }
  } catch (error) {
    console.error("获取开始类型列表失败:", error);
  }
};

// 加载结束类型列表
const loadEndTypeList = async () => {
  try {
    const res = await getEndTypeList();
    if (res.code === 0) {
      endTypeList.value = res.data;
    }
  } catch (error) {
    console.error("获取结束类型列表失败:", error);
  }
};

// 检查是否为电池设备
const checkIsBatteryDevice = () => {
  // 根据设备类别判断是否为电池设备
  // 这里需要根据实际的电池设备类别来判断
  // 参考Angular代码中的batteryDeviceList逻辑
  if (
    props.templateData?.template?.equipmentCategory ||
    props.templateData?.equipmentCategory
  ) {
    const category =
      props.templateData?.template?.equipmentCategory ||
      props.templateData?.equipmentCategory;
    // TODO: 这里需要获取电池设备类别列表来判断
    // 暂时设置为false，后续可以通过API获取电池设备类别列表
    isBatteryDevice.value = false;
  }
};

// 加载信号列表
const loadSignalList = async () => {
  if (!props.templateData?.id) return;

  try {
    const res = await getSignalList(props.templateData.id);
    if (res.code === 0 && res.data) {
      signalList.value = res.data.map((signal: any) => ({
        id: signal.signalId,
        name: signal.signalName
      }));
    }
  } catch (error) {
    console.error("获取信号列表失败:", error);
  }
};

// 处理事件数据
const processEventData = (data: EventInfo[]) => {
  // 清理之前的原始数据缓存
  originalData.value.clear();

  data.forEach(item => {
    // 处理事件条件列表标签 - 与Angular版本保持一致
    if (item.eventConditionList && item.eventConditionList.length > 0) {
      let text = "";
      item.eventConditionList.forEach((obj: any) => {
        if (text.length === 0) {
          text = obj["eventConditionId"].toString();
        } else {
          text = text + "/" + obj["eventConditionId"].toString();
        }
      });
      item.eventConditionListLabel = text;
    } else {
      item.eventConditionListLabel = "";
    }

    // 初始化编辑状态
    item.eventName_editing = false;
    item.displayIndex_editing = false;
    item.eventCategory_editing = false;
    item.startType_editing = false;
    item.endType_editing = false;
    item.signalId_editing = false;
    item.description_editing = false;
    item.enable_editing = false;
    item.visible_editing = false;
    item.moduleNo_editing = false;
    item.turnover_editing = false;
  });

  tableData.value = data;

  // 数据加载后更新过滤器选项
  updateDynamicOptions();
};

// 行事件处理器
const rowEventHandlers = {
  onClick: ({ rowData, rowIndex, event }: any) => {
    // 单击时选中行
    const clickedRowIndex = filteredData.value.findIndex(
      item => item.eventId === rowData.eventId
    );
    if (clickedRowIndex !== -1) {
      // 如果按住Ctrl键，支持多选
      if (event.ctrlKey || event.metaKey) {
        const isSelected = selectedRowIndexes.value.includes(clickedRowIndex);
        if (isSelected) {
          // 取消选中
          selectedRowIndexes.value = selectedRowIndexes.value.filter(
            index => index !== clickedRowIndex
          );
        } else {
          // 添加选中
          selectedRowIndexes.value.push(clickedRowIndex);
        }
      } else {
        // 单选
        selectedRowIndexes.value = [clickedRowIndex];
      }

      // 更新选中的行数据
      selectedRows.value = selectedRowIndexes.value.map(
        index => filteredData.value[index]
      );
    }
  },
  onContextmenu: ({ rowData, rowIndex, event }: any) => {
    event.preventDefault();

    // 如果右键的是某一行，确保该行被选中
    const currentIndex = filteredData.value.findIndex(
      item => item.eventId === rowData.eventId
    );
    if (
      currentIndex !== -1 &&
      !selectedRowIndexes.value.includes(currentIndex)
    ) {
      selectedRowIndexes.value = [currentIndex];
      selectedRows.value = [rowData];
    }

    // 只有在非只读模板时才显示右键菜单
    if (props.isRootTemplate) return;

    showContextMenu(event);
  }
};

// 处理行选中
const handleRowSelect = (selectedIndexes: number[]) => {
  selectedRowIndexes.value = selectedIndexes;
  selectedRows.value = selectedIndexes.map(index => filteredData.value[index]);
};

// 显示右键菜单
const showContextMenu = (event: MouseEvent) => {
  const hasSelectedRows = selectedRowIndexes.value.length > 0;
  const hasOneSelectedRow = selectedRowIndexes.value.length === 1;

  ContextMenu.showContextMenu({
    x: event.x,
    y: event.y,
    items: [
      {
        label: "增加事件",
        icon: "h:plus",
        onClick: () => {
          handleRowChangeConfirm("add");
        }
      },
      {
        label: "从模板增加事件",
        icon: "h:document-add",
        onClick: () => {
          handleRowChangeConfirm("addTemp");
        }
      },
      {
        label: "删除事件",
        icon: "h:delete",
        disabled: !hasSelectedRows,
        onClick: () => {
          handleRowChangeConfirm("delete");
        }
      },
      {
        label: "行拷贝并粘贴",
        icon: "h:document-duplicate",
        disabled: !hasSelectedRows,
        onClick: () => {
          handleRowChangeConfirm("copy");
        }
      }
    ]
  });
};

// 处理行变更确认
const handleRowChangeConfirm = async (type: string) => {
  if (!props.templateData?.id) {
    ElMessage.error("模板信息不存在");
    return;
  }

  // 从模板增加事件直接打开选择对话框
  if (type === "addTemp") {
    showTemplateSelector.value = true;
    return;
  }

  // 检查是否需要显示确认对话框
  if (!deviceTemplateService.getNotShowState()) {
    // 需要显示确认对话框
    pendingActionType.value = type;
    showConfirmDialog.value = true;
    return;
  }

  // 直接执行操作
  await executeAction(type);
};

// 执行具体操作
const executeAction = async (type: string, newTemplateId?: number) => {
  try {
    loading.value = true;
    const templateId = newTemplateId || props.templateData.id;

    switch (type) {
      case "add":
        await handleAddNewEvent(templateId);
        break;
      case "delete":
        await handleDeleteEvent(templateId);
        break;
      case "copy":
        await handleCopyEvent(templateId);
        break;
      default:
        ElMessage.warning(`未知操作类型: ${type}`);
        return;
    }

    ElMessage.success("操作成功！");
    // 刷新数据
    await loadEventList();

    // 如果是新模板，通知父组件刷新
    if (newTemplateId) {
      emit("refresh");
    }
  } catch (error) {
    console.error(`操作失败:`, error);
    ElMessage.error("操作失败");
  } finally {
    loading.value = false;
  }
};

// 增加新事件
const handleAddNewEvent = async (templateId: number) => {
  // 生成新的事件ID和显示顺序
  const maxDisplayIndex =
    tableData.value.length > 0
      ? Math.max(...tableData.value.map(item => item.displayIndex), 0)
      : 0;

  const newEvent = {
    id: null,
    equipmentTemplateId: templateId,
    eventId: null,
    enable: true,
    visible: true,
    eventName: `新增事件${Date.now()}`,
    eventCategory: 1,
    startType: 1,
    endType: 1,
    displayIndex: maxDisplayIndex + 1,
    moduleNo: 0,
    turnover: 0,
    startExpression: "",
    suppressExpression: "",
    eventConditionList: [],
    eventConditionListLabel: "",
    description: "",
    signalId: null,
    hasInstance: false
  };

  const res = await addEvent(newEvent);
  if (res.code !== 0) {
    throw new Error(res.msg || "添加事件失败");
  }
};

// 删除事件
const handleDeleteEvent = async (templateId: number) => {
  if (selectedRowIndexes.value.length === 0) {
    ElMessage.warning("请先选择要删除的事件！");
    return;
  }

  const eventIds = selectedRowIndexes.value
    .map(index => filteredData.value[index].eventId)
    .join(",");

  const res = await deleteEvent(templateId, eventIds);
  if (res.code !== 0) {
    throw new Error(res.msg || "删除事件失败");
  }

  // 清空选中状态
  selectedRowIndexes.value = [];
  selectedRows.value = [];
};

// 复制事件
const handleCopyEvent = async (templateId: number) => {
  if (selectedRowIndexes.value.length === 0) {
    ElMessage.warning("请先选择要复制的事件！");
    return;
  }

  const selectedEvents = selectedRowIndexes.value.map(
    index => filteredData.value[index]
  );
  const maxDisplayIndex =
    tableData.value.length > 0
      ? Math.max(...tableData.value.map(item => item.displayIndex), 0)
      : 0;

  for (let i = 0; i < selectedEvents.length; i++) {
    const originalEvent = selectedEvents[i];

    // 生成复制名称（按Angular版本的逻辑）
    let copyIndex = 1;
    let copyName = `${originalEvent.eventName}${copyIndex}#`;
    while (tableData.value.some(item => item.eventName === copyName)) {
      copyIndex++;
      copyName = `${originalEvent.eventName}${copyIndex}#`;
    }

    const copiedEvent = {
      ...originalEvent,
      id: null,
      eventId: null, // 后端会自动生成新的eventId
      equipmentTemplateId: templateId,
      eventName: copyName,
      displayIndex: maxDisplayIndex + i + 1,
      // 清除一些不应该复制的字段
      eventConditionList: [],
      eventConditionListLabel: "",
      hasInstance: false
    };

    // 移除编辑状态字段
    Object.keys(copiedEvent).forEach(key => {
      if (key.endsWith("_editing")) {
        delete (copiedEvent as any)[key];
      }
    });

    const res = await addEvent(copiedEvent);
    if (res.code !== 0) {
      throw new Error(res.msg || `复制第${i + 1}个事件失败`);
    }
  }
};

// 处理从模板选择事件确认
const handleTemplateSelectorConfirm = async (data: {
  templateId: number;
  selectedSignal: any;
}) => {
  if (!props.templateData?.id) {
    ElMessage.error("模板信息不存在");
    return;
  }

  try {
    loading.value = true;

    // 准备新事件数据
    const newEvent = {
      ...data.selectedSignal,
      id: null,
      eventId: null, // 后端会自动生成新的eventId
      equipmentTemplateId: props.templateData.id,
      // 清除一些不应该复制的字段
      eventConditionList: undefined,
      eventConditionListLabel: undefined,
      acrossSignal: false,
      hasInstance: false
    };

    // 移除编辑状态字段
    Object.keys(newEvent).forEach(key => {
      if (key.endsWith("_editing")) {
        delete (newEvent as any)[key];
      }
    });

    const res = await addEvent(newEvent);
    if (res.code === 0) {
      ElMessage.success("从模板添加事件成功！");
      // 刷新数据
      await loadEventList();
    } else {
      ElMessage.error(res.msg || "从模板添加事件失败");
    }
  } catch (error) {
    console.error("从模板添加事件失败:", error);
    ElMessage.error("从模板添加事件失败");
  } finally {
    loading.value = false;
  }
};

// 处理从模板选择事件取消
const handleTemplateSelectorCancel = () => {
  // 取消操作，无需特殊处理
};

// 处理条件编辑器确认
const handleConditionEditorConfirm = async () => {
  if (currentEditingEvent.value) {
    // 更新事件条件列表标签 - 与Angular版本保持一致
    const event = currentEditingEvent.value;
    if (event.eventConditionList && event.eventConditionList.length > 0) {
      let text = "";
      event.eventConditionList.forEach((obj: any) => {
        if (text.length === 0) {
          text = obj["eventConditionId"].toString();
        } else {
          text = text + "/" + obj["eventConditionId"].toString();
        }
      });
      event.eventConditionListLabel = text;
    } else {
      event.eventConditionListLabel = "";
    }

    // 刷新表格数据
    const tableRowIndex = tableData.value.findIndex(
      item => item.eventId === event.eventId
    );
    if (tableRowIndex !== -1) {
      tableData.value[tableRowIndex] = { ...event };
    }
  }

  showConditionEditor.value = false;
  currentEditingEvent.value = null;

  // 重新加载事件列表以确保数据一致性
  await loadEventList();
};

// 处理条件编辑器取消
const handleConditionEditorCancel = () => {
  // 恢复原始数据
  if (currentEditingEvent.value) {
    const originalKey = `${currentEditingEvent.value.eventId}_eventConditionList`;
    const originalValue = originalData.value.get(originalKey);
    if (originalValue) {
      try {
        currentEditingEvent.value.eventConditionList =
          JSON.parse(originalValue);

        // 更新表格行数据
        const tableRowIndex = tableData.value.findIndex(
          item => item.eventId === currentEditingEvent.value!.eventId
        );
        if (tableRowIndex !== -1) {
          if (
            currentEditingEvent.value.eventConditionList &&
            currentEditingEvent.value.eventConditionList.length > 0
          ) {
            let text = "";
            currentEditingEvent.value.eventConditionList.forEach((obj: any) => {
              if (text.length === 0) {
                text = obj["eventConditionId"].toString();
              } else {
                text = text + "/" + obj["eventConditionId"].toString();
              }
            });
            currentEditingEvent.value.eventConditionListLabel = text;
          } else {
            currentEditingEvent.value.eventConditionListLabel = "";
          }
          tableData.value[tableRowIndex] = { ...currentEditingEvent.value };
        }

        // 清理原始数据缓存
        originalData.value.delete(originalKey);
      } catch (error) {
        console.error("恢复条件数据失败:", error);
      }
    }
  }

  showConditionEditor.value = false;
  currentEditingEvent.value = null;
};

// 获取行样式类
const getRowClass = ({ rowIndex }: { rowIndex: number }) => {
  const isSelected = selectedRowIndexes.value.includes(rowIndex);
  return isSelected ? "selected-row" : "";
};

// 组件卸载时清理监听器
onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
  // 清理原始数据缓存
  originalData.value.clear();
  // 清理选中状态
  selectedRowIndexes.value = [];
  selectedRows.value = [];
  window.removeEventListener("resize", updateTableSize);
});

// 暴露方法给父组件
defineExpose({
  loadEventList
});
</script>

<style scoped>
.device-template-event {
  width: 100%;
  height: 100%;
}

.event-table-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* 可编辑单元格样式 */
:deep(.table-v2-inline-editing-trigger) {
  border: 1px transparent dotted;
  cursor: pointer;
  min-width: 55px; /* 确保占满整个单元格宽度 */
}

:deep(.table-v2-inline-editing-trigger:hover) {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

/* 只读单元格样式 */
.read-only-cell {
  padding: 4px 8px;
  min-height: 24px;
  line-height: 24px;
  color: var(--el-text-color-placeholder);
}

/* 可点击的只读单元格样式 */
.read-only-clickable-cell {
  padding: 4px 8px;
  min-height: 24px;
  line-height: 24px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid transparent;
}

.read-only-clickable-cell:hover {
  background-color: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary-light-5);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.read-only-clickable-cell:hover .edit-hint {
  opacity: 1;
}

.read-only-clickable-cell .edit-hint {
  opacity: 0.6;
  transition: opacity 0.2s;
  font-size: 12px;
  color: var(--el-color-primary);
  flex-shrink: 0;
}

.read-only-clickable-cell .cell-content {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 过滤器样式 */
.el-table-v2__demo-filter {
  border-top: var(--el-border);
  margin: 12px -12px -12px;
  padding: 0 12px;
  display: flex;
  justify-content: space-between;
}

.filter-wrapper {
  padding: 8px 0;
}

.filter-group {
  margin-bottom: 8px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.text-xs {
  font-size: 12px;
}

.mr-2 {
  margin-right: 8px;
}

.cursor-pointer {
  cursor: pointer;
}

/* 全局表格样式 */
:deep(.el-table) {
  font-size: 12px;
}

:deep(.el-table .cell) {
  padding: 4px 8px;
}

:deep(.el-input__inner) {
  font-size: 12px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-checkbox) {
  margin: 0;
}

/* 编辑状态下的输入框样式 */
:deep(.el-input--small) {
  font-size: 12px;
}

:deep(.el-input--small .el-input__inner) {
  height: 28px;
  line-height: 28px;
}

:deep(.el-select--small) {
  font-size: 12px;
}

/* 确认对话框样式 */
:deep(.el-dialog) {
  border-radius: 8px;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  border-bottom: 1px solid var(--el-border-color-light);
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 16px 20px;
  border-top: 1px solid var(--el-border-color-light);
}

/* 选中行样式 */
:deep(.selected-row) {
  background-color: var(--el-color-primary-light-9) !important;
}

:deep(.selected-row:hover) {
  background-color: var(--el-color-primary-light-8) !important;
}
</style>
