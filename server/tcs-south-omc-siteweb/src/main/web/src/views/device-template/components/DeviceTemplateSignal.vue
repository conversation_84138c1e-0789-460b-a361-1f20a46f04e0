<template>
  <div class="device-template-signal">
    <!-- 表格区域 -->
    <div class="signal-table-container">
      <el-table-v2
        ref="tableRef"
        v-loading="loading"
        :columns="tableColumns"
        :data="filteredData"
        :width="tableWidth"
        :height="tableHeight"
        :row-height="40"
        :header-height="40"
        :row-class="getRowClass"
        :row-event-handlers="rowEventHandlers"
        fixed
      />
    </div>

    <!-- 表达式配置对话框 -->
    <ExpressionConfigDialog
      v-model="showExpressionDialog"
      :signal-data="currentEditingSignal"
      :template-id="templateData?.id"
      :disabled="isRootTemplate"
      @confirm="handleExpressionConfirm"
    />

    <!-- 状态信号配置对话框 -->
    <SignalMeaningDialog
      v-model="showSignalMeaningDialog"
      :signal-data="currentEditingSignal"
      :disabled="isRootTemplate"
      @confirm="handleSignalMeaningConfirm"
    />

    <!-- 基类配置对话框 -->
    <BaseClassSelectorDialog
      v-model="showBaseClassDialog"
      :signal-data="currentEditingSignal"
      :template-id="templateData?.id"
      :type="0"
      @confirm="handleBaseClassConfirm"
    />

    <!-- 模板修改确认对话框 -->
    <DeviceTemplateConfirmDialog
      v-model="showConfirmDialog"
      :template-id="templateData?.id"
      :template-data="templateData"
      @confirm="handleConfirmDialogResult"
      @cancel="handleConfirmCancel"
    />

    <!-- 从模板选择信号对话框 -->
    <DeviceTemplateSecSelector
      v-model="showTemplateSelector"
      :type="0"
      :equipment-category="
        templateData?.template?.equipmentCategory ||
        templateData?.equipmentCategory ||
        0
      "
      :origin-template-id="templateData?.template?.id || templateData?.id || 0"
      :origin-signal-list="tableData"
      :protocol-code="
        templateData?.template?.protocolCode || templateData?.protocolCode || ''
      "
      @confirm="handleTemplateSelectorConfirm"
      @cancel="handleTemplateSelectorCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, h, withKeys } from "vue";
import {
  ElMessage,
  ElButton,
  ElSelect,
  ElOption,
  ElIcon,
  ElPopover,
  ElInput,
  ElCheckbox,
  type InputInstance,
  type SelectInstance
} from "element-plus";
import { Filter } from "@element-plus/icons-vue";
import type { FunctionalComponent } from "vue";
import ContextMenu from "@imengyu/vue3-context-menu";
import ExpressionConfigDialog from "./ExpressionConfigDialog.vue";
import SignalMeaningDialog from "./SignalMeaningDialog.vue";
import BaseClassSelectorDialog from "./BaseClassSelectorDialog.vue";
import DeviceTemplateConfirmDialog from "./DeviceTemplateConfirmDialog.vue";
import DeviceTemplateSecSelector from "./DeviceTemplateSecSelector.vue";
import {
  getSignalList,
  getSignalListByTempIdEqId,
  getSignalCategoryList,
  getSignalTypeList,
  getChannelTypeList,
  getDataTypeList,
  getSignalPropertyList,
  getBatteryDeviceCategory,
  updateSignal,
  addSignal,
  deleteSignal,
  addRelateEvent,
  batchSignalFieldCopy,
  getTemplateInfoById,
  getTemplateTreeByCategory,
  BaseClassSelectorType,
  deviceTemplateService,
  type SignalInfo as BaseSignalInfo,
  type DataDictionaryItem,
  type TemplateTreeNode
} from "@/api/device-template";

// 扩展信号信息类型，包含编辑状态
interface SignalInfo extends BaseSignalInfo {
  signalName_editing?: boolean;
  displayIndex_editing?: boolean;
  signalCategory_editing?: boolean;
  signalType_editing?: boolean;
  channelNo_editing?: boolean;
  channelType_editing?: boolean;
  dataType_editing?: boolean;
  showPrecision_editing?: boolean;
  unit_editing?: boolean;
  storeInterval_editing?: boolean;
  enable_editing?: boolean;
  visible_editing?: boolean;
  description_editing?: boolean;
  signalProperty_editing?: boolean;
  moduleNo_editing?: boolean;
  [key: string]: any; // 允许动态属性
}

interface Props {
  templateData?: any;
  tabIndex?: number;
  muCategory?: number;
  tableSearchText?: string;
  equipmentId?: string | number;
  buttonFlag?: boolean;
  isRootTemplate?: boolean;
}

interface Emits {
  (e: "refresh"): void;
  (e: "selectTab", data: { index: number }): void;
}

const props = withDefaults(defineProps<Props>(), {
  templateData: null,
  tabIndex: 0,
  muCategory: 0,
  tableSearchText: "",
  equipmentId: "",
  buttonFlag: false,
  isRootTemplate: false
});

const emit = defineEmits<Emits>();

// 过滤器状态接口
interface FilterState {
  [key: string]: {
    value: any[];
    options: Array<{ label: string; value: any }>;
  };
}

// 编辑单元格组件类型定义
type EditCellProps = {
  value: any;
  onChange: (value: any) => void;
  onBlur: () => void;
  onKeydownEnter: () => void;
  forwardRef: (el: any) => void;
  options?: Array<{ label: string; value: any }>;
  type?: "input" | "number" | "select" | "checkbox" | "multiselect";
  placeholder?: string;
};

// 输入框编辑组件
const InputCell: FunctionalComponent<EditCellProps> = ({
  value,
  onChange,
  onBlur,
  onKeydownEnter,
  forwardRef,
  placeholder = ""
}) => {
  return h(ElInput, {
    ref: forwardRef,
    modelValue: value,
    placeholder,
    size: "small",
    onInput: onChange,
    onBlur,
    onKeydown: withKeys(onKeydownEnter, ["enter"])
  });
};

// 下拉选择编辑组件
const SelectCell: FunctionalComponent<EditCellProps> = ({
  value,
  onChange,
  onBlur,
  onKeydownEnter,
  forwardRef,
  options = [],
  placeholder = "请选择"
}) => {
  return h(
    ElSelect,
    {
      ref: forwardRef,
      modelValue: value,
      placeholder,
      size: "small",
      clearable: true,
      "onUpdate:modelValue": onChange,
      onBlur,
      onKeydown: withKeys(onKeydownEnter, ["enter"])
    },
    {
      default: () =>
        options.map(option =>
          h(ElOption, {
            key: option.value,
            label: option.label,
            value: option.value
          })
        )
    }
  );
};

// 多选下拉编辑组件
const MultiSelectCell: FunctionalComponent<EditCellProps> = ({
  value,
  onChange,
  onBlur,
  onKeydownEnter,
  forwardRef,
  options = [],
  placeholder = "请选择"
}) => {
  const arrayValue = Array.isArray(value)
    ? value
    : value
      ? value.toString().split(";").map(Number)
      : [];

  return h(
    ElSelect,
    {
      ref: forwardRef,
      modelValue: arrayValue,
      placeholder,
      size: "small",
      multiple: true,
      clearable: true,
      "onUpdate:modelValue": (val: number[]) => {
        onChange(val.join(";"));
      },
      onBlur,
      onKeydown: withKeys(onKeydownEnter, ["enter"])
    },
    {
      default: () =>
        options.map(option =>
          h(ElOption, {
            key: option.value,
            label: option.label,
            value: option.value
          })
        )
    }
  );
};

// 复选框编辑组件
const CheckboxCell: FunctionalComponent<EditCellProps> = ({
  value,
  onChange,
  onBlur,
  onKeydownEnter,
  forwardRef
}) => {
  return h(ElCheckbox, {
    ref: forwardRef,
    modelValue: !!value,
    "onUpdate:modelValue": onChange,
    onBlur,
    onKeydown: withKeys(onKeydownEnter, ["enter"])
  });
};

// 状态变量
const loading = ref(false);
const tableData = ref<SignalInfo[]>([]);
const batteryData = ref<any>(null);

// 表格选中相关
const selectedRowIndexes = ref<number[]>([]);
const selectedRows = ref<SignalInfo[]>([]);

// 下拉选项数据
const signalCategoryList = ref<DataDictionaryItem[]>([]);
const signalTypeList = ref<DataDictionaryItem[]>([]);
const channelTypeList = ref<DataDictionaryItem[]>([]);
const dataTypeList = ref<DataDictionaryItem[]>([]);
const signalPropertyList = ref<DataDictionaryItem[]>([]);

// 过滤器状态
const filterState = ref<FilterState>({});

// 存储周期选项
const storeIntervalOptions = [
  "86400",
  "28800",
  "14400",
  "3600",
  "1800",
  "600",
  "300",
  "43200",
  "21600",
  "17280",
  "10800",
  "9600",
  "864",
  "7200",
  "5760",
  "5400",
  "4800",
  "4320",
  "3456",
  "3200",
  "2880",
  "2700",
  "2400",
  "2160",
  "1920",
  "1728",
  "1600",
  "1440",
  "1350",
  "1200",
  "1152",
  "1080",
  "960",
  "900",
  "864",
  "800",
  "720",
  "675",
  "640",
  "576",
  "540",
  "480",
  "450",
  "432",
  "400",
  "384",
  "360",
  "320",
  "288",
  "270",
  "240",
  "225",
  "216",
  "200",
  "192",
  "180",
  "160",
  "150",
  "144",
  "135",
  "128",
  "120",
  "108",
  "100",
  "96",
  "90",
  "80",
  "75",
  "72",
  "64",
  "60",
  "54",
  "50",
  "48",
  "45",
  "40",
  "36",
  "32",
  "30",
  "27",
  "25",
  "24",
  "20",
  "18",
  "16",
  "15",
  "12",
  "10",
  "9",
  "8",
  "6",
  "5",
  "4",
  "3",
  "2",
  "1",
  "0"
];

// 存储原始数据用于对比
const originalData = ref<Map<string, any>>(new Map());

// 处理单元格值变化（编辑过程中）- 只保存值，不提交
const handleCellValueChange = (
  rowData: SignalInfo,
  fieldKey: string,
  value: any
) => {
  // 直接更新值，不触发提交
  rowData[fieldKey] = value;
};

// 处理编辑完成（失去焦点或按回车）- 这时才提交变更
const handleCellEditComplete = (rowData: SignalInfo, fieldKey: string) => {
  // 获取原始值
  const originalKey = `${rowData.signalId}_${fieldKey}`;
  const originalValue = originalData.value.get(originalKey);
  const currentValue = rowData[fieldKey];

  // 特殊验证：精度字段
  if (fieldKey === "showPrecision" && currentValue) {
    const isValid = /^0(\.0*)?$/.test(currentValue);
    if (!isValid) {
      ElMessage.warning(
        "只允许输入以 0 开头，且小数部分只能是 0 的数字，如 0, 0.0, 0.00"
      );
      // 恢复原始值
      rowData[fieldKey] = originalValue;
      return;
    }
  }

  // 检查值是否真的发生了变化
  if (originalValue !== currentValue) {
    // 值确实发生了变化，提交更改
    submitSignalChange(rowData, fieldKey);
  }
};

// 开始编辑时保存原始值
const handleCellEditStart = (rowData: SignalInfo, fieldKey: string) => {
  const originalKey = `${rowData.signalId}_${fieldKey}`;
  if (!originalData.value.has(originalKey)) {
    originalData.value.set(originalKey, rowData[fieldKey]);
  }
};

// 提交信号变更 - 需要前置声明
const submitSignalChange = async (
  rowData: SignalInfo,
  fieldKey: string = ""
) => {
  // 防止重复确认
  if (isConfirming.value) return;

  // 检查是否需要显示确认对话框
  if (!deviceTemplateService.getNotShowState()) {
    // 需要显示确认对话框
    pendingSignalData.value = { ...rowData };
    pendingFieldKey.value = fieldKey;
    showConfirmDialog.value = true;
    return;
  }

  // 直接更新
  await performSignalUpdate(rowData);
};

// 执行实际的信号更新
const performSignalUpdate = async (
  rowData: SignalInfo,
  newTemplateId?: number
) => {
  try {
    loading.value = true;

    // 处理特殊字段
    const submitData = { ...rowData };

    // 如果是新模板，更新模板ID
    if (newTemplateId) {
      submitData.equipmentTemplateId = newTemplateId;
      // 处理信号含义列表的模板ID
      if (submitData.signalMeaningsList) {
        submitData.signalMeaningsList.forEach((meaning: any) => {
          meaning.equipmentTemplateId = newTemplateId;
        });
      }
      // 处理信号属性列表的模板ID
      if (submitData.signalPropertyList) {
        submitData.signalPropertyList.forEach((prop: any) => {
          prop.equipmentTemplateId = newTemplateId;
          delete prop.id; // 删除原有ID，让后端生成新ID
        });
      }
    }

    // 处理信号属性
    if (submitData.signalProperty) {
      const ids = submitData.signalProperty
        .toString()
        .split(";")
        .map(Number)
        .filter(id => !isNaN(id));
      submitData.signalPropertyList = ids.map(id => ({
        equipmentTemplateId: submitData.equipmentTemplateId,
        signalId: submitData.signalId,
        signalPropertyId: id,
        ...(newTemplateId ? {} : { id: undefined }) // 新模板时不包含原ID
      }));
    }

    const res = await updateSignal(submitData);
    if (res.code === 0) {
      ElMessage.success("更新成功！");

      // 不管是否为新模板，都重新加载信号列表以保证数据一致性
      await loadSignalList();

      // 只有在新模板的情况下才通知父组件刷新（用于更新模板树）
      if (newTemplateId) {
        emit("refresh");
      }
    } else {
      ElMessage.error(res.msg || "更新失败");
      loadSignalList(); // 失败时重新加载数据
    }
  } catch (error) {
    console.error("更新信号失败:", error);
    ElMessage.error("更新失败");
    loadSignalList(); // 失败时重新加载数据
  } finally {
    loading.value = false;
  }
};

// 创建可编辑单元格渲染器
const createEditableCell = (column: any, cellType: string = "input") => {
  return ({ rowData }: any) => {
    const fieldKey = column.dataKey;
    const editingKey = `${fieldKey}_editing`;

    // 获取选项数据（需要在前面定义，以便只读状态也能使用）
    const getOptions = () => {
      switch (fieldKey) {
        case "signalCategory":
          return signalCategoryList.value.map(item => ({
            label: item.itemValue,
            value: item.itemId
          }));
        case "signalType":
          return signalTypeList.value.map(item => ({
            label: item.itemValue,
            value: item.itemId
          }));
        case "channelType":
          return channelTypeList.value.map(item => ({
            label: item.itemValue,
            value: item.itemId
          }));
        case "dataType":
          return dataTypeList.value.map(item => ({
            label: item.itemValue,
            value: item.itemId
          }));
        case "signalProperty":
          return signalPropertyList.value.map(item => ({
            label: item.itemValue,
            value: item.itemId
          }));
        case "storeInterval":
          return storeIntervalOptions.map(item => ({
            label: item,
            value: Number(item)
          }));
        default:
          return [];
      }
    };

    // 获取显示值（用于只读和编辑状态）
    const getDisplayValue = () => {
      const value = rowData[fieldKey];
      if (cellType === "select" || cellType === "multiselect") {
        const options = getOptions();
        if (cellType === "multiselect") {
          if (!value) return "";
          const ids = value.toString().split(";").map(Number);
          return ids
            .map((id: number) => {
              const option = options.find(opt => opt.value === id);
              return option ? option.label : id;
            })
            .join(", ");
        } else {
          const option = options.find(opt => opt.value === value);
          return option ? option.label : value || "";
        }
      } else if (cellType === "checkbox") {
        return value ? "是" : "否";
      }
      return value || "";
    };

    // 检查是否为只读模板
    if (props.isRootTemplate) {
      return h("div", { class: "read-only-cell" }, getDisplayValue());
    }

    const onChange = (value: any) => {
      handleCellValueChange(rowData, fieldKey, value);
    };

    const onEnterEditMode = () => {
      // 开始编辑时保存原始值
      handleCellEditStart(rowData, fieldKey);
      rowData[editingKey] = true;
    };

    const onExitEditMode = () => {
      // 退出编辑时检查并提交变更
      handleCellEditComplete(rowData, fieldKey);
      rowData[editingKey] = false;
    };

    const inputRef = ref();
    const setRef = (el: any) => {
      inputRef.value = el;
      if (el) {
        el.focus?.();
      }
    };

    if (rowData[editingKey]) {
      const cellProps = {
        forwardRef: setRef,
        value: rowData[fieldKey],
        onChange,
        onBlur: onExitEditMode,
        onKeydownEnter: onExitEditMode,
        options: getOptions()
      };

      switch (cellType) {
        case "select":
          return h(SelectCell, cellProps);
        case "multiselect":
          return h(MultiSelectCell, cellProps);
        case "checkbox":
          return h(CheckboxCell, cellProps);
        default:
          return h(InputCell, cellProps);
      }
    } else {
      const displayValue = getDisplayValue();
      return h(
        "div",
        {
          class: "table-v2-inline-editing-trigger",
          onClick: onEnterEditMode
        },
        displayValue || "\u00A0"
      ); // 使用不间断空格确保有内容可点击
    }
  };
};

// 创建只读单元格渲染器（用于不可编辑的字段）
const createReadOnlyCell = (
  column: any,
  clickHandler?: Function,
  customTitle?: string
) => {
  return ({ rowData }: any) => {
    const fieldKey = column.dataKey;
    const value = rowData[fieldKey];

    if (clickHandler) {
      // 检查是否为只读模板
      if (props.isRootTemplate) {
        return h(
          "div",
          {
            class: "read-only-cell"
          },
          value || ""
        );
      }

      // 特殊处理状态信号
      if (fieldKey === "stateValue") {
        const isEditable = rowData.signalCategory === 2;
        const title = isEditable ? "点击配置状态信号" : "仅开关信号可配置状态";
        const placeholder = isEditable
          ? "点击配置状态信号"
          : "仅开关信号可配置";

        return h(
          "div",
          {
            class: isEditable ? "read-only-clickable-cell" : "read-only-cell",
            onClick: isEditable ? () => clickHandler(rowData) : undefined,
            title
          },
          isEditable
            ? [h("span", { class: "cell-content" }, value || placeholder)]
            : value || placeholder
        );
      }

      // 默认可点击单元格
      const title = customTitle || "点击配置";
      const placeholder =
        fieldKey === "expression"
          ? "点击配置表达式"
          : fieldKey === "baseTypeName"
            ? "点击配置基类信号"
            : "点击配置";

      return h(
        "div",
        {
          class: "read-only-clickable-cell",
          onClick: () => clickHandler(rowData),
          title
        },
        [h("span", { class: "cell-content" }, value || placeholder)]
      );
    }

    return h(
      "div",
      {
        class: "read-only-cell"
      },
      value || ""
    );
  };
};

// 处理表达式点击
const handleExpressionClick = (rowData: SignalInfo) => {
  if (props.isRootTemplate) return;

  // 保存原始值
  const originalKey = `${rowData.signalId}_expression`;
  if (!originalData.value.has(originalKey)) {
    originalData.value.set(originalKey, rowData.expression);
  }

  // 打开表达式配置对话框
  showExpressionDialog.value = true;
  currentEditingSignal.value = rowData;
};

// 处理状态信号点击
const handleStateValueClick = (rowData: SignalInfo) => {
  if (props.isRootTemplate) return;

  if (rowData.signalCategory !== 2) {
    ElMessage.warning("非开关信号不可编辑!");
    return;
  }

  // 保存原始值
  const originalKey = `${rowData.signalId}_stateValue`;
  if (!originalData.value.has(originalKey)) {
    originalData.value.set(originalKey, rowData.stateValue);
  }

  // 打开状态信号配置对话框
  showSignalMeaningDialog.value = true;
  currentEditingSignal.value = rowData;
};

// 处理基类信号点击
const handleBaseTypeClick = (rowData: SignalInfo) => {
  if (props.isRootTemplate) return;

  // 保存原始值
  const originalKey = `${rowData.signalId}_baseTypeName`;
  if (!originalData.value.has(originalKey)) {
    originalData.value.set(originalKey, rowData.baseTypeName);
  }

  // 打开基类配置对话框
  showBaseClassDialog.value = true;
  currentEditingSignal.value = rowData;
};

// 表达式对话框相关
const showExpressionDialog = ref(false);
const currentEditingSignal = ref<SignalInfo | null>(null);

// 状态信号对话框相关
const showSignalMeaningDialog = ref(false);

// 基类配置对话框相关
const showBaseClassDialog = ref(false);

// 模板修改确认对话框相关
const showConfirmDialog = ref(false);
const pendingSignalData = ref<SignalInfo | null>(null);
const pendingFieldKey = ref<string>("");
const pendingActionType = ref<string>("");
const isConfirming = ref(false);

// 从模板选择信号对话框相关
const showTemplateSelector = ref(false);

// 处理表达式配置确认
const handleExpressionConfirm = (expression: string) => {
  if (currentEditingSignal.value) {
    // 保存原始值（如果尚未保存）
    const originalKey = `${currentEditingSignal.value.signalId}_expression`;
    if (!originalData.value.has(originalKey)) {
      originalData.value.set(
        originalKey,
        currentEditingSignal.value.expression
      );
    }

    // 更新值
    currentEditingSignal.value.expression = expression;

    // 检查是否有变化
    const originalValue = originalData.value.get(originalKey);
    if (originalValue !== expression) {
      submitSignalChange(currentEditingSignal.value, "expression");
    }
  }
  showExpressionDialog.value = false;
  currentEditingSignal.value = null;
};

// 处理表达式配置取消
const handleExpressionCancel = () => {
  // 表达式对话框取消时不需要特殊处理，因为我们没有修改原始数据
  showExpressionDialog.value = false;
  currentEditingSignal.value = null;
};

// 处理状态信号配置确认
const handleSignalMeaningConfirm = (meanings: any[]) => {
  if (currentEditingSignal.value) {
    // 保存原始值（如果尚未保存）
    const originalKey = `${currentEditingSignal.value.signalId}_stateValue`;
    if (!originalData.value.has(originalKey)) {
      originalData.value.set(
        originalKey,
        currentEditingSignal.value.stateValue
      );
    }

    // 更新信号的状态含义列表
    currentEditingSignal.value.signalMeaningsList = meanings;

    // 更新显示的状态值
    const newStateValue =
      meanings && meanings.length > 0
        ? meanings.map((item: any) => item.stateValue).join("/")
        : "";
    currentEditingSignal.value.stateValue = newStateValue;

    // 检查是否有变化
    const originalValue = originalData.value.get(originalKey);
    if (originalValue !== newStateValue) {
      submitSignalChange(currentEditingSignal.value, "stateValue");
    }
  }
  showSignalMeaningDialog.value = false;
  currentEditingSignal.value = null;
};

// 处理状态信号配置取消
const handleSignalMeaningCancel = () => {
  // 状态信号对话框取消时不需要特殊处理，因为我们没有修改原始数据
  showSignalMeaningDialog.value = false;
  currentEditingSignal.value = null;
};

// 处理基类配置确认
const handleBaseClassConfirm = (result: {
  action: string;
  baseTypeId: number | null;
  baseTypeName?: string;
  moduleNumber?: number | null;
}) => {
  if (currentEditingSignal.value) {
    // 保存原始值（如果尚未保存）
    const originalKey = `${currentEditingSignal.value.signalId}_baseTypeName`;
    if (!originalData.value.has(originalKey)) {
      originalData.value.set(
        originalKey,
        currentEditingSignal.value.baseTypeName
      );
    }

    let newBaseTypeName = "";
    if (result.action === "delete") {
      // 取消关联基类
      currentEditingSignal.value.baseTypeId = null;
      currentEditingSignal.value.baseTypeName = "";
      newBaseTypeName = "";
    } else if (result.action === "confirm") {
      // 设置基类
      currentEditingSignal.value.baseTypeId = result.baseTypeId;
      currentEditingSignal.value.baseTypeName = result.baseTypeName || "";
      newBaseTypeName = result.baseTypeName || "";
    }

    // 检查是否有变化
    const originalValue = originalData.value.get(originalKey);
    if (originalValue !== newBaseTypeName) {
      submitSignalChange(currentEditingSignal.value, "baseTypeName");
    }
  }
  showBaseClassDialog.value = false;
  currentEditingSignal.value = null;
};

// 处理基类配置取消
const handleBaseClassCancel = () => {
  // 基类对话框取消时不需要特殊处理，因为我们没有修改原始数据
  showBaseClassDialog.value = false;
  currentEditingSignal.value = null;
};

// 处理确认对话框结果
const handleConfirmDialogResult = async (
  action: "update" | "copy",
  data: any
) => {
  isConfirming.value = true;

  try {
    // 保存"不再提醒"状态
    if (data.notShowAgain) {
      deviceTemplateService.setNotShowState(true);
    }

    if (pendingSignalData.value && pendingFieldKey.value) {
      // 处理信号更新操作
      const signalId = pendingSignalData.value.signalId;
      const fieldKey = pendingFieldKey.value;

      if (action === "update") {
        // 更新原模板
        await performSignalUpdate(pendingSignalData.value);
      } else if (action === "copy") {
        // 另存为新模板
        if (data.newTemplateId) {
          await performSignalUpdate(
            pendingSignalData.value,
            data.newTemplateId
          );
          ElMessage.success("已在新模板中更新成功！");
        }
      }

      // 更新成功后，清理对应的原始数据缓存
      const originalKey = `${signalId}_${fieldKey}`;
      originalData.value.delete(originalKey);
    } else if (pendingActionType.value) {
      // 处理右键菜单操作
      if (action === "update") {
        // 在原模板上执行操作
        await executeAction(pendingActionType.value);
      } else if (action === "copy") {
        // 在新模板上执行操作
        if (data.newTemplateId) {
          await executeAction(pendingActionType.value, data.newTemplateId);
          ElMessage.success("已在新模板中操作成功！");
        }
      }
    }
  } catch (error) {
    console.error("处理确认对话框结果失败:", error);
    ElMessage.error("操作失败");
    // 操作失败时也重新加载数据以确保界面一致性
    loadSignalList();
  } finally {
    // 重置状态
    isConfirming.value = false;
    pendingSignalData.value = null;
    pendingFieldKey.value = "";
    pendingActionType.value = "";
    showConfirmDialog.value = false;
  }
};

// 处理确认取消
const handleConfirmCancel = () => {
  // 恢复修改前的值（仅对信号更新操作有效）
  if (pendingSignalData.value && pendingFieldKey.value) {
    const originalKey = `${pendingSignalData.value.signalId}_${pendingFieldKey.value}`;
    const originalValue = originalData.value.get(originalKey);
    if (originalValue !== undefined) {
      // 恢复到原始值
      pendingSignalData.value[pendingFieldKey.value] = originalValue;

      // 更新对应的表格行数据
      const tableRowIndex = tableData.value.findIndex(
        item => item.signalId === pendingSignalData.value!.signalId
      );
      if (tableRowIndex !== -1) {
        tableData.value[tableRowIndex][pendingFieldKey.value] = originalValue;
      }

      // 清理原始数据缓存
      originalData.value.delete(originalKey);
    }
  }

  // 重置状态
  isConfirming.value = false;
  pendingSignalData.value = null;
  pendingFieldKey.value = "";
  pendingActionType.value = "";
  showConfirmDialog.value = false;
};

// 初始化过滤器状态
const initFilterState = () => {
  filterState.value = {
    signalName: {
      value: [],
      options: []
    },
    signalId: {
      value: [],
      options: []
    },
    displayIndex: {
      value: [],
      options: []
    },
    signalCategory: {
      value: [],
      options: [
        { label: "模拟量", value: 1 },
        { label: "开关量", value: 2 },
        { label: "脉冲量", value: 3 }
      ]
    },
    signalType: {
      value: [],
      options: [
        { label: "输入信号", value: 1 },
        { label: "输出信号", value: 2 },
        { label: "计算信号", value: 3 }
      ]
    },
    channelNo: {
      value: [],
      options: []
    },
    channelType: {
      value: [],
      options: [
        { label: "AI", value: 1 },
        { label: "DI", value: 2 },
        { label: "AO", value: 3 },
        { label: "DO", value: 4 }
      ]
    },
    expression: {
      value: [],
      options: []
    },
    dataType: {
      value: [],
      options: []
    },
    showPrecision: {
      value: [],
      options: []
    },
    unit: {
      value: [],
      options: []
    },
    storeInterval: {
      value: [],
      options: storeIntervalOptions.map(item => ({
        label: item,
        value: Number(item)
      }))
    },
    enable: {
      value: [],
      options: [
        { label: "是", value: true },
        { label: "否", value: false }
      ]
    },
    visible: {
      value: [],
      options: [
        { label: "是", value: true },
        { label: "否", value: false }
      ]
    },
    description: {
      value: [],
      options: []
    },
    stateValue: {
      value: [],
      options: []
    },
    baseTypeName: {
      value: [],
      options: []
    }
  };
};

// 更新动态选项
const updateDynamicOptions = () => {
  if (tableData.value.length === 0) return;

  // 更新信号名称选项
  const signalNames = [
    ...new Set(tableData.value.map(item => item.signalName).filter(Boolean))
  ];
  filterState.value.signalName.options = signalNames.map(name => ({
    label: name,
    value: name
  }));

  // 更新信号ID选项
  const signalIds = [
    ...new Set(
      tableData.value
        .map(item => item.signalId)
        .filter(id => id !== null && id !== undefined)
    )
  ];
  filterState.value.signalId.options = signalIds.map(id => ({
    label: id.toString(),
    value: id
  }));

  // 更新显示顺序选项
  const displayIndexes = [
    ...new Set(
      tableData.value
        .map(item => item.displayIndex)
        .filter(index => index !== null && index !== undefined)
    )
  ];
  filterState.value.displayIndex.options = displayIndexes.map(index => ({
    label: index.toString(),
    value: index
  }));

  // 更新通道号选项
  const channelNos = [
    ...new Set(
      tableData.value
        .map(item => item.channelNo)
        .filter(no => no !== null && no !== undefined)
    )
  ];
  filterState.value.channelNo.options = channelNos.map(no => ({
    label: no.toString(),
    value: no
  }));

  // 更新表达式选项
  const expressions = [
    ...new Set(tableData.value.map(item => item.expression).filter(Boolean))
  ];
  filterState.value.expression.options = expressions.map(expr => ({
    label: expr,
    value: expr
  }));

  // 更新精度选项
  const precisions = [
    ...new Set(tableData.value.map(item => item.showPrecision).filter(Boolean))
  ];
  filterState.value.showPrecision.options = precisions.map(precision => ({
    label: precision,
    value: precision
  }));

  // 更新单位选项
  const units = [
    ...new Set(tableData.value.map(item => item.unit).filter(Boolean))
  ];
  filterState.value.unit.options = units.map(unit => ({
    label: unit,
    value: unit
  }));

  // 更新说明选项
  const descriptions = [
    ...new Set(tableData.value.map(item => item.description).filter(Boolean))
  ];
  filterState.value.description.options = descriptions.map(desc => ({
    label: desc,
    value: desc
  }));

  // 更新状态值选项
  const stateValues = [
    ...new Set(tableData.value.map(item => item.stateValue).filter(Boolean))
  ];
  filterState.value.stateValue.options = stateValues.map(state => ({
    label: state,
    value: state
  }));

  // 更新基类信号选项
  const baseTypeNames = [
    ...new Set(tableData.value.map(item => item.baseTypeName).filter(Boolean))
  ];
  filterState.value.baseTypeName.options = baseTypeNames.map(name => ({
    label: name,
    value: name
  }));

  // 从字典更新选项
  if (signalCategoryList.value.length > 0) {
    filterState.value.signalCategory.options = signalCategoryList.value.map(
      item => ({
        label: item.itemValue,
        value: item.itemId
      })
    );
  }

  if (signalTypeList.value.length > 0) {
    filterState.value.signalType.options = signalTypeList.value.map(item => ({
      label: item.itemValue,
      value: item.itemId
    }));
  }

  if (channelTypeList.value.length > 0) {
    filterState.value.channelType.options = channelTypeList.value.map(item => ({
      label: item.itemValue,
      value: item.itemId
    }));
  }

  if (dataTypeList.value.length > 0) {
    filterState.value.dataType.options = dataTypeList.value.map(item => ({
      label: item.itemValue,
      value: item.itemId
    }));
  }
};

// 创建过滤器头部组件
const createFilterHeader = (column: any) => {
  const filterKey = column.dataKey;

  return (props: any) => {
    const popoverRef = ref();

    const onFilter = () => {
      popoverRef.value?.hide();
    };

    const onReset = () => {
      if (filterState.value[filterKey]) {
        filterState.value[filterKey].value = [];
      }
    };

    return h("div", { class: "flex items-center justify-center" }, [
      h("span", { class: "mr-2 text-xs" }, props.column.title),
      h(
        ElPopover,
        {
          ref: popoverRef,
          trigger: "click",
          width: 250
        },
        {
          default: () =>
            h("div", { class: "filter-wrapper" }, [
              h("div", { class: "filter-group" }, [
                h(
                  ElSelect,
                  {
                    modelValue: filterState.value[filterKey]?.value || [],
                    "onUpdate:modelValue": (value: any[]) => {
                      if (filterState.value[filterKey]) {
                        filterState.value[filterKey].value = value;
                      }
                    },
                    placeholder: "选择过滤条件",
                    size: "small",
                    multiple: true,
                    collapseTags: true,
                    filterable: true,
                    clearable: true,
                    style: { width: "100%" }
                  },
                  {
                    default: () =>
                      (filterState.value[filterKey]?.options || []).map(
                        (option: any) =>
                          h(ElOption, {
                            key: option.value,
                            label: option.label,
                            value: option.value
                          })
                      )
                  }
                )
              ]),
              h("div", { class: "el-table-v2__demo-filter" }, [
                h(ElButton, { text: true, onClick: onFilter }, () => "确认"),
                h(ElButton, { text: true, onClick: onReset }, () => "重置")
              ])
            ]),
          reference: () =>
            h(ElIcon, { class: "cursor-pointer" }, () => [h(Filter)])
        }
      )
    ]);
  };
};

// 过滤后的数据
const filteredData = computed(() => {
  let data = tableData.value;

  // 首先应用搜索文本过滤
  if (props.tableSearchText && props.tableSearchText.trim() !== "") {
    const searchText = props.tableSearchText.toLowerCase();
    data = data.filter(
      item =>
        item.signalName?.toLowerCase().includes(searchText) ||
        item.signalId?.toString().includes(searchText) ||
        item.description?.toLowerCase().includes(searchText)
    );
  }

  // 应用列过滤器
  Object.entries(filterState.value).forEach(([key, filter]) => {
    if (filter.value && filter.value.length > 0) {
      data = data.filter(item => {
        const itemValue = item[key as keyof SignalInfo];
        return filter.value.includes(itemValue);
      });
    }
  });

  return data;
});

// 虚拟表格配置
const tableWidth = ref(1200);
const tableHeight = ref(500);

// 获取容器尺寸并更新表格尺寸
const updateTableSize = () => {
  // 查找 el-tabs__content 容器
  const tabsContent = document.querySelector(
    ".el-tabs__content"
  ) as HTMLElement;
  if (tabsContent) {
    const rect = tabsContent.getBoundingClientRect();
    tableWidth.value = rect.width - 32; // 减去内边距
    tableHeight.value = rect.height - 32; // 减去内边距
  }
};

// 监听窗口尺寸变化
let resizeObserver: ResizeObserver | null = null;

// 表格列配置
const tableColumns: any[] = [
  {
    key: "signalName",
    title: "名称",
    dataKey: "signalName",
    width: 150,
    cellRenderer: createEditableCell({ dataKey: "signalName" }, "input"),
    headerCellRenderer: createFilterHeader({ dataKey: "signalName" })
  },
  {
    key: "signalId",
    title: "信号ID",
    dataKey: "signalId",
    width: 100,
    cellRenderer: createReadOnlyCell({ dataKey: "signalId" }),
    headerCellRenderer: createFilterHeader({ dataKey: "signalId" })
  },
  {
    key: "displayIndex",
    title: "显示顺序",
    dataKey: "displayIndex",
    width: 100,
    cellRenderer: createEditableCell({ dataKey: "displayIndex" }, "input"),
    headerCellRenderer: createFilterHeader({ dataKey: "displayIndex" })
  },
  {
    key: "signalCategory",
    title: "种类",
    dataKey: "signalCategory",
    width: 120,
    cellRenderer: createEditableCell({ dataKey: "signalCategory" }, "select"),
    headerCellRenderer: createFilterHeader({ dataKey: "signalCategory" })
  },
  {
    key: "signalType",
    title: "分类",
    dataKey: "signalType",
    width: 120,
    cellRenderer: createEditableCell({ dataKey: "signalType" }, "select"),
    headerCellRenderer: createFilterHeader({ dataKey: "signalType" })
  },
  {
    key: "channelNo",
    title: "通道号",
    dataKey: "channelNo",
    width: 90,
    cellRenderer: createEditableCell({ dataKey: "channelNo" }, "input"),
    headerCellRenderer: createFilterHeader({ dataKey: "channelNo" })
  },
  {
    key: "channelType",
    title: "通道类型",
    dataKey: "channelType",
    width: 120,
    cellRenderer: createEditableCell({ dataKey: "channelType" }, "select"),
    headerCellRenderer: createFilterHeader({ dataKey: "channelType" })
  },
  {
    key: "expression",
    title: "表达式",
    dataKey: "expression",
    width: 170,
    cellRenderer: createReadOnlyCell(
      { dataKey: "expression" },
      handleExpressionClick,
      "点击配置表达式"
    ),
    headerCellRenderer: createFilterHeader({ dataKey: "expression" })
  },
  {
    key: "dataType",
    title: "数据类型",
    dataKey: "dataType",
    width: 180,
    cellRenderer: createEditableCell({ dataKey: "dataType" }, "select"),
    headerCellRenderer: createFilterHeader({ dataKey: "dataType" })
  },
  {
    key: "showPrecision",
    title: "精度",
    dataKey: "showPrecision",
    width: 100,
    cellRenderer: createEditableCell({ dataKey: "showPrecision" }, "input"),
    headerCellRenderer: createFilterHeader({ dataKey: "showPrecision" })
  },
  {
    key: "unit",
    title: "单位",
    dataKey: "unit",
    width: 100,
    cellRenderer: createEditableCell({ dataKey: "unit" }, "input"),
    headerCellRenderer: createFilterHeader({ dataKey: "unit" })
  },
  {
    key: "storeInterval",
    title: "存储周期(秒)",
    dataKey: "storeInterval",
    width: 120,
    cellRenderer: createEditableCell({ dataKey: "storeInterval" }, "select"),
    headerCellRenderer: createFilterHeader({ dataKey: "storeInterval" })
  },
  {
    key: "enable",
    title: "有效",
    dataKey: "enable",
    width: 80,
    align: "center",
    cellRenderer: createEditableCell({ dataKey: "enable" }, "checkbox"),
    headerCellRenderer: createFilterHeader({ dataKey: "enable" })
  },
  {
    key: "visible",
    title: "可见",
    dataKey: "visible",
    width: 80,
    align: "center",
    cellRenderer: createEditableCell({ dataKey: "visible" }, "checkbox"),
    headerCellRenderer: createFilterHeader({ dataKey: "visible" })
  },
  {
    key: "description",
    title: "说明",
    dataKey: "description",
    width: 150,
    cellRenderer: createEditableCell({ dataKey: "description" }, "input"),
    headerCellRenderer: createFilterHeader({ dataKey: "description" })
  },
  {
    key: "signalProperty",
    title: "信号属性",
    dataKey: "signalProperty",
    width: 150,
    cellRenderer: createEditableCell(
      { dataKey: "signalProperty" },
      "multiselect"
    ),
    headerCellRenderer: createFilterHeader({ dataKey: "signalProperty" })
  },
  {
    key: "stateValue",
    title: "状态信号",
    dataKey: "stateValue",
    width: 200,
    cellRenderer: createReadOnlyCell(
      { dataKey: "stateValue" },
      handleStateValueClick,
      "点击配置状态信号"
    ),
    headerCellRenderer: createFilterHeader({ dataKey: "stateValue" })
  },
  {
    key: "moduleNo",
    title: "所属模块",
    dataKey: "moduleNo",
    width: 100,
    cellRenderer: createEditableCell({ dataKey: "moduleNo" }, "input"),
    headerCellRenderer: createFilterHeader({ dataKey: "moduleNo" })
  },
  {
    key: "baseTypeName",
    title: "基类信号",
    dataKey: "baseTypeName",
    width: 180,
    cellRenderer: createReadOnlyCell(
      { dataKey: "baseTypeName" },
      handleBaseTypeClick,
      "点击配置基类信号"
    ),
    headerCellRenderer: createFilterHeader({ dataKey: "baseTypeName" })
  }
];

// 组件初始化
onMounted(() => {
  // 初始化过滤器状态
  initFilterState();

  initializeDictionaryData();

  // 初始化表格尺寸
  updateTableSize();

  // 设置 ResizeObserver 监听容器尺寸变化
  const tabsContent = document.querySelector(
    ".el-tabs__content"
  ) as HTMLElement;
  if (tabsContent && window.ResizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      updateTableSize();
    });
    resizeObserver.observe(tabsContent);
  }

  // 监听窗口尺寸变化作为备选方案
  window.addEventListener("resize", updateTableSize);
});

// 监听模板数据变化
watch(
  () => props.templateData,
  newData => {
    if (newData && newData.template) {
      if (props.tabIndex === 1) {
        loadSignalList();
      }
    }
  },
  { immediate: true }
);

// 监听标签页变化
watch(
  () => props.tabIndex,
  newIndex => {
    if (newIndex === 1 && props.templateData) {
      loadSignalList();
    }
  }
);

// 初始化字典数据
const initializeDictionaryData = async () => {
  try {
    await Promise.all([
      loadSignalCategoryList(),
      loadSignalTypeList(),
      loadChannelTypeList(),
      loadDataTypeList(),
      loadSignalPropertyList(),
      loadBatteryDeviceCategory()
    ]);
  } catch (error) {
    console.error("初始化字典数据失败:", error);
  }
};

// 加载信号种类列表
const loadSignalCategoryList = async () => {
  try {
    const res = await getSignalCategoryList();
    if (res.code === 0) {
      signalCategoryList.value = res.data;
    }
  } catch (error) {
    console.error("获取信号种类列表失败:", error);
  }
};

// 加载信号分类列表
const loadSignalTypeList = async () => {
  try {
    const res = await getSignalTypeList();
    if (res.code === 0) {
      signalTypeList.value = res.data;
    }
  } catch (error) {
    console.error("获取信号分类列表失败:", error);
  }
};

// 加载通道类型列表
const loadChannelTypeList = async () => {
  try {
    const res = await getChannelTypeList();
    if (res.code === 0) {
      channelTypeList.value = res.data;
    }
  } catch (error) {
    console.error("获取通道类型列表失败:", error);
  }
};

// 加载数据类型列表
const loadDataTypeList = async () => {
  try {
    const res = await getDataTypeList();
    if (res.code === 0) {
      dataTypeList.value = res.data;
    }
  } catch (error) {
    console.error("获取数据类型列表失败:", error);
  }
};

// 加载信号属性列表
const loadSignalPropertyList = async () => {
  try {
    const res = await getSignalPropertyList();
    if (res.code === 0) {
      signalPropertyList.value = res.data;
    }
  } catch (error) {
    console.error("获取信号属性列表失败:", error);
  }
};

// 加载电池设备分类
const loadBatteryDeviceCategory = async () => {
  try {
    const res = await getBatteryDeviceCategory();
    if (res.code === 0) {
      batteryData.value = res.data;
    }
  } catch (error) {
    console.error("获取电池设备分类失败:", error);
  }
};

// 加载信号列表 - 需要前置声明
const loadSignalList = async () => {
  if (!props.templateData?.id) return;

  loading.value = true;
  try {
    let res;
    if (props.muCategory === 24 && props.equipmentId) {
      res = await getSignalListByTempIdEqId(
        props.templateData.id,
        props.equipmentId
      );
    } else {
      res = await getSignalList(props.templateData.id);
    }

    if (res.code === 0 && res.data) {
      processSignalData(res.data);
    } else {
      tableData.value = [];
    }
  } catch (error) {
    console.error("获取信号列表失败:", error);
    ElMessage.error("获取信号列表失败");
  } finally {
    loading.value = false;
  }
};

// 处理信号数据
const processSignalData = (data: SignalInfo[]) => {
  // 清理之前的原始数据缓存
  originalData.value.clear();

  data.forEach(item => {
    // 处理状态信号显示
    if (item.signalMeaningsList && item.signalMeaningsList.length > 0) {
      item.stateValue = item.signalMeaningsList
        .map((meaning: any) => meaning.stateValue)
        .join("/");
    }

    // 处理信号属性显示
    if (item.signalPropertyList && item.signalPropertyList.length > 0) {
      item.signalProperty = item.signalPropertyList
        .map((prop: any) => prop.signalPropertyId)
        .join(";");
    }

    // 初始化编辑状态
    item.signalName_editing = false;
    item.displayIndex_editing = false;
    item.signalCategory_editing = false;
    item.signalType_editing = false;
    item.channelNo_editing = false;
    item.channelType_editing = false;
    item.dataType_editing = false;
    item.showPrecision_editing = false;
    item.unit_editing = false;
    item.storeInterval_editing = false;
    item.enable_editing = false;
    item.visible_editing = false;
    item.description_editing = false;
    item.signalProperty_editing = false;
    item.moduleNo_editing = false;
  });

  tableData.value = data;

  // 数据加载后更新过滤器选项
  updateDynamicOptions();
};

// 行事件处理器
const rowEventHandlers = {
  onClick: ({ rowData, rowIndex, event }: any) => {
    // 单击时选中行
    const clickedRowIndex = filteredData.value.findIndex(
      item => item.signalId === rowData.signalId
    );
    if (clickedRowIndex !== -1) {
      // 如果按住Ctrl键，支持多选
      if (event.ctrlKey || event.metaKey) {
        const isSelected = selectedRowIndexes.value.includes(clickedRowIndex);
        if (isSelected) {
          // 取消选中
          selectedRowIndexes.value = selectedRowIndexes.value.filter(
            index => index !== clickedRowIndex
          );
        } else {
          // 添加选中
          selectedRowIndexes.value.push(clickedRowIndex);
        }
      } else {
        // 单选
        selectedRowIndexes.value = [clickedRowIndex];
      }

      // 更新选中的行数据
      selectedRows.value = selectedRowIndexes.value.map(
        index => filteredData.value[index]
      );
    }
  },
  onContextmenu: ({ rowData, rowIndex, event }: any) => {
    event.preventDefault();

    // 如果右键的是某一行，确保该行被选中
    const currentIndex = filteredData.value.findIndex(
      item => item.signalId === rowData.signalId
    );
    if (
      currentIndex !== -1 &&
      !selectedRowIndexes.value.includes(currentIndex)
    ) {
      selectedRowIndexes.value = [currentIndex];
      selectedRows.value = [rowData];
    }

    // 只有在非只读模板时才显示右键菜单
    if (props.isRootTemplate) return;

    showContextMenu(event);
  }
};

// 处理行选中
const handleRowSelect = (selectedIndexes: number[]) => {
  selectedRowIndexes.value = selectedIndexes;
  selectedRows.value = selectedIndexes.map(index => filteredData.value[index]);
};

// 显示右键菜单
const showContextMenu = (event: MouseEvent) => {
  const hasSelectedRows = selectedRowIndexes.value.length > 0;
  const hasOneSelectedRow = selectedRowIndexes.value.length === 1;
  ContextMenu.showContextMenu({
    zIndex: 10000,
    x: event.x,
    y: event.y,
    items: [
      {
        label: "增加信号",
        icon: "h:plus",
        onClick: () => {
          handleRowChangeConfirm("add");
        }
      },
      {
        label: "从模板增加信号",
        icon: "h:document-add",
        onClick: () => {
          handleRowChangeConfirm("addTemp");
        }
      },
      {
        label: "增加关联事件",
        icon: "h:document-add",
        onClick: () => {
          handleRowChangeConfirm("addRelateEvent");
        }
      },
      {
        label: "删除信号",
        icon: "h:delete",
        disabled: !hasSelectedRows,
        onClick: () => {
          handleRowChangeConfirm("delete");
        }
      },
      {
        label: "行拷贝并粘贴",
        icon: "h:document-duplicate",
        disabled: !hasSelectedRows,
        onClick: () => {
          handleRowChangeConfirm("copy");
        }
      }
      // {
      //   label: "拷贝表达式到...",
      //   icon: "h:document-duplicate",
      //   disabled: !hasOneSelectedRow,
      //   onClick: () => {
      //     handleCopyFieldTo('copyExpression');
      //   }
      // },
      // {
      //   label: "拷贝状态信号到...",
      //   icon: "h:document-duplicate",
      //   disabled: !hasOneSelectedRow,
      //   onClick: () => {
      //     handleCopyFieldTo('copySignalMeaning');
      //   }
      // }
    ]
  });
};

// 处理行变更确认
const handleRowChangeConfirm = async (type: string) => {
  if (!props.templateData?.id) {
    ElMessage.error("模板信息不存在");
    return;
  }

  // 从模板增加信号直接打开选择对话框
  if (type === "addTemp") {
    showTemplateSelector.value = true;
    return;
  }

  // 检查是否需要显示确认对话框
  if (!deviceTemplateService.getNotShowState()) {
    // 需要显示确认对话框
    pendingActionType.value = type;
    showConfirmDialog.value = true;
    return;
  }

  // 直接执行操作
  await executeAction(type);
};

// 执行具体操作
const executeAction = async (type: string, newTemplateId?: number) => {
  try {
    loading.value = true;
    const templateId = newTemplateId || props.templateData.id;

    switch (type) {
      case "add":
        await handleAddNewSignal(templateId);
        break;
      case "delete":
        await handleDeleteSignal(templateId);
        break;
      case "copy":
        await handleCopySignal(templateId);
        break;
      case "addRelateEvent":
        await handleAddRelateEvent(templateId);
        break;
      default:
        ElMessage.warning(`未知操作类型: ${type}`);
        return;
    }

    ElMessage.success("操作成功！");
    // 刷新数据
    await loadSignalList();

    // 如果是新模板，通知父组件刷新
    if (newTemplateId) {
      emit("refresh");
    }
  } catch (error) {
    console.error(`操作失败:`, error);
    ElMessage.error("操作失败");
  } finally {
    loading.value = false;
  }
};

// 增加新信号
const handleAddNewSignal = async (templateId: number) => {
  // 生成新的信号ID和显示顺序
  const maxSignalId =
    tableData.value.length > 0
      ? Math.max(...tableData.value.map(item => item.signalId), 0)
      : 0;
  const maxDisplayIndex =
    tableData.value.length > 0
      ? Math.max(...tableData.value.map(item => item.displayIndex), 0)
      : 0;

  const newSignal = {
    id: null,
    equipmentTemplateId: templateId,
    signalId: null,
    enable: true,
    visible: true,
    signalName: `新增信号${maxSignalId + 1}`,
    signalCategory: 0,
    signalType: 0,
    channelNo: 0,
    channelType: 0,
    displayIndex: maxDisplayIndex + 1,
    moduleNo: 0,
    expression: "",
    dataType: 0,
    showPrecision: "",
    unit: "",
    storeInterval: 86400,
    absValueThreshold: 0,
    percentThreshold: 0,
    staticsPeriod: 0,
    chargeStoreInterVal: 0,
    chargeAbsValue: 0,
    description: "",
    signalProperty: "",
    stateValue: "",
    baseTypeName: "",
    baseTypeId: null,
    acrossSignal: false,
    hasInstance: false
  };

  const res = await addSignal(newSignal);
  if (res.code !== 0) {
    throw new Error(res.msg || "添加信号失败");
  }
};

// 删除信号
const handleDeleteSignal = async (templateId: number) => {
  if (selectedRowIndexes.value.length === 0) {
    ElMessage.warning("请先选择要删除的信号！");
    return;
  }

  const signalIds = selectedRowIndexes.value
    .map(index => filteredData.value[index].signalId)
    .join(",");

  const res = await deleteSignal(templateId, signalIds);
  if (res.code !== 0) {
    throw new Error(res.msg || "删除信号失败");
  }

  // 清空选中状态
  selectedRowIndexes.value = [];
  selectedRows.value = [];
};

// 复制信号
const handleCopySignal = async (templateId: number) => {
  if (selectedRowIndexes.value.length === 0) {
    ElMessage.warning("请先选择要复制的信号！");
    return;
  }

  const selectedSignals = selectedRowIndexes.value.map(
    index => filteredData.value[index]
  );
  const maxDisplayIndex =
    tableData.value.length > 0
      ? Math.max(...tableData.value.map(item => item.displayIndex), 0)
      : 0;
  const maxChannelNo =
    tableData.value.length > 0
      ? Math.max(...tableData.value.map(item => item.channelNo), 0)
      : 0;

  for (let i = 0; i < selectedSignals.length; i++) {
    const originalSignal = selectedSignals[i];

    // 生成复制名称（按Angular版本的逻辑）
    let copyIndex = 1;
    let copyName = `${originalSignal.signalName}${copyIndex}#`;
    while (tableData.value.some(item => item.signalName === copyName)) {
      copyIndex++;
      copyName = `${originalSignal.signalName}${copyIndex}#`;
    }

    const copiedSignal = {
      ...originalSignal,
      id: null,
      signalId: null, // 后端会自动生成新的signalId
      equipmentTemplateId: templateId,
      signalName: copyName,
      displayIndex: maxDisplayIndex + i + 1,
      channelNo: maxChannelNo + i + 1,
      // 清除一些不应该复制的字段，保持和原始信号相同的结构但移除ID相关字段
      signalMeaningsList: originalSignal.signalMeaningsList ? [] : undefined,
      signalPropertyList: originalSignal.signalPropertyList ? [] : undefined
    };

    // 移除编辑状态字段
    Object.keys(copiedSignal).forEach(key => {
      if (key.endsWith("_editing")) {
        delete (copiedSignal as any)[key];
      }
    });

    const res = await addSignal(copiedSignal);
    if (res.code !== 0) {
      throw new Error(res.msg || `复制第${i + 1}个信号失败`);
    }
  }
};

// 增加关联事件
const handleAddRelateEvent = async (templateId: number) => {
  if (selectedRowIndexes.value.length === 0) {
    ElMessage.warning("请先选择信号！");
    return;
  }

  // 只处理第一个选中的信号
  const signalId = filteredData.value[selectedRowIndexes.value[0]].signalId;

  const res = await addRelateEvent(templateId, signalId);
  if (res.code !== 0) {
    throw new Error(res.msg || "增加关联事件失败");
  }

  // 切换到事件标签页
  emit("selectTab", { index: 2 });
};

// 处理从模板选择信号确认
const handleTemplateSelectorConfirm = async (data: {
  templateId: number;
  selectedSignal: SignalInfo;
}) => {
  if (!props.templateData?.id) {
    ElMessage.error("模板信息不存在");
    return;
  }

  try {
    loading.value = true;

    // 准备新信号数据
    const newSignal = {
      ...data.selectedSignal,
      id: null,
      signalId: null, // 后端会自动生成新的signalId
      equipmentTemplateId: props.templateData.id,
      // 清除一些不应该复制的字段
      signalMeaningsList: undefined,
      signalPropertyList: undefined,
      acrossSignal: false,
      hasInstance: false
    };

    // 移除编辑状态字段
    Object.keys(newSignal).forEach(key => {
      if (key.endsWith("_editing")) {
        delete (newSignal as any)[key];
      }
    });

    const res = await addSignal(newSignal);
    if (res.code === 0) {
      ElMessage.success("从模板添加信号成功！");
      // 刷新数据
      await loadSignalList();
    } else {
      ElMessage.error(res.msg || "从模板添加信号失败");
    }
  } catch (error) {
    console.error("从模板添加信号失败:", error);
    ElMessage.error("从模板添加信号失败");
  } finally {
    loading.value = false;
  }
};

// 处理从模板选择信号取消
const handleTemplateSelectorCancel = () => {
  // 取消操作，无需特殊处理
};

// 处理字段拷贝到...（占位函数）
const handleCopyFieldTo = (type: string) => {
  console.log("Copy field to:", type, selectedRowIndexes.value);
  ElMessage.info(`${type} 功能待实现`);
};

// 获取行样式类
const getRowClass = ({ rowIndex }: { rowIndex: number }) => {
  const isSelected = selectedRowIndexes.value.includes(rowIndex);
  return isSelected ? "selected-row" : "";
};

// 组件卸载时清理监听器
onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
  // 清理原始数据缓存
  originalData.value.clear();
  // 清理选中状态
  selectedRowIndexes.value = [];
  selectedRows.value = [];
  window.removeEventListener("resize", updateTableSize);
});

// 暴露方法给父组件
defineExpose({
  loadSignalList
});
</script>

<style scoped>
.device-template-signal {
  width: 100%;
  height: 100%;
}

.signal-table-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* 可编辑单元格样式 */
:deep(.table-v2-inline-editing-trigger) {
  border: 1px transparent dotted;
  cursor: pointer;
  min-width: 55px; /* 确保占满整个单元格宽度 */
}

:deep(.table-v2-inline-editing-trigger:hover) {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

/* 只读单元格样式 */
.read-only-cell {
  padding: 4px 8px;
  min-height: 24px;
  line-height: 24px;
  color: var(--el-text-color-placeholder);
}

/* 可点击的只读单元格样式 */
.read-only-clickable-cell {
  padding: 4px 8px;
  min-height: 24px;
  line-height: 24px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid transparent;
}

.read-only-clickable-cell:hover {
  background-color: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary-light-5);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.read-only-clickable-cell:hover .edit-hint {
  opacity: 1;
}

.read-only-clickable-cell .edit-hint {
  opacity: 0.6;
  transition: opacity 0.2s;
  font-size: 12px;
  color: var(--el-color-primary);
  flex-shrink: 0;
}

.read-only-clickable-cell .cell-content {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 过滤器样式 */
.el-table-v2__demo-filter {
  border-top: var(--el-border);
  margin: 12px -12px -12px;
  padding: 0 12px;
  display: flex;
  justify-content: space-between;
}

.filter-wrapper {
  padding: 8px 0;
}

.filter-group {
  margin-bottom: 8px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.text-xs {
  font-size: 12px;
}

.mr-2 {
  margin-right: 8px;
}

.cursor-pointer {
  cursor: pointer;
}

/* 全局表格样式 */
:deep(.el-table) {
  font-size: 12px;
}

:deep(.el-table .cell) {
  padding: 4px 8px;
}

:deep(.el-input__inner) {
  font-size: 12px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-checkbox) {
  margin: 0;
}

/* 编辑状态下的输入框样式 */
:deep(.el-input--small) {
  font-size: 12px;
}

:deep(.el-input--small .el-input__inner) {
  height: 28px;
  line-height: 28px;
}

:deep(.el-select--small) {
  font-size: 12px;
}

/* 确认对话框样式 */
:deep(.el-dialog) {
  border-radius: 8px;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  border-bottom: 1px solid var(--el-border-color-light);
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 16px 20px;
  border-top: 1px solid var(--el-border-color-light);
}

/* 选中行样式 */
:deep(.selected-row) {
  background-color: var(--el-color-primary-light-9) !important;
}

:deep(.selected-row:hover) {
  background-color: var(--el-color-primary-light-8) !important;
}
</style>
