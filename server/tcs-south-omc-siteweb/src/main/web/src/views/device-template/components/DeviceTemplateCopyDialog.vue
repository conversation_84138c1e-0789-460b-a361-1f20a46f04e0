<template>
  <el-dialog
    v-model="visible"
    title="新模板创建"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      label-position="left"
    >
      <el-form-item label="新模板名称" prop="name" required>
        <el-input
          v-model="form.name"
          placeholder="请输入新模板名称"
          clearable
        />
      </el-form-item>
      
      <el-form-item label="创建原因" prop="reason" required>
        <el-input
          v-model="form.reason"
          placeholder="请输入创建原因"
          type="textarea"
          :rows="3"
          clearable
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleConfirm">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import { copyTemplate } from '@/api/device-template';

interface Props {
  modelValue: boolean;
  templateId?: number;
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'confirm', data: { newTemplateId: number; name: string; reason: string }): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  templateId: 0
});

const emit = defineEmits<Emits>();

// 状态变量
const loading = ref(false);
const formRef = ref<FormInstance>();

// 表单数据
const form = ref({
  name: '',
  reason: ''
});

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入新模板名称', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  reason: [
    { required: true, message: '请输入创建原因', trigger: 'blur' },
    { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' }
  ]
};

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
});

// 监听对话框显示状态，重置表单
watch(visible, (newValue) => {
  if (newValue) {
    resetForm();
  }
});

// 重置表单
const resetForm = () => {
  form.value = {
    name: '',
    reason: ''
  };
  formRef.value?.clearValidate();
};

// 处理对话框关闭
const handleClose = () => {
  visible.value = false;
  resetForm();
};

// 处理取消
const handleCancel = () => {
  handleClose();
};

// 处理确认
const handleConfirm = async () => {
  if (!formRef.value) return;
  
  try {
    // 验证表单
    const valid = await formRef.value.validate();
    if (!valid) return;
    
    if (!props.templateId) {
      ElMessage.error('模板ID不能为空');
      return;
    }
    
    loading.value = true;
    
    // 调用复制模板接口
    const params = {
      originEquipmentTemplateId: props.templateId,
      newEquipmentTemplateName: form.value.name,
      reason: form.value.reason
    };
    
    const res = await copyTemplate(params);
    if (res.code === 0 && res.data) {
      ElMessage.success('模板复制成功');
      emit('confirm', {
        newTemplateId: res.data,
        name: form.value.name,
        reason: form.value.reason
      });
      handleClose();
    } else {
      ElMessage.error(res.msg || '模板复制失败');
    }
  } catch (error) {
    console.error('复制模板失败:', error);
    ElMessage.error('复制模板失败');
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style> 