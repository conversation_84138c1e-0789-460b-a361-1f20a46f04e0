<template>
  <el-dialog
    v-model="dialogVisible"
    title="选择设备模板"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
  >
    <!-- 搜索框 -->
    <div class="mb-4">
      <el-input
        v-model="searchText"
        placeholder="请输入关键字查询..."
        clearable
        :prefix-icon="Search"
        @input="handleSearch"
      />
    </div>

    <!-- 可选择的树形表格 -->
    <div class="template-table-container">
      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="filteredTemplates"
        :height="330"
        stripe
        row-key="id"
        :tree-props="treeProps"
        :default-expand-all="true"
        highlight-current-row
        @current-change="handleCurrentChange"
        @row-click="handleRowClick"
      >
        <el-table-column prop="name" label="模板名称" />
        <el-table-column prop="id" label="模板ID" />

        <!-- 空状态 -->
        <template #empty>
          <div class="text-center py-8">
            <el-icon size="48" class="text-gray-400 mb-4">
              <Files />
            </el-icon>
            <p class="text-gray-500">暂无模板数据</p>
          </div>
        </template>
      </el-table>
    </div>

    <template #footer>
      <div class="flex justify-between items-center">
        <div class="flex space-x-3">
          <el-button
            type="primary"
            :disabled="isTotal"
            @click="showAllTemplates"
          >
            显示全部模板
          </el-button>
          <el-button
            type="primary"
            :disabled="!isTotal"
            @click="showCategoryTemplates"
          >
            显示同类模板
          </el-button>
        </div>
        <div class="flex space-x-3">
          <el-button @click="closeDialog">
            取消
          </el-button>
          <el-button
            type="primary"
            :disabled="selectedTemplates.length === 0"
            @click="confirmSelection"
            :loading="updating"
          >
            {{ updating ? '更新中...' : '确定' }}
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Folder, Document, Files } from '@element-plus/icons-vue'
import { 
  getTemplateTreeByCategory, 
  getTotalTemplateTree, 
  getTemplateDllByIds, 
  type TemplateTreeNode 
} from '@/api/device-template'
import { debounce } from 'lodash'

// Props
interface Props {
  visible: boolean
  originTemplate: TemplateTreeNode | null
  selectedDeviceIds: string[]
  equipmentCategory?: string
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  originTemplate: null,
  selectedDeviceIds: () => [],
  equipmentCategory: ''
})

// Emits
const emit = defineEmits<{
  'update:visible': [visible: boolean]
  'confirm': [templateData: { originTemplate: TemplateTreeNode, newTemplate: TemplateTreeNode, deviceIds: string[] }]
  'show-effect-dialog': [templateData: { originTemplate: TemplateTreeNode, newTemplate: TemplateTreeNode, deviceIds: string[] }]
}>()

// 响应式数据
const loading = ref(false)
const updating = ref(false)
const searchText = ref('')
const templates = ref<TemplateTreeNode[]>([])
const selectedTemplates = ref<TemplateTreeNode[]>([])
const isTotal = ref(false)
const tableRef = ref()

// 树形表格配置
const treeProps = reactive({
  checkStrictly: false,
  children: 'children',
  hasChildren: 'hasChildren'
})

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 过滤后的模板列表（直接使用树形数据）
const filteredTemplates = computed(() => {
  return templates.value
})

// 监听对话框显示状态
watch(() => props.visible, (visible) => {
  if (visible) {
    loadTemplates()
    resetSelection()
  }
})

// 重置选择状态
const resetSelection = () => {
  selectedTemplates.value = []
  searchText.value = ''
  isTotal.value = false
}

// 加载模板数据
const loadTemplates = async (showAll = false) => {
  try {
    loading.value = true
    
    let response
    if (showAll) {
      // 显示全部模板
      response = await getTotalTemplateTree(searchText.value)
      isTotal.value = true
    } else {
      // 显示同类模板
      response = await getTemplateTreeByCategory(props.equipmentCategory || '', searchText.value)
      isTotal.value = false
    }
    
    if (response.code === 0) {
      templates.value = response.data || []
    } else {
      ElMessage.error(response.msg || '获取模板列表失败')
      templates.value = []
    }
  } catch (error) {
    console.error('获取模板列表失败:', error)
    ElMessage.error('获取模板列表失败')
    templates.value = []
  } finally {
    loading.value = false
  }
}

// 搜索处理（防抖）
const handleSearch = debounce(() => {
  // 重新加载数据以应用搜索
  loadTemplates(isTotal.value)
}, 300)

// 处理当前行变化（单选）
const handleCurrentChange = (currentRow: TemplateTreeNode | null) => {
  if (currentRow && currentRow.template === true) {
    selectedTemplates.value = [currentRow]
  } else {
    selectedTemplates.value = []
  }
}

// 处理行点击事件
const handleRowClick = (row: TemplateTreeNode) => {
  if (row.template === true) {
    selectedTemplates.value = [row]
  }
}

// 显示全部模板
const showAllTemplates = () => {
  loadTemplates(true)
}

// 显示同类模板
const showCategoryTemplates = () => {
  loadTemplates(false)
}

// 确认选择
const confirmSelection = async () => {
  if (selectedTemplates.value.length === 0 || !props.originTemplate) {
    ElMessage.warning('请选择一个模板')
    return
  }
  
  const selectedTemplate = selectedTemplates.value[0]
  
  // 检查是否选择了相同的模板
  if (selectedTemplate.id === props.originTemplate.id) {
    ElMessage.warning('选择了相同模板！')
    return
  }
  
  try {
    updating.value = true
    
    // 检查模板DLL差异
    await checkTemplateDllDifference(selectedTemplate)
    
  } catch (error) {
    updating.value = false
    if (error !== 'cancel') {
      console.error('模板切换失败:', error)
    }
  }
}

// 检查模板DLL差异
const checkTemplateDllDifference = async (selectedTemplate: TemplateTreeNode) => {
  try {
    const ids = `${selectedTemplate.id},${props.originTemplate!.id}`
    const response = await getTemplateDllByIds(ids)
    
    if (response.code === 0 && response.data && response.data.length >= 2) {
      const newTempDll = response.data[0]
      const originTempDll = response.data[1]
      
      if (newTempDll !== originTempDll) {
        // DLL不同，需要用户确认
        await ElMessageBox.confirm(
          `模板切换前后的采集库动态库名称分别为${originTempDll}和${newTempDll}，是否确认切换模板？`,
          '提示',
          {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
      }
    }
    
    // 继续确认流程 - 显示影响对话框
    await finalConfirmation(selectedTemplate)
    
  } catch (error) {
    updating.value = false
    if (error !== 'cancel') {
      ElMessage.error('获取模板信息失败')
      console.error(error)
    }
    throw error
  }
}

// 最终确认 - 显示模板切换影响弹框
const finalConfirmation = async (selectedTemplate: TemplateTreeNode) => {
  try {
    // 显示模板切换影响弹框
    showTemplateEffectDialog(selectedTemplate)
  } finally {
    updating.value = false
  }
}

// 显示模板切换影响对话框
const showTemplateEffectDialog = (selectedTemplate: TemplateTreeNode) => {
  emit('show-effect-dialog', {
    originTemplate: props.originTemplate!,
    newTemplate: selectedTemplate,
    deviceIds: props.selectedDeviceIds
  })
}

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.template-table-container {
  border-radius: 6px;
  overflow: hidden;
}

/* 表格样式 */
:deep(.el-table th.el-table__cell) {
  background-color: var(--el-fill-color-lighter);
  padding: 12px 0;
}

:deep(.el-table td.el-table__cell) {
  padding: 8px 12px;
}

:deep(.el-table--border) {
  border: 1px solid var(--el-border-color);
}

:deep(.el-table--border th) {
  border-right: 1px solid var(--el-border-color);
}

:deep(.el-table--border td) {
  border-right: 1px solid var(--el-border-color);
}

/* 当前行高亮 */
:deep(.el-table__row.current-row) {
  background-color: var(--el-color-primary-light-9);
}

:deep(.el-table__row.current-row td) {
  color: var(--el-color-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .template-table-container {
    font-size: 12px;
  }
}
</style> 