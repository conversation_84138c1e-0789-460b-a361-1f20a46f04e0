<template>
  <el-dialog
    v-model="visible"
    :title="getDialogTitle"
    width="900px"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="template-selector-container">
      <!-- 左侧模板树 -->
      <div class="template-tree-panel">
        <el-tree
          ref="treeRef"
          :data="treeData"
          :props="treeProps"
          :default-expand-all="true"
          :highlight-current="true"
          node-key="id"
          :expand-on-click-node="false"
          @node-click="handleNodeClick"
        />
      </div>

      <!-- 右侧数据列表 -->
      <div class="data-list-panel">
        <!-- 表格工具栏 -->
        <div class="table-toolbar">
          <el-input
            v-model="globalFilter"
            placeholder="搜索..."
            size="small"
            clearable
            style="width: 200px"
            @input="handleGlobalFilterChange"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="filteredTableData"
          height="350"
          highlight-current-row
          @current-change="handleRowSelect"
        >
          <!-- 信号列 -->
          <template v-if="type === 0">
            <el-table-column
              prop="signalId"
              label="信号ID"
              width="100"
              align="center"
            >
              <template #header>
                <div class="filter-header">
                  <span>信号ID</span>
                  <el-input
                    v-model="idFilter"
                    placeholder="筛选"
                    size="small"
                    clearable
                    class="filter-input"
                    @input="handleFilterChange"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="signalName" label="信号名称" min-width="150">
              <template #header>
                <div class="filter-header">
                  <span>信号名称</span>
                  <el-input
                    v-model="nameFilter"
                    placeholder="筛选"
                    size="small"
                    clearable
                    class="filter-input"
                    @input="handleFilterChange"
                  />
                </div>
              </template>
            </el-table-column>
          </template>

          <!-- 控制列 -->
          <template v-else-if="type === 2">
            <el-table-column
              prop="controlId"
              label="控制ID"
              width="100"
              align="center"
            >
              <template #header>
                <div class="filter-header">
                  <span>控制ID</span>
                  <el-input
                    v-model="idFilter"
                    placeholder="筛选"
                    size="small"
                    clearable
                    class="filter-input"
                    @input="handleFilterChange"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="controlName"
              label="控制名称"
              min-width="150"
            >
              <template #header>
                <div class="filter-header">
                  <span>控制名称</span>
                  <el-input
                    v-model="nameFilter"
                    placeholder="筛选"
                    size="small"
                    clearable
                    class="filter-input"
                    @input="handleFilterChange"
                  />
                </div>
              </template>
            </el-table-column>
          </template>

          <!-- 事件列 -->
          <template v-else-if="type === 1">
            <el-table-column
              prop="eventId"
              label="事件ID"
              width="100"
              align="center"
            >
              <template #header>
                <div class="filter-header">
                  <span>事件ID</span>
                  <el-input
                    v-model="idFilter"
                    placeholder="筛选"
                    size="small"
                    clearable
                    class="filter-input"
                    @input="handleFilterChange"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="eventName" label="事件名称" min-width="150">
              <template #header>
                <div class="filter-header">
                  <span>事件名称</span>
                  <el-input
                    v-model="nameFilter"
                    placeholder="筛选"
                    size="small"
                    clearable
                    class="filter-input"
                    @input="handleFilterChange"
                  />
                </div>
              </template>
            </el-table-column>
          </template>
        </el-table>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from "vue";
import {
  ElMessage,
  ElDialog,
  ElTree,
  ElTable,
  ElTableColumn,
  ElButton,
  ElInput,
  ElIcon
} from "element-plus";
import { Search } from "@element-plus/icons-vue";
import {
  getTemplateInfoById,
  getTemplateTreeByCategory,
  getSignalList,
  getTemplateEventById,
  getEquipmentTemplateControl,
  type TemplateTreeNode,
  type SignalInfo,
  type EventInfo,
  type ControlInfo
} from "@/api/device-template";

// 联合类型定义
type DataItem = SignalInfo | EventInfo | ControlInfo;

// 组件接口定义
interface Props {
  modelValue: boolean;
  type: 0 | 1 | 2; // 0: 信号, 1: 事件, 2: 控制
  equipmentCategory: number;
  originTemplateId: number;
  originSignalList: DataItem[];
  protocolCode?: string;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
  (e: "confirm", data: { templateId: number; selectedSignal: DataItem }): void;
  (e: "cancel"): void;
}

const props = withDefaults(defineProps<Props>(), {
  type: 0,
  equipmentCategory: 0,
  originTemplateId: 0,
  originSignalList: () => [],
  protocolCode: ""
});

const emit = defineEmits<Emits>();

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: value => emit("update:modelValue", value)
});

const treeRef = ref();
const tableRef = ref();
const treeData = ref<TemplateTreeNode[]>([]);
const tableData = ref<DataItem[]>([]);
const tableLoading = ref(false);
const selectedTemplateId = ref<number>(0);
const selectedSignal = ref<DataItem | null>(null);

// 筛选相关
const idFilter = ref("");
const nameFilter = ref("");
const globalFilter = ref("");

// 获取对话框标题
const getDialogTitle = computed(() => {
  switch (props.type) {
    case 0:
      return "从模板增加信号";
    case 1:
      return "从模板增加事件";
    case 2:
      return "从模板增加控制";
    default:
      return "从模板增加";
  }
});

// 筛选后的表格数据
const filteredTableData = computed(() => {
  let data = tableData.value;

  // 全局筛选
  if (globalFilter.value.trim()) {
    const filter = globalFilter.value.trim().toLowerCase();
    data = data.filter(item => {
      if (props.type === 0) {
        const signal = item as SignalInfo;
        return (
          signal.signalId?.toString().toLowerCase().includes(filter) ||
          signal.signalName?.toLowerCase().includes(filter)
        );
      } else if (props.type === 1) {
        const event = item as EventInfo;
        return (
          event.eventId?.toString().toLowerCase().includes(filter) ||
          event.eventName?.toLowerCase().includes(filter)
        );
      } else if (props.type === 2) {
        const control = item as ControlInfo;
        return (
          control.controlId?.toString().toLowerCase().includes(filter) ||
          control.controlName?.toLowerCase().includes(filter)
        );
      }
      return false;
    });
  }

  // 按ID筛选（列级筛选）
  if (idFilter.value.trim()) {
    const filter = idFilter.value.trim().toLowerCase();
    data = data.filter(item => {
      if (props.type === 0) {
        return (item as SignalInfo).signalId
          ?.toString()
          .toLowerCase()
          .includes(filter);
      } else if (props.type === 1) {
        return (item as EventInfo).eventId
          ?.toString()
          .toLowerCase()
          .includes(filter);
      } else if (props.type === 2) {
        return (item as ControlInfo).controlId
          ?.toString()
          .toLowerCase()
          .includes(filter);
      }
      return false;
    });
  }

  // 按名称筛选（列级筛选）
  if (nameFilter.value.trim()) {
    const filter = nameFilter.value.trim().toLowerCase();
    data = data.filter(item => {
      if (props.type === 0) {
        return (item as SignalInfo).signalName?.toLowerCase().includes(filter);
      } else if (props.type === 1) {
        return (item as EventInfo).eventName?.toLowerCase().includes(filter);
      } else if (props.type === 2) {
        return (item as ControlInfo).controlName
          ?.toLowerCase()
          .includes(filter);
      }
      return false;
    });
  }

  return data;
});

// 树组件配置
const treeProps = {
  children: "children",
  label: "name",
  isLeaf: "isLeaf"
};

// 监听对话框显示状态
watch(visible, async newVal => {
  if (newVal) {
    await initializeData();
  } else {
    // 重置数据
    resetData();
  }
});

// 初始化数据
const initializeData = async () => {
  try {
    // 获取原模板信息
    const deviceInfo = await getTemplateInfoById(
      props.originTemplateId.toString()
    );
    if (deviceInfo.code !== 0) {
      ElMessage.error("获取模板信息失败");
      return;
    }

    // 获取同类型模板树
    const protocolCode =
      props.protocolCode || deviceInfo.data.protocolCode || "";
    const treeRes = await getTemplateTreeByCategory(
      props.equipmentCategory.toString(),
      ""
    );

    if (treeRes.code === 0 && treeRes.data) {
      treeData.value = buildTreeData(treeRes.data);

      // 默认选中第一个节点
      await nextTick();
      if (treeData.value.length > 0) {
        const firstNode = findFirstLeafNode(treeData.value[0]);
        if (firstNode) {
          treeRef.value?.setCurrentKey(firstNode.id);
          await handleNodeClick(firstNode);
        }
      }
    }
  } catch (error) {
    console.error("初始化数据失败:", error);
    ElMessage.error("初始化数据失败");
  }
};

// 构建树数据
const buildTreeData = (nodes: any[]): TemplateTreeNode[] => {
  return nodes.map(node => {
    const treeNode: TemplateTreeNode = {
      id: node.id.toString(),
      name: node.name,
      title: node.name,
      key: node.id.toString(),
      template: node.template || false,
      equipmentCategory: node.equipmentCategory,
      parentId: node.parentId,
      isLeaf: !node.children || node.children.length === 0,
      expanded: true
    };

    if (node.children && node.children.length > 0) {
      treeNode.children = buildTreeData(node.children);
    }

    return treeNode;
  });
};

// 查找第一个叶子节点
const findFirstLeafNode = (node: TemplateTreeNode): TemplateTreeNode | null => {
  if (node.isLeaf) {
    return node;
  }
  if (node.children && node.children.length > 0) {
    return findFirstLeafNode(node.children[0]);
  }
  return null;
};

// 处理树节点点击
const handleNodeClick = async (node: TemplateTreeNode) => {
  const templateId = parseInt(node.id);
  selectedTemplateId.value = templateId;

  // 如果是原模板，清空表格
  if (templateId === props.originTemplateId) {
    tableData.value = [];
    return;
  }

  // 根据类型加载对应数据
  if (props.type === 0) {
    await loadSignalList(templateId);
  } else if (props.type === 1) {
    await loadEventList(templateId);
  } else if (props.type === 2) {
    await loadControlList(templateId);
  }
};

// 加载信号列表
const loadSignalList = async (templateId: number) => {
  try {
    tableLoading.value = true;
    const res = await getSignalList(templateId);

    if (res.code === 0 && res.data) {
      tableData.value = res.data;
    } else {
      tableData.value = [];
      ElMessage.error(res.msg || "获取信号列表失败");
    }
  } catch (error) {
    console.error("获取信号列表失败:", error);
    tableData.value = [];
    ElMessage.error("获取信号列表失败");
  } finally {
    tableLoading.value = false;
  }
};

// 加载事件列表
const loadEventList = async (templateId: number) => {
  try {
    tableLoading.value = true;
    const res = await getTemplateEventById(templateId);

    if (res.code === 0 && res.data) {
      tableData.value = res.data;
    } else {
      tableData.value = [];
      ElMessage.error(res.msg || "获取事件列表失败");
    }
  } catch (error) {
    console.error("获取事件列表失败:", error);
    tableData.value = [];
    ElMessage.error("获取事件列表失败");
  } finally {
    tableLoading.value = false;
  }
};

// 加载控制列表
const loadControlList = async (templateId: number) => {
  try {
    tableLoading.value = true;
    const res = await getEquipmentTemplateControl(templateId);

    if (res.code === 0 && res.data) {
      tableData.value = res.data;
    } else {
      tableData.value = [];
      ElMessage.error(res.msg || "获取控制列表失败");
    }
  } catch (error) {
    console.error("获取控制列表失败:", error);
    tableData.value = [];
    ElMessage.error("获取控制列表失败");
  } finally {
    tableLoading.value = false;
  }
};

// 处理行选择
const handleRowSelect = (row: DataItem) => {
  selectedSignal.value = row;
};

// 处理筛选变化
const handleFilterChange = () => {
  // 筛选后清除当前选中的数据（如果它不在筛选结果中）
  if (selectedSignal.value) {
    const isInFilteredData = filteredTableData.value.some(item => {
      if (props.type === 0) {
        return (
          (item as SignalInfo).signalId ===
          (selectedSignal.value as SignalInfo).signalId
        );
      } else if (props.type === 1) {
        return (
          (item as EventInfo).eventId ===
          (selectedSignal.value as EventInfo).eventId
        );
      } else if (props.type === 2) {
        return (
          (item as ControlInfo).controlId ===
          (selectedSignal.value as ControlInfo).controlId
        );
      }
      return false;
    });
    if (!isInFilteredData) {
      selectedSignal.value = null;
      tableRef.value?.setCurrentRow();
    }
  }
};

// 处理全局筛选变化
const handleGlobalFilterChange = () => {
  handleFilterChange();
};

// 获取ID和名称字段
const getIdField = () => {
  switch (props.type) {
    case 0:
      return "signalId";
    case 1:
      return "eventId";
    case 2:
      return "controlId";
    default:
      return "id";
  }
};

const getNameField = () => {
  switch (props.type) {
    case 0:
      return "signalName";
    case 1:
      return "eventName";
    case 2:
      return "controlName";
    default:
      return "name";
  }
};

const getDataTypeName = () => {
  switch (props.type) {
    case 0:
      return "信号";
    case 1:
      return "事件";
    case 2:
      return "控制";
    default:
      return "数据";
  }
};

// 处理确认
const handleConfirm = () => {
  if (!selectedSignal.value) {
    ElMessage.warning("请选择一条数据");
    return;
  }

  const idField = getIdField();
  const nameField = getNameField();
  const dataTypeName = getDataTypeName();

  // 检查是否已存在相同数据
  const exists = props.originSignalList.find(item => {
    return (item as any)[idField] === (selectedSignal.value as any)[idField];
  });

  if (exists) {
    const existsName = (exists as any)[nameField];
    ElMessage.warning(`原模板已具有${dataTypeName}${existsName}，请重新选择`);
    return;
  }

  // 发送确认事件
  emit("confirm", {
    templateId: selectedTemplateId.value,
    selectedSignal: selectedSignal.value
  });

  // 关闭对话框
  visible.value = false;
};

// 处理取消
const handleCancel = () => {
  emit("cancel");
  visible.value = false;
};

// 处理对话框关闭
const handleClose = () => {
  visible.value = false;
};

// 重置数据
const resetData = () => {
  treeData.value = [];
  tableData.value = [];
  selectedTemplateId.value = 0;
  selectedSignal.value = null;
  tableLoading.value = false;
  // 清除筛选条件
  idFilter.value = "";
  nameFilter.value = "";
  globalFilter.value = "";
};

// 暴露方法
defineExpose({
  resetData
});
</script>

<style scoped>
.template-selector-container {
  display: flex;
  justify-content: space-between;
  height: 400px;
  gap: 20px;
}

.template-tree-panel {
  border: 1px solid var(--el-border-color);
  width: calc(50% - 10px);
  overflow: auto;
  padding: 8px;
  border-radius: 4px;
}

.data-list-panel {
  border: 1px solid var(--el-border-color);
  width: calc(50% - 10px);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
}

.table-toolbar {
  padding: 8px 12px;
  border-bottom: 1px solid var(--el-border-color-light);
  background-color: var(--el-bg-color-page);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 树组件样式 */
:deep(.el-tree) {
  background: transparent;
}

:deep(.el-tree-node__content) {
  height: 32px;
  line-height: 32px;
}

:deep(.el-tree-node__content:hover) {
  background-color: var(--el-color-primary-light-9);
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: var(--el-color-primary-light-8);
  color: var(--el-color-primary);
}

/* 表格样式 */
:deep(.el-table) {
  border: none;
}

:deep(.el-table__header) {
  background-color: var(--el-bg-color-page);
}

:deep(.el-table tr.current-row td) {
  background-color: var(--el-color-primary-light-9) !important;
}

/* 筛选头部样式 */
.filter-header {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
}

.filter-header span {
  font-weight: 500;
  color: var(--el-text-color-primary);
  font-size: 14px;
}
</style>
