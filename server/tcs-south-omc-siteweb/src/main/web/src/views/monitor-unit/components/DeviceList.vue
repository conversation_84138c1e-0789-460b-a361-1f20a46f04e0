<template>
  <div class="device-list-container">
    <div class="table-header">
      <div class="filter-row">
        <div class="filter-item">
          <span class="filter-label">设备名称</span>
          <el-input
            v-model="filters.equipmentName"
            placeholder="输入设备名称"
            size="small"
            clearable
            @input="handleFilterChange"
          />
        </div>
        <div class="filter-item">
          <span class="filter-label">所属监控单元</span>
          <el-input
            v-model="filters.monitorUnitName"
            placeholder="输入监控单元名称"
            size="small"
            clearable
            @input="handleFilterChange"
          />
        </div>
        <div class="filter-item">
          <span class="filter-label">端口</span>
          <el-input
            v-model="filters.portName"
            placeholder="输入端口"
            size="small"
            clearable
            @input="handleFilterChange"
          />
        </div>
        <div class="filter-item">
          <span class="filter-label">最后修改时间</span>
          <el-input
            v-model="filters.updateTime"
            placeholder="输入时间"
            size="small"
            clearable
            @input="handleFilterChange"
          />
        </div>
      </div>
    </div>

    <div class="table-content" @contextmenu="handleTableContextMenu">
      <el-table
        v-loading="loading"
        :data="filteredDevices"
        height="100%"
        stripe
        highlight-current-row
        @current-change="handleCurrentChange"
        @row-contextmenu="handleRowContextMenu"
      >
        <el-table-column label="设备名称" min-width="150">
          <template #default="{ row }">
            <span
              class="device-name-link"
              :title="row.equipmentName"
              @click="handleDeviceNameClick(row)"
            >
              {{ row.equipmentName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="monitorUnitName"
          label="所属监控单元"
          min-width="150"
        />
        <el-table-column prop="portName" label="端口" width="100" />
        <el-table-column label="最后修改时间" width="300">
          <template #default="{ row }">
            {{ formatDateTime(row.updateTime) }}
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 新增设备弹框 -->
    <el-dialog
      v-model="showAddDeviceDialog"
      title="新增设备"
      width="800px"
      :before-close="handleAddDeviceCancel"
    >
      <AddDeviceForm
        ref="addDeviceFormRef"
        :monitor-unit="props.monitorUnit"
        :current-node="currentNode"
        :mu-list="muList"
        @success="handleAddDeviceSuccess"
      />
      <template #footer>
        <el-button @click="handleAddDeviceCancel">取消</el-button>
        <el-button type="primary" @click="handleAddDeviceConfirm"
          >确定</el-button
        >
      </template>
    </el-dialog>

    <!-- 重新关联模板弹框 -->
    <el-dialog
      v-model="showSelectTemplateDialog"
      title="选择模板"
      width="800px"
      :before-close="handleSelectTemplateCancel"
    >
      <SelectTemplateForm
        ref="selectTemplateFormRef"
        @confirm="handleSelectTemplateConfirm"
      />
      <template #footer>
        <el-button @click="handleSelectTemplateCancel">取消</el-button>
        <el-button type="primary" @click="handleSelectTemplateSubmit"
          >确定</el-button
        >
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Edit,
  Delete,
  Plus,
  Refresh,
  FolderOpened
} from "@element-plus/icons-vue";
import ContextMenu from "@imengyu/vue3-context-menu";
import {
  getMonitorUnitDevices,
  deleteEquipment,
  switchEquipmentTemplate
} from "@/api/monitor-unit";
import AddDeviceForm from "./AddDeviceForm.vue";
import SelectTemplateForm from "./SelectTemplateForm.vue";

interface Props {
  monitorUnit: any;
  currentNode?: any;
  muList?: any[];
}

const props = defineProps<Props>();

// 路由实例
const router = useRouter();

// 响应式数据
const loading = ref(false);
const devices = ref<any[]>([]);
const currentRow = ref<any>(null);

// 弹框控制
const showAddDeviceDialog = ref(false);
const showSelectTemplateDialog = ref(false);

// 表单引用
const addDeviceFormRef = ref();
const selectTemplateFormRef = ref();

// 当前操作的设备
const currentEditDevice = ref<any>(null);

// 过滤条件
const filters = ref({
  equipmentName: "",
  monitorUnitName: "",
  portName: "",
  updateTime: ""
});

// 计算属性
const filteredDevices = computed(() => {
  let result = devices.value;

  if (filters.value.equipmentName) {
    result = result.filter(item =>
      item.equipmentName
        ?.toLowerCase()
        .includes(filters.value.equipmentName.toLowerCase())
    );
  }

  if (filters.value.monitorUnitName) {
    result = result.filter(item =>
      item.monitorUnitName
        ?.toLowerCase()
        .includes(filters.value.monitorUnitName.toLowerCase())
    );
  }

  if (filters.value.portName) {
    result = result.filter(item =>
      item.portName
        ?.toLowerCase()
        .includes(filters.value.portName.toLowerCase())
    );
  }

  if (filters.value.updateTime) {
    result = result.filter(item =>
      item.updateTime
        ?.toLowerCase()
        .includes(filters.value.updateTime.toLowerCase())
    );
  }

  return result;
});

// 格式化时间方法
const formatDateTime = (dateTimeStr: string) => {
  if (!dateTimeStr) return "";

  try {
    const date = new Date(dateTimeStr);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const seconds = String(date.getSeconds()).padStart(2, "0");

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (error) {
    console.error("时间格式化失败:", error);
    return dateTimeStr;
  }
};

// 方法
const loadDevices = async () => {
  if (!props.monitorUnit?.monitorUnitId) return;

  try {
    loading.value = true;
    const response = await getMonitorUnitDevices(
      props.monitorUnit.monitorUnitId
    );
    devices.value = response.data || [];
    // 添加监控单元名称
    devices.value.forEach(device => {
      device.monitorUnitName = props.monitorUnit.monitorUnitName;
    });
  } catch (error) {
    console.error("加载设备列表失败:", error);
    ElMessage.error("加载设备列表失败");
  } finally {
    loading.value = false;
  }
};

// 刷新设备列表
const refreshDevices = () => {
  console.log("DeviceList refreshDevices 被调用");
  loadDevices();
};

const handleFilterChange = () => {
  // 过滤逻辑在计算属性中处理
};

const handleDeviceNameClick = (device: any) => {
  if (!device.equipmentId) {
    ElMessage.warning("设备ID不存在，无法跳转");
    return;
  }

  // 跳转到设备管理页面
  router.push({
    path: `/siteweb-omc/device-management/${device.equipmentId}`,
    query: {
      title: device.equipmentName || "设备管理"
    }
  });
};

const handleCurrentChange = (row: any) => {
  currentRow.value = row;
};

const handleRowContextMenu = (row: any, column: any, event: MouseEvent) => {
  event.preventDefault();
  currentRow.value = row;
  showContextMenu(event, false); // false表示是行右键
};

const handleTableContextMenu = (event: MouseEvent) => {
  // 检查是否点击在表格的空白区域（不是行）
  const target = event.target as HTMLElement;

  // 如果点击的是表格行相关元素，不处理（让行右键事件处理）
  if (
    target.closest(".el-table__row") ||
    target.closest("td") ||
    target.closest("th")
  ) {
    return;
  }

  // 只有点击在真正的空白区域才处理
  const isTableEmpty =
    target.classList.contains("table-content") ||
    target.closest(".el-table__empty-block") ||
    (target.closest(".el-table__body-wrapper") &&
      !target.closest(".el-table__row"));

  if (isTableEmpty) {
    event.preventDefault();
    currentRow.value = null;
    showContextMenu(event, true); // true表示是空白区域右键
  }
};

const showContextMenu = (event: MouseEvent, isEmptyArea = false) => {
  const hasMonitorUnit =
    props.monitorUnit || (props.muList && props.muList.length > 0);

  // 如果是空白区域，只显示新增设备
  if (isEmptyArea) {
    ContextMenu.showContextMenu({
      zIndex: 10000,
      x: event.x,
      y: event.y,
      items: [
        {
          label: "新增设备",
          icon: "h:plus",
          disabled: !hasMonitorUnit,
          onClick: () => {
            handleAddDevice();
          }
        }
      ]
    });
    return;
  }

  // 行右键菜单，显示完整菜单
  ContextMenu.showContextMenu({
    zIndex: 10000,
    x: event.x,
    y: event.y,
    items: [
      {
        label: "新增设备",
        icon: "h:plus",
        disabled: !hasMonitorUnit,
        onClick: () => {
          handleAddDevice();
        }
      },
      {
        label: "重新关联模板",
        icon: "h:document-duplicate",
        disabled: !currentRow.value,
        onClick: () => {
          handleResetTemplate();
        }
      },
      {
        label: "编辑设备",
        icon: "h:pencil-alt",
        disabled: !currentRow.value,
        onClick: () => {
          handleEditDevice();
        }
      },
      {
        label: "删除设备",
        icon: "h:trash",
        disabled: !currentRow.value,
        onClick: () => {
          handleDeleteDevice();
        }
      }
    ]
  });
};

// 新增设备
const handleAddDevice = () => {
  showAddDeviceDialog.value = true;
};

const handleAddDeviceConfirm = () => {
  if (addDeviceFormRef.value) {
    addDeviceFormRef.value.submit();
  }
};

const handleAddDeviceSuccess = () => {
  showAddDeviceDialog.value = false;
  ElMessage.success("新增设备成功！");
  loadDevices();
};

const handleAddDeviceCancel = () => {
  showAddDeviceDialog.value = false;
};

// 重新关联模板
const handleResetTemplate = () => {
  if (!currentRow.value) {
    ElMessage.warning("请先选择设备");
    return;
  }
  currentEditDevice.value = currentRow.value;
  showSelectTemplateDialog.value = true;
};

const handleSelectTemplateSubmit = () => {
  if (selectTemplateFormRef.value) {
    selectTemplateFormRef.value.confirm();
  }
};

const handleSelectTemplateConfirm = async (templateId: any) => {
  if (!currentEditDevice.value || !templateId) return;

  try {
    const params = {
      destTemplateId: templateId.templateId,
      originTemplateId: currentEditDevice.value.templateId,
      equipmentIds: [currentEditDevice.value.equipmentId]
    };

    await switchEquipmentTemplate(params);
    showSelectTemplateDialog.value = false;
    ElMessage.success("修改设备绑定模板成功！");
    loadDevices();
  } catch (error) {
    console.error("切换模板失败:", error);
    ElMessage.error("切换模板失败");
  }
};

const handleSelectTemplateCancel = () => {
  showSelectTemplateDialog.value = false;
  currentEditDevice.value = null;
};

// 编辑设备
const handleEditDevice = () => {
  if (!currentRow.value) {
    ElMessage.warning("请先选择设备");
    return;
  }

  router.push({
    path: `/siteweb-omc/device-management/${currentRow.value.equipmentId}`,
    query: {
      title: currentRow.value.equipmentName || "设备管理"
    }
  });
};

// 删除设备
const handleDeleteDevice = () => {
  if (!currentRow.value) {
    ElMessage.warning("请先选择设备");
    return;
  }

  ElMessageBox.confirm(
    `确认要删除设备 ${currentRow.value.equipmentName} 吗？`,
    "警告",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  )
    .then(async () => {
      try {
        await deleteEquipment(currentRow.value.equipmentId);
        ElMessage.success("删除成功！");
        loadDevices();
      } catch (error) {
        console.error("删除设备失败:", error);
        ElMessage.error("删除设备失败");
      }
    })
    .catch(() => {
      // 取消删除
    });
};

// 监听props变化
watch(
  () => props.monitorUnit,
  () => {
    loadDevices();
  },
  { immediate: true }
);

// 生命周期
onMounted(() => {
  loadDevices();
});

// 暴露方法给父组件调用
defineExpose({
  refreshDevices
});
</script>

<style scoped>
.device-list-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-header {
  padding: 16px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  flex-shrink: 0;
}

.filter-row {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  flex-direction: column;
  min-width: 150px;
}

.filter-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.table-content {
  flex: 1;
  padding: 16px;
  min-height: 0;
}

.el-table {
  --el-table-border-color: #e4e7ed;
}

.el-table :deep(.el-table__row:hover) {
  background-color: #f0f9ff;
}

.el-table :deep(.current-row) {
  background-color: #e1f5fe;
}

.device-name-link {
  color: #409eff;
  cursor: pointer;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease;
}

.device-name-link:hover {
  color: #337ecc;
  text-decoration: underline;
}
</style>
