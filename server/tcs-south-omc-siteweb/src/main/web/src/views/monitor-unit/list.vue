<template>
  <div
    class="monitor-unit-list-container bg-gray-50 dark:bg-gray-900 p-4 flex flex-col"
    style="height: calc(100vh - 48px)"
  >
    <!-- 页面头部 -->
    <div class="mb-4 flex-shrink-0">
      <div class="flex items-center justify-between mb-3">
        <div class="flex items-center">
          <div class="w-1 h-6 bg-primary rounded-full mr-3" />
          <h1 class="text-xl font-bold text-gray-900 dark:text-white">
            采集器列表
          </h1>
        </div>
        <span class="text-sm text-gray-600 dark:text-gray-400">
          共 {{ filteredData.length }} 个采集器
        </span>
      </div>

      <!-- 工具栏 -->
      <div
        class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4"
      >
        <!-- 操作按钮 -->
        <div class="flex items-center space-x-3">
          <el-button
            type="primary"
            size="small"
            :disabled="loading"
            @click="showAddDialog"
          >
            <el-icon size="14" class="mr-1"><Plus /></el-icon>
            新增采集器
          </el-button>
          <el-button
            type="primary"
            plain
            size="small"
            :disabled="filteredData.length === 0 || loading"
            @click="showDistributionDialog"
          >
            <el-icon size="14" class="mr-1"><Connection /></el-icon>
            分发配置
          </el-button>
          <el-button
            type="danger"
            plain
            size="small"
            :disabled="selectedIds.length === 0 || loading"
            @click="handleBatchDelete"
          >
            <el-icon size="14" class="mr-1"><Delete /></el-icon>
            删除采集器
          </el-button>
        </div>
      </div>
    </div>

    <!-- 采集器列表表格 -->
    <div
      class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-3 flex-1 min-h-0"
    >
      <div class="h-full">
        <el-auto-resizer>
          <template #default="{ height, width }">
            <el-table-v2
              v-loading="loading"
              :columns="tableColumns"
              :data="filteredData"
              :width="width"
              :height="height"
              fixed
              :row-height="48"
              @row-select="handleRowSelect"
            />
          </template>
        </el-auto-resizer>
      </div>
    </div>

    <!-- 新增/编辑采集器对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="采集器名称" prop="monitorUnitName">
          <el-input
            v-model="formData.monitorUnitName"
            placeholder="请输入采集器名称"
          />
        </el-form-item>
        <el-form-item label="IP地址" prop="ipAddress">
          <el-input
            v-model="formData.ipAddress"
            placeholder="请输入IP地址"
          />
        </el-form-item>
        <el-form-item label="采集器类型" prop="monitorUnitCategory">
          <el-select
            v-model="formData.monitorUnitCategory"
            placeholder="请选择采集器类型"
            style="width: 100%"
          >
            <el-option
              v-for="item in monitorUnitCategories"
              :key="item.typeId"
              :label="item.typeName"
              :value="item.typeId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入描述信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-end gap-3">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="submitLoading"
            @click="handleSubmit"
          >
            {{ submitLoading ? "提交中..." : "确认" }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 监控单元分发配置对话框 -->
    <MuDistribution
      :key="distributionDialogKey"
      v-model:visible="distributionDialogVisible"
      :mu-list="tableData"
      :monitor-unit-categories="monitorUnitCategories"
      @close="handleDistributionClose"
    />

    <!-- 新增端口对话框 -->
    <el-dialog
      v-model="addPortDialogVisible"
      title="新增端口"
      width="800px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <AddPortForm
        v-if="currentPortUnit"
        ref="addPortFormRef"
        :monitor-unit="currentPortUnit"
        @success="handlePortSuccess"
      />

      <template #footer>
        <div class="flex justify-end space-x-3">
          <el-button @click="addPortDialogVisible = false"> 取消 </el-button>
          <el-button
            type="primary"
            @click="handlePortSubmit"
          >
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="tsx">
import {
  ref,
  computed,
  onMounted,
  reactive,
  nextTick,
  type FunctionalComponent
} from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox, ElCheckbox, ElIcon, ElTag, ElButton, ElInput, ElPopover, ElSelect, ElOption } from "element-plus";
import type { Column } from "element-plus";
import {
  Plus,
  Search,
  Delete,
  Monitor,
  Connection,
  Edit,
  View,
  Filter
} from "@element-plus/icons-vue";
import {
  getMonitorUnits,
  getMonitorUnitTypes,
  createMonitorUnit,
  updateMonitorUnit,
  deleteMonitorUnit,
  type MonitorUnit
} from "@/api/monitor-unit";
import MuDistribution from "@/components/MuDistribution.vue";
import AddPortForm from "./components/AddPortForm.vue";

defineOptions({
  name: "MonitorUnitList"
});

// 过滤器状态接口
interface FilterState {
  [key: string]: {
    value: any[];
    options: Array<{ label: string; value: any }>;
  };
}

const router = useRouter();

// 状态定义
const loading = ref(false);
const submitLoading = ref(false);
const selectedIds = ref<string[]>([]);
const tableData = ref<MonitorUnit[]>([]);
const monitorUnitCategories = ref<any[]>([]);
const muTypeObj = ref<Record<number, string>>({});

// 过滤器状态
const filterState = ref<FilterState>({});

// 弹框相关
const dialogVisible = ref(false);
const dialogTitle = ref("新增采集器");
const formRef = ref();

// 分发配置弹框
const distributionDialogVisible = ref(false);
const distributionDialogKey = ref(0);

// 新增端口弹框
const addPortDialogVisible = ref(false);
const addPortFormRef = ref();
const currentPortUnit = ref<MonitorUnit | null>(null);

// 表单数据
const formData = reactive({
  monitorUnitId: "",
  monitorUnitName: "",
  ipAddress: "",
  monitorUnitCategory: 18,
  workStationId: "",
  description: "",
  stationId: ""
});

// 表单验证规则
const formRules = {
  monitorUnitName: [
    { required: true, message: "请输入采集器名称", trigger: "blur" }
  ],
  ipAddress: [
    { required: true, message: "请输入IP地址", trigger: "blur" },
    {
      pattern:
        /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
      message: "请输入正确的IP地址格式",
      trigger: "blur"
    }
  ],
  monitorUnitCategory: [
    { required: true, message: "请选择采集器类型", trigger: "change" }
  ],
};

// 生命周期
onMounted(() => {
  loadMonitorUnits();
  loadMonitorUnitTypes();
  initFilterState();
});

// 初始化过滤器状态
const initFilterState = () => {
  filterState.value = {
    monitorUnitName: { value: [], options: [] },
    ipAddress: { value: [], options: [] },
    monitorUnitCategory: { value: [], options: [] },
    connectState: { value: [], options: [] },
    isConfigOK: { value: [], options: [] },
    isSync: { value: [], options: [] },
    runMode: { value: [], options: [] }
  };
};

// 更新过滤器选项
const updateFilterOptions = () => {
  const data = tableData.value;

  // 采集器名称选项
  const nameOptions = [...new Set(data.map(item => item.workStationName || item.monitorUnitName).filter(Boolean))]
    .map(name => ({ label: name, value: name }));
  filterState.value.monitorUnitName.options = nameOptions;

  // IP地址选项
  const ipOptions = [...new Set(data.map(item => item.ipAddress).filter(Boolean))]
    .map(ip => ({ label: ip, value: ip }));
  filterState.value.ipAddress.options = ipOptions;

  // 采集器类型选项
  const typeOptions = [...new Set(data.map(item => item.monitorUnitCategory))]
    .map(category => ({
      label: muTypeObj.value[category] || "未知类型",
      value: category
    }));
  filterState.value.monitorUnitCategory.options = typeOptions;

  // 连接状态选项
  filterState.value.connectState.options = [
    { label: "在线", value: 1 },
    { label: "离线", value: 2 }
  ];

  // 配置状态选项
  filterState.value.isConfigOK.options = [
    { label: "配置正常", value: true },
    { label: "配置异常", value: false }
  ];

  // 同步状态选项
  filterState.value.isSync.options = [
    { label: "已同步", value: true },
    { label: "未同步", value: false }
  ];

  // 运行模式选项
  filterState.value.runMode.options = [
    { label: "正常模式", value: 1 },
    { label: "调试模式", value: 2 }
  ];
};

// 创建过滤器头部组件
const createFilterHeader = (column: any) => {
  const filterKey = column.dataKey;

  return (props: any) => {
    const popoverRef = ref();

    const onFilter = () => {
      popoverRef.value?.hide();
    };

    const onReset = () => {
      if (filterState.value[filterKey]) {
        filterState.value[filterKey].value = [];
      }
    };

    return (
      <div class="flex items-center justify-center">
        <span class="mr-2 text-xs">{props.column.title}</span>
        <ElPopover
          ref={popoverRef}
          trigger="click"
          width={250}
        >
          {{
            default: () => (
              <div class="filter-wrapper">
                <div class="filter-group">
                  <ElSelect
                    modelValue={filterState.value[filterKey]?.value || []}
                    onUpdate:modelValue={(value: any[]) => {
                      if (filterState.value[filterKey]) {
                        filterState.value[filterKey].value = value;
                      }
                    }}
                    placeholder="选择过滤条件"
                    size="small"
                    multiple
                    collapseTags
                    filterable
                    clearable
                    style={{ width: "100%" }}
                  >
                    {(filterState.value[filterKey]?.options || []).map((option: any) => (
                      <ElOption
                        key={option.value}
                        label={option.label}
                        value={option.value}
                      />
                    ))}
                  </ElSelect>
                </div>
                <div class="el-table-v2__demo-filter">
                  <ElButton text onClick={onFilter}>确认</ElButton>
                  <ElButton text onClick={onReset}>重置</ElButton>
                </div>
              </div>
            ),
            reference: () => (
              <ElIcon class="cursor-pointer">
                <Filter />
              </ElIcon>
            )
          }}
        </ElPopover>
      </div>
    );
  };
};

// 过滤后的数据
const filteredData = computed(() => {
  let data = tableData.value;

  // 应用列过滤器
  Object.entries(filterState.value).forEach(([key, filter]) => {
    if (filter.value && filter.value.length > 0) {
      data = data.filter(item => {
        const itemValue = item[key as keyof MonitorUnit];
        if (key === 'monitorUnitName') {
          // 对于采集器名称，需要检查 workStationName 或 monitorUnitName
          const nameValue = item.workStationName || item.monitorUnitName;
          return filter.value.includes(nameValue);
        }
        return filter.value.includes(itemValue);
      });
    }
  });

  return data;
});

// 表格列配置
const tableColumns = computed<Column<MonitorUnit>[]>(() => [
  // 选择列
  {
    key: "selection",
    width: 60,
    cellRenderer: ({ rowData }) => (
      <ElCheckbox
        modelValue={selectedIds.value.includes(String(rowData.monitorUnitId))}
        onChange={(checked: boolean) => {
          const id = String(rowData.monitorUnitId);
          if (checked) {
            if (!selectedIds.value.includes(id)) {
              selectedIds.value.push(id);
            }
          } else {
            const index = selectedIds.value.indexOf(id);
            if (index > -1) {
              selectedIds.value.splice(index, 1);
            }
          }
        }}
      />
    ),
    headerCellRenderer: () => (
      <ElCheckbox
        modelValue={
          selectedIds.value.length === filteredData.value.length &&
          filteredData.value.length > 0
        }
        indeterminate={
          selectedIds.value.length > 0 &&
          selectedIds.value.length < filteredData.value.length
        }
        onChange={(checked: boolean) => {
          if (checked) {
            selectedIds.value = filteredData.value.map(item => String(item.monitorUnitId));
          } else {
            selectedIds.value = [];
          }
        }}
      />
    )
  },
  // 采集器名称
  {
    key: "monitorUnitName",
    title: "采集器名称",
    dataKey: "monitorUnitName",
    width: 200,
    cellRenderer: ({ rowData }) => (
      <div class="flex items-center cursor-pointer" onClick={() => handleViewDetail(rowData)}>
        <div class="w-6 h-6 bg-primary rounded flex items-center justify-center mr-2 flex-shrink-0">
          <ElIcon size={12} class="text-white">
            <Monitor />
          </ElIcon>
        </div>
        <span class="font-medium text-sm">{rowData.workStationName || rowData.monitorUnitName}</span>
      </div>
    ),
    headerCellRenderer: createFilterHeader({ dataKey: "monitorUnitName" })
  },
  // IP地址
  {
    key: "ipAddress",
    title: "IP地址",
    dataKey: "ipAddress",
    width: 200,
    cellRenderer: ({ rowData }) => <span>{rowData.ipAddress}</span>,
    headerCellRenderer: createFilterHeader({ dataKey: "ipAddress" })
  },
  // 采集器类型
  {
    key: "typeName",
    title: "采集器类型",
    dataKey: "typeName",
    width: 150,
    cellRenderer: ({ rowData }) => (
      <span>{muTypeObj.value[rowData.monitorUnitCategory] || "未知类型"}</span>
    ),
    headerCellRenderer: createFilterHeader({ dataKey: "monitorUnitCategory" })
  },
  // 连接状态
  {
    key: "connectState",
    title: "连接状态",
    dataKey: "connectState",
    width: 120,
    cellRenderer: ({ rowData }) => (
      <ElTag
        type={rowData.connectState === 1 ? "success" : "danger"}
        effect="light"
      >
        {rowData.connectState === 1 ? "在线" : "离线"}
      </ElTag>
    ),
    headerCellRenderer: createFilterHeader({ dataKey: "connectState" })
  },
  // 配置状态
  {
    key: "isConfigOK",
    title: "配置状态",
    width: 120,
    cellRenderer: ({ rowData }) => (
      <ElTag
        type={rowData.isConfigOK ? "success" : "warning"}
        effect="light"
      >
        {rowData.isConfigOK ? "配置正常" : "配置异常"}
      </ElTag>
    ),
    headerCellRenderer: createFilterHeader({ dataKey: "isConfigOK" })
  },
  // 同步状态
  {
    key: "isSync",
    title: "同步状态",
    width: 120,
    cellRenderer: ({ rowData }) => (
      <ElTag
        type={rowData.isSync ? "success" : "info"}
        effect="light"
      >
        {rowData.isSync ? "已同步" : "未同步"}
      </ElTag>
    ),
    headerCellRenderer: createFilterHeader({ dataKey: "isSync" })
  },
  // 运行模式
  {
    key: "runMode",
    title: "运行模式",
    width: 120,
    cellRenderer: ({ rowData }) => (
      <span class="text-sm">
        {rowData.runMode === 1 ? "正常模式" : rowData.runMode === 2 ? "调试模式" : "未知模式"}
      </span>
    ),
    headerCellRenderer: createFilterHeader({ dataKey: "runMode" })
  },
  // 操作列
  {
    key: "actions",
    title: "操作",
    width: 280,
    cellRenderer: ({ rowData }) => (
      <div class="flex items-center space-x-1">
        <ElButton
          size="small"
          type="primary"
          link
          onClick={() => handleViewDetail(rowData)}
        >
          查看详情
        </ElButton>
        <ElButton
          size="small"
          type="primary"
          link
          onClick={() => handleEdit(rowData)}
        >
          编辑
        </ElButton>
        <ElButton
          size="small"
          type="success"
          link
          onClick={() => handleAddPort(rowData)}
        >
          新增端口
        </ElButton>
        <ElButton
          size="small"
          type="danger"
          link
          onClick={() => handleDelete(rowData)}
        >
          删除
        </ElButton>
      </div>
    )
  }
]);

// 方法
const loadMonitorUnits = async () => {
  try {
    loading.value = true;
    const response = await getMonitorUnits();
    tableData.value = response.data || [];
    // 更新过滤器选项
    updateFilterOptions();
  } catch (error) {
    console.error("加载采集器列表失败:", error);
    ElMessage.error("加载采集器列表失败");
  } finally {
    loading.value = false;
  }
};

const loadMonitorUnitTypes = async () => {
  try {
    const response = await getMonitorUnitTypes();
    monitorUnitCategories.value = response.data || [];

    // 创建类型映射对象
    const typeObj: Record<number, string> = {};
    monitorUnitCategories.value.forEach((element: any) => {
      typeObj[element.typeId] = element.typeName;
    });
    muTypeObj.value = typeObj;

    // 排序
    monitorUnitCategories.value.sort((a: any, b: any) => a.order - b.order);
  } catch (error) {
    console.error("加载采集器类型失败:", error);
  }
};

// 行选择处理
const handleRowSelect = (selection: MonitorUnit[]) => {
  selectedIds.value = selection.map(item => String(item.monitorUnitId));
};

// 查看详情
const handleViewDetail = (unit: MonitorUnit) => {
  router.push({
    path: `/siteweb-omc/monitor-unit-detail/${unit.monitorUnitId}`,
    query: {
      title: unit.workStationName || unit.monitorUnitName
    }
  });
};

// 显示新增对话框
const showAddDialog = () => {
  dialogTitle.value = "新增采集器";
  resetFormData();
  dialogVisible.value = true;
};

// 编辑采集器
const handleEdit = (unit: MonitorUnit) => {
  dialogTitle.value = "编辑采集器";
  Object.assign(formData, {
    monitorUnitId: String(unit.monitorUnitId),
    monitorUnitName: unit.monitorUnitName,
    ipAddress: unit.ipAddress,
    monitorUnitCategory: unit.monitorUnitCategory,
    workStationId: unit.workStationId || "",
    description: unit.description || "",
    stationId: unit.stationId || ""
  });
  dialogVisible.value = true;
};

// 删除采集器
const handleDelete = async (unit: MonitorUnit) => {
  try {
    await ElMessageBox.confirm(
      `确认删除采集器 "${unit.monitorUnitName}" 吗？`,
      "确认删除",
      {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    const response = await deleteMonitorUnit(Number(unit.monitorUnitId));
    if (response.code === 0) {
      ElMessage.success("删除成功");
      await loadMonitorUnits();
    } else {
      ElMessage.error(response.message || "删除失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("请求异常");
      console.error(error);
    }
  }
};

// 批量删除
const handleBatchDelete = async () => {
  if (selectedIds.value.length === 0) {
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确认删除选中的 ${selectedIds.value.length} 个采集器吗？`,
      "确认删除",
      {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    // 批量删除逻辑
    for (const id of selectedIds.value) {
      await deleteMonitorUnit(Number(id));
    }

    ElMessage.success("批量删除成功");
    selectedIds.value = [];
    await loadMonitorUnits();
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("请求异常");
      console.error(error);
    }
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    submitLoading.value = true;

    const isEdit = !!formData.monitorUnitId;
    const apiCall = isEdit ? updateMonitorUnit : createMonitorUnit;

    const response = await apiCall(formData);
    if (response.code === 0) {
      ElMessage.success(isEdit ? "更新成功" : "创建成功");
      dialogVisible.value = false;
      await loadMonitorUnits();
    } else {
      ElMessage.error(response.message || (isEdit ? "更新失败" : "创建失败"));
    }
  } catch (error) {
    ElMessage.error("请求异常");
    console.error(error);
  } finally {
    submitLoading.value = false;
  }
};

// 重置表单数据
const resetFormData = () => {
  Object.assign(formData, {
    monitorUnitId: "",
    monitorUnitName: "",
    ipAddress: "",
    monitorUnitCategory: 18,
    workStationId: "",
    description: "",
    stationId: ""
  });
  nextTick(() => {
    formRef.value?.clearValidate();
  });
};

// 显示分发配置对话框
const showDistributionDialog = () => {
  distributionDialogKey.value++; // 强制重新创建组件
  distributionDialogVisible.value = true;
};

// 处理分发配置对话框关闭
const handleDistributionClose = () => {
  distributionDialogVisible.value = false;
};

// 显示新增端口对话框
const handleAddPort = (unit: MonitorUnit) => {
  currentPortUnit.value = unit;
  addPortDialogVisible.value = true;
};

// 处理端口表单提交成功
const handlePortSuccess = () => {
  ElMessage.success("新增端口成功");
  addPortDialogVisible.value = false;
  // 可以在这里刷新相关数据
};

// 提交端口表单
const handlePortSubmit = async () => {
  if (!addPortFormRef.value) return;

  const success = await addPortFormRef.value.submit();
  if (success) {
    handlePortSuccess();
  }
};
</script>

<style scoped>
.filter-wrapper {
  padding: 8px 0;
}

.filter-group {
  margin-bottom: 12px;
}

.el-table-v2__demo-filter {
  border-top: var(--el-border);
  margin: 12px -12px -12px;
  padding: 0 12px;
  display: flex;
  justify-content: space-between;
}
</style>
