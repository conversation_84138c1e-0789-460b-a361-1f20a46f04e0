<template>
  <div class="monitor-unit-container dark:bg-gray-900">
    <!-- 页面头部 -->

    <!-- 主内容区域 -->
    <div class="flex gap-6 h-[calc(100vh-100px)]">
      <!-- 左侧采集器列表 -->
      <div class="w-80 flex-shrink-0">
        <div
          class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 h-full flex flex-col"
        >
          <!-- 列表头部 -->
          <div class="p-4 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                采集器列表
              </h3>
              <div class="flex items-center space-x-2">
                <el-button
                  type="primary"
                  text
                  size="small"
                  title="新增监控单元"
                  @click="showAddDialog"
                >
                  <el-icon size="16"><Plus /></el-icon>
                </el-button>
                <el-button
                  v-if="filteredMonitorUnits.length > 0"
                  type="primary"
                  text
                  size="small"
                  title="分发监控单元配置"
                  @click="showDistributionDialog"
                >
                  <el-icon size="16"><Connection /></el-icon>
                </el-button>
                <el-button
                  type="primary"
                  text
                  size="small"
                  :loading="loading"
                  @click="refreshMonitorUnits"
                >
                  <el-icon size="16"><Refresh /></el-icon>
                </el-button>
              </div>
            </div>

            <el-input
              v-model="searchKeyword"
              placeholder="搜索采集器..."
              clearable
              :prefix-icon="Search"
              size="default"
              class="mb-3"
              @input="handleSearch"
            />

            <div class="text-xs text-gray-500 dark:text-gray-400">
              共 {{ filteredMonitorUnits.length }} 个采集器
            </div>
          </div>

          <!-- 列表内容 -->
          <div class="flex-1 overflow-auto p-2">
            <div v-if="loading" class="flex justify-center items-center h-32">
              <el-icon size="20" class="text-primary animate-spin">
                <Loading />
              </el-icon>
            </div>

            <div
              v-else-if="filteredMonitorUnits.length === 0"
              class="text-center py-12"
            >
              <div
                class="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4"
              >
                <el-icon size="20" class="text-gray-400"><Monitor /></el-icon>
              </div>
              <h3
                class="text-lg font-medium text-gray-900 dark:text-white mb-2"
              >
                {{ searchKeyword ? "未找到相关采集器" : "暂无采集器" }}
              </h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{
                  searchKeyword
                    ? "请尝试其他搜索条件"
                    : "请联系管理员添加采集器"
                }}
              </p>
            </div>

            <div v-else class="space-y-2">
              <div
                v-for="unit in filteredMonitorUnits"
                :key="unit.monitorUnitId"
                class="group p-4 rounded-lg cursor-pointer transition-all duration-200 hover:bg-gray-50 dark:hover:bg-gray-700 hover:shadow-sm border"
                :class="{
                  'bg-blue-50 dark:bg-blue-900/20 border-primary/20 shadow-sm':
                    selectedUnit?.monitorUnitId === unit.monitorUnitId,
                  'border-transparent':
                    selectedUnit?.monitorUnitId !== unit.monitorUnitId
                }"
                @click="selectMonitorUnit(unit)"
                @contextmenu="handleContextMenu($event, unit)"
              >
                <div class="flex items-start space-x-3">
                  <div
                    class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0"
                  >
                    <el-icon size="18" class="text-primary"
                      ><Monitor
                    /></el-icon>
                  </div>
                  <div class="flex-1 min-w-0">
                    <h4
                      class="font-semibold text-gray-900 dark:text-white text-sm truncate mb-1"
                    >
                      {{ unit.monitorUnitName || "未命名采集器" }}
                    </h4>
                    <div class="flex items-center space-x-2">
                      <span class="text-xs text-gray-400 dark:text-gray-500">
                        {{ unit.ipAddress }}
                      </span>
                      <el-tag
                        :type="unit.enable ? 'success' : 'info'"
                        size="small"
                        effect="light"
                      >
                        {{ unit.enable ? "已启用" : "已禁用" }}
                      </el-tag>
                    </div>
                  </div>
                  <div
                    v-if="selectedUnit?.monitorUnitId === unit.monitorUnitId"
                    class="w-2 h-2 bg-primary rounded-full flex-shrink-0 mt-2"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧内容区域 -->
      <div class="flex-1 min-w-0">
        <div
          v-if="selectedUnit"
          class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 h-full flex flex-col"
        >
          <!-- 选中采集器信息头部 -->
          <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-start space-x-4">
              <div
                class="w-14 h-14 bg-primary rounded-lg flex items-center justify-center flex-shrink-0"
              >
                <el-icon size="24" class="text-white"><Monitor /></el-icon>
              </div>
              <div class="flex-1 min-w-0">
                <h2
                  class="text-xl font-bold text-gray-900 dark:text-white mb-2"
                >
                  {{
                    selectedUnit.workStationName || selectedUnit.monitorUnitName
                  }}
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div class="flex items-center space-x-2">
                    <span class="text-gray-500 dark:text-gray-400">ID:</span>
                    <span class="font-medium text-gray-900 dark:text-white">
                      {{ selectedUnit.monitorUnitId }}
                    </span>
                  </div>
                  <div class="flex items-center space-x-2">
                    <span class="text-gray-500 dark:text-gray-400">IP:</span>
                    <span class="font-medium text-gray-900 dark:text-white">
                      {{ selectedUnit.ipAddress }}
                    </span>
                  </div>
                  <div class="flex items-center space-x-2">
                    <span class="text-gray-500 dark:text-gray-400">状态:</span>
                    <el-tag
                      :type="selectedUnit.enable ? 'success' : 'info'"
                      size="small"
                      effect="light"
                    >
                      {{ selectedUnit.enable ? "已启用" : "已禁用" }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Tab区域 -->
          <div class="flex-1 flex flex-col overflow-hidden">
            <el-tabs
              v-model="activeTab"
              class="monitor-tabs"
              @tab-click="handleTabChange"
            >
              <el-tab-pane label="设备列表" name="devices">
                <DeviceList
                  ref="deviceListRef"
                  :key="selectedUnit.monitorUnitId"
                  :monitor-unit="selectedUnit"
                />
              </el-tab-pane>
              <el-tab-pane label="采集信息" name="sampler">
                <SamplerInfo
                  ref="samplerInfoRef"
                  :key="selectedUnit.monitorUnitId"
                  :monitor-unit="selectedUnit"
                />
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>

        <!-- 未选择状态 -->
        <div
          v-else
          class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 h-full flex items-center justify-center"
        >
          <div class="text-center">
            <div
              class="w-20 h-20 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-6"
            >
              <el-icon size="24" class="text-gray-400"><Monitor /></el-icon>
            </div>
            <h3 class="text-xl font-medium text-gray-900 dark:text-white mb-3">
              请选择一个采集器
            </h3>
            <p class="text-gray-500 dark:text-gray-400 max-w-md">
              从左侧列表中选择要查看的采集器，查看其设备列表和采集信息
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- 新增/编辑监控单元对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="140px"
        size="default"
      >
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- ID字段（编辑时显示） -->
          <el-form-item
            v-if="formData.monitorUnitId"
            label="ID"
            prop="monitorUnitId"
          >
            <el-input
              v-model="formData.monitorUnitId"
              disabled
              maxlength="128"
            />
          </el-form-item>

          <!-- 监控单元名称 -->
          <el-form-item label="监控单元名称" prop="monitorUnitName" required>
            <el-input
              v-model="formData.monitorUnitName"
              placeholder="请输入监控单元名称"
              maxlength="128"
              @input="handleNameInput"
            />
          </el-form-item>

          <!-- IP地址 -->
          <el-form-item label="IP地址" prop="ipAddress" required>
            <el-input v-model="formData.ipAddress" placeholder="请输入IP地址" />
          </el-form-item>

          <!-- 监控单元类型 -->
          <el-form-item
            label="监控单元类型"
            prop="monitorUnitCategory"
            required
          >
            <el-select
              v-model="formData.monitorUnitCategory"
              placeholder="请选择监控单元类型"
              :disabled="!!formData.monitorUnitId"
              style="width: 100%"
            >
              <el-option
                v-for="item in monitorUnitCategories"
                :key="item.typeId"
                :label="item.typeName"
                :value="item.typeId"
              />
            </el-select>
          </el-form-item>

          <!-- 所属局站 -->
          <el-form-item label="所属局站">
            <el-input v-model="currentStation.stationName" disabled />
          </el-form-item>
        </div>

        <!-- 归属RMU（当类型为1或24时显示） -->
        <el-form-item
          v-if="
            formData.monitorUnitCategory === 1 ||
            formData.monitorUnitCategory === 24
          "
          label="归属RMU"
          prop="workStationId"
          required
        >
          <el-select
            v-model="formData.workStationId"
            placeholder="请选择归属RMU"
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="item in rmuServers"
              :key="item.workStationId"
              :label="item.workStationName"
              :value="item.workStationId"
            />
          </el-select>
        </el-form-item>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- 数据服务器 -->
          <el-form-item label="数据服务器">
            <el-select
              v-model="dataServerSelected"
              placeholder="请选择数据服务器"
              multiple
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="item in dataServers"
                :key="item.workStationId"
                :label="item.workStationName"
                :value="item.workStationId"
              />
            </el-select>
          </el-form-item>

          <!-- RDS服务器 -->
          <el-form-item label="RDS服务器">
            <el-select
              v-model="rdsServerSelected"
              placeholder="请选择RDS服务器"
              multiple
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="item in rdsServers"
                :key="item.workStationId"
                :label="item.workStationName"
                :value="item.workStationId"
              />
            </el-select>
          </el-form-item>

        </div>

        <!-- 描述 -->
        <el-form-item label="描述">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="4"
            placeholder="请输入描述"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="flex justify-end space-x-3">
          <el-button @click="dialogVisible = false"> 取消 </el-button>
          <el-button
            type="primary"
            :loading="submitLoading"
            @click="handleSubmit"
          >
            {{ submitLoading ? "提交中..." : "确认" }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 监控单元分发配置对话框 -->
    <MuDistribution
      :key="distributionDialogKey"
      v-model:visible="distributionDialogVisible"
      :mu-list="monitorUnits"
      :monitor-unit-categories="monitorUnitCategories"
      @close="handleDistributionClose"
    />

    <!-- 新增端口对话框 -->
    <el-dialog
      v-model="addPortDialogVisible"
      title="新增端口"
      width="800px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form
        ref="portFormRef"
        :model="portFormData"
        label-width="120px"
        size="default"
      >
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- 监控单元名称（只读） -->
          <el-form-item label="监控单元名称">
            <el-input
              :value="
                contextMenuUnit?.monitorUnitName ||
                contextMenuUnit?.workStationName
              "
              disabled
            />
          </el-form-item>

          <!-- 监控单元ID（只读） -->
          <el-form-item label="监控单元ID">
            <el-input :value="contextMenuUnit?.monitorUnitId" disabled />
          </el-form-item>

          <!-- 端口名称（只读） -->
          <el-form-item label="端口名称">
            <el-input v-model="portFormData.portName" disabled />
          </el-form-item>

          <!-- 端口号 -->
          <el-form-item
            label="端口号"
            prop="portNo"
            :rules="[
              { required: true, message: '请输入端口号', trigger: 'blur' }
            ]"
          >
            <el-input-number
              v-model="portFormData.portNo"
              :min="1"
              :max="999"
              placeholder="请输入端口号"
              style="width: 100%"
              @change="handlePortNoChange"
            />
          </el-form-item>

          <!-- 端口类型 -->
          <el-form-item label="端口类型">
            <div class="flex items-center space-x-2" style="width: 100%">
              <el-select
                v-model="portFormData.portType"
                placeholder="请选择端口类型"
                style="flex: 1"
                @change="handlePortTypeChange"
              >
                <el-option
                  v-for="item in filteredPortTypes"
                  :key="item.typeId"
                  :label="item.typeName"
                  :value="item.typeId"
                />
              </el-select>
              <el-switch
                v-model="showAllPortTypes"
                active-text="所有"
                inactive-text="所有"
                style="margin-left: 12px"
                @change="handlePortTypeSwitch"
              />
            </div>
          </el-form-item>

          <!-- 电话号码 -->
          <el-form-item label="电话号码">
            <el-input
              v-model="portFormData.phoneNumber"
              placeholder="请输入电话号码"
            />
          </el-form-item>
        </div>

        <!-- 设置 -->
        <el-form-item label="设置">
          <el-input
            v-model="portFormData.setting"
            type="textarea"
            :rows="4"
            placeholder="请输入设置"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="flex justify-end space-x-3">
          <el-button @click="addPortDialogVisible = false"> 取消 </el-button>
          <el-button
            type="primary"
            :loading="portSubmitLoading"
            @click="handlePortSubmit"
          >
            {{ portSubmitLoading ? "提交中..." : "确认" }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, reactive, nextTick } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Refresh,
  Search,
  Monitor,
  DataLine,
  Loading,
  Plus,
  Edit,
  Delete,
  Connection
} from "@element-plus/icons-vue";
import ContextMenu from "@imengyu/vue3-context-menu";
import DeviceList from "./components/DeviceList.vue";
import SamplerInfo from "./components/SamplerInfo.vue";
import MuDistribution from "@/components/MuDistribution.vue";
import {
  getMonitorUnits,
  getMonitorUnitTypes,
  getWorkStationServerList,
  createMonitorUnit,
  updateMonitorUnit,
  deleteMonitorUnit
} from "@/api/monitor-unit";
import {
  getPortTypes,
  createPort,
  type PortInfo,
  type PortType
} from "@/api/port";

// 响应式数据
const searchKeyword = ref("");
const activeTab = ref("devices");
const loading = ref(false);
const monitorUnits = ref<any[]>([]);
const selectedUnit = ref<any>(null);

// 子组件引用
const deviceListRef = ref();
const samplerInfoRef = ref();

// 弹框相关
const dialogVisible = ref(false);
const dialogTitle = ref("新增监控单元");
const submitLoading = ref(false);
const formRef = ref();

// 分发配置弹框
const distributionDialogVisible = ref(false);
const distributionDialogKey = ref(0);

// 新增端口弹框
const addPortDialogVisible = ref(false);
const portFormRef = ref();
const portFormData = reactive<PortInfo>({
  portName: "",
  portNo: 0,
  portType: 1,
  phoneNumber: "",
  setting: "9600,n,8,1",
  monitorUnitId: "",
  linkSamplerUnitId: 0,
  description: ""
});
const portTypes = ref<PortType[]>([]);
const showAllPortTypes = ref(false);
const filteredPortTypes = ref<PortType[]>([]);
const portSubmitLoading = ref(false);

// 选项数据
const monitorUnitCategories = ref<any[]>([]);
const dataServers = ref<any[]>([]);
const rdsServers = ref<any[]>([]);
const rmuServers = ref<any[]>([]);

// 当前局站信息
const currentStation = ref({ stationName: "默认局站", stationId: "" });

// 右键菜单
const contextMenuUnit = ref<any>(null);

// 表单数据
const formData = reactive({
  monitorUnitId: "",
  monitorUnitName: "",
  ipAddress: "",
  monitorUnitCategory: 18,
  workStationId: "",
  description: "",
  stationId: ""
});

// 服务器选择
const dataServerSelected = ref<string[]>([]);
const rdsServerSelected = ref<string[]>([]);

// 表单验证规则
const formRules = {
  monitorUnitName: [
    { required: true, message: "请输入监控单元名称", trigger: "blur" }
  ],
  ipAddress: [
    { required: true, message: "请输入IP地址", trigger: "blur" },
    {
      pattern:
        /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
      message: "请输入正确的IP地址格式",
      trigger: "blur"
    }
  ],
  monitorUnitCategory: [
    { required: true, message: "请选择监控单元类型", trigger: "change" }
  ],
  workStationId: [
    {
      validator: (rule: any, value: any, callback: any) => {
        if (
          (formData.monitorUnitCategory === 1 ||
            formData.monitorUnitCategory === 24) &&
          !value
        ) {
          callback(new Error("请选择归属RMU"));
        } else {
          callback();
        }
      },
      trigger: "change"
    }
  ],
};

// 计算属性
const filteredMonitorUnits = computed(() => {
  if (!searchKeyword.value) {
    return monitorUnits.value;
  }
  const keyword = searchKeyword.value.toLowerCase();
  return monitorUnits.value.filter(
    unit =>
      (unit.workStationName &&
        unit.workStationName.toLowerCase().includes(keyword)) ||
      (unit.monitorUnitName &&
        unit.monitorUnitName.toLowerCase().includes(keyword)) ||
      (unit.ipAddress && unit.ipAddress.toLowerCase().includes(keyword))
  );
});

// 方法
const loadMonitorUnits = async () => {
  try {
    loading.value = true;
    const response = await getMonitorUnits();
    monitorUnits.value = response.data || [];

    // 首次加载时，如果有数据且没有选中的单元，自动选中第一个
    if (monitorUnits.value.length > 0 && !selectedUnit.value) {
      selectedUnit.value = monitorUnits.value[0];
      activeTab.value = "devices"; // 默认选中设备列表tab
    }
  } catch (error) {
    console.error("加载采集器列表失败:", error);
    ElMessage.error("加载采集器列表失败");
  } finally {
    loading.value = false;
  }
};

const refreshMonitorUnits = () => {
  loadMonitorUnits();
};

const selectMonitorUnit = (unit: any) => {
  selectedUnit.value = unit;
  activeTab.value = "devices"; // 默认选中设备列表
};

// 处理Tab切换
const handleTabChange = (tab: any) => {
  if (!selectedUnit.value) return;

  const tabName = tab.paneName || tab.name;
  console.log(
    "切换tab:",
    tabName,
    "deviceListRef:",
    deviceListRef.value,
    "samplerInfoRef:",
    samplerInfoRef.value
  );

  // 延时确保组件已经渲染
  nextTick(() => {
    if (tabName === "devices" && deviceListRef.value) {
      console.log("调用设备列表刷新");
      deviceListRef.value.refreshDevices();
    } else if (tabName === "sampler" && samplerInfoRef.value) {
      console.log("调用采集信息刷新");
      samplerInfoRef.value.refreshSamplerTree();
    }
  });
};

const handleSearch = () => {
  // 搜索逻辑在计算属性中处理
};

// 获取监控单元类型
const getMonitorUnitTypesData = async () => {
  try {
    const response = await getMonitorUnitTypes();
    monitorUnitCategories.value = response.data || [];
    monitorUnitCategories.value.sort((a: any, b: any) => a.order - b.order);
  } catch (error) {
    console.error("获取监控单元类型失败:", error);
  }
};

// 获取服务器列表
const getServerList = async () => {
  try {
    const response = await getWorkStationServerList();
    const servers = response.data || [];

    // 清空数组
    dataServers.value = [];
    rdsServers.value = [];
    rmuServers.value = [];

    servers.forEach((server: any) => {
      server.workStationId = server.workStationId.toString();

      if (server.workStationType === 2) {
        dataServers.value.push(server);
      }
      if (server.workStationType === 8) {
        rmuServers.value.push(server);
      }
      if (server.workStationType === 23) {
        rdsServers.value.push(server);
      }
    });
  } catch (error) {
    console.error("获取服务器列表失败:", error);
  }
};

// 显示新增对话框
const showAddDialog = () => {
  dialogTitle.value = "新增监控单元";
  resetForm();
  dialogVisible.value = true;
};

// 显示分发配置对话框
const showDistributionDialog = () => {
  distributionDialogKey.value++; // 强制重新创建组件
  distributionDialogVisible.value = true;
};

// 处理分发配置对话框关闭
const handleDistributionClose = () => {
  distributionDialogVisible.value = false;
};

// 显示新增端口对话框
const showAddPortDialog = async (unit: any) => {
  try {
    // 获取端口类型
    const response = await getPortTypes(unit.monitorUnitCategory);
    if (response.state) {
      portTypes.value = response.data || [];
      filterPortTypes();
    }

    // 重置表单数据
    Object.assign(portFormData, {
      portName: "",
      portNo: 0,
      portType: 1,
      phoneNumber: "",
      setting: "9600,n,8,1",
      monitorUnitId: unit.monitorUnitId,
      linkSamplerUnitId: 0,
      description: ""
    });

    contextMenuUnit.value = unit;
    addPortDialogVisible.value = true;
  } catch (error) {
    console.error("获取端口类型失败:", error);
    ElMessage.error("获取端口类型失败");
  }
};

// 过滤端口类型
const filterPortTypes = () => {
  if (showAllPortTypes.value) {
    filteredPortTypes.value = [...portTypes.value].sort(
      (a, b) => a.order - b.order
    );
  } else {
    filteredPortTypes.value = portTypes.value
      .filter(type => type.order < 50)
      .sort((a, b) => a.order - b.order);
  }
};

// 端口类型切换
const handlePortTypeSwitch = (value: boolean) => {
  showAllPortTypes.value = value;
  filterPortTypes();
};

// 端口号改变
const handlePortNoChange = () => {
  if (portFormData.portNo) {
    portFormData.portName = `COM${portFormData.portNo}`;
  } else {
    portFormData.portName = "";
  }
};

// 端口类型改变
const handlePortTypeChange = () => {
  const portType = portFormData.portType;
  switch (portType) {
    case 1:
    case 4:
    case 7:
    case 8:
    case 11:
    case 12:
    case 16:
      portFormData.setting = "9600,n,8,1";
      break;
    case 2:
    case 3:
    case 5:
    case 9:
    case 10:
      portFormData.setting = "0.0.0.0:0";
      break;
    case 6:
      portFormData.setting = "127.0.0.1:7070";
      break;
    case 13:
    case 14:
    case 15:
      portFormData.setting = "16002";
      break;
    case 17:
    case 18:
    case 19:
    case 20:
    case 21:
    case 22:
    case 23:
    case 24:
    case 25:
    case 26:
    case 27:
    case 28:
    case 29:
    case 30:
      portFormData.setting = "";
      break;
    case 31:
      portFormData.setting = "udp,:8080,";
      break;
    case 32:
      portFormData.setting = "127.0.0.1/2003";
      break;
    case 33:
      portFormData.setting = "127.0.0.1/public:private";
      break;
    case 34:
      portFormData.setting = "comm_host_dev.so";
      break;
    case 35:
      portFormData.setting = "comm_io_dev.so";
      break;
    default:
      portFormData.setting = "";
  }
};

// 提交端口表单
const handlePortSubmit = async () => {
  try {
    await portFormRef.value?.validate();

    portSubmitLoading.value = true;

    const response = await createPort(portFormData);
    if (response.state) {
      ElMessage.success("新增端口成功");
      addPortDialogVisible.value = false;
      // 可以在这里刷新相关数据
    } else {
      ElMessage.error(response.err_msg || "新增端口失败");
    }
  } catch (error) {
    console.error("新增端口失败:", error);
    ElMessage.error("新增端口失败");
  } finally {
    portSubmitLoading.value = false;
  }
};

// 显示编辑对话框
const showEditDialog = (unit: any) => {
  dialogTitle.value = "编辑监控单元";
  fillFormData(unit);
  dialogVisible.value = true;
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    monitorUnitId: "",
    monitorUnitName: "",
    ipAddress: "",
    monitorUnitCategory:
      monitorUnitCategories.value.length > 0
        ? monitorUnitCategories.value[0].typeId
        : 18,
    workStationId: "",
    description: "",
    stationId: ""
  });
  dataServerSelected.value = [];
  rdsServerSelected.value = [];

  nextTick(() => {
    formRef.value?.clearValidate();
  });
};

// 填充表单数据
const fillFormData = (unit: any) => {
  Object.assign(formData, {
    monitorUnitId: unit.monitorUnitId,
    monitorUnitName: unit.monitorUnitName,
    ipAddress: unit.ipAddress,
    monitorUnitCategory: unit.monitorUnitCategory,
    workStationId: unit.workStationId ? unit.workStationId.toString() : "",
    description: unit.description || "",
    stationId: unit.stationId || ""
  });

  // 处理服务器选择
  dataServerSelected.value = unit.dataServer ? unit.dataServer.split(", ") : [];
  rdsServerSelected.value = unit.rdsServer ? unit.rdsServer.split(", ") : [];
};

// 处理名称输入（过滤特殊字符）
const handleNameInput = () => {
  formData.monitorUnitName = formData.monitorUnitName.replace(
    /[\\\/\?<>*:.|""]/g,
    ""
  );
};

// 表单提交
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();

    submitLoading.value = true;

    // 准备提交数据
    const submitData = {
      ...formData,
      dataServer:
        dataServerSelected.value.length > 0
          ? dataServerSelected.value.join(", ")
          : "",
      rdsServer:
        rdsServerSelected.value.length > 0
          ? rdsServerSelected.value.join(", ")
          : "",
      stationId: currentStation.value.stationId || ""
    };

    if (formData.monitorUnitId) {
      // 编辑
      await updateMonitorUnit(submitData);
      ElMessage.success("编辑监控单元成功");
    } else {
      // 新增
      await createMonitorUnit(submitData);
      ElMessage.success("新增监控单元成功");
    }

    dialogVisible.value = false;
    refreshMonitorUnits();
  } catch (error) {
    console.error("提交失败:", error);
    ElMessage.error("提交失败");
  } finally {
    submitLoading.value = false;
  }
};

// 右键菜单处理
const handleContextMenu = (event: MouseEvent, unit: any) => {
  event.preventDefault();
  contextMenuUnit.value = unit;

  // 显示右键菜单
  ContextMenu.showContextMenu({
    x: event.x,
    y: event.y,
    items: [
      {
        label: "编辑监控单元",
        icon: "h:edit",
        onClick: () => {
          showEditDialog(unit);
        }
      },
      {
        label: "新增端口",
        icon: "h:plus",
        onClick: () => {
          showAddPortDialog(unit);
        }
      },
      {
        label: "删除监控单元",
        icon: "h:delete",
        onClick: () => {
          handleDelete(unit);
        }
      }
    ]
  });
};

// 删除监控单元
const handleDelete = async (unit: any) => {
  try {
    await ElMessageBox.confirm(
      `确认删除监控单元 "${unit.monitorUnitName}" 吗？`,
      "确认删除",
      {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    const response = await deleteMonitorUnit(Number(unit.monitorUnitId));

    // 检查响应的code字段
    if (response.code === 0) {
      ElMessage.success("删除成功");

      // 如果删除的是当前选中的单元，清空选中状态
      if (selectedUnit.value?.monitorUnitId === unit.monitorUnitId) {
        selectedUnit.value = null;
      }

      refreshMonitorUnits();
    } else if (response.code === 1001) {
      // 监控单元下存在设备，询问是否强制删除
      try {
        await ElMessageBox.confirm(
          `监控单元 "${unit.monitorUnitName}" 下存在设备，依然要删除并同时删除所有设备吗？`,
          "删除监控单元",
          {
            confirmButtonText: "确认",
            cancelButtonText: "取消",
            type: "warning"
          }
        );

        // 强制删除（包括设备）
        const forceResponse = await deleteMonitorUnit(
          Number(unit.monitorUnitId),
          true
        );

        if (forceResponse.code === 0) {
          ElMessage.success("删除成功");

          // 如果删除的是当前选中的单元，清空选中状态
          if (selectedUnit.value?.monitorUnitId === unit.monitorUnitId) {
            selectedUnit.value = null;
          }

          refreshMonitorUnits();
        } else {
          ElMessage.error(forceResponse.msg || "删除失败");
        }
      } catch (forceError) {
        // 用户取消强制删除
        if (forceError !== "cancel") {
          console.error("强制删除失败:", forceError);
          ElMessage.error("删除失败");
        }
      }
    } else {
      // 其他错误
      ElMessage.error(response.msg || "删除失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除失败:", error);
      ElMessage.error("删除失败");
    }
  }
};

// 生命周期
onMounted(async () => {
  await Promise.all([
    loadMonitorUnits(),
    getMonitorUnitTypesData(),
    getServerList()
  ]);
});
</script>

<style scoped>
.monitor-tabs {
  height: 100%;
}

.monitor-tabs :deep(.el-tabs__header) {
  margin-bottom: 0;
  background-color: transparent;
  border-bottom: 1px solid var(--el-border-color);
}

.monitor-tabs :deep(.el-tabs__nav-wrap) {
  padding: 0 24px;
  background-color: var(--el-bg-color);
}

.monitor-tabs :deep(.el-tabs__content) {
  padding: 0;
  height: calc(100% - 40px);
  overflow: hidden;
}

.monitor-tabs :deep(.el-tab-pane) {
  height: 100%;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .monitor-unit-container {
    padding: 16px;
  }

  .monitor-unit-container .flex {
    flex-direction: column;
    height: auto;
  }

  .monitor-unit-container .w-80 {
    width: 100%;
    height: 400px;
    margin-bottom: 24px;
  }
}
</style>
