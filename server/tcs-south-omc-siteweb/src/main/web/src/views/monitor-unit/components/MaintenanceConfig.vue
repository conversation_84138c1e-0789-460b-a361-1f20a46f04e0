<template>
  <div class="maintenance-config">
    <!-- 步骤一：备份当前配置 -->
    <div :class="getStepClassName(1)">
      <div class="flex items-center gap-3 mb-4">
        <div class="step-number">1</div>
        <div class="text-lg font-semibold">备份当前配置</div>
      </div>
      <div class="ml-11">
        <p class="mb-4">在上传新配置前，请先备份当前配置以确保数据安全。</p>
        
        <div class="flex items-center gap-4 mb-4">
          <!-- 已有备份提示 -->
          <el-alert
            v-if="hasRecentBackup"
            type="info"
            title="检测到最新备份"
            :closable="false"
            class="flex-1"
            size="small"
          >
            最近10分钟内已有可用备份
          </el-alert>
        </div>
        
        <div class="flex gap-2 mb-4">
          <el-button
            type="primary"
            :loading="backupLoading"
            @click="startBackup"
          >
            {{ hasRecentBackup ? '创建新备份' : '创建备份' }}
          </el-button>
          <el-button @click="refreshBackupList">
            刷新备份列表
          </el-button>
        </div>
        
        <!-- 备份状态显示 -->
        <el-alert
          v-if="backupStatus"
          :type="backupStatus === 'success' ? 'success' : 'warning'"
          :title="backupStatus === 'success' ? '备份创建成功!' : '正在创建备份...'"
          :closable="false"
          class="mb-4"
        >
          <div v-if="backupLoading && taskCurrentStep" class="mt-2">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm">{{ taskCurrentStep }}</span>
              <el-progress 
                :percentage="taskProgress" 
                :width="50" 
                type="circle" 
                :stroke-width="4"
                :show-text="false"
              />
            </div>
          </div>
        </el-alert>
        
        <!-- 备份状态信息 -->
        <div v-if="recentBackupStatus" class="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <el-icon class="text-blue-600 mr-2"><InfoFilled /></el-icon>
              <span class="text-sm text-blue-800 dark:text-blue-200">{{ recentBackupStatus }}</span>
            </div>
            <div v-if="lastCheckTime" class="text-xs text-blue-600 dark:text-blue-300">
              检查时间: {{ lastCheckTime }}
            </div>
          </div>
        </div>
        
        <!-- 备份文件列表 -->
        <div class="border border-gray-200 rounded-lg max-h-32 overflow-y-auto">
          <div v-if="backupList.length === 0" class="p-4 text-center text-gray-500">
            暂无可用备份，请先创建备份
          </div>
          <div
            v-for="(backup, index) in backupList"
            :key="index"
            class="flex justify-between items-center p-2 border-b border-gray-100 last:border-b-0"
          >
            <div>
              <div class="font-medium">{{ backup.name }}</div>
              <div class="text-sm text-gray-500">{{ backup.time }} - {{ backup.size }}</div>
            </div>
            <el-button 
              size="small" 
              :disabled="!backup.canDownload"
              :type="backup.canDownload ? 'primary' : 'default'"
              @click="downloadBackup(backup)"
            >
              {{ backup.canDownload ? '下载' : '不可用' }}
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 步骤二：上传新配置文件 -->
    <div :class="getStepClassName(2)" :style="{ pointerEvents: (hasRecentBackup || backupCompleted) ? 'auto' : 'none' }">
      <div class="flex items-center gap-3 mb-4">
        <div class="step-number">2</div>
        <div class="text-lg font-semibold">上传新配置文件</div>
      </div>
      <div class="ml-11">
        <p class="mb-4">请选择要上传的ZIP配置文件包。</p>
        
        <!-- 文件上传区域 -->
        <el-upload
          ref="uploadRef"
          class="upload-demo"
          drag
          :auto-upload="false"
          :show-file-list="false"
          accept=".zip"
          :before-upload="handleBeforeUpload"
          :on-change="handleFileChange"
        >
          <el-icon class="el-icon--upload text-4xl mb-4">
            <UploadFilled />
          </el-icon>
          <div class="el-upload__text">
            <p class="font-semibold">点击选择文件 或拖拽文件到此处</p>
            <p class="text-gray-500 mt-2">支持 .zip 格式，最大 50MB</p>
          </div>
        </el-upload>
        
        <!-- 选中文件信息 -->
        <div v-if="selectedFile" class="bg-gray-50 p-3 rounded-lg mt-4">
          <div><strong>已选择文件:</strong> {{ selectedFile.name }}</div>
          <div><strong>文件大小:</strong> {{ (selectedFile.size / 1024 / 1024).toFixed(2) }} MB</div>
        </div>
        
        <!-- 上传进度 -->
        <div v-if="showProgress" class="mt-4">
          <el-progress :percentage="uploadProgress" />
          <div class="mt-2 text-sm text-gray-600">
            <div class="flex items-center justify-between">
              <span>{{ taskCurrentStep || `上传中... ${Math.round(uploadProgress)}%` }}</span>
              <span v-if="uploadProgress < 100">{{ Math.round(uploadProgress) }}%</span>
            </div>
          </div>
        </div>
        
        <div class="mt-4">
          <el-button
            type="primary"
            :loading="uploadLoading"
            :disabled="!selectedFile"
            @click="uploadFile"
          >
            上传文件
          </el-button>
        </div>
      </div>
    </div>

    <!-- 步骤三：配置文件对比 -->
    <div :class="getStepClassName(3)">
      <div class="flex items-center gap-3 mb-4">
        <div class="step-number">3</div>
        <div class="text-lg font-semibold">配置文件对比</div>
      </div>
      <div class="ml-11">
        <p class="mb-4">以下是新旧配置文件的对比结果，请仔细检查变更内容。</p>
        
        <!-- 配置对比视图 -->
        <div class="grid grid-cols-2 gap-3 mb-3">
          <div class="border border-gray-200 rounded-lg overflow-hidden">
            <div class="bg-gray-50 p-2 border-b border-gray-200 font-semibold text-sm">
              当前配置 (config.xml)
            </div>
            <div class="p-3 font-mono text-xs bg-gray-50 max-h-48 overflow-y-auto">
              <div>&lt;configuration&gt;</div>
              <div>&nbsp;&nbsp;&lt;server&gt;</div>
              <div class="diff-removed">&nbsp;&nbsp;&nbsp;&nbsp;&lt;host&gt;*************&lt;/host&gt;</div>
              <div class="diff-removed">&nbsp;&nbsp;&nbsp;&nbsp;&lt;port&gt;8080&lt;/port&gt;</div>
              <div>&nbsp;&nbsp;&nbsp;&nbsp;&lt;timeout&gt;30000&lt;/timeout&gt;</div>
              <div>&nbsp;&nbsp;&lt;/server&gt;</div>
              <div>&nbsp;&nbsp;&lt;database&gt;</div>
              <div>&nbsp;&nbsp;&nbsp;&nbsp;&lt;url&gt;************************************************;</div>
              <div class="diff-removed">&nbsp;&nbsp;&nbsp;&nbsp;&lt;maxConnections&gt;10&lt;/maxConnections&gt;</div>
              <div>&nbsp;&nbsp;&lt;/database&gt;</div>
              <div>&lt;/configuration&gt;</div>
            </div>
          </div>
          
          <div class="border border-gray-200 rounded-lg overflow-hidden">
            <div class="bg-gray-50 p-2 border-b border-gray-200 font-semibold text-sm">
              新配置 (config.xml)
            </div>
            <div class="p-3 font-mono text-xs bg-gray-50 max-h-48 overflow-y-auto">
              <div>&lt;configuration&gt;</div>
              <div>&nbsp;&nbsp;&lt;server&gt;</div>
              <div class="diff-added">&nbsp;&nbsp;&nbsp;&nbsp;&lt;host&gt;*************&lt;/host&gt;</div>
              <div class="diff-added">&nbsp;&nbsp;&nbsp;&nbsp;&lt;port&gt;8090&lt;/port&gt;</div>
              <div>&nbsp;&nbsp;&nbsp;&nbsp;&lt;timeout&gt;30000&lt;/timeout&gt;</div>
              <div>&nbsp;&nbsp;&lt;/server&gt;</div>
              <div>&nbsp;&nbsp;&lt;database&gt;</div>
              <div>&nbsp;&nbsp;&nbsp;&nbsp;&lt;url&gt;************************************************;</div>
              <div class="diff-added">&nbsp;&nbsp;&nbsp;&nbsp;&lt;maxConnections&gt;20&lt;/maxConnections&gt;</div>
              <div>&nbsp;&nbsp;&lt;/database&gt;</div>
              <div>&lt;/configuration&gt;</div>
            </div>
          </div>
        </div>
        
        <!-- 关键变更提醒 -->
        <el-alert
          type="warning"
          title="检测到关键配置变更"
          :closable="false"
          class="mb-3"
        >
          <ul class="mt-2 ml-5 list-disc">
            <li>服务器地址从 ************* 变更为 *************</li>
            <li>端口从 8080 变更为 8090</li>
            <li>数据库最大连接数从 10 增加到 20</li>
          </ul>
        </el-alert>
        
        <div class="flex gap-2 mt-3">
          <el-button
            type="success"
            :loading="confirmLoading"
            @click="confirmUpload"
          >
            确认并应用配置
          </el-button>
          <el-button @click="cancelUpload">
            取消
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { Loading, UploadFilled, InfoFilled } from "@element-plus/icons-vue";
import {
  backupConfigWithPolling,
  importConfigWithPolling,
  checkRecentBackup,
  downloadBackupConfig,
  TaskPoller,
  type ConfigImportRequest
} from "@/api/config-distribute";

interface Props {
  monitorUnit?: any;
}

const props = defineProps<Props>();

// 响应式数据
const currentStep = ref(1);
const backupCompleted = ref(false);
const selectedFile = ref<File | null>(null);
const backupLoading = ref(false);
const uploadLoading = ref(false);
const confirmLoading = ref(false);
const uploadProgress = ref(0);
const showProgress = ref(false);
const backupStatus = ref("");

// 新增：任务轮询相关
const currentTaskPoller = ref<TaskPoller | null>(null);
const taskProgress = ref<number>(0);
const taskCurrentStep = ref<string>("");
const hasRecentBackup = ref(false);
const recentBackupStatus = ref("");

// 备份列表 - 现在基于真实API状态
const backupList = ref<any[]>([]);
const lastBackupTime = ref<string>("");
const lastCheckTime = ref<string>("");

// 上传组件引用
const uploadRef = ref();

// 计算步骤样式类名
const getStepClassName = (stepNum: number) => {
  let className = 'step';
  
  if (stepNum < currentStep.value) {
    className += ' completed';
  } else if (stepNum === currentStep.value) {
    className += ' active';
  } else if (stepNum === 2 && (hasRecentBackup.value || backupCompleted.value)) {
    // 第二步：如果有备份或备份已完成，则启用
    className += currentStep.value >= 2 ? ' active' : ' enabled';
  } else {
    className += ' disabled';
  }
  
  return className;
};

// 任务管理方法
const stopCurrentTask = () => {
  if (currentTaskPoller.value) {
    currentTaskPoller.value.stop();
    currentTaskPoller.value = null;
  }
  taskProgress.value = 0;
  taskCurrentStep.value = "";
};

// 开始备份
const startBackup = async () => {
  if (!props.monitorUnit?.monitorUnitId) {
    ElMessage.error("监控单元信息不完整，无法创建备份");
    return;
  }

  try {
    backupLoading.value = true;
    backupStatus.value = 'warning';
    taskProgress.value = 0;
    taskCurrentStep.value = "正在创建备份任务...";

    await backupConfigWithPolling(
      props.monitorUnit.monitorUnitId,
      (message: string, progress: number) => {
        taskProgress.value = progress;
        taskCurrentStep.value = message;
      }
    );

    // 备份完成
    backupLoading.value = false;
    backupStatus.value = 'success';
    backupCompleted.value = true;
    taskCurrentStep.value = "备份创建完成";
    taskProgress.value = 100;
    
    // 记录新备份时间
    const now = new Date();
    lastBackupTime.value = now.toISOString().slice(0, 19).replace('T', ' ');
    
    const successMsg = hasRecentBackup.value ? 
      "新的配置备份创建成功！已替换之前的备份。" : 
      "配置备份创建成功！";
    ElMessage.success(successMsg);
    
    // 延迟刷新备份状态，确保服务器端备份已就绪
    setTimeout(async () => {
      await refreshBackupStatus();
    }, 1000);
    
    currentStep.value = 2;
    
  } catch (error: any) {
    backupLoading.value = false;
    backupStatus.value = '';
    taskCurrentStep.value = "";
    const errorMsg = error.message || '备份创建失败';
    ElMessage.error(errorMsg);
    console.error('备份创建失败:', error);
  }
};

// 刷新备份状态
const refreshBackupStatus = async () => {
  if (!props.monitorUnit?.monitorUnitId) return;
  
  try {
    // 记录检查时间
    const checkTime = new Date();
    lastCheckTime.value = checkTime.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
    
    const response = await checkRecentBackup(props.monitorUnit.monitorUnitId, 10);
    const result = response.data;
    hasRecentBackup.value = result.hasRecentBackup;
    
    // 清空现有列表，确保数据一致性
    backupList.value = [];
    
    if (result.hasRecentBackup) {
      const tenMinutesAgo = new Date(checkTime.getTime() - 10 * 60 * 1000);
      const timeRangeStart = tenMinutesAgo.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
      const timeRangeEnd = checkTime.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
      
      recentBackupStatus.value = `时间范围 ${timeRangeStart} - ${timeRangeEnd} 内有备份可下载`;
      
      // 使用固定时间避免每次刷新都创建新时间戳
      let currentBackupTime = lastBackupTime.value;
      if (!currentBackupTime) {
        const now = new Date();
        currentBackupTime = now.toISOString().slice(0, 19).replace('T', ' ');
        lastBackupTime.value = currentBackupTime;
      }
      
      const backupItem = {
        monitorUnitId: props.monitorUnit.monitorUnitId,
        name: `backup_${props.monitorUnit.monitorUnitId}_${currentBackupTime.replace(/[:\s-]/g, '')}.zip`,
        time: currentBackupTime,
        size: '约 2-5 MB',
        canDownload: true
      };
      
      backupList.value.push(backupItem);
    } else {
      const tenMinutesAgo = new Date(checkTime.getTime() - 10 * 60 * 1000);
      const timeRangeStart = tenMinutesAgo.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
      const timeRangeEnd = checkTime.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
      
      recentBackupStatus.value = `时间范围 ${timeRangeStart} - ${timeRangeEnd} 内无可用备份`;
      lastBackupTime.value = "";
    }
  } catch (error) {
    console.error('检查备份状态失败:', error);
    recentBackupStatus.value = "检查备份状态失败";
    backupList.value = [];
  }
};

// 刷新备份列表
const refreshBackupList = async () => {
  await refreshBackupStatus();
  ElMessage.success(`备份状态已刷新 (${lastCheckTime.value})`);
};


// 下载备份
const downloadBackup = async (backup: any) => {
  if (!backup.canDownload) {
    ElMessage.warning("该备份不可下载");
    return;
  }
  
  try {
    await downloadBackupConfig(props.monitorUnit.monitorUnitId);
    ElMessage.success("备份下载成功！");
  } catch (error: any) {
    console.error("下载备份失败:", error);
    ElMessage.error(error.message || "下载备份失败");
  }
};

// 文件上传前验证
const handleBeforeUpload = (file: File) => {
  if (!file.name.endsWith('.zip')) {
    ElMessage.error('请选择 .zip 格式的文件');
    return false;
  }
  
  if (file.size > 50 * 1024 * 1024) {
    ElMessage.error('文件大小不能超过 50MB');
    return false;
  }
  
  return true;
};

// 文件选择变化
const handleFileChange = (file: any) => {
  selectedFile.value = file.raw;
};

// 上传文件
const uploadFile = async () => {
  if (!selectedFile.value) return;

  try {
    uploadLoading.value = true;
    showProgress.value = true;
    uploadProgress.value = 0;
    taskCurrentStep.value = "正在上传配置文件...";

    const importParams: ConfigImportRequest = {
      file: selectedFile.value,
      userId: 'admin',
      overwrite: true,
      importMode: 'FULL',
      remarks: `监控单元${props.monitorUnit?.monitorUnitId || ''}配置导入`
    };

    await importConfigWithPolling(
      importParams,
      (message: string, progress: number) => {
        uploadProgress.value = progress;
        taskCurrentStep.value = message;
      }
    );

    // 上传完成
    uploadLoading.value = false;
    uploadProgress.value = 100;
    taskCurrentStep.value = "配置文件上传完成";
    
    ElMessage.success("配置文件上传成功！");
    
    setTimeout(() => {
      currentStep.value = 3;
    }, 1000);

  } catch (error: any) {
    uploadLoading.value = false;
    showProgress.value = false;
    taskCurrentStep.value = "";
    const errorMsg = error.message || '配置文件上传失败';
    ElMessage.error(errorMsg);
    console.error('配置文件上传失败:', error);
  }
};

// 确认上传
const confirmUpload = () => {
  confirmLoading.value = true;

  setTimeout(() => {
    confirmLoading.value = false;
    ElMessage.success("配置更新成功！新配置已成功应用到采集器服务器。");
    // 可以选择重新开始流程
    setTimeout(() => {
      restartProcess();
    }, 2000);
  }, 2000);
};

// 取消上传
const cancelUpload = () => {
  ElMessage.confirm('确定要取消上传吗？', '确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    currentStep.value = 2;
    selectedFile.value = null;
    showProgress.value = false;
    uploadProgress.value = 0;
    if (uploadRef.value) {
      uploadRef.value.clearFiles();
    }
  }).catch(() => {
    // 用户取消
  });
};

// 重新开始流程
const restartProcess = () => {
  // 停止当前任务
  stopCurrentTask();
  
  currentStep.value = 1;
  backupCompleted.value = false;
  selectedFile.value = null;
  showProgress.value = false;
  uploadProgress.value = 0;
  backupStatus.value = '';
  taskProgress.value = 0;
  taskCurrentStep.value = '';
  hasRecentBackup.value = false;
  recentBackupStatus.value = '';
  lastBackupTime.value = '';
  lastCheckTime.value = '';
  backupList.value = [];
  
  if (uploadRef.value) {
    uploadRef.value.clearFiles();
  }
};

// 查看日志
const viewLogs = () => {
  ElMessage.info("正在打开日志查看器...");
};

// 组件初始化
onMounted(async () => {
  if (props.monitorUnit?.monitorUnitId) {
    await refreshBackupStatus();
    // 不自动跳转，让用户选择
  }
});
</script>

<style scoped>
.maintenance-config {
  padding: 12px;
  height: 100%;
  overflow-y: auto;
}

.step {
  border: 1px solid #e0e6ed;
  border-radius: 6px;
  padding: 16px;
  transition: all 0.3s;
  margin-bottom: 16px;
}

.step.active {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.step.completed {
  border-color: #67c23a;
  background-color: #f0f9ff;
}

.step.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.step.enabled {
  border-color: #409eff;
  background-color: #f8faff;
  cursor: pointer;
}

.step-number {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: #e0e6ed;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 13px;
}

.step.active .step-number {
  background: #409eff;
  color: white;
}

.step.completed .step-number {
  background: #67c23a;
  color: white;
}

.step.enabled .step-number {
  background: #e1f3ff;
  color: #409eff;
  border: 1px solid #409eff;
}

.diff-added {
  background: #f0f9ff;
  color: #389e0d;
}

.diff-removed {
  background: #fff2f0;
  color: #cf1322;
}

:deep(.el-upload-dragger) {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  padding: 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

:deep(.el-upload-dragger:hover) {
  border-color: #409eff;
  background-color: #ecf5ff;
}

:deep(.el-alert__content) {
  text-align: left;
}

.list-disc {
  list-style-type: disc;
}
</style>
