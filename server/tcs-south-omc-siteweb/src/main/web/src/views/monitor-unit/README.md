# 采集器页面拆分重构说明

## 概述

将原有的采集器页面（`index.vue`）拆分成两个独立的页面：
- **列表页面**（`list.vue`）：显示所有采集器的列表，支持搜索、筛选、操作等功能
- **详情页面**（`detail.vue`）：显示单个采集器的详细信息，包含设备列表和采集信息两个Tab页

## 页面结构

### 1. 列表页面 (`list.vue`)

#### 功能特性
- **表格展示**：使用 `el-table-v2` 虚拟表格展示采集器列表
- **搜索筛选**：支持按采集器名称、IP地址等字段搜索
- **批量操作**：支持批量选择和删除采集器
- **操作按钮**：每行包含查看详情、编辑、删除等操作按钮
- **新增功能**：支持新增采集器
- **分发配置**：支持批量分发配置

#### 表格列配置
- 选择列：支持单选和全选
- 采集器名称：可点击跳转到详情页
- IP地址：显示采集器IP
- 采集器类型：显示类型名称
- 连接状态：在线/离线状态标签
- 项目信息：项目名称和合同号
- 操作列：查看详情、编辑、删除按钮

#### 右键菜单转操作按钮
原有的右键菜单功能已转换为表格中的操作按钮：
- **编辑监控单元** → **编辑按钮**
- **新增端口** → 移至详情页面
- **删除监控单元** → **删除按钮**

### 2. 详情页面 (`detail.vue`)

#### 功能特性
- **路由参数**：通过 URL 参数 `/monitor-unit-detail/:id` 接收采集器ID
- **采集器信息头部**：显示采集器基本信息和状态
- **Tab页切换**：设备列表和采集信息两个Tab页
- **返回导航**：支持返回到列表页面
- **操作按钮**：编辑和删除采集器

#### Tab页内容
- **设备列表**：复用原有的 `DeviceList` 组件
- **采集信息**：复用原有的 `SamplerInfo` 组件

## 路由配置

在 `home.ts` 中添加了新的路由配置：

```typescript
{
  path: "/siteweb-omc/monitor-unit",
  name: "SitewebOmcMonitorUnit",
  component: () => import("@/views/monitor-unit/list.vue"),
  meta: {
    icon: "ic:outline-monitor-heart",
    title: "采集器",
    showParent: true
  }
},
{
  path: "/siteweb-omc/monitor-unit-detail/:id",
  name: "SitewebOmcMonitorUnitDetail",
  component: () => import("@/views/monitor-unit/detail.vue"),
  meta: {
    icon: "ic:outline-monitor-heart",
    title: "采集器详情",
    showLink: false
  }
}
```

## 页面跳转逻辑

### 从列表页跳转到详情页
1. **点击采集器名称**：直接跳转到详情页
2. **点击查看详情按钮**：跳转到详情页
3. **URL格式**：`/siteweb-omc/monitor-unit-detail/{monitorUnitId}`

### 从详情页返回列表页
1. **返回列表按钮**：页面头部的返回按钮
2. **删除成功后**：自动返回列表页

## API调用

### 列表页面
- `getMonitorUnits()`: 获取采集器列表
- `getMonitorUnitTypes()`: 获取采集器类型
- `createMonitorUnit()`: 创建采集器
- `updateMonitorUnit()`: 更新采集器
- `deleteMonitorUnit()`: 删除采集器

### 详情页面
- `getMonitorUnits()`: 根据ID获取采集器详情
- `deleteMonitorUnit()`: 删除采集器

## 组件复用

以下组件在详情页面中继续复用：
- `DeviceList.vue`: 设备列表组件
- `SamplerInfo.vue`: 采集信息组件
- `MuDistribution.vue`: 分发配置组件

## 样式优化

- 使用 Tailwind CSS 进行样式设计
- 支持深色模式
- 响应式布局设计
- 统一的视觉风格

## 测试建议

1. **功能测试**
   - 列表页面的搜索、筛选功能
   - 批量选择和删除功能
   - 新增和编辑采集器功能
   - 页面跳转功能

2. **详情页面测试**
   - 通过URL直接访问详情页
   - Tab页切换功能
   - 编辑和删除功能
   - 返回列表功能

3. **边界情况测试**
   - 无效的采集器ID
   - 网络请求失败
   - 空数据状态

## 注意事项

1. **原有页面保留**：原有的 `index.vue` 文件保留，以防需要回滚
2. **组件兼容性**：确保子组件（DeviceList、SamplerInfo）与新的页面结构兼容
3. **权限控制**：如有权限控制需求，需要在路由配置中添加相应的权限检查
4. **性能优化**：大数据量时考虑分页加载和虚拟滚动优化
