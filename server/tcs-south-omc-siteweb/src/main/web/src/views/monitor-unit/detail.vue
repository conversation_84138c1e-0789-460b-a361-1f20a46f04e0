<template>
  <div
    class="monitor-unit-detail-container bg-gray-50 dark:bg-gray-900 p-4 flex flex-col"
    style="height: calc(100vh - 48px)"
  >
    <!-- 主内容区域 -->
    <div
      v-if="selectedUnit"
      class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 flex-1 min-h-0 flex flex-col"
    >
      <!-- 采集器信息头部 -->
      <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <div class="w-6 h-6 bg-primary rounded flex items-center justify-center flex-shrink-0">
              <el-icon size="12" class="text-white"><Monitor /></el-icon>
            </div>
            <div>
              <h2 class="text-lg font-bold text-gray-900 dark:text-white">
                {{ selectedUnit.workStationName || selectedUnit.monitorUnitName }}
              </h2>
              <div class="flex items-center space-x-4 text-sm mt-1">
                <span>
                  <span class="text-gray-500 dark:text-gray-400">IP：</span>
                  <span class="text-gray-900 dark:text-white">{{ selectedUnit.ipAddress }}</span>
                </span>
                <span>
                  <span class="text-gray-500 dark:text-gray-400">类型：</span>
                  <span class="text-gray-900 dark:text-white">{{ muTypeObj[selectedUnit.monitorUnitCategory] || "未知类型" }}</span>
                </span>
                <span>
                  <span class="text-gray-500 dark:text-gray-400">状态：</span>
                  <el-tag
                    :type="selectedUnit.connectState === 1 ? 'success' : 'danger'"
                    effect="light"
                    size="small"
                  >
                    {{ selectedUnit.connectState === 1 ? "在线" : "离线" }}
                  </el-tag>
                </span>
                <span v-if="selectedUnit.projectName">
                  <span class="text-gray-500 dark:text-gray-400">项目：</span>
                  <span class="text-gray-900 dark:text-white">{{ selectedUnit.projectName }}</span>
                </span>
              </div>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <el-button
              type="primary"
              size="small"
              @click="handleEdit"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete"
            >
              删除
            </el-button>
            <el-button
              size="small"
              @click="goBack"
            >
              返回
            </el-button>
          </div>
        </div>
      </div>

      <!-- Tab区域 -->
      <div class="flex-1 min-h-0 flex flex-col">
        <el-tabs
          v-model="activeTab"
          class="monitor-tabs flex-1 min-h-0 flex flex-col"
          @tab-click="handleTabChange"
        >
          <el-tab-pane label="设备列表" name="devices" class="flex-1 min-h-0">
            <div class="h-full pl-4">
              <DeviceList
                ref="deviceListRef"
                :key="selectedUnit.monitorUnitId"
                :monitor-unit="selectedUnit"
              />
            </div>
          </el-tab-pane>
          <el-tab-pane label="采集信息" name="sampler" class="flex-1 min-h-0">
            <div class="h-full pl-4">
              <SamplerInfo
                ref="samplerInfoRef"
                :key="selectedUnit.monitorUnitId"
                :monitor-unit="selectedUnit"
              />
            </div>
          </el-tab-pane>
          <el-tab-pane label="维护" name="maintenance" class="flex-1 min-h-0">
            <div class="h-full pl-4">
              <MaintenanceConfig
                :monitor-unit="selectedUnit"
              />
            </div>
          </el-tab-pane>
          <el-tab-pane label="调试" name="debug" class="flex-1 min-h-0">
            <div class="h-full pl-4 flex items-center justify-center">
              <div class="text-center">
                <div class="w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-3">
                  <el-icon size="20" class="text-gray-400"><Setting /></el-icon>
                </div>
                <p class="text-gray-500 dark:text-gray-400 text-sm">调试功能开发中...</p>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 加载状态 -->
    <div
      v-else-if="loading"
      class="flex justify-center items-center h-64"
    >
      <el-icon size="32" class="text-primary animate-spin">
        <Loading />
      </el-icon>
    </div>

    <!-- 未找到数据 -->
    <div
      v-else
      class="text-center py-12"
    >
      <div
        class="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4"
      >
        <el-icon size="24" class="text-gray-400"><Monitor /></el-icon>
      </div>
      <p class="text-gray-500 dark:text-gray-400 mb-4">未找到采集器信息</p>
      <el-button type="primary" @click="goBack">返回列表</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  ArrowLeft,
  Monitor,
  Edit,
  Delete,
  Loading,
  Tools,
  Setting
} from "@element-plus/icons-vue";
import DeviceList from "./components/DeviceList.vue";
import SamplerInfo from "./components/SamplerInfo.vue";
import MaintenanceConfig from "./components/MaintenanceConfig.vue";
import {
  getMonitorUnits,
  getMonitorUnitTypes,
  deleteMonitorUnit,
  type MonitorUnit
} from "@/api/monitor-unit";

defineOptions({
  name: "MonitorUnitDetail"
});

const route = useRoute();
const router = useRouter();

// 响应式数据
const activeTab = ref("devices");
const loading = ref(false);
const selectedUnit = ref<MonitorUnit | null>(null);
const muTypeObj = ref<Record<number, string>>({});

// 子组件引用
const deviceListRef = ref();
const samplerInfoRef = ref();

// 获取路由参数中的采集器ID
const monitorUnitId = computed(() => route.params.id as string);

// 生命周期
onMounted(() => {
  if (monitorUnitId.value) {
    loadMonitorUnitTypes();
    loadMonitorUnitDetail();
  } else {
    ElMessage.error("缺少采集器ID参数");
    goBack();
  }
});

// 方法
const loadMonitorUnitTypes = async () => {
  try {
    const response = await getMonitorUnitTypes();
    const types = response.data || [];

    // 创建类型映射对象
    const typeObj: Record<number, string> = {};
    types.forEach((element: any) => {
      typeObj[element.typeId] = element.typeName;
    });
    muTypeObj.value = typeObj;
  } catch (error) {
    console.error("加载采集器类型失败:", error);
  }
};

const loadMonitorUnitDetail = async () => {
  try {
    loading.value = true;
    const response = await getMonitorUnits();
    const units = response.data || [];
    
    // 根据ID查找对应的采集器
    selectedUnit.value = units.find(
      unit => String(unit.monitorUnitId) === monitorUnitId.value
    ) || null;
    
    if (!selectedUnit.value) {
      ElMessage.error("未找到指定的采集器");
      goBack();
    }
  } catch (error) {
    console.error("加载采集器详情失败:", error);
    ElMessage.error("加载采集器详情失败");
  } finally {
    loading.value = false;
  }
};

const handleTabChange = (tab: any) => {
  activeTab.value = tab.name;

  // 刷新对应的子组件数据
  if (tab.name === "devices" && deviceListRef.value) {
    deviceListRef.value.refreshData?.();
  } else if (tab.name === "sampler" && samplerInfoRef.value) {
    samplerInfoRef.value.refreshData?.();
  } else if (tab.name === "maintenance") {
    // 维护Tab页逻辑（暂时为空）
    console.log("切换到维护Tab页");
  } else if (tab.name === "debug") {
    // 调试Tab页逻辑（暂时为空）
    console.log("切换到调试Tab页");
  }
};

const handleEdit = () => {
  // 跳转到编辑页面或打开编辑对话框
  router.push({
    path: `/siteweb-omc/monitor-unit`,
    query: {
      action: "edit",
      id: monitorUnitId.value
    }
  });
};

const handleDelete = async () => {
  if (!selectedUnit.value) return;

  try {
    await ElMessageBox.confirm(
      `确认删除采集器 "${selectedUnit.value.monitorUnitName}" 吗？`,
      "确认删除",
      {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    const response = await deleteMonitorUnit(Number(selectedUnit.value.monitorUnitId));
    if (response.code === 0) {
      ElMessage.success("删除成功");
      goBack();
    } else {
      ElMessage.error(response.message || "删除失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("请求异常");
      console.error(error);
    }
  }
};

const goBack = () => {
  router.push("/siteweb-omc/monitor-unit");
};
</script>

<style scoped>
.monitor-tabs :deep(.el-tabs__header) {
  margin: 0;
  padding: 0 16px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.monitor-tabs :deep(.el-tabs__content) {
  flex: 1;
  min-height: 0;
  padding: 0;
}

.monitor-tabs :deep(.el-tab-pane) {
  height: 100%;
}
</style>
