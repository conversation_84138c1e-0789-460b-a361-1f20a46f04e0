<template>
  <div class="sampler-info-container">
    <div class="tree-container" @contextmenu="handleEmptyContextMenu">
      <el-tree
        ref="treeRef"
        :data="treeData"
        :props="treeProps"
        :expand-on-click-node="false"
        :default-expand-all="true"
        node-key="key"
        highlight-current
        v-loading="loading"
        @node-click="handleNodeClick"
        @node-contextmenu="handleNodeContextMenu"
      >
        <template #default="{ node, data }">
          <div class="tree-node-content">
            <el-icon class="node-icon">
              <!-- 端口图标 -->
              <OfficeBuilding v-if="data.samplerUnits" />
              <!-- 采集单元图标 -->
              <Cpu v-else-if="data.equipments" />
              <!-- 设备图标 -->
              <Monitor v-else />
            </el-icon>
            <span 
              class="node-label"
              :class="{ 'device-label': data.equipmentId }"
              :title="data.equipmentId ? '点击跳转到设备管理' : ''"
            >
              {{ data.label }}
            </span>
          </div>
        </template>
      </el-tree>

      <el-empty 
        v-if="treeData.length === 0 && !loading" 
        description="暂无采集信息" 
        :image-size="80"
      />
    </div>

    <!-- 右键菜单 -->
    <el-dropdown
      ref="contextMenuRef"
      :show-timeout="0"
      :hide-timeout="0"
      trigger="contextmenu"
      @command="handleContextMenuCommand"
    >
      <span></span>
      <template #dropdown>
        <el-dropdown-menu>
          <!-- 端口级别菜单 -->
          <template v-if="contextMenuType === 'port'">
            <el-dropdown-item command="editPort">
              <el-icon><Edit /></el-icon>
              编辑端口
            </el-dropdown-item>
            <el-dropdown-item command="deletePort">
              <el-icon><Delete /></el-icon>
              删除端口
            </el-dropdown-item>
            <el-dropdown-item command="addSamplerUnit" divided>
              <el-icon><Plus /></el-icon>
              添加采集单元
            </el-dropdown-item>
          </template>

          <!-- 采集单元级别菜单 -->
          <template v-if="contextMenuType === 'samplerUnit'">
            <el-dropdown-item command="editSamplerUnit">
              <el-icon><Edit /></el-icon>
              编辑采集单元
            </el-dropdown-item>
            <el-dropdown-item command="deleteSamplerUnit">
              <el-icon><Delete /></el-icon>
              删除采集单元
            </el-dropdown-item>
            <el-dropdown-item command="addDevice" divided>
              <el-icon><Plus /></el-icon>
              添加设备
            </el-dropdown-item>
          </template>

          <!-- 设备级别菜单 -->
          <template v-if="contextMenuType === 'device'">
            <el-dropdown-item command="editDevice">
              <el-icon><Edit /></el-icon>
              编辑设备
            </el-dropdown-item>
            <el-dropdown-item command="deleteDevice">
              <el-icon><Delete /></el-icon>
              删除设备
            </el-dropdown-item>
          </template>

          <!-- 空白处菜单 -->
          <template v-if="contextMenuType === 'empty'">
            <el-dropdown-item command="addPort">
              <el-icon><Plus /></el-icon>
              添加端口
            </el-dropdown-item>
            <el-dropdown-item command="addDevice" divided>
              <el-icon><Plus /></el-icon>
              添加设备
            </el-dropdown-item>
          </template>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Edit, Delete, Plus, OfficeBuilding, Cpu, Monitor } from '@element-plus/icons-vue'
import { getMonitorUnitSamplerTree } from '@/api/monitor-unit'

interface Props {
  monitorUnit: any
}

const props = defineProps<Props>()

// 路由实例
const router = useRouter()

// 响应式数据
const loading = ref(false)
const treeData = ref<any[]>([])
const contextMenuRef = ref()
const treeRef = ref()
const contextMenuType = ref('')
const currentContextNode = ref<any>(null)

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'label'
}

// 方法
const loadSamplerTree = async () => {
  if (!props.monitorUnit?.monitorUnitId) return
  
  try {
    loading.value = true
    const response = await getMonitorUnitSamplerTree(props.monitorUnit.monitorUnitId)
    const rawData = response.data || []
    treeData.value = processTreeData(rawData)
  } catch (error) {
    console.error('加载采集信息失败:', error)
    ElMessage.error('加载采集信息失败')
  } finally {
    loading.value = false
  }
}

const processTreeData = (data: any[]): any[] => {
  if (!data) return []
  
  return data.map((node: any) => {
    const processedNode: any = {
      key: '',
      label: '',
      children: []
    }
    
    // 端口节点
    if (node.samplerUnits) {
      processedNode.key = `port_${node.portId}`
      processedNode.label = node.portName
      processedNode.samplerUnits = node.samplerUnits
      processedNode.portId = node.portId
      processedNode.portName = node.portName
      
      if (node.samplerUnits.length > 0) {
        processedNode.children = processTreeData(node.samplerUnits)
      }
    }
    // 采集单元节点
    else if (node.equipments) {
      processedNode.key = `sampler_${node.samplerUnitId}`
      processedNode.label = node.samplerUnitName
      processedNode.equipments = node.equipments
      processedNode.samplerUnitId = node.samplerUnitId
      processedNode.samplerUnitName = node.samplerUnitName
      
      if (node.equipments.length > 0) {
        processedNode.children = processTreeData(node.equipments)
      }
    }
    // 设备节点
    else {
      processedNode.key = `device_${node.equipmentId}`
      processedNode.label = node.equipmentName
      processedNode.equipmentId = node.equipmentId
      processedNode.equipmentName = node.equipmentName
    }
    
    return processedNode
  })
}

const handleNodeClick = (data: any, node: any) => {
  // 如果是设备节点，支持点击跳转
  if (data.equipmentId) {
    handleDeviceClick(data)
  }
}

const handleDeviceClick = (device: any) => {
  if (!device.equipmentId) {
    ElMessage.warning('设备ID不存在，无法跳转')
    return
  }
  
  // 跳转到设备管理页面
  router.push({
    path: `/siteweb-omc/device-management/${device.equipmentId}`,
    query: {
      title: device.equipmentName || '设备管理'
    }
  })
}

const handleNodeContextMenu = (event: MouseEvent, data: any, node: any) => {
  event.preventDefault()
  event.stopPropagation()
  
  currentContextNode.value = data
  
  // 确定菜单类型
  if (data.samplerUnits) {
    contextMenuType.value = 'port'
  } else if (data.equipments) {
    contextMenuType.value = 'samplerUnit'
  } else {
    contextMenuType.value = 'device'
  }
}

const handleEmptyContextMenu = (event: MouseEvent) => {
  event.preventDefault()
  contextMenuType.value = 'empty'
  currentContextNode.value = null
}

const handleContextMenuCommand = (command: string) => {
  switch (command) {
    case 'addPort':
      addPort()
      break
    case 'editPort':
      editPort(currentContextNode.value)
      break
    case 'deletePort':
      deletePort(currentContextNode.value)
      break
    case 'addSamplerUnit':
      addSamplerUnit(currentContextNode.value)
      break
    case 'editSamplerUnit':
      editSamplerUnit(currentContextNode.value)
      break
    case 'deleteSamplerUnit':
      deleteSamplerUnit(currentContextNode.value)
      break
    case 'addDevice':
      addDevice(currentContextNode.value)
      break
    case 'editDevice':
      editDevice(currentContextNode.value)
      break
    case 'deleteDevice':
      deleteDevice(currentContextNode.value)
      break
  }
}

const addPort = () => {
  ElMessage.info('添加端口功能待实现')
}

const editPort = (port: any) => {
  ElMessage.info('编辑端口功能待实现')
}

const deletePort = (port: any) => {
  ElMessageBox.confirm(
    `确认要删除端口 ${port.portName} 吗？`,
    '确认删除端口',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // 删除端口的逻辑
    ElMessage.success('删除成功')
    setTimeout(() => {
      loadSamplerTree()
    }, 500)
  }).catch(() => {
    // 取消删除
  })
}

const addSamplerUnit = (port: any) => {
  ElMessage.info('添加采集单元功能待实现')
}

const editSamplerUnit = (samplerUnit: any) => {
  ElMessage.info('编辑采集单元功能待实现')
}

const deleteSamplerUnit = (samplerUnit: any) => {
  ElMessageBox.confirm(
    `确认要删除采集单元 ${samplerUnit.samplerUnitName} 吗？`,
    '确认删除采集单元',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // 删除采集单元的逻辑
    ElMessage.success('删除成功')
    setTimeout(() => {
      loadSamplerTree()
    }, 500)
  }).catch(() => {
    // 取消删除
  })
}

const addDevice = (node: any) => {
  ElMessage.info('添加设备功能待实现')
}

const editDevice = (device: any) => {
  ElMessage.info('编辑设备功能待实现')
}

const deleteDevice = (device: any) => {
  ElMessageBox.confirm(
    `确认要删除设备 ${device.equipmentName} 吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // 删除设备的逻辑
    ElMessage.success('删除成功')
    setTimeout(() => {
      loadSamplerTree()
    }, 500)
  }).catch(() => {
    // 取消删除
  })
}

// 监听props变化
watch(() => props.monitorUnit, () => {
  loadSamplerTree()
}, { immediate: true })

// 生命周期
onMounted(() => {
  loadSamplerTree()
})
</script>

<style scoped>
.sampler-info-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tree-container {
  flex: 1;
  padding: 16px;
  overflow: auto;
}

.tree-node-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.node-icon {
  font-size: 16px;
  color: #409eff;
}

.node-label {
  font-size: 14px;
  color: #333;
}

.device-label {
  color: #409eff;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.device-label:hover {
  color: #337ecc;
  text-decoration: underline;
}

.el-tree {
  --el-tree-node-hover-bg-color: #f0f9ff;
}

.el-tree :deep(.el-tree-node__content:hover) {
  background-color: #f0f9ff;
}

.el-tree :deep(.is-current > .el-tree-node__content) {
  background-color: #e1f5fe;
}

.el-tree :deep(.el-tree-node__content) {
  height: 32px;
}

.el-tree :deep(.el-tree-node__expand-icon) {
  color: #409eff;
}
</style> 