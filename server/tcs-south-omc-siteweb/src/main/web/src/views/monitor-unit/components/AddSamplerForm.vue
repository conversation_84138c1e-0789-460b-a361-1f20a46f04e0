<template>
  <div class="add-sampler-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      size="default"
    >
      <div class="form-row">
        <el-form-item
          label="采集器"
          prop="samplerId"
          class="form-item required"
        >
          <el-select
            v-model="formData.selectedSampler"
            placeholder="请选择采集器"
            style="width: 250px"
            :disabled="isEdit"
            @change="handleSamplerChange"
          >
            <el-option
              v-for="item in samplers"
              :key="item.samplerId"
              :label="item.samplerName"
              :value="item"
            />
          </el-select>
        </el-form-item>
      </div>

      <div class="form-row">
        <el-form-item
          label="采集单元名称"
          prop="samplerUnitName"
          class="form-item required"
        >
          <el-input
            v-model="formData.samplerUnitName"
            placeholder="请输入采集单元名称"
            maxlength="128"
            style="width: 250px"
          />
        </el-form-item>
      </div>

      <div class="form-row">
        <el-form-item label="地址" prop="address" class="form-item required">
          <el-input-number
            v-model="formData.address"
            :min="0"
            :max="32767"
            placeholder="(0~32767)"
            style="width: 250px"
          />
        </el-form-item>
      </div>

      <div class="form-row">
        <el-form-item label="DLL路径" prop="dllPath" class="form-item required">
          <el-input
            v-model="formData.dllPath"
            placeholder="请输入DLL路径"
            style="width: 250px"
          />
        </el-form-item>
      </div>

      <div v-if="!isEdit" class="form-row">
        <el-form-item label="端口" prop="portNo" class="form-item">
          <el-input v-model="formData.portNo" disabled style="width: 250px" />
        </el-form-item>
      </div>

      <div class="form-row">
        <el-form-item
          label="采集间隔"
          prop="spUnitInterval"
          class="form-item required"
        >
          <el-input-number
            v-model="formData.spUnitInterval"
            :min="1"
            placeholder="请输入采集间隔"
            style="width: 250px"
          />
        </el-form-item>
      </div>

      <div class="form-row">
        <el-form-item label="电话" prop="phoneNumber" class="form-item">
          <el-input
            v-model="formData.phoneNumber"
            placeholder="请输入电话号码"
            style="width: 250px"
          />
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from "vue";
import { ElMessage } from "element-plus";
import {
  createSamplerUnit,
  updateSamplerUnit,
  getSamplerList,
  getSamplerUnitById
} from "@/api/monitor-unit";
import { id } from "element-plus/es/locales.mjs";

interface Props {
  monitorUnit: any;
  port?: any;
  sampler?: any;
  isEdit?: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  success: [];
}>();

// 表单引用
const formRef = ref();

// 采集器选项
const samplers = ref<any[]>([]);

// 表单数据
const formData = reactive({
  selectedSampler: null as any,
  samplerUnitName: "",
  address: 4,
  dllPath: "",
  portNo: "",
  spUnitInterval: 2,
  phoneNumber: "",
  samplerId: null as number | null,
  samplerType: null as number | null,
  portId: null as number | null,
  id: ""
});

// 表单验证规则
const formRules = {
  selectedSampler: [
    { required: true, message: "请选择采集器", trigger: "change" }
  ],
  samplerUnitName: [
    { required: true, message: "请输入采集单元名称", trigger: "blur" },
    {
      min: 1,
      max: 128,
      message: "采集单元名称长度在 1 到 128 个字符",
      trigger: "blur"
    }
  ],
  address: [
    { required: true, message: "请输入地址", trigger: "blur" },
    {
      type: "number" as const,
      min: 0,
      max: 32767,
      message: "地址必须在 0-32767 之间",
      trigger: "blur"
    }
  ],
  dllPath: [{ required: true, message: "请输入DLL路径", trigger: "blur" }],
  spUnitInterval: [
    { required: true, message: "请输入采集间隔", trigger: "blur" },
    {
      type: "number" as const,
      min: 1,
      message: "采集间隔必须大于0",
      trigger: "blur"
    }
  ]
};

// 获取采集器列表
const getSamplers = async () => {
  try {
    const response = await getSamplerList();
    samplers.value = response.data || [];
  } catch (error) {
    console.error("获取采集器列表失败:", error);
    // 使用模拟数据作为后备
    samplers.value = [
      {
        samplerId: 1,
        samplerName: "默认采集器",
        dllPath: "default.dll",
        samplerType: 1
      },
      {
        samplerId: 2,
        samplerName: "RS232采集器",
        dllPath: "rs232.dll",
        samplerType: 2
      },
      {
        samplerId: 3,
        samplerName: "TCP采集器",
        dllPath: "tcp.dll",
        samplerType: 3
      }
    ];
    ElMessage.warning("获取采集器列表失败，使用默认列表");
  }
};

// 加载采集单元数据
const loadSamplerData = async () => {
  console.log(
    "loadSamplerData 被调用，编辑模式:",
    props.isEdit,
    "采集单元:",
    props.sampler
  );

  if (!props.sampler?.samplerUnitId) {
    console.log("没有采集单元ID，跳过数据加载");
    return;
  }

  try {
    console.log("开始获取采集单元数据，ID:", props.sampler.samplerUnitId);
    const response = await getSamplerUnitById(props.sampler.samplerUnitId);

    if (response.state && response.data) {
      const samplerData = response.data;
      console.log("获取到的采集单元数据:", samplerData);

      Object.assign(formData, {
        samplerUnitName: samplerData.samplerUnitName || "",
        address: samplerData.address || 4,
        dllPath: samplerData.dllPath || "",
        spUnitInterval: samplerData.spUnitInterval || 2,
        phoneNumber: samplerData.phoneNumber || "",
        samplerId: samplerData.samplerId || null,
        samplerType: samplerData.samplerType || null,
        portId: samplerData.portId || null,
        id: samplerData.id || ""
      });

      // 找到对应的采集器
      const selectedSampler = samplers.value.find(
        s => s.samplerId === samplerData.samplerId
      );
      if (selectedSampler) {
        formData.selectedSampler = selectedSampler;
        console.log("找到对应的采集器:", selectedSampler);
      } else {
        console.log("未找到对应的采集器，samplerId:", samplerData.samplerId);
      }
    } else {
      console.log("API响应不正确:", response);
      // 使用传入的sampler数据作为后备
      loadDataFromProps();
    }
  } catch (error) {
    console.error("加载采集单元数据失败:", error);
    // 使用传入的sampler数据作为后备
    loadDataFromProps();
  }
};

// 从props加载数据（后备方案）
const loadDataFromProps = () => {
  console.log("使用props数据作为后备方案");
  const samplerData = props.sampler;

  Object.assign(formData, {
    samplerUnitName: samplerData.samplerUnitName || "",
    address: samplerData.address || 4,
    dllPath: samplerData.dllPath || "",
    spUnitInterval: samplerData.spUnitInterval || 2,
    phoneNumber: samplerData.phoneNumber || "",
    samplerId: samplerData.samplerId || null,
    samplerType: samplerData.samplerType || null,
    portId: samplerData.portId || null
  });

  // 找到对应的采集器
  const selectedSampler = samplers.value.find(
    s => s.samplerId === samplerData.samplerId
  );
  if (selectedSampler) {
    formData.selectedSampler = selectedSampler;
  }
};

// 采集器变化处理
const handleSamplerChange = () => {
  if (formData.selectedSampler) {
    formData.samplerId = formData.selectedSampler.samplerId;
    formData.dllPath = formData.selectedSampler.dllPath;
    formData.samplerType = formData.selectedSampler.samplerType;
  }
};

// 初始化表单数据
const initFormData = () => {
  console.log("初始化表单数据，编辑模式:", props.isEdit);

  // 如果是编辑模式，不重置数据，保留加载的数据
  if (props.isEdit && props.sampler) {
    console.log("编辑模式，跳过重置，保留已加载的数据");
    formData.portNo = props.port?.portName || "";
    formData.portId = props.port?.portId || formData.portId;
    return;
  }

  // 新增模式才重置表单数据
  Object.assign(formData, {
    selectedSampler: null,
    samplerUnitName: "",
    address: 4,
    dllPath: "",
    portNo: props.port?.portName || "",
    spUnitInterval: 2,
    phoneNumber: "",
    samplerId: null,
    samplerType: null,
    portId: props.port?.portId || null
  });
};

// 提交表单
const submit = async () => {
  if (!formRef.value) return false;

  // 验证必填项
  if (
    !formData.samplerUnitName ||
    formData.samplerUnitName.trim() === "" ||
    !formData.address ||
    !formData.spUnitInterval ||
    !formData.dllPath ||
    formData.dllPath.trim() === "" ||
    !formData.selectedSampler ||
    !formData.samplerId
  ) {
    ElMessage.error("请输入必填项");
    return false;
  }

  try {
    const submitData: any = {
      samplerUnitName: formData.samplerUnitName,
      address: formData.address,
      spUnitInterval: formData.spUnitInterval,
      dllPath: formData.dllPath,
      phoneNumber: formData.phoneNumber,
      samplerId: formData.samplerId,
      samplerType: formData.samplerType,
      monitorUnitId: props.monitorUnit.monitorUnitId,
      portId: formData.portId,
      parentSamplerUnitId: 0,
      connectState: 0,
      description: "",
      updateTime: new Date().toISOString().replace("T", " ").substr(0, 19),
      id: formData.id || ""
    };

    if (props.isEdit && props.sampler) {
      submitData.samplerUnitId = props.sampler.samplerUnitId;
      await updateSamplerUnit(submitData);
    } else {
      await createSamplerUnit(submitData);
    }

    emit("success");
    return true;
  } catch (error) {
    console.error("保存采集单元失败:", error);
    ElMessage.error("保存失败，请检查输入");
    return false;
  }
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  initFormData();
};

// 监听props变化
watch(
  () => [props.sampler, props.isEdit, props.port, props.monitorUnit],
  async () => {
    console.log("Props变化，重新初始化");

    // 先确保采集器列表已加载
    if (samplers.value.length === 0) {
      await getSamplers();
    }

    // 如果是编辑模式且有采集单元数据，先加载数据再初始化
    if (props.isEdit && props.sampler) {
      await loadSamplerData();
    }

    // 初始化表单数据
    initFormData();
  },
  { immediate: true }
);

// 生命周期
onMounted(() => {
  // watch会处理初始化，这里不需要重复调用
  console.log("组件已挂载");
});

// 暴露方法给父组件
defineExpose({
  submit,
  resetForm
});
</script>

<style scoped>
.add-sampler-form {
  padding: 20px;
}

.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.form-item {
  display: flex;
  align-items: center;
  flex: none;
}

.form-item .el-form-item__label {
  width: 120px;
  text-align: right;
  margin-right: 12px;
}

.required .el-form-item__label::before {
  content: "*";
  color: red;
  font-size: 10px;
  margin-right: 4px;
}
</style>
