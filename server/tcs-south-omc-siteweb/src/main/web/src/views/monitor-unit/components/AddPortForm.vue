<template>
  <div class="add-port-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      size="default"
    >
      <div class="form-row">
        <el-form-item
          label="监控单元名称"
          prop="monitorUnitName"
          class="form-item"
        >
          <el-input
            v-model="formData.monitorUnitName"
            disabled
            style="width: 250px"
          />
        </el-form-item>

        <el-form-item label="监控单元ID" prop="monitorUnitId" class="form-item">
          <el-input
            v-model="formData.monitorUnitId"
            disabled
            style="width: 250px"
          />
        </el-form-item>
      </div>

      <div class="form-row">
        <el-form-item label="端口名称" prop="portName" class="form-item">
          <el-input v-model="formData.portName" disabled style="width: 250px" />
        </el-form-item>

        <el-form-item label="端口号" prop="portNo" class="form-item required">
          <el-input-number
            v-model="formData.portNo"
            :min="1"
            :max="999"
            placeholder="请输入端口号"
            style="width: 250px"
            @change="handlePortNoChange"
          />
        </el-form-item>
      </div>

      <div class="form-row">
        <el-form-item label="端口类型" prop="portType" class="form-item">
          <div class="port-type-container">
            <el-select
              v-model="formData.portType"
              placeholder="请选择端口类型"
              style="width: 250px"
              @change="handlePortTypeChange"
            >
              <el-option
                v-for="item in showTypes"
                :key="item.itemId"
                :label="item.itemValue"
                :value="item.itemId"
              />
            </el-select>
            <el-switch
              v-model="isAll"
              active-text="所有"
              inactive-text="所有"
              style="margin-left: 12px; width: 60px"
              @change="handleShowAllChange"
            />
          </div>
        </el-form-item>
      </div>

      <div class="form-row">
        <el-form-item label="电话" prop="phoneNumber" class="form-item">
          <el-input
            v-model="formData.phoneNumber"
            placeholder="请输入电话号码"
            style="width: 250px"
          />
        </el-form-item>
      </div>

      <div class="form-row">
        <el-form-item label="设置" prop="setting" class="form-item-full">
          <el-input
            v-model="formData.setting"
            placeholder="请输入设置信息"
            style="width: 635px"
          />
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from "vue";
import { ElMessage } from "element-plus";
import {
  createPort,
  updatePort,
  getPortTypes,
  getPortById
} from "@/api/monitor-unit";
import { id } from "element-plus/es/locales.mjs";

interface Props {
  monitorUnit: any;
  port?: any;
  isEdit?: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  success: [];
}>();

// 表单引用
const formRef = ref();

// 端口类型相关
const portTypes = ref<any[]>([]);
const showTypes = ref<any[]>([]);
const isAll = ref(false);

// 表单数据
const formData = reactive({
  monitorUnitName: "",
  monitorUnitId: "",
  portName: "",
  portNo: null as number | null,
  portType: 1,
  phoneNumber: "",
  setting: "9600,n,8,1"
});

// 表单验证规则
const formRules = {
  portNo: [
    { required: true, message: "请输入端口号", trigger: "blur" },
    {
      type: "number" as const,
      min: 1,
      max: 999,
      message: "端口号必须在 1-999 之间",
      trigger: "blur"
    }
  ]
};

// 获取端口类型
const getPortTypesData = async () => {
  if (!props.monitorUnit?.monitorUnitCategory) return;

  try {
    const response = await getPortTypes(props.monitorUnit.monitorUnitCategory);
    // 处理真实API数据结构
    const responseData = response.data || [];
    portTypes.value = responseData.map((item: any) => ({
      itemId: item.itemId,
      itemValue: item.itemValue,
      itemAlias: item.itemAlias,
      order: parseInt(item.extendField1) || 50,
      enable: item.enable,
      monitorUnitCategories: item.extendField2
        ? item.extendField2.split(",").map((id: string) => parseInt(id.trim()))
        : []
    }));

    updateShowTypes();

    if (props.isEdit && props.port) {
      await loadPortData();
    }
  } catch (error) {
    console.error("获取端口类型失败:", error);
    // 使用模拟数据作为后备
    portTypes.value = [
      {
        itemId: 1,
        itemValue: "标准串口",
        itemAlias: "Standard Serial Port",
        order: 1,
        enable: true,
        monitorUnitCategories: []
      },
      {
        itemId: 2,
        itemValue: "SNU口",
        itemAlias: "SNU Port",
        order: 52,
        enable: true,
        monitorUnitCategories: []
      },
      {
        itemId: 3,
        itemValue: "SNMP口",
        itemAlias: "SNMP Port",
        order: 3,
        enable: true,
        monitorUnitCategories: []
      },
      {
        itemId: 5,
        itemValue: "虚拟端口",
        itemAlias: "Virtual Port",
        order: 2,
        enable: true,
        monitorUnitCategories: []
      },
      {
        itemId: 19,
        itemValue: "简单逻辑控制口",
        itemAlias: "Simple Logic Control Port",
        order: 5,
        enable: true,
        monitorUnitCategories: []
      }
    ];
    updateShowTypes();
    ElMessage.warning("获取端口类型失败，使用默认类型");
  }
};

// 加载端口数据
const loadPortData = async () => {
  if (!props.port?.portId) return;

  try {
    const response = await getPortById(props.port.portId);
    const portData = response.data;

    Object.assign(formData, {
      monitorUnitName: props.monitorUnit.monitorUnitName || "",
      monitorUnitId: props.monitorUnit.monitorUnitId || "",
      portName: portData.portName || "",
      portNo: portData.portNo || null,
      portType: portData.portType || 1,
      phoneNumber: portData.phoneNumber || "",
      setting: portData.setting || "9600,n,8,1",
      id: portData.id || ""
    });

    // 检查是否需要显示所有端口类型
    const currentType = portTypes.value.find(
      t => t.itemId === portData.portType
    );
    if (currentType && currentType.order > 50) {
      isAll.value = true;
      updateShowTypes();
    }
  } catch (error) {
    console.error("加载端口数据失败:", error);
    // 使用传入的port数据作为后备
    const portData = props.port;

    Object.assign(formData, {
      monitorUnitName: props.monitorUnit.monitorUnitName || "",
      monitorUnitId: props.monitorUnit.monitorUnitId || "",
      portName: portData.portName || "",
      portNo: portData.portNo || null,
      portType: portData.portType || 1,
      phoneNumber: portData.phoneNumber || "",
      setting: portData.setting || "9600,n,8,1"
    });

    // 检查是否需要显示所有端口类型
    const currentType = portTypes.value.find(
      t => t.itemId === portData.portType
    );
    if (currentType && currentType.order > 50) {
      isAll.value = true;
      updateShowTypes();
    }
  }
};

// 更新显示的端口类型
const updateShowTypes = () => {
  if (isAll.value) {
    showTypes.value = [...portTypes.value]
      .filter(t => t.enable)
      .sort((a, b) => a.order - b.order);
  } else {
    showTypes.value = portTypes.value
      .filter(t => t.enable && t.order <= 50)
      .sort((a, b) => a.order - b.order);
  }
};

// 初始化表单数据
const initFormData = () => {
  if (props.monitorUnit) {
    Object.assign(formData, {
      monitorUnitName: props.monitorUnit.monitorUnitName || "",
      monitorUnitId: props.monitorUnit.monitorUnitId || "",
      portName: "",
      portNo: null,
      portType: 1,
      phoneNumber: "",
      setting: "9600,n,8,1"
    });
  }
};

// 端口号变化处理
const handlePortNoChange = () => {
  if (formData.portNo) {
    formData.portName = `COM${formData.portNo}`;
  } else {
    formData.portName = "";
  }
};

// 端口类型变化处理
const handlePortTypeChange = () => {
  switch (formData.portType) {
    case 1: // 标准串口
    case 4: // PSTN巡检口
    case 7: // PSTN告警回叫口
    case 8: // PSTN手动维护口
    case 11: // ISDN拨号备份口
    case 12: // 专线备份口
    case 16: // IDU-Serial口
      formData.setting = "9600,n,8,1";
      break;
    case 2: // SNU口
    case 3: // SNMP口
    case 5: // 虚拟端口
    case 9: // OMC口
    case 10: // 系统接入口
      formData.setting = "0.0.0.0:0";
      break;
    case 6: // 终端服务器口
      formData.setting = "127.0.0.1:7070";
      break;
    case 13: // IDU-IP口
    case 14: // IDU-SMS口
    case 15: // IDU-IP-SMS口
      formData.setting = "16002";
      break;
    case 17: // DTU口
    case 18: // Host口
    case 19: // 简单逻辑控制口
    case 20: // IP巡检口
    case 21: // IP告警回叫口
    case 22: // IP手动维护口
    case 23: // GPRS巡检口
    case 24: // GPRS告警回叫口
    case 25: // GPRS手动维护口
    case 26: // GSM巡检口
    case 27: // GSM告警回叫口
    case 28: // GSM手动维护口
    case 29: // I2C端口
    case 30: // MDU端口
      formData.setting = "";
      break;
    case 31: // 移动B接口门禁透传端口
      formData.setting = "udp,:8080,";
      break;
    case 34: // 自诊断端口
      formData.setting = "comm_host_dev.so";
      break;
    case 35: // 板载IO端口
      formData.setting = "comm_io_dev.so";
      break;
    default:
      formData.setting = "9600,n,8,1";
      break;
  }
};

// 显示所有端口类型切换
const handleShowAllChange = () => {
  updateShowTypes();
};

// 提交表单
const submit = async () => {
  if (!formRef.value) return false;

  // 验证必填项
  if (!formData.portNo) {
    ElMessage.error("请输入必填项");
    return false;
  }

  try {
    const submitData: any = {
      ...formData,
      monitorUnitId: props.monitorUnit.monitorUnitId,
      linkSamplerUnitId: 0,
      description: null
    };

    if (props.isEdit && props.port) {
      submitData.portId = props.port.portId;
      await updatePort(submitData);
    } else {
      await createPort(submitData);
    }

    emit("success");
    return true;
  } catch (error) {
    console.error("保存端口失败:", error);
    ElMessage.error("保存失败，请检查输入");
    return false;
  }
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  initFormData();
};

// 监听props变化
watch(
  () => [props.port, props.isEdit, props.monitorUnit],
  () => {
    if (props.monitorUnit) {
      initFormData();
      getPortTypesData();
    }
  },
  { immediate: true }
);

// 生命周期
onMounted(() => {
  if (props.monitorUnit) {
    getPortTypesData();
  }
});

// 暴露方法给父组件
defineExpose({
  submit,
  resetForm
});
</script>

<style scoped>
.add-port-form {
  padding: 20px;
}

.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 20px;
}

.form-item {
  display: flex;
  align-items: center;
  flex: none;
}

.form-item-full {
  display: flex;
  align-items: center;
  width: 100%;
}

.form-item .el-form-item__label {
  width: 120px;
  text-align: right;
  margin-right: 12px;
}

.form-item-full .el-form-item__label {
  width: 120px;
  text-align: right;
  margin-right: 12px;
}

.required .el-form-item__label::before {
  content: "*";
  color: red;
  font-size: 10px;
  margin-right: 4px;
}

.port-type-container {
  display: flex;
  align-items: center;
}

.el-switch {
  --el-switch-on-color: #007fff;
}

.el-switch :deep(.el-switch__label) {
  width: 25px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
