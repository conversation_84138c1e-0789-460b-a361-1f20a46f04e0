import { http } from "@/utils/http";

// 设备模板树节点接口
export interface TemplateTreeNode {
  id: string;
  name: string;
  title: string;
  key: string;
  template: boolean;
  equipmentCategory?: string;
  parentId?: string;
  children?: TemplateTreeNode[];
  isLeaf?: boolean;
  expanded?: boolean;
}

// API响应格式
export type ApiResponse<T> = {
  code: number;
  timestamp: number;
  data: T;
  msg: string | null;
};

// 复制模板参数
export interface CopyTemplateParams {
  originEquipmentTemplateId: string;
  newEquipmentTemplateName: string;
  reason?: string;
}

// 批量切换模板参数
export interface BatchChangeTemplateParams {
  newEquipmentTemplateId: string;
  equipmentIds: string[];
}

// 枚举定义
export enum DeviceTemplateConfirmAction {
  UPDATE = "update",
  SAVE_AS = "saveAs",
  CANCEL = "cancel"
}

export enum BaseClassSelectorType {
  SIGNAL = 0,
  EVENT = 1,
  CONTROL = 2
}

// 模板服务状态管理
class DeviceTemplateService {
  private notShowAgain: boolean = false;

  getNotShowState(): boolean {
    return this.notShowAgain;
  }

  setNotShowState(state: boolean): void {
    this.notShowAgain = state;
  }
}

// 导出单例服务实例
export const deviceTemplateService = new DeviceTemplateService();

/**
 * 获取设备模板树（隐藏动态配置）
 */
export const getTemplateTree = () => {
  return http.request<ApiResponse<TemplateTreeNode[]>>(
    "get",
    "/api/thing/south-omc-siteweb/equipmenttemplate/tree?hideDynamicConfigTemplate=true"
  );
};

/**
 * 获取设备模板树（显示动态配置）
 */
export const getTemplateTreeShowHide = () => {
  return http.request<ApiResponse<TemplateTreeNode[]>>(
    "get",
    "/api/thing/south-omc-siteweb/equipmenttemplate/tree?hideDynamicConfigTemplate=false"
  );
};

/**
 * 根据ID获取模板详情
 */
export const getTemplateInfoById = (id: string) => {
  return http.request<ApiResponse<any>>(
    "get",
    `/api/thing/south-omc-siteweb/equipmenttemplate/${id}`
  );
};

/**
 * 删除模板
 */
export const deleteTemplate = (id: string) => {
  return http.request<ApiResponse<any>>(
    "delete",
    `/api/thing/south-omc-siteweb/equipmenttemplate/${id}`
  );
};

/**
 * 复制模板
 */
export const copyTemplate = (params: {
  originEquipmentTemplateId: number;
  newEquipmentTemplateName: string;
  reason?: string;
}) => {
  return http.request<ApiResponse<number>>(
    "post",
    "/api/thing/south-omc-siteweb/equipmenttemplate/copy",
    { data: params }
  );
};

/**
 * 导出模板
 */
export const exportTemplate = (id: string) => {
  return http.request<Blob>(
    "post",
    `/api/thing/south-omc-siteweb/equipmenttemplate/export?equipmentTemplateId=${id}`,
    {
      data: null,
      responseType: "blob"
    }
  );
};

/**
 * 升级为根模板
 */
export const upgradeTemplate = (id: string) => {
  return http.request<ApiResponse<any>>(
    "put",
    `/api/thing/south-omc-siteweb/equipmenttemplate/upgradetoroottemplate?equipmentTemplateId=${id}`,
    { data: {} }
  );
};

/**
 * 导出模板配置信息
 */
export const exportTemplateConfig = (id: string) => {
  return http.request<Blob>(
    "post",
    `/api/thing/south-omc-siteweb/equipmenttemplate/excel/export?equipmentTemplateId=${id}`,
    {
      data: null,
      responseType: "blob"
    }
  );
};

/**
 * 获取模板关联的设备
 */
export const getDevicesByTemplateId = (templateId: number) => {
  return http.request<ApiResponse<any[]>>(
    "get",
    `/api/thing/south-omc-siteweb/equipment/reference?equipmentTemplateId=${templateId}`
  );
};

/**
 * 导出关联设备
 */
export const exportAssociatedDevice = (id: string) => {
  return http.request<Blob>(
    "get",
    `/api/thing/south-omc-siteweb/equipment/reference/export?equipmentTemplateId=${id}`,
    { responseType: "blob" }
  );
};

/**
 * 批量切换设备模板
 */
export const batchChangeTemplate = (params: BatchChangeTemplateParams) => {
  return http.request<ApiResponse<any>>(
    "put",
    "/api/thing/south-omc-siteweb/equipment/batch/changetemplate",
    { data: params }
  );
};

/**
 * 根据模板ID列表获取模板DLL信息
 */
export const getTemplateDllByIds = (ids: string) => {
  return http.request<ApiResponse<string[]>>(
    "get",
    `/api/thing/south-omc-siteweb/equipmenttemplate/dllpath?equipmentTemplateIds=${ids}`
  );
};

/**
 * 根据设备分类获取模板树（同类型模板）
 */
export const getTemplateTreeByCategory = (
  equipmentCategory: string,
  search: string = ""
) => {
  return http.request<ApiResponse<TemplateTreeNode[]>>(
    "get",
    `/api/thing/south-omc-siteweb/equipmenttemplate/tree/excludecategory?equipmentCategory=${equipmentCategory}&equipmentTemplateName=${search}`
  );
};

/**
 * 获取全部模板树
 */
export const getTotalTemplateTree = (search: string = "") => {
  return http.request<ApiResponse<TemplateTreeNode[]>>(
    "get",
    `/api/thing/south-omc-siteweb/equipmenttemplate/tree/excludecategory?equipmentTemplateName=${search}`
  );
};

// 模板切换影响数据接口
export interface TemplateChangeEffect {
  stationName: string;
  equipmentName: string;
  objectType: string;
  objectName: string;
  description: string;
  samplerId: string;
}

// 模板切换影响参数
export interface TemplateChangeEffectParams {
  destTemplateId: string;
  originTemplateId: string;
  equipmentIds: string[];
}

/**
 * 获取模板切换影响数据
 */
export const getTemplateChangeEffect = (params: TemplateChangeEffectParams) => {
  return http.request<ApiResponse<TemplateChangeEffect[]>>(
    "post",
    "/api/thing/south-omc-siteweb/equipment/switchtemplate/checkchange",
    { data: params }
  );
};

/**
 * 导出模板切换影响数据
 */
export const exportTemplateChangeEffect = (
  params: TemplateChangeEffectParams
) => {
  return http.request<Blob>(
    "post",
    "/api/thing/south-omc-siteweb/equipment/switchtemplate/export",
    {
      data: params,
      responseType: "blob"
    }
  );
};

/**
 * 执行模板切换
 */
export const switchTemplate = (params: TemplateChangeEffectParams) => {
  return http.request<ApiResponse<any>>(
    "post",
    "/api/thing/south-omc-siteweb/equipment/switchtemplate",
    { data: params }
  );
};

/**
 * 根据监控单元ID列表获取监控单元信息
 */
export const getMonitorUnitsByIds = (monitorUnitIds: string) => {
  return http.request<ApiResponse<any[]>>(
    "get",
    `/api/thing/south-omc-siteweb/monitor-unit?monitorUnitIds=${monitorUnitIds}`
  );
};

/**
 * 获取监控单元类型
 */
export const getMonitorUnitCategories = () => {
  return http.request<ApiResponse<any[]>>(
    "get",
    "/api/thing/south-omc-siteweb/monitor-unit/types"
  );
};

// 设备模板信息接口
export interface DeviceTemplateInfo {
  equipmentTemplateId: number | null;
  equipmentTemplateName: string;
  equipmentType: number | null;
  equipmentCategory: number | null;
  vendor: string | null;
  unit: string | null;
  protocolCode: string | null;
  equipmentStyle: string | null;
  memo: string | null;
  propertyList: number[];
  equipmentBaseType: number | null;
  stationCategory: number | null;
  samplerName: string;
  description: string;
}

// 数据字典项接口
export interface DataDictionaryItem {
  itemId: number;
  itemValue: string;
}

// 设备基类接口
export interface BaseTypeItem {
  baseEquipmentId: number;
  baseEquipmentName: string;
}

// 局站类型接口
export interface StationCategoryItem {
  id: number;
  value: string;
}

// 厂商接口
export interface VendorItem {
  label: string;
  value: string;
}

// 信号接口
export interface SignalInfo {
  id: number | null;
  signalId: number;
  equipmentTemplateId: number;
  signalName: string;
  displayIndex: number;
  signalCategory: number;
  signalType: number;
  channelNo: number;
  channelType: number;
  expression: string;
  dataType: number;
  showPrecision: string;
  unit: string;
  storeInterval: number;
  absValueThreshold: number;
  percentThreshold: number;
  staticsPeriod: number;
  enable: boolean;
  visible: boolean;
  chargeStoreInterVal: number;
  chargeAbsValue: number;
  description: string;
  signalProperty: string;
  stateValue: string;
  moduleNo: number;
  baseTypeName: string;
  baseTypeId: number | null;
  acrossSignal: boolean;
  hasInstance: boolean;
  signalMeaningsList?: any[];
  signalPropertyList?: any[];
}

/**
 * 更新模板信息
 */
export const updateTemplate = (params: DeviceTemplateInfo) => {
  return http.request<ApiResponse<any>>(
    "put",
    "/api/thing/south-omc-siteweb/equipmenttemplate",
    { data: params }
  );
};

/**
 * 更新模板设备类型
 */
export const updateTemplateEquipmentType = (params: any) => {
  return http.request<ApiResponse<any>>(
    "put",
    "/api/thing/south-omc-siteweb/equipmenttemplate/inheritupdate",
    { data: params }
  );
};

/**
 * 获取设备类型列表
 */
export const getDeviceTypeList = () => {
  return http.request<ApiResponse<DataDictionaryItem[]>>(
    "get",
    "/api/thing/south-omc-siteweb/dataitems?entryId=8"
  );
};

/**
 * 获取设备分类列表
 */
export const getDeviceCategoryList = () => {
  return http.request<ApiResponse<DataDictionaryItem[]>>(
    "get",
    "/api/thing/south-omc-siteweb/dataitems?entryId=7"
  );
};

/**
 * 获取属性列表
 */
export const getPropertyList = () => {
  return http.request<ApiResponse<DataDictionaryItem[]>>(
    "get",
    "/api/thing/south-omc-siteweb/dataitems?entryId=9"
  );
};

/**
 * 获取厂商列表
 */
export const getVendorList = () => {
  return http.request<ApiResponse<DataDictionaryItem[]>>(
    "get",
    "/api/thing/south-omc-siteweb/dataitems?entryId=14"
  );
};

/**
 * 获取设备基类列表
 */
export const getDeviceBaseTypeList = () => {
  return http.request<ApiResponse<BaseTypeItem[]>>(
    "get",
    "/api/thing/south-omc-siteweb/equipmentbasetype/list"
  );
};

/**
 * 获取局站类型列表
 */
export const getStationCategoryList = () => {
  return http.request<ApiResponse<StationCategoryItem[]>>(
    "get",
    "/api/thing/south-omc-siteweb/station/category"
  );
};

// 信号相关API

/**
 * 获取信号种类列表
 */
export const getSignalCategoryList = () => {
  return http.request<ApiResponse<DataDictionaryItem[]>>(
    "get",
    "/api/thing/south-omc-siteweb/dataitems?entryId=17"
  );
};

/**
 * 获取信号分类列表
 */
export const getSignalTypeList = () => {
  return http.request<ApiResponse<DataDictionaryItem[]>>(
    "get",
    "/api/thing/south-omc-siteweb/dataitems?entryId=18"
  );
};

/**
 * 获取通道类型列表
 */
export const getChannelTypeList = () => {
  return http.request<ApiResponse<DataDictionaryItem[]>>(
    "get",
    "/api/thing/south-omc-siteweb/dataitems?entryId=22"
  );
};

/**
 * 获取数据类型列表
 */
export const getDataTypeList = () => {
  return http.request<ApiResponse<DataDictionaryItem[]>>(
    "get",
    "/api/thing/south-omc-siteweb/dataitems?entryId=70"
  );
};

/**
 * 获取信号属性列表
 */
export const getSignalPropertyList = () => {
  return http.request<ApiResponse<DataDictionaryItem[]>>(
    "get",
    "/api/thing/south-omc-siteweb/dataitems?entryId=21"
  );
};

/**
 * 获取模板信号列表
 * @param templateId 模板ID
 */
export const getSignalList = (templateId: string | number) => {
  return http.request<ApiResponse<SignalInfo[]>>(
    "get",
    `/api/thing/south-omc-siteweb/signal?equipmentTemplateId=${templateId}`
  );
};

/**
 * 根据设备模板和设备ID获取信号列表
 * @param templateId 模板ID
 * @param equipmentId 设备ID
 */
export const getSignalListByTempIdEqId = (
  templateId: string | number,
  equipmentId: string | number
) => {
  return http.request<ApiResponse<SignalInfo[]>>(
    "get",
    `/api/thing/south-omc-siteweb/signal?equipmentTemplateId=${templateId}&equipmentId=${equipmentId}`
  );
};

/**
 * 更新信号
 * @param params 信号数据
 */
export const updateSignal = (params: SignalInfo) => {
  return http.request<ApiResponse<any>>(
    "put",
    "/api/thing/south-omc-siteweb/signal/update",
    { data: params }
  );
};

/**
 * 新增信号
 * @param params 信号数据
 */
export const addSignal = (params: Partial<SignalInfo>) => {
  return http.request<ApiResponse<any>>(
    "post",
    "/api/thing/south-omc-siteweb/signal/create",
    { data: params }
  );
};

/**
 * 删除信号
 * @param templateId 模板ID
 * @param signalIds 信号ID列表，逗号分隔
 */
export const deleteSignal = (
  templateId: string | number,
  signalIds: string
) => {
  return http.request<ApiResponse<any>>(
    "delete",
    `/api/thing/south-omc-siteweb/signal/batchdelete?eqTemplateId=${templateId}&signalIds=${signalIds}`
  );
};

/**
 * 增加关联事件
 * @param templateId 模板ID
 * @param signalId 信号ID
 */
export const addRelateEvent = (
  templateId: string | number,
  signalId: string | number
) => {
  return http.request<ApiResponse<any>>(
    "post",
    "/api/thing/south-omc-siteweb/event/linkevent",
    {
      data: {
        equipmentTemplateId: templateId,
        signalId: signalId
      }
    }
  );
};

/**
 * 批量更新信号字段
 */
export const batchSignalFieldCopy = (
  params: Array<{
    equipmentTemplateId: number;
    signalId: number;
    fieldName: string;
    fieldValue: string;
  }>
) => {
  return http.request<ApiResponse<any>>(
    "put",
    "/api/thing/south-omc-siteweb/signal/field/copy",
    { data: params }
  );
};

/**
 * 获取电池设备分类
 */
export const getBatteryDeviceCategory = () => {
  return http.request<ApiResponse<any>>(
    "get",
    "/api/thing/south-omc-siteweb/dataitems/equipmentcategorys?originCategoryKey=24"
  );
};

/**
 * 验证表达式
 * @param expression 表达式内容
 */
export const validateExpression = (expression: string) => {
  return http.request<ApiResponse<boolean>>(
    "post",
    "/api/thing/south-omc-siteweb/expression/validate",
    { data: { expression } }
  );
};

/**
 * 获取表达式描述
 * @param templateId 模板ID
 * @param expression 表达式内容
 * @param isCrossStation 是否跨站表达式
 */
export const getExpressionDescription = (
  templateId: string | number,
  expression: string,
  isCrossStation?: boolean
) => {
  const params = isCrossStation
    ? {
        equipmentTemplateId: templateId,
        expression,
        isCrossStationMonitorUnit: true
      }
    : { equipmentTemplateId: templateId, expression };

  return http.request<ApiResponse<string>>(
    "post",
    "/api/thing/south-omc-siteweb/expression/analysis",
    { data: params }
  );
};

// 信号含义接口
export interface SignalMeaning {
  id?: number;
  equipmentTemplateId: number;
  signalId: number;
  stateValue: number;
  meanings: string;
  baseCondId?: number;
}

// 基类状态含义接口
export interface BaseStatusMeaning {
  value: number;
  meaning: string;
}

// 基类状态信息接口
export interface BaseStatusInfo {
  baseStatusName: string;
  statusMeanings: BaseStatusMeaning[];
}

/**
 * 获取信号基类状态含义
 * @param equipmentTemplateId 设备模板ID
 * @param signalId 信号ID
 */
export const getSignalBaseTypeMeanings = (
  equipmentTemplateId: string | number,
  signalId: string | number
) => {
  return http.request<ApiResponse<BaseStatusInfo>>(
    "get",
    `/api/config/signal/basetypemeanings?equipmentTemplateId=${equipmentTemplateId}&signalId=${signalId}`
  );
};

/**
 * 获取控制基类状态含义
 * @param equipmentTemplateId 设备模板ID
 * @param controlId 控制ID
 */
export const getControlBaseTypeMeanings = (
  equipmentTemplateId: string | number,
  controlId: string | number
) => {
  return http.request<ApiResponse<BaseStatusInfo>>(
    "get",
    `/api/config/control/basetypemeanings?equipmentTemplateId=${equipmentTemplateId}&controlId=${controlId}`
  );
};

// 基类选择器相关接口

// 设备类型节点接口
export interface EquipmentTypeNode {
  value: number;
  label: string;
  children: EquipmentSubTypeNode[];
}

// 设备子类型节点接口
export interface EquipmentSubTypeNode {
  value: number;
  label: string;
  children?: never[];
}

// 基类信息接口
export interface BaseClassInfo {
  baseTypeId: number;
  baseTypeName: string;
  baseNameExt?: string;
  description?: string;
}

// 基类选择器参数接口
export interface BaseClassSelectorParams {
  type: BaseClassSelectorType;
  baseTypeId: number | null;
  name: string;
  eventCondition?: string;
  equipmentTemplateId: number;
}

/**
 * 获取基类设备类型树
 */
export const getBaseClassEquipmentTree = () => {
  return http.request<ApiResponse<EquipmentTypeNode[]>>(
    "get",
    "/api/config/equipmentsubtype/tree"
  );
};

/**
 * 根据设备类型获取基类列表
 * @param apiType API类型 (signalbasedic | eventbasedic | commandbasedic)
 * @param params 查询参数
 */
export const getBaseClassList = (
  apiType: string,
  params: { eqTypeId: number; eqSubTypeId: number }
) => {
  return http.request<ApiResponse<BaseClassInfo[]>>(
    "get",
    `/api/config/${apiType}/search`,
    { params }
  );
};

/**
 * 根据基类ID获取设备类型
 * @param apiType API类型
 * @param baseTypeId 基类ID
 */
export const getEquipmentTypeByBaseTypeId = (
  apiType: string,
  baseTypeId: number
) => {
  return http.request<ApiResponse<{ baseEquipmentId: number }>>(
    "get",
    `/api/config/${apiType}/${baseTypeId}`
  );
};

/**
 * 根据基础设备ID获取设备类型
 * @param baseEquipmentId 基础设备ID
 */
export const getEquipmentTypeByBaseEquipmentId = (baseEquipmentId: number) => {
  return http.request<
    ApiResponse<Array<{ equipmentTypeId: number; equipmentSubTypeId: number }>>
  >("get", `/api/config/equipmentbasetype/${baseEquipmentId}`);
};

/**
 * 更新表格行数据
 * @param apiType API类型 (signal | event | control)
 * @param rowData 行数据
 */
export const updateTableRow = (apiType: string, rowData: any) => {
  return http.request<ApiResponse<any>>(
    "put",
    `/api/thing/south-omc-siteweb/${apiType}/update`,
    { data: rowData }
  );
};

/**
 * 添加基类类型
 * @param apiType API类型
 * @param params 参数
 */
export const addBaseType = (apiType: string, params: any) => {
  return http.request<ApiResponse<any>>(
    "post",
    `/api/thing/south-omc-siteweb/${apiType}/create`,
    { data: params }
  );
};

// 事件相关接口定义
export interface EventInfo {
  id?: number;
  eventId?: number;
  equipmentTemplateId?: number;
  eventName?: string;
  displayIndex?: number;
  eventCategory?: number;
  startType?: number;
  endType?: number;
  eventConditionList?: any[];
  eventConditionListLabel?: string;
  startExpression?: string;
  suppressExpression?: string;
  signalId?: number;
  description?: string;
  enable?: boolean;
  visible?: boolean;
  moduleNo?: number;
  turnover?: number;
  hasInstance?: boolean;
}

// 控制相关接口定义
export interface ControlInfo {
  id?: number;
  controlId?: number;
  equipmentTemplateId?: number;
  controlName?: string;
  displayIndex?: number;
  controlCategory?: number;
  controlSeverity?: number;
  cmdToken?: string;
  timeOut?: number;
  retry?: number;
  commandType?: number;
  controlType?: number;
  channelNo?: number;
  channelType?: number;
  expression?: string;
  dataType?: number;
  maxValue?: number;
  minValue?: number;
  signalId?: number;
  defaultValue?: any;
  unit?: string;
  enable?: boolean;
  visible?: boolean;
  description?: string;
  moduleNo?: number;
  baseTypeName?: string;
  baseTypeId?: number | null;
  hasInstance?: boolean;
}

// 事件相关API

/**
 * 根据模板ID获取事件列表
 * @param templateId 模板ID
 */
export const getTemplateEventById = (templateId: string | number) => {
  return http.request<ApiResponse<EventInfo[]>>(
    "get",
    `/api/thing/south-omc-siteweb/event?equipmentTemplateId=${templateId}`
  );
};

/**
 * 更新事件行数据
 * @param params 事件数据
 */
export const updateTemplateEventRow = (params: EventInfo) => {
  return http.request<ApiResponse<any>>(
    "put",
    "/api/thing/south-omc-siteweb/event/update",
    { data: params }
  );
};

/**
 * 批量更新事件行数据
 * @param params 事件数据数组
 */
export const batchUpdateTemplateEventRow = (params: EventInfo[]) => {
  return http.request<ApiResponse<any>>(
    "put",
    "/api/thing/south-omc-siteweb/event/update/batch",
    { data: params }
  );
};

/**
 * 添加事件
 * @param params 事件数据
 */
export const addEvent = (params: Partial<EventInfo>) => {
  return http.request<ApiResponse<any>>(
    "post",
    "/api/thing/south-omc-siteweb/event/create",
    { data: params }
  );
};

/**
 * 删除事件
 * @param templateId 模板ID
 * @param eventId 事件ID
 */
export const deleteEvent = (
  templateId: string | number,
  eventId: string | number
) => {
  return http.request<ApiResponse<any>>(
    "delete",
    `/api/thing/south-omc-siteweb/event/delete?eqTemplateId=${templateId}&eventId=${eventId}`
  );
};

/**
 * 批量更新事件字段
 * @param params 批量更新参数
 */
export const batchEventFieldCopy = (
  params: Array<{
    equipmentTemplateId: number;
    eventId: number;
    fieldName: string;
    fieldValue: string;
  }>
) => {
  return http.request<ApiResponse<any>>(
    "put",
    "/api/thing/south-omc-siteweb/event/field/copy",
    { data: params }
  );
};

/**
 * 获取事件告警等级列表
 */
export const getEventSeverityList = () => {
  return http.request<ApiResponse<DataDictionaryItem[]>>(
    "get",
    "/api/thing/south-omc-siteweb/dataitems?entryId=23"
  );
};

/**
 * 获取事件类型列表
 */
export const getEventCategoryList = () => {
  return http.request<ApiResponse<DataDictionaryItem[]>>(
    "get",
    "/api/thing/south-omc-siteweb/dataitems?entryId=24"
  );
};

/**
 * 获取开始类型列表
 */
export const getStartTypeList = () => {
  return http.request<ApiResponse<DataDictionaryItem[]>>(
    "get",
    "/api/thing/south-omc-siteweb/dataitems?entryId=25"
  );
};

/**
 * 获取结束类型列表
 */
export const getEndTypeList = () => {
  return http.request<ApiResponse<DataDictionaryItem[]>>(
    "get",
    "/api/thing/south-omc-siteweb/dataitems?entryId=26"
  );
};

/**
 * 获取控制重要度列表
 */
export const getControlSeverityList = () => {
  return http.request<ApiResponse<DataDictionaryItem[]>>(
    "get",
    "/api/thing/south-omc-siteweb/dataitems?entryId=28"
  );
};

/**
 * 获取控制命令种类列表
 */
export const getControlCategoryList = () => {
  return http.request<ApiResponse<DataDictionaryItem[]>>(
    "get",
    "/api/thing/south-omc-siteweb/dataitems?entryId=31"
  );
};

/**
 * 获取控制命令类型列表
 */
export const getCommandTypeList = () => {
  return http.request<ApiResponse<DataDictionaryItem[]>>(
    "get",
    "/api/thing/south-omc-siteweb/dataitems?entryId=32"
  );
};

/**
 * 获取控件分类列表
 */
export const getControlTypeList = () => {
  return http.request<ApiResponse<DataDictionaryItem[]>>(
    "get",
    "/api/thing/south-omc-siteweb/dataitems?entryId=68"
  );
};

// 控制相关API

/**
 * 获取设备模板控制列表
 * @param templateId 设备模板ID
 */
export const getEquipmentTemplateControl = (templateId: string | number) => {
  return http.request<ApiResponse<ControlInfo[]>>(
    "get",
    `/api/thing/south-omc-siteweb/control/list?equipmentTemplateId=${templateId}`
  );
};

/**
 * 创建控制
 * @param params 控制数据
 */
export const createControl = (params: Partial<ControlInfo>) => {
  return http.request<ApiResponse<any>>(
    "post",
    "/api/thing/south-omc-siteweb/control/create",
    { data: params }
  );
};

/**
 * 更新控制
 * @param params 控制数据
 */
export const updateControl = (params: ControlInfo) => {
  return http.request<ApiResponse<any>>(
    "put",
    "/api/thing/south-omc-siteweb/control/update",
    { data: params }
  );
};

/**
 * 删除设备模板控制
 * @param params 删除参数
 */
export const deleteEquipmentTemplateControl = (params: {
  equipmentTemplateId: string | number;
  controlIds: string;
}) => {
  return http.request<ApiResponse<any>>(
    "delete",
    "/api/thing/south-omc-siteweb/control/batchdelete",
    { params }
  );
};

/**
 * 批量更新控制字段
 * @param params 批量更新参数
 */
export const batchControlFieldCopy = (
  params: Array<{
    equipmentTemplateId: number;
    controlId: number;
    fieldName: string;
    fieldValue: string;
  }>
) => {
  return http.request<ApiResponse<any>>(
    "put",
    "/api/thing/south-omc-siteweb/control/field/copy",
    { data: params }
  );
};

// 其他工具API

/**
 * 根据设备分类和协议获取模板树
 * @param equipmentCategory 设备分类
 * @param protocolCode 协议代码
 */
export const getTemplateTreeFormCategory = (
  equipmentCategory: number,
  protocolCode: string
) => {
  return http.request<ApiResponse<TemplateTreeNode[]>>(
    "get",
    `/api/thing/south-omc-siteweb/equipmenttemplate/tree/excludecategory?equipmentCategory=${equipmentCategory}&protocolCode=${protocolCode}`
  );
};

/**
 * 根据设备模板ID获取信号|事件|控制
 * @param templateId 设备模板ID
 */
export const getSecFromTemplateId = (templateId: string | number) => {
  return http.request<ApiResponse<any>>(
    "get",
    `/api/thing/south-omc-siteweb/equipmenttemplate/equipmentTemplateId?equipmentTemplateId=${templateId}`
  );
};

/**
 * 获取数据字典
 * @param entryId 类型ID
 */
export const getDataitems = (entryId: number) => {
  return http.request<ApiResponse<DataDictionaryItem[]>>(
    "get",
    `/api/thing/south-omc-siteweb/dataitems?entryId=${entryId}`
  );
};

/**
 * 根据设备模板获取简化信号列表
 * @param templateId 设备模板ID
 */
export const getSignalListByTempId = (templateId: string | number) => {
  return http.request<ApiResponse<any[]>>(
    "get",
    `/api/thing/south-omc-siteweb/signal/simplifysignals?equipmentTemplateId=${templateId}`
  );
};

/**
 * 获取电池状态列表
 */
export const getBatteryStateList = () => {
  return http.request<ApiResponse<DataDictionaryItem[]>>(
    "get",
    "/api/thing/south-omc-siteweb/dataitems?entryId=12"
  );
};

/**
 * 获取设备模板操作日志列表
 * @param templateId 模板ID
 * @param current 当前页
 * @param size 页面大小
 * @param startTime 开始时间
 * @param endTime 结束时间
 */
export const getEquipmentTemplateOperationLog = (
  templateId: string | number,
  current?: number,
  size?: number,
  startTime?: string | null,
  endTime?: string | null
) => {
  // 分页参数放在URL查询参数中
  const params = new URLSearchParams();
  if (current !== undefined) {
    params.append("current", current.toString());
  }
  if (size !== undefined) {
    params.append("size", size.toString());
  }

  // 请求体数据
  const requestData = {
    objectTypes: 10, // 设备模板类型固定为10
    objectId: templateId.toString(),
    startTime: startTime || null,
    endTime: endTime || null
  };

  return http.request<
    ApiResponse<{
      records: Array<{
        userName: string;
        objectId: string;
        objectType: string;
        propertyName: string;
        operationTime: string;
        operationType: string;
        oldValue: string | null;
        newValue: string;
        objectName: string | null;
      }>;
      total: number;
      current: number;
      size: number;
      pages: number;
    }>
  >(
    "post",
    `/api/thing/south-omc-siteweb/operationdetail/page/equipmenttemplate?${params.toString()}`,
    { data: requestData }
  );
};

/**
 * 获取操作类型列表
 */
export const getOperationTypes = () => {
  return http.request<
    ApiResponse<
      Array<{
        itemId: number;
        itemValue: string;
      }>
    >
  >("get", "/api/thing/south-omc-siteweb/operationdetailtype");
};
