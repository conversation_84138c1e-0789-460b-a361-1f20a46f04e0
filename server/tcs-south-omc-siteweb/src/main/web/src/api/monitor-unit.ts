import { http } from "@/utils/http";

// 监控单元类型定义
export interface MonitorUnit {
  monitorUnitId: number;
  monitorUnitName: string;
  monitorUnitCategory: number;
  monitorUnitCode: string;
  workStationId: number;
  stationId: number;
  ipAddress: string;
  runMode: number;
  configFileCode: string;
  configUpdateTime: string | null;
  sampleConfigCode: string | null;
  softwareVersion: string;
  description: string;
  startTime: string | null;
  heartbeatTime: string | null;
  connectState: number;
  updateTime: string;
  isSync: boolean;
  syncTime: string | null;
  isConfigOK: boolean;
  configFileCode_Old: string | null;
  sampleConfigCode_Old: string | null;
  appConfigId: number;
  canDistribute: boolean;
  enable: boolean;
  rdsServer: string | null;
  dataServer: string | null;
  installTime: string;
  fsu: boolean;
  state: number;
  portNos: string;
  workStationName: string | null;
  stationName: string;
}

// 设备类型定义
export interface Equipment {
  equipmentId: number;
  equipmentName: string;
  monitorUnitId: number;
  monitorUnitName?: string;
  portName?: string;
  updateTime?: string;
}

// 采集树节点类型定义
export interface SamplerTreeNode {
  portId?: number;
  portName?: string;
  samplerUnitId?: number;
  samplerUnitName?: string;
  equipmentId?: number;
  equipmentName?: string;
  samplerUnits?: SamplerTreeNode[];
  equipments?: SamplerTreeNode[];
}

// 工作站服务器类型定义
export interface WorkStationServer {
  workStationId: number;
  workStationName: string;
  workStationType: number;
  ipAddress?: string;
  description?: string;
}

// 监控单元类型定义
export interface MonitorUnitType {
  typeId: number;
  typeName: string;
  order: number;
}

// API响应类型定义
export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message?: string;
  timestamp?: number;
  msg?: string | null;
  state?: boolean;
}

// 获取所有采集器列表
export function getMonitorUnits() {
  return http.request<ApiResponse<MonitorUnit[]>>(
    "get",
    "/api/thing/south-omc-siteweb/monitor-unit"
  );
}

// 获取指定采集器的设备列表
export function getMonitorUnitDevices(monitorUnitId: number) {
  return http.request<ApiResponse<Equipment[]>>(
    "get",
    `/api/thing/south-omc-siteweb/monitor-unit/equipments/${monitorUnitId}`
  );
}

// 获取指定采集器的采集信息树
export function getMonitorUnitSamplerTree(monitorUnitId: number) {
  return http.request<ApiResponse<SamplerTreeNode[]>>(
    "get",
    `/api/thing/south-omc-siteweb/monitor-unit/sampler-tree/${monitorUnitId}`
  );
}

// 获取指定采集器的端口列表
export function getMonitorUnitPorts(monitorUnitId: number) {
  return http.request<ApiResponse<any[]>>(
    "get",
    `/api/thing/south-omc-siteweb/monitor-unit/ports/${monitorUnitId}`
  );
}

// 获取采集器类型列表
export function getMonitorUnitTypes() {
  return http.request<ApiResponse<MonitorUnitType[]>>(
    "get",
    "/api/thing/south-omc-siteweb/monitor-unit/types"
  );
}

// 获取工作站服务器列表
export function getWorkStationServerList() {
  return http.request<ApiResponse<WorkStationServer[]>>(
    "get",
    "/api/thing/south-omc-siteweb/workstation/server-source-list"
  );
}

// 创建采集器
export function createMonitorUnit(data: any) {
  return http.request<ApiResponse<any>>(
    "post",
    "/api/thing/south-omc-siteweb/monitor-unit/config",
    { data }
  );
}

// 更新采集器
export function updateMonitorUnit(data: any) {
  return http.request<ApiResponse<any>>(
    "put",
    "/api/thing/south-omc-siteweb/monitor-unit/config",
    { data }
  );
}

// 删除采集器
export function deleteMonitorUnit(
  monitorUnitId: number,
  isDelEqs: boolean = false
) {
  return http.request<ApiResponse<any>>(
    "delete",
    `/api/thing/south-omc-siteweb/monitor-unit/config/${monitorUnitId}${isDelEqs ? "?isDelEqs=true" : ""}`
  );
}

// 删除设备
export function deleteEquipment(equipmentId: number) {
  return http.request<ApiResponse<any>>(
    "delete",
    `/api/thing/south-omc-siteweb/equipment/config/${equipmentId}`
  );
}

// 更新设备局房
export function updateEquipmentHouse(data: {
  stationId: number;
  equipmentId: number;
  houseId: number;
}) {
  return http.request<ApiResponse<any>>(
    "put",
    "/api/thing/south-omc-siteweb/equipment/updateHouseId",
    { data }
  );
}

// 切换设备模板
export function switchEquipmentTemplate(data: {
  destTemplateId: number;
  originTemplateId: number;
  equipmentIds: number[];
}) {
  return http.request<ApiResponse<any>>(
    "post",
    "/api/thing/south-omc-siteweb/equipment/switchtemplate",
    { data }
  );
}

// 创建端口
export function createPort(data: any) {
  return http.request<ApiResponse<any>>(
    "post",
    "/api/thing/south-omc-siteweb/port/config",
    { data }
  );
}

// 更新端口
export function updatePort(data: any) {
  return http.request<ApiResponse<any>>(
    "put",
    "/api/thing/south-omc-siteweb/port/config",
    { data }
  );
}

// 删除端口
export function deletePort(portId: number) {
  return http.request<ApiResponse<any>>(
    "delete",
    `/api/thing/south-omc-siteweb/port/config/${portId}`
  );
}

// 创建采集单元
export function createSamplerUnit(data: any) {
  return http.request<ApiResponse<any>>(
    "post",
    "/api/thing/south-omc-siteweb/sampler-unit/config",
    { data }
  );
}

// 更新采集单元
export function updateSamplerUnit(data: any) {
  return http.request<ApiResponse<any>>(
    "put",
    "/api/thing/south-omc-siteweb/sampler-unit/config",
    { data }
  );
}

// 删除采集单元
export function deleteSamplerUnit(samplerUnitId: number) {
  return http.request<ApiResponse<any>>(
    "delete",
    `/api/thing/south-omc-siteweb/sampler-unit/config/${samplerUnitId}`
  );
}

// 获取端口类型列表
export function getPortTypes(monitorUnitCategory: number) {
  return http.request<ApiResponse<any[]>>(
    "get",
    `/api/thing/south-omc-siteweb/port/types?monitorUnitCategory=${monitorUnitCategory}`
  );
}

// 根据端口ID获取端口信息
export function getPortById(portId: number) {
  return http.request<ApiResponse<any>>(
    "get",
    `/api/thing/south-omc-siteweb/port/config/${portId}`
  );
}

// 获取采集器列表
export function getSamplerList() {
  return http.request<ApiResponse<any[]>>(
    "get",
    "/api/thing/south-omc-siteweb/sampler"
  );
}

// 根据采集单元ID获取采集单元信息
export function getSamplerUnitById(samplerUnitId: number) {
  return http.request<ApiResponse<any>>(
    "get",
    `/api/thing/south-omc-siteweb/sampler-unit/config/${samplerUnitId}`
  );
}

// 创建设备
export function createEquipment(data: any) {
  return http.request<ApiResponse<any>>(
    "post",
    "/api/thing/south-omc-siteweb/equipment/config",
    { data }
  );
}
