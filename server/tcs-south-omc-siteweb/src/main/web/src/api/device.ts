import { http } from "@/utils/http";

// API响应格式
export type ApiResponse<T> = {
  state: boolean;
  code: number;
  timestamp: number;
  data: T;
  err_msg: string | null;
  err_code: string | null;
};

// 设备信息接口
export interface DeviceInfo {
  equipmentId: string;
  equipmentName: string;
  equipmentTemplateId: string;
  monitorUnitId: string;
  stationId: string;
  instantiated: boolean;
  parentEquipmentId?: string;
  samplerUnitId: string;
  description?: string;
  workStationId?: string;
  houseId?: string;
}

// 端口类型接口
export interface PortType {
  typeId: number;
  typeName: string;
  order: number;
}

// 端口信息接口
export interface PortInfo {
  portId: string;
  portNo: string;
  portName: string;
  portType: number;
  setting: string;
  phoneNumber?: string;
  monitorUnitId: string;
  linkSamplerUnitId: number;
  description?: string;
}

// 采集单元信息接口
export interface SamplerUnitInfo {
  samplerUnitId: string;
  samplerUnitName: string;
  samplerId: string;
  spUnitInterval: string;
  address: string;
  phoneNumber?: string;
  dllPath: string;
  monitorUnitId: string;
  portId: string;
  parentSamplerUnitId: number;
  connectState: number;
  description?: string;
  updateTime?: string;
  samplerName: string;
  samplerType: number;
}

// 设备模板类型定义
export interface EquipmentTemplate {
  equipmentTemplateId: number;
  equipmentTemplateName: string;
  protocolCode: string;
  equipmentCategory: number;
}

// 采集器类型定义
export interface Sampler {
  samplerId: number;
  samplerName: string;
  protocolCode: string;
  dllPath: string;
  samplerType: number;
}

// 采集单元类型定义
export interface SamplerUnit {
  samplerUnitId: number;
  samplerUnitName: string;
  samplerId: number;
  spUnitInterval: string;
  address: string;
  phoneNumber?: string;
}

// 端口类型定义
export interface Port {
  portId: number;
  portName: string;
  portNo: number;
  portType: number;
  setting: string;
  phoneNumber?: string;
  monitorUnitId: number;
}

// 获取设备模板
export function getEquipmentTemplate(templateId: number) {
  return http.request<ApiResponse<EquipmentTemplate>>(
    "get",
    `/api/thing/south-omc-siteweb/equipmenttemplate/${templateId}`
  );
}

/**
 * 获取端口类型列表
 * @param monitorUnitCategory 监控单元类别
 */
export const getPortTypes = (monitorUnitCategory: number) => {
  return http.request<ApiResponse<PortType[]>>(
    "get",
    `/api/thing/south-omc-siteweb/port/types?monitorUnitCategory=${monitorUnitCategory}`
  );
};

// 获取采集器列表
export function getSamplers() {
  return http.request<ApiResponse<Sampler[]>>(
    "get",
    "/api/thing/south-omc-siteweb/sampler"
  );
}

/**
 * 获取监控单元端口列表
 * @param monitorUnitId 监控单元ID
 */
export const getMonitorUnitPorts = (monitorUnitId: string) => {
  return http.request<ApiResponse<PortInfo[]>>(
    "get",
    `/api/thing/south-omc-siteweb/monitor-unit/ports/${monitorUnitId}`
  );
};

/**
 * 获取端口采集单元列表
 * @param portId 端口ID
 */
export const getPortSamplerUnits = (portId: string) => {
  return http.request<ApiResponse<SamplerUnitInfo[]>>(
    "get",
    `/api/thing/south-omc-siteweb/port/sampler-units/${portId}`
  );
};

/**
 * 获取采集单元配置
 * @param samplerUnitId 采集单元ID
 */
export const getSamplerUnitConfig = (samplerUnitId: string) => {
  return http.request<ApiResponse<SamplerUnitInfo>>(
    "get",
    `/api/thing/south-omc-siteweb/sampler-unit/config/${samplerUnitId}`
  );
};

/**
 * 创建设备
 * @param data 设备信息
 */
export const createDevice = (data: DeviceInfo) => {
  return http.request<ApiResponse<DeviceInfo>>(
    "post",
    "/api/thing/south-omc-siteweb/equipment/config",
    { data }
  );
};

/**
 * 创建采集单元
 * @param data 采集单元信息
 */
export const createSamplerUnit = (data: Partial<SamplerUnitInfo>) => {
  return http.request<ApiResponse<SamplerUnitInfo>>(
    "post",
    "/api/thing/south-omc-siteweb/sampler-unit/config",
    { data }
  );
};
