import { http } from "@/utils/http";

// 监控单元接口
export interface MonitorUnit {
  monitorUnitId: string;
  monitorUnitName: string;
  monitorUnitCategory: number;
  typeName?: string;
  ipAddress: string;
  state: number;
  statusTxt?: string;
  syncTime?: string;
  portNos?: string;
  workStationId?: string;
  workStationName?: string;
  stationId?: string;
  projectName?: string;
  contractNo?: string;
  description?: string;
  enable?: boolean;
}

// 监控单元类型接口
export interface MonitorUnitType {
  typeId: number;
  typeName: string;
  order: number;
}

// API响应格式
export interface ApiResponse<T = any> {
  code: number;
  timestamp: number;
  data: T;
  msg: string | null;
}

// 分发状态接口
export interface DistributeStatus {
  stationName: string;
  unitName: string;
  unitType: string;
  ipAddress: string;
  status: number;
  statusText: string;
  progress?: number;
  lastUpdate?: string;
}

// WebSocket消息接口
export interface WebSocketMessage {
  type: 'status' | 'log' | 'progress';
  data: any;
  timestamp: number;
}

/**
 * 获取监控单元列表（非RMU）
 */
export const getMonitorUnitList = () => {
  return http.request<ApiResponse<MonitorUnit[]>>(
    "get",
    "/api/config/monitor-unit"
  );
};

/**
 * 获取监控单元类型列表
 */
export const getMonitorUnitTypes = () => {
  return http.request<ApiResponse<MonitorUnitType[]>>(
    "get",
    "/api/config/monitor-unit/types"
  );
};

/**
 * 获取监控单元分发日志
 * @param monitorUnitId 监控单元ID
 */
export const getMonitorUnitLog = (monitorUnitId: string) => {
  return http.request<ApiResponse<string>>(
    "get",
    `/api/config/monitor-unit/log?monitorUnitId=${monitorUnitId}`
  );
};

/**
 * 批量分发监控单元配置
 * @param monitorUnitIds 监控单元ID列表
 */
export const batchDistributeConfig = (monitorUnitIds: string[]) => {
  return http.request<ApiResponse<any>>(
    "post",
    "/api/config/monitorunitxml/batch-distribute",
    {
      data: { monitorUnitIds }
    }
  );
};

/**
 * 单个分发监控单元配置
 * @param monitorUnitId 监控单元ID
 */
export const distributeConfig = (monitorUnitId: string) => {
  return http.request<ApiResponse<any>>(
    "post",
    "/api/config/monitorunitxml/distribute",
    {
      data: { monitorUnitId }
    }
  );
};

/**
 * 获取全局分发状态
 */
export const getGlobalDistributeStatus = () => {
  return http.request<ApiResponse<DistributeStatus[]>>(
    "get",
    "/api/config/monitorunitxml/global-status"
  );
};

/**
 * 获取分发状态日志
 * @param stationName 局站名称
 * @param unitName 单元名称
 */
export const getDistributeStatusLog = (stationName: string, unitName: string) => {
  return http.request<ApiResponse<string[]>>(
    "get",
    `/api/config/monitorunitxml/status-log?stationName=${stationName}&unitName=${unitName}`
  );
};

/**
 * 创建WebSocket连接用于实时状态更新
 * @param url WebSocket地址
 * @returns WebSocket实例
 */
export const createWebSocketConnection = (url: string): WebSocket => {
  const wsUrl = url.startsWith('ws') ? url : `ws://${window.location.host}${url}`;
  return new WebSocket(wsUrl);
};

/**
 * WebSocket消息处理器
 * @param onMessage 消息处理回调
 * @param onError 错误处理回调
 * @param onClose 关闭处理回调
 */
export const setupWebSocketHandlers = (
  ws: WebSocket,
  onMessage?: (message: WebSocketMessage) => void,
  onError?: (error: Event) => void,
  onClose?: (event: CloseEvent) => void
) => {
  if (onMessage) {
    ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data) as WebSocketMessage;
        onMessage(message);
      } catch (error) {
        console.error('WebSocket消息解析失败:', error);
      }
    };
  }

  if (onError) {
    ws.onerror = onError;
  }

  if (onClose) {
    ws.onclose = onClose;
  }
};

/**
 * 发送WebSocket消息
 * @param ws WebSocket实例
 * @param message 消息内容
 */
export const sendWebSocketMessage = (ws: WebSocket, message: any) => {
  if (ws.readyState === WebSocket.OPEN) {
    ws.send(JSON.stringify(message));
  } else {
    console.warn('WebSocket连接未打开，无法发送消息');
  }
}; 