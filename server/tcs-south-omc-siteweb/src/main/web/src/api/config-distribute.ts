import { http } from "@/utils/http";

// 监控单元接口
export interface MonitorUnit {
  monitorUnitId: string;
  monitorUnitName: string;
  monitorUnitCategory: number;
  typeName?: string;
  ipAddress: string;
  state: number;
  statusTxt?: string;
  syncTime?: string;
  portNos?: string;
  workStationId?: string;
  workStationName?: string;
  stationId?: string;
  stationName?: string;
  description?: string;
  enable?: boolean;
}

// 监控单元类型接口
export interface MonitorUnitType {
  typeId: number;
  typeName: string;
  order: number;
}

// API响应格式
export interface ApiResponse<T = any> {
  code: number;
  timestamp?: number;
  data: T;
  msg?: string | null;
  message?: string;
}

// 分发状态接口
export interface DistributeStatus {
  stationName: string;
  unitName: string;
  unitType: string;
  ipAddress: string;
  status: number;
  statusText: string;
  progress?: number;
  lastUpdate?: string;
}

// WebSocket消息接口
export interface WebSocketMessage {
  type: "status" | "log" | "progress";
  data: any;
  timestamp: number;
}

// ==================== 新增：异步任务相关接口 ====================

// 任务状态枚举
export enum TaskStatus {
  PENDING = 'PENDING',
  RUNNING = 'RUNNING', 
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

// 任务类型枚举
export enum TaskType {
  CONFIGURATION_GENERATION = 'CONFIGURATION_GENERATION',
  CONFIGURATION_DISTRIBUTION = 'CONFIGURATION_DISTRIBUTION',
  CONFIGURATION_BACKUP = 'CONFIGURATION_BACKUP',
  REMOTE_CONFIG_DOWNLOAD = 'REMOTE_CONFIG_DOWNLOAD',
  CONFIGURATION_IMPORT = 'CONFIGURATION_IMPORT'
}

// 协议类型
export type ProtocolType = 'ftp' | 'sftp' | 'ssh';

// 任务状态响应接口
export interface TaskStatusResponse {
  taskId: string;
  taskType: TaskType;
  monitorUnitId?: number;
  status: TaskStatus;
  currentStep: string;
  progress: number;
  message: string;
  isFinal: boolean;
  errorMessage?: string;
  updateTime: string;
  createTime: string;
}

// 任务创建响应接口
export interface TaskCreateResponse {
  taskId: string;
  status: TaskStatus;
}

// 配置生成请求参数
export interface ConfigGenerationRequest {
  monitorUnitIds: number[];
  userId?: string;
  additionalParams?: Record<string, any>;
}

// 配置下发请求参数
export interface ConfigDistributionRequest {
  monitorUnitIds: number[];
  username: string;
  password: string;
  port: number;
  protocol: ProtocolType;
  userId?: string;
}

// 配置备份请求参数
export interface ConfigBackupRequest {
  monitorUnitId: number;
  userId?: string;
}

// 配置导入请求参数
export interface ConfigImportRequest {
  file: File;
  userId?: string;
  overwrite?: boolean;
  importMode?: 'FULL' | 'TEMPLATE_ONLY' | 'MONITOR_UNIT_ONLY';
  remarks?: string;
}

/**
 * 获取活跃监控单元列表（用于分发状态查看）
 */
export const getActiveMonitorUnits = () => {
  return http.request<ApiResponse<MonitorUnit[]>>(
    "get",
    "/api/thing/south-omc-siteweb/monitor-unit/active"
  );
};

/**
 * 获取监控单元列表（非RMU）
 */
export const getMonitorUnitList = () => {
  return http.request<ApiResponse<MonitorUnit[]>>(
    "get",
    "/api/thing/south-omc-siteweb/monitor-unit"
  );
};

/**
 * 获取监控单元类型列表
 */
export const getMonitorUnitTypes = () => {
  return http.request<ApiResponse<MonitorUnitType[]>>(
    "get",
    "/api/thing/south-omc-siteweb/monitor-unit/types"
  );
};

/**
 * 获取监控单元分发日志
 * @param monitorUnitId 监控单元ID
 */
export const getMonitorUnitLog = (monitorUnitId: string) => {
  return http.request<ApiResponse<string>>(
    "get",
    `/api/thing/south-omc-siteweb/monitor-unit/log?monitorUnitId=${monitorUnitId}`
  );
};

/**
 * 批量分发监控单元配置
 * @param monitorUnitIds 监控单元ID列表
 */
export const batchDistributeConfig = (monitorUnitIds: string[]) => {
  return http.request<ApiResponse<any>>(
    "post",
    "/api/config/monitorunitxml/batch-distribute",
    {
      data: { monitorUnitIds }
    }
  );
};

// ==================== 新的异步任务API ====================

// 基础路径
const TASK_BASE_PATH = '/api/thing/south-omc-siteweb/config-tasks';

/**
 * 1. 配置生成相关API
 */

/**
 * 创建配置生成任务
 * 替换原有的 WebSocket 配置生成消息发送
 */
export const createConfigGenerationTask = (params: ConfigGenerationRequest) => {
  return http.request<ApiResponse<TaskCreateResponse>>(
    "post",
    `${TASK_BASE_PATH}/generation`,
    {
      data: {
        ...params,
        userId: params.userId || 'admin'
      }
    }
  );
};

/**
 * 下载生成的配置文件
 * 替换原有的 onDownFile 方法
 */
export const downloadGeneratedConfigs = async (monitorUnitIds: number[]): Promise<void> => {
  const token = localStorage.getItem('token');
  const url = `${TASK_BASE_PATH}/monitor-unit-configs/download?monitorUnitIds=${monitorUnitIds.join(',')}`;
  
  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'Authorization': token ? `Bearer ${token}` : ''
    }
  });

  if (!response.ok) {
    throw new Error('下载配置文件失败');
  }

  // 处理文件下载
  const blob = await response.blob();
  const downloadUrl = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = downloadUrl;
  
  const filename = monitorUnitIds.join(',');
  a.download = `${filename.length <= 30 ? filename : filename.substring(0, 29) + '+'}监控单元配置文件.zip`;
  a.click();
  
  URL.revokeObjectURL(downloadUrl);
};

/**
 * 2. 配置下发相关API
 */

/**
 * 创建配置下发任务
 * 替换原有的 WebSocket 配置下发消息发送
 */
export const createConfigDistributionTask = (params: ConfigDistributionRequest) => {
  return http.request<ApiResponse<TaskCreateResponse>>(
    "post",
    `${TASK_BASE_PATH}/distribution`,
    {
      data: {
        ...params,
        userId: params.userId || 'admin'
      }
    }
  );
};

/**
 * 3. 任务状态查询API
 */

/**
 * 查询任务状态
 * 替换原有的 WebSocket 消息监听
 */
export const getTaskStatus = (taskId: string) => {
  return http.request<ApiResponse<TaskStatusResponse>>(
    "get",
    `${TASK_BASE_PATH}/${taskId}`
  );
};

/**
 * 取消任务
 */
export const cancelTask = (taskId: string) => {
  return http.request<ApiResponse<string>>(
    "delete",
    `${TASK_BASE_PATH}/${taskId}`
  );
};

/**
 * 4. 扩展功能API
 */

/**
 * 创建配置备份任务
 */
export const createConfigBackupTask = (params: ConfigBackupRequest) => {
  return http.request<ApiResponse<TaskCreateResponse>>(
    "post",
    `${TASK_BASE_PATH}/backup`,
    {
      data: {
        ...params,
        userId: params.userId || 'admin'
      }
    }
  );
};

/**
 * 下载备份文件
 */
export const downloadBackupConfig = async (monitorUnitId: number): Promise<void> => {
  const token = localStorage.getItem('token');
  const url = `${TASK_BASE_PATH}/backup/download/${monitorUnitId}`;
  
  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'Authorization': token ? `Bearer ${token}` : ''
    }
  });

  if (!response.ok) {
    throw new Error('下载备份文件失败');
  }

  const blob = await response.blob();
  const downloadUrl = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = downloadUrl;
  a.download = `监控单元${monitorUnitId}_配置备份.zip`;
  a.click();
  
  URL.revokeObjectURL(downloadUrl);
};

/**
 * 检查近期备份状态
 */
export const checkRecentBackup = (monitorUnitId: number, minutes: number = 10) => {
  return http.request<ApiResponse<{ hasRecentBackup: boolean; monitorUnitId: number; checkMinutes: number }>>(
    "get",
    `${TASK_BASE_PATH}/backup/recent/${monitorUnitId}?minutes=${minutes}`
  );
};

/**
 * 5. 配置导入相关API
 */

/**
 * 创建配置导入任务
 */
export const createConfigImportTask = async (params: ConfigImportRequest): Promise<TaskCreateResponse> => {
  const formData = new FormData();
  formData.append('file', params.file);
  formData.append('userId', params.userId || 'admin');
  formData.append('overwrite', params.overwrite?.toString() || 'true');
  formData.append('importMode', params.importMode || 'FULL');
  if (params.remarks) {
    formData.append('remarks', params.remarks);
  }

  const token = localStorage.getItem('token');
  const response = await fetch(`${TASK_BASE_PATH}/import`, {
    method: 'POST',
    headers: {
      'Authorization': token ? `Bearer ${token}` : ''
    },
    body: formData
  });

  if (!response.ok) {
    throw new Error('配置导入任务创建失败');
  }

  const result = await response.json();
  return result.data;
};

// ==================== 兼容性保持：旧的API方法 ====================

export const generateConfig = (monitorUnitIds: string[]) => {
  // 兼容旧版本，转换为新的API调用
  const numIds = monitorUnitIds.map(id => parseInt(id));
  return createConfigGenerationTask({ 
    monitorUnitIds: numIds, 
    userId: "1", 
    additionalParams: {} 
  });
};

export const checkGenerationStatus = (taskId: string) => {
  return getTaskStatus(taskId);
};

/**
 * 单个分发监控单元配置
 * @param monitorUnitId 监控单元ID
 */
export const distributeConfig = (monitorUnitId: string) => {
  return http.request<ApiResponse<any>>(
    "post",
    "/api/config/monitorunitxml/distribute",
    {
      data: { monitorUnitId }
    }
  );
};

/**
 * 获取全局分发状态
 */
export const getGlobalDistributeStatus = () => {
  return http.request<ApiResponse<DistributeStatus[]>>(
    "get",
    "/api/config/monitorunitxml/global-status"
  );
};

/**
 * 获取分发状态日志
 * @param stationName 局站名称
 * @param unitName 单元名称
 */
export const getDistributeStatusLog = (
  stationName: string,
  unitName: string
) => {
  return http.request<ApiResponse<string[]>>(
    "get",
    `/api/config/monitorunitxml/status-log?stationName=${stationName}&unitName=${unitName}`
  );
};

/**
 * 创建WebSocket连接用于实时状态更新
 * @param url WebSocket地址
 * @returns WebSocket实例
 */
export const createWebSocketConnection = (url: string): WebSocket => {
  const wsUrl = url.startsWith("ws")
    ? url
    : `ws://${window.location.host}${url}`;
  return new WebSocket(wsUrl);
};

/**
 * WebSocket消息处理器
 * @param onMessage 消息处理回调
 * @param onError 错误处理回调
 * @param onClose 关闭处理回调
 */
export const setupWebSocketHandlers = (
  ws: WebSocket,
  onMessage?: (message: WebSocketMessage) => void,
  onError?: (error: Event) => void,
  onClose?: (event: CloseEvent) => void
) => {
  if (onMessage) {
    ws.onmessage = event => {
      try {
        const message = JSON.parse(event.data) as WebSocketMessage;
        onMessage(message);
      } catch (error) {
        console.error("WebSocket消息解析失败:", error);
      }
    };
  }

  if (onError) {
    ws.onerror = onError;
  }

  if (onClose) {
    ws.onclose = onClose;
  }
};

/**
 * 发送WebSocket消息
 * @param ws WebSocket实例
 * @param message 消息内容
 */
export const sendWebSocketMessage = (ws: WebSocket, message: any) => {
  if (ws.readyState === WebSocket.OPEN) {
    ws.send(JSON.stringify(message));
  } else {
    console.warn("WebSocket连接未打开，无法发送消息");
  }
};

// ==================== 任务轮询工具类 ====================

export interface TaskPollingOptions {
  interval?: number;           // 轮询间隔，默认3秒
  timeout?: number;           // 超时时间，默认5分钟
  onProgress?: (status: TaskStatusResponse) => void;  // 进度回调
  onCompleted?: (status: TaskStatusResponse) => void; // 完成回调
  onFailed?: (status: TaskStatusResponse) => void;    // 失败回调
}

/**
 * 任务轮询器
 * 用于替换 WebSocket 的实时状态更新
 */
export class TaskPoller {
  private taskId: string;
  private options: Required<TaskPollingOptions>;
  private intervalId: NodeJS.Timeout | null = null;
  private timeoutId: NodeJS.Timeout | null = null;
  private isPolling = false;

  constructor(taskId: string, options: TaskPollingOptions = {}) {
    this.taskId = taskId;
    this.options = {
      interval: options.interval || 3000,
      timeout: options.timeout || 300000, // 5分钟
      onProgress: options.onProgress || (() => {}),
      onCompleted: options.onCompleted || (() => {}),
      onFailed: options.onFailed || (() => {})
    };
  }

  /**
   * 开始轮询
   */
  start(): Promise<TaskStatusResponse> {
    return new Promise((resolve, reject) => {
      if (this.isPolling) {
        reject(new Error('轮询已在进行中'));
        return;
      }

      this.isPolling = true;

      // 设置超时
      this.timeoutId = setTimeout(() => {
        this.stop();
        reject(new Error('任务轮询超时'));
      }, this.options.timeout);

      // 开始轮询
      const poll = async () => {
        try {
          const response = await getTaskStatus(this.taskId);
          const status = response.data;
          
          // 调用进度回调
          this.options.onProgress(status);

          // 检查任务是否完成
          if (status.isFinal) {
            this.stop();
            
            if (status.status === TaskStatus.COMPLETED) {
              this.options.onCompleted(status);
              resolve(status);
            } else if (status.status === TaskStatus.FAILED) {
              this.options.onFailed(status);
              reject(new Error(status.errorMessage || '任务执行失败'));
            } else if (status.status === TaskStatus.CANCELLED) {
              reject(new Error('任务已被取消'));
            }
          }
        } catch (error) {
          this.stop();
          reject(error);
        }
      };

      // 立即执行一次
      poll();

      // 设置定时轮询
      this.intervalId = setInterval(poll, this.options.interval);
    });
  }

  /**
   * 停止轮询
   */
  stop(): void {
    this.isPolling = false;
    
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
  }

  /**
   * 取消任务并停止轮询
   */
  async cancel(): Promise<void> {
    this.stop();
    await cancelTask(this.taskId);
  }
}

// ==================== 便捷的组合方法 ====================

/**
 * 完整的配置生成流程
 * 包含任务创建、轮询监控、完成后返回结果
 */
export async function generateConfigsWithPolling(
  monitorUnitIds: number[],
  onProgress?: (message: string, progress: number) => void
): Promise<void> {
  // 1. 创建生成任务
  const response = await createConfigGenerationTask({ 
    monitorUnitIds,
    userId: 'admin'
  });

  const task = response.data;

  // 2. 轮询任务状态
  const poller = new TaskPoller(task.taskId, {
    onProgress: (status) => {
      onProgress?.(status.message, status.progress);
    }
  });

  await poller.start();
}

/**
 * 完整的配置下发流程
 * 包含任务创建、轮询监控、完成后返回结果
 */
export async function distributeConfigsWithPolling(
  params: ConfigDistributionRequest,
  onProgress?: (message: string, progress: number) => void
): Promise<void> {
  // 1. 创建下发任务
  const response = await createConfigDistributionTask(params);
  const task = response.data;

  // 2. 轮询任务状态
  const poller = new TaskPoller(task.taskId, {
    onProgress: (status) => {
      onProgress?.(status.message, status.progress);
    }
  });

  await poller.start();
}

/**
 * 监控单元状态刷新
 * 替换原有的 refreshStatus 方法
 */
export async function refreshMonitorUnitStatus(monitorUnitIds: number[]): Promise<any[]> {
  try {
    const response = await http.request<ApiResponse<any[]>>(
      "get",
      `/api/config/monitor-unit/active?monitorUnitIds=${monitorUnitIds.join(',')}`
    );
    return response.data || [];
  } catch (error) {
    console.error('刷新监控单元状态失败:', error);
    throw error;
  }
}

/**
 * 完整的配置备份流程
 * 包含任务创建、轮询监控、完成后返回结果
 */
export async function backupConfigWithPolling(
  monitorUnitId: number,
  onProgress?: (message: string, progress: number) => void
): Promise<void> {
  // 1. 创建备份任务
  const response = await createConfigBackupTask({ 
    monitorUnitId,
    userId: 'admin'
  });

  const task = response.data;

  // 2. 轮询任务状态
  const poller = new TaskPoller(task.taskId, {
    onProgress: (status) => {
      onProgress?.(status.message, status.progress);
    }
  });

  await poller.start();
}

/**
 * 完整的配置导入流程
 * 包含任务创建、轮询监控、完成后返回结果
 */
export async function importConfigWithPolling(
  params: ConfigImportRequest,
  onProgress?: (message: string, progress: number) => void
): Promise<void> {
  // 1. 创建导入任务
  const task = await createConfigImportTask(params);

  // 2. 轮询任务状态
  const poller = new TaskPoller(task.taskId, {
    onProgress: (status) => {
      onProgress?.(status.message, status.progress);
    }
  });

  await poller.start();
}
