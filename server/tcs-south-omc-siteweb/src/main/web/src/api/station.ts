import { http } from "@/utils/http";

// API响应格式
export type ApiResponse<T> = {
  code: number;
  timestamp: number;
  data: T;
  msg: string | null;
};

// 局站信息接口
export interface StationInfo {
  stationId: string;
  stationName: string;
  hStationName?: string;
  stationCategory?: number;
  description?: string;
}

// 采集器信息接口
export interface SamplerInfo {
  samplerId: string;
  samplerName: string;
  protocolCode: string;
  dllPath: string;
  samplerType: number;
  description?: string;
}

// 采集单元信息接口
export interface SamplerUnitInfo {
  samplerUnitId: string;
  samplerUnitName: string;
  samplerId: string;
  spUnitInterval: string;
  address: string;
  phoneNumber?: string;
  dllPath: string;
  monitorUnitId: string;
  portId: string;
  parentSamplerUnitId: number;
  connectState: number;
  description?: string;
  updateTime?: string;
}

/**
 * 获取局站列表
 */
export const getStations = () => {
  return http.request<ApiResponse<StationInfo[]>>(
    "get",
    "/api/thing/south-omc-siteweb/stations"
  );
};

/**
 * 获取局站详情
 * @param stationId 局站ID
 */
export const getStationDetail = (stationId: string) => {
  return http.request<ApiResponse<StationInfo>>(
    "get",
    `/api/thing/south-omc-siteweb/station/${stationId}`
  );
};

/**
 * 获取采集器列表
 */
export const getSamplers = () => {
  return http.request<ApiResponse<SamplerInfo[]>>("get", "/api/thing/south-omc-siteweb/sampler");
};

/**
 * 获取采集器详情
 * @param samplerId 采集器ID
 */
export const getSamplerDetail = (samplerId: string) => {
  return http.request<ApiResponse<SamplerInfo>>(
    "get",
    `/api/thing/south-omc-siteweb/sampler/${samplerId}`
  );
};

/**
 * 获取采集单元列表
 * @param params 查询参数
 */
export const getSamplerUnits = (params?: {
  monitorUnitId?: string;
  portId?: string;
  samplerId?: string;
}) => {
  const queryString = params
    ? `?${new URLSearchParams(params as any).toString()}`
    : "";
  return http.request<ApiResponse<SamplerUnitInfo[]>>(
    "get",
    `/api/thing/south-omc-siteweb/sampler-units${queryString}`
  );
};

/**
 * 获取采集单元详情
 * @param samplerUnitId 采集单元ID
 */
export const getSamplerUnitDetail = (samplerUnitId: string) => {
  return http.request<ApiResponse<SamplerUnitInfo>>(
    "get",
    `/api/thing/south-omc-siteweb/sampler-unit/${samplerUnitId}`
  );
};

// 局房类型定义
export interface House {
  houseId: number;
  houseName: string;
  stationId: number;
}

// 获取局房列表
export function getHouseList(stationId: number) {
  return http.request<ApiResponse<House[]>>(
    "get",
    `/api/thing/south-omc-siteweb/house/config?stationId=${stationId}`
  );
}
