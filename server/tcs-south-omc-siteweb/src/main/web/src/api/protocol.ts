import { http } from "@/utils/http";

// 协议信息类型定义
export interface ProtocolInfo {
  samplerId: string;
  samplerName: string;
  dllPath: string;
  equipmentTemplateName: string;
  updateState: string;
  modal: string;
  samplerType: string;
  protocolCode: string;
  uploadProtocolFile: boolean;
  createTime?: string;
  updateTime?: string;
}

// 设备模板类型定义
export interface DeviceTemplate {
  id: string;
  templateName: string;
  category: string;
}

// 设备型号类型定义
export interface DeviceModel {
  itemId: string;
  itemValue: string;
}

// 设备种类映射类型定义
export interface DeviceCategoryMap {
  equipmentCategory: string;
  equipmentCategoryName: string;
}

// API响应类型定义
export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message?: string;
}

// 分页响应类型
export interface PageResponse<T> {
  records: T[];
  total: number;
  current: number;
  size: number;
}

// 获取协议列表
export const getProtocolList = () => {
  return http.request<ApiResponse<ProtocolInfo[]>>(
    "get",
    "/api/thing/south-omc-siteweb/sampler"
  );
};

// 更新协议信息
export const updateProtocol = (data: Partial<ProtocolInfo>) => {
  return http.request<ApiResponse<boolean>>(
    "put",
    "/api/thing/south-omc-siteweb/sampler",
    { data }
  );
};

// 批量删除协议
export const deleteProtocolByIds = (samplerIds: string) => {
  return http.request<ApiResponse<boolean>>(
    "delete",
    `/api/thing/south-omc-siteweb/sampler/${samplerIds}`
  );
};

// 上传协议模板文件
export const uploadProtocolTemplate = (file: FormData) => {
  return http.request<ApiResponse<any>>(
    "post",
    "/api/thing/south-omc-siteweb/equipmenttemplate/import",
    {
      data: file,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }
  );
};

// 上传动态库文件
export const uploadDllFile = (data: FormData) => {
  return http.request<ApiResponse<any>>(
    "post",
    "/api/thing/south-omc-siteweb/sampler/upload/dllpath",
    {
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }
  );
};

// 更新设备种类
export const updateDeviceCategory = (equipmentTemplateId: string, equipmentCategory: string) => {
  return http.request<ApiResponse<boolean>>(
    "put",
    `/api/thing/south-omc-siteweb/equipmenttemplate/updatecategory?equipmentTemplateId=${equipmentTemplateId}&equipmentCategory=${equipmentCategory}`
  );
};

// 删除动态库文件
export const deleteDllFile = (protocolCode: string, protocolType: string) => {
  return http.request<ApiResponse<boolean>>(
    "delete",
    `/api/thing/south-omc-siteweb/sampler/protocolfile?protocolCode=${protocolCode}&protocolType=${protocolType}`
  );
};

// 下载动态库文件
export const downloadDllFile = (filePath: string) => {
  return http.request<Blob>(
    "get",
    `/api/thing/south-omc-siteweb/sampler/download/dllpath?protocolCode=${filePath}`,
    {
      responseType: 'blob'
    }
  );
};

// 获取设备模板列表
export const getDeviceTemplates = () => {
  return http.request<ApiResponse<DeviceTemplate[]>>(
    "get",
    "/api/thing/south-omc-siteweb/config/equipmenttemplate"
  );
};

// 获取设备型号列表
export const getDeviceModels = () => {
  return http.request<ApiResponse<DeviceModel[]>>(
    "get",
    "/api/thing/south-omc-siteweb/dataitems?entryId=37"
  );
};

// 获取设备种类映射
export const getDeviceCategoryMap = (category: string) => {
  return http.request<ApiResponse<{ standardName: string; equipmentCategoryMapList: DeviceCategoryMap[] }>>(
    "get",
    `/api/thing/south-omc-siteweb/equipmentcategorymap/list/${category}`
  );
};
