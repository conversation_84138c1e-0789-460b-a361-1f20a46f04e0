import { http } from "../utils/http";

// API响应类型定义
export interface ApiResponse<T = any> {
  state: boolean;
  data: T;
  err_msg?: string;
  timestamp?: number;
  err_code?: number;
}

// 数据字典项接口
export interface DictionaryItemData {
  entryItemId?: number;
  entryId: number;
  itemId: string;
  itemValue: string;
  itemAlias?: string;
  description?: string;
}

/**
 * 获取字典列表
 * 7 设备种类
 * 14 设备厂商
 * 71 局站种类
 * @param id 条目类型ID
 * @returns 字典项列表
 */
export function getList(id: number) {
  return http.request<ApiResponse<DictionaryItemData[]>>(
    "get",
    `/api/thing/south-omc-siteweb/dataitems?entryId=${id}`
  );
}

/**
 * 新增字典项
 * @param data 字典项数据
 * @returns
 */
export function addItem(data: DictionaryItemData) {
  return http.request<ApiResponse<any>>(
    "post",
    `/api/thing/south-omc-siteweb/dataitems`,
    { data }
  );
}

/**
 * 修改字典项
 * @param data 字典项数据
 * @returns
 */
export function updateItem(data: DictionaryItemData) {
  return http.request<ApiResponse<any>>(
    "put",
    `/api/thing/south-omc-siteweb/dataitems`,
    { data }
  );
}

/**
 * 删除字典项
 * @param ids 条目ID
 * @returns
 */
export function deleteEntryItemIds(ids: number | string) {
  return http.request<ApiResponse<any>>(
    "delete",
    `/api/thing/south-omc-siteweb/dataitems?entryItemIds=${ids}`,
    {}
  );
}
