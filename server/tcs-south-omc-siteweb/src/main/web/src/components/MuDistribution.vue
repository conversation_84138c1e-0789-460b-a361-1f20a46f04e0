<template>
  <el-dialog
    :model-value="visible"
    title="监控单元配置分发"
    width="85%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="mu-distribution-dialog"
    @update:model-value="$emit('update:visible', $event)"
    @close="handleClose"
  >
    <div class="min-h-[520px] bg-gray-50 dark:bg-gray-900 p-3">
      <!-- 步骤条 -->
      <div
        class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-3 mb-3"
      >
        <el-steps :active="current" align-center size="small" simple>
          <el-step title="选择监控单元" />
          <el-step title="生成配置文件" />
          <el-step title="配置分发及状态查看" />
        </el-steps>
      </div>

      <!-- 步骤内容 -->
      <div
        class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 flex flex-col"
        style="min-height: 450px"
      >
        <!-- 第一步：选择监控单元 -->
        <div v-if="current === 0" class="flex flex-col h-full">
          <!-- 搜索工具栏 -->
          <div class="p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
            <div class="flex items-center space-x-3">
              <el-input
                v-model="filters.name"
                placeholder="监控单元名称"
                clearable
                size="small"
                :prefix-icon="Search"
                style="width: 280px"
                @input="filterChange"
              />
              <el-input
                v-model="filters.type"
                placeholder="类型"
                clearable
                size="small"
                style="width: 160px"
                @input="filterChange"
              />
              <el-input
                v-model="filters.address"
                placeholder="IP地址"
                clearable
                size="small"
                style="width: 160px"
                @input="filterChange"
              />
              <div class="text-xs text-gray-500 dark:text-gray-400 ml-auto font-medium">
                已选择 {{ allSelectedItems.length }} / {{ showMuList.length }} 个
              </div>
            </div>
          </div>

          <!-- 表格区域 -->
          <div class="flex-1 p-3">
            <el-table
              ref="tableRef"
              :data="showMuList"
              height="360"
              row-key="monitorUnitId"
              size="small"
              class="modern-table"
              @selection-change="handleSelectionChange"
              @select-all="handleSelectAll"
            >
              <el-table-column
                type="selection"
                width="50"
                :reserve-selection="true"
                :selectable="() => true"
              />
              <el-table-column
                prop="monitorUnitName"
                label="监控单元名称"
                min-width="300"
                show-overflow-tooltip
              >
                <template #default="{ row }">
                  <div class="flex items-center space-x-2">
                    <div
                      class="w-6 h-6 bg-primary/10 rounded flex items-center justify-center flex-shrink-0"
                    >
                      <el-icon size="12" class="text-primary"
                        ><Monitor
                      /></el-icon>
                    </div>
                    <span class="font-medium text-gray-900 dark:text-white text-sm">{{
                      row.monitorUnitName
                    }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="typeName" label="类型" width="120">
                <template #default="{ row }">
                  <el-tag size="small" effect="plain">{{
                    row.typeName
                  }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="ipAddress" label="IP地址" width="140">
                <template #default="{ row }">
                  <span class="text-xs text-gray-600 dark:text-gray-400 font-mono">{{
                    row.ipAddress
                  }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <!-- 第二步：生成配置文件 -->
        <div v-else-if="current === 1" class="flex flex-col h-full">
          <!-- 操作工具栏 -->
          <div class="p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
            <div class="flex items-center space-x-3">
              <el-button
                type="primary"
                :loading="generating"
                size="small"
                @click="onGenerate"
              >
                <el-icon class="mr-1"><Cpu /></el-icon>
                {{ generating ? "生成中..." : "生成配置文件" }}
              </el-button>
              <el-button
                v-if="canGenerate"
                type="success"
                size="small"
                @click="onDownFile"
              >
                <el-icon class="mr-1"><Download /></el-icon>
                下载配置文件
              </el-button>
              <div class="text-xs text-gray-500 dark:text-gray-400 ml-auto font-medium">
                {{ allSelectedItems.length }} 个监控单元待生成
              </div>
            </div>
          </div>

          <!-- 日志区域 -->
          <div class="flex-1 p-3">
            <div class="bg-gray-50 dark:bg-gray-900 rounded-lg p-3 h-full">
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center">
                  <el-icon size="14" class="text-primary mr-2"
                    ><DataLine
                  /></el-icon>
                  <span
                    class="text-xs font-medium text-gray-700 dark:text-gray-300"
                    >生成日志</span
                  >
                </div>
                <div v-if="generating" class="flex items-center space-x-2">
                  <span class="text-xs text-gray-500">{{ taskCurrentStep }}</span>
                  <el-progress 
                    :percentage="taskProgress" 
                    :width="60" 
                    type="circle" 
                    :stroke-width="4"
                    :show-text="false"
                  />
                </div>
              </div>
              <el-input
                v-model="logDetail"
                type="textarea"
                :rows="14"
                readonly
                placeholder="生成日志将在这里显示..."
                class="log-textarea"
              />
            </div>
          </div>
        </div>

        <!-- 第三步：配置分发及状态查看 -->
        <div v-else-if="current === 2" class="flex flex-col h-full">
          <!-- 分发配置表单 -->
          <div class="p-3 border-b border-gray-200 dark:border-gray-700">
            <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
              <div class="flex items-center mb-2">
                <el-icon size="14" class="text-blue-600 mr-2"
                  ><Setting
                /></el-icon>
                <span
                  class="text-xs font-medium text-blue-800 dark:text-blue-200"
                  >分发配置</span
                >
              </div>
              <el-form
                ref="distributeFormRef"
                :model="distributeForm"
                :rules="distributeRules"
                label-width="80px"
                size="small"
                class="distribute-form"
              >
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <el-form-item label="用户名" prop="userName">
                    <el-input
                      v-model="distributeForm.userName"
                      placeholder="请输入用户名"
                      :prefix-icon="User"
                    />
                  </el-form-item>

                  <el-form-item label="密码" prop="passWord">
                    <el-input
                      v-model="distributeForm.passWord"
                      :type="passwordVisible ? 'text' : 'password'"
                      placeholder="请输入密码"
                      :prefix-icon="Lock"
                    >
                      <template #suffix>
                        <el-icon
                          class="cursor-pointer"
                          @click="passwordVisible = !passwordVisible"
                        >
                          <View v-if="passwordVisible" />
                          <Hide v-else />
                        </el-icon>
                      </template>
                    </el-input>
                  </el-form-item>

                  <el-form-item label="端口" prop="port">
                    <el-input-number
                      v-model="distributeForm.port"
                      :min="0"
                      :max="65535"
                      style="width: 100%"
                    />
                  </el-form-item>

                  <el-form-item label="" class="flex items-center">
                    <div class="flex items-center space-x-2">
                      <el-checkbox v-model="distributeForm.protocol" />
                      <span class="text-xs text-gray-500">加密传输</span>
                      <el-button
                        type="primary"
                        :disabled="!canDistribute"
                        :loading="distributing"
                        size="small"
                        @click="onDistribute"
                      >
                        <el-icon class="mr-1"><Upload /></el-icon>
                        {{ distributing ? "下发中..." : "开始下发" }}
                      </el-button>
                    </div>
                  </el-form-item>
                </div>
              </el-form>
            </div>
          </div>

          <!-- Tab 区域 -->
          <div class="flex-1 p-3">
            <el-tabs type="card" size="small" class="distribution-tabs">
              <el-tab-pane label="分发日志">
                <template #label>
                  <div class="flex items-center space-x-1">
                    <el-icon size="12"><DataLine /></el-icon>
                    <span>分发日志</span>
                  </div>
                </template>
                <div class="bg-gray-50 dark:bg-gray-900 rounded-lg p-3">
                  <div v-if="distributing" class="flex items-center justify-between mb-2">
                    <span class="text-xs text-gray-500">{{ taskCurrentStep }}</span>
                    <el-progress 
                      :percentage="taskProgress" 
                      :width="50" 
                      type="circle" 
                      :stroke-width="4"
                      :show-text="false"
                    />
                  </div>
                  <el-input
                    v-model="logDetail"
                    type="textarea"
                    :rows="12"
                    readonly
                    placeholder="分发日志将在这里显示..."
                    class="log-textarea"
                  />
                </div>
              </el-tab-pane>

              <el-tab-pane v-if="allSelectedItems.length > 0" label="状态监控">
                <template #label>
                  <div class="flex items-center space-x-1">
                    <el-icon size="12"><Monitor /></el-icon>
                    <span>状态监控</span>
                  </div>
                </template>
                <div class="space-y-3">
                  <!-- 状态筛选 -->
                  <div class="flex items-center space-x-3">
                    <el-input
                      v-model="statusFilters.name"
                      placeholder="监控单元名称"
                      clearable
                      size="small"
                      :prefix-icon="Search"
                      style="width: 240px"
                      @input="filterChangeStatus"
                    />
                    <el-input
                      v-model="statusFilters.type"
                      placeholder="类型"
                      clearable
                      size="small"
                      style="width: 120px"
                      @input="filterChangeStatus"
                    />
                    <el-input
                      v-model="statusFilters.address"
                      placeholder="IP地址"
                      clearable
                      size="small"
                      style="width: 120px"
                      @input="filterChangeStatus"
                    />
                    <el-input
                      v-model="statusFilters.status"
                      placeholder="状态"
                      clearable
                      size="small"
                      style="width: 120px"
                      @input="filterChangeStatus"
                    />
                  </div>

                  <!-- 状态表格 -->
                  <el-table
                    :data="showAllSelectedItems"
                    height="240"
                    row-key="monitorUnitId"
                    size="small"
                    class="modern-table"
                  >
                    <el-table-column
                      prop="monitorUnitName"
                      label="监控单元名称"
                      min-width="300"
                      show-overflow-tooltip
                    >
                      <template #default="{ row }">
                        <div class="flex items-center space-x-2">
                          <div
                            class="w-5 h-5 bg-primary/10 rounded flex items-center justify-center flex-shrink-0"
                          >
                            <el-icon size="10" class="text-primary"
                              ><Monitor
                            /></el-icon>
                          </div>
                          <span class="text-xs font-medium">{{
                            row.monitorUnitName
                          }}</span>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="typeName" label="类型" width="120">
                      <template #default="{ row }">
                        <el-tag size="small" effect="plain">{{
                          row.typeName
                        }}</el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="ipAddress" label="IP地址" width="120">
                      <template #default="{ row }">
                        <span
                          class="text-xs text-gray-600 dark:text-gray-400 font-mono"
                          >{{ row.ipAddress }}</span
                        >
                      </template>
                    </el-table-column>
                    <el-table-column prop="statusTxt" label="状态" width="120">
                      <template #default="{ row }">
                        <el-tag
                          :type="getStatusType(row.statusTxt)"
                          size="small"
                          effect="plain"
                        >
                          {{ row.statusTxt || "未知" }}
                        </el-tag>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div
        class="flex items-center justify-between bg-gray-50 dark:bg-gray-900 p-3"
      >
        <div class="text-xs text-gray-500 dark:text-gray-400">
          <span v-if="current === 0">请选择要配置的监控单元</span>
          <span v-else-if="current === 1">生成配置文件后可进行下一步</span>
          <span v-else-if="current === 2">配置下发完成后点击完成</span>
        </div>
        <div class="flex items-center space-x-2">
          <el-button
            v-if="current > 0"
            :disabled="current === 1 && canGenerate"
            size="small"
            @click="pre"
          >
            <el-icon class="mr-1"><ArrowLeft /></el-icon>
            上一步
          </el-button>

          <el-button
            v-if="current < 2"
            type="primary"
            :disabled="
              (current === 0 && allSelectedItems.length === 0) ||
              (current === 1 && !canGenerate)
            "
            size="small"
            @click="next"
          >
            下一步
            <el-icon class="ml-1"><ArrowRight /></el-icon>
          </el-button>

          <el-button
            v-if="current === 2"
            type="success"
            :disabled="current === 2 && !completed"
            size="small"
            @click="done"
          >
            <el-icon class="mr-1"><Check /></el-icon>
            完成
          </el-button>

          <el-button size="small" @click="handleClose"> 取消 </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import {
  ref,
  reactive,
  computed,
  watch,
  onMounted,
  onUnmounted,
  nextTick
} from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  User,
  Lock,
  View,
  Hide,
  Search,
  Monitor,
  Connection,
  Document,
  Upload,
  Download,
  DataLine,
  Setting,
  Cpu,
  ArrowLeft,
  ArrowRight,
  Check
} from "@element-plus/icons-vue";
import _ from "lodash";
import {
  createConfigGenerationTask,
  createConfigDistributionTask,
  downloadGeneratedConfigs,
  refreshMonitorUnitStatus,
  TaskPoller,
  TaskStatus,
  type ConfigGenerationRequest,
  type ConfigDistributionRequest,
  type TaskStatusResponse,
  type ProtocolType
} from "@/api/config-distribute";

// Props
interface Props {
  visible: boolean;
  muList: any[];
  monitorUnitCategories: any[];
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  muList: () => [],
  monitorUnitCategories: () => []
});

// Emits
const emit = defineEmits<{
  "update:visible": [visible: boolean];
  close: [];
}>();

// 响应式数据
const current = ref(0);
const tableRef = ref();
const distributeFormRef = ref();
const passwordVisible = ref(false);
const generating = ref(false);
const distributing = ref(false);
const canGenerate = ref(false);
const completed = ref(false);
const logDetail = ref("");

// 新增：任务轮询相关
const currentTaskPoller = ref<TaskPoller | null>(null);
const currentTaskId = ref<string>("");
const taskProgress = ref<number>(0);
const taskCurrentStep = ref<string>("");

// 保留状态刷新定时器
let refreshTimer: NodeJS.Timeout | null = null;

// 筛选条件
const filters = reactive({
  name: "",
  type: "",
  address: ""
});

const statusFilters = reactive({
  name: "",
  type: "",
  address: "",
  status: ""
});

// 分发表单
const distributeForm = reactive({
  userName: "root",
  passWord: "hello",
  port: 21,
  protocol: false
});

// 表单验证规则
const distributeRules = {
  userName: [{ required: true, message: "请输入用户名", trigger: "blur" }],
  passWord: [{ required: true, message: "请输入密码", trigger: "blur" }],
  port: [{ required: true, message: "请输入端口", trigger: "blur" }]
};

// 数据
const showMuList = ref<any[]>([]);
const allSelectedItems = ref<any[]>([]);
const showAllSelectedItems = ref<any[]>([]);
const muStatusList = ref<any[]>([]);

// 计算属性
const canDistribute = computed(() => {
  return (
    distributeForm.userName &&
    distributeForm.passWord &&
    distributeForm.port !== 0 &&
    allSelectedItems.value.length > 0
  );
});

// 方法
const initData = () => {
  if (props.muList && props.muList.length > 0) {
    const muListWithType = props.muList.map(item => ({
      ...item,
      typeName:
        props.monitorUnitCategories?.find(
          cat => cat.typeId === item.monitorUnitCategory
        )?.typeName || "未知"
    }));
    showMuList.value = _.cloneDeep(muListWithType);
  } else {
    showMuList.value = [];
  }
};

const filterChange = () => {
  if (!props.muList) return;

  const muListWithType = props.muList.map(item => ({
    ...item,
    typeName:
      props.monitorUnitCategories.find(
        cat => cat.typeId === item.monitorUnitCategory
      )?.typeName || "未知"
  }));

  showMuList.value = muListWithType.filter(item => {
    const nameMatch =
      !filters.name ||
      item.monitorUnitName?.toLowerCase().includes(filters.name.toLowerCase());
    const typeMatch =
      !filters.type ||
      item.typeName?.toLowerCase().includes(filters.type.toLowerCase());
    const addressMatch =
      !filters.address ||
      item.ipAddress?.toLowerCase().includes(filters.address.toLowerCase());
    return nameMatch && typeMatch && addressMatch;
  });
};

const filterChangeStatus = () => {
  if (!allSelectedItems.value) return;

  showAllSelectedItems.value = allSelectedItems.value.filter(item => {
    const nameMatch =
      !statusFilters.name ||
      item.monitorUnitName
        ?.toLowerCase()
        .includes(statusFilters.name.toLowerCase());
    const typeMatch =
      !statusFilters.type ||
      item.typeName?.toLowerCase().includes(statusFilters.type.toLowerCase());
    const addressMatch =
      !statusFilters.address ||
      item.ipAddress
        ?.toLowerCase()
        .includes(statusFilters.address.toLowerCase());
    const statusMatch =
      !statusFilters.status ||
      item.statusTxt
        ?.toLowerCase()
        .includes(statusFilters.status.toLowerCase());
    return nameMatch && typeMatch && addressMatch && statusMatch;
  });
};

const handleSelectionChange = (selection: any[]) => {
  allSelectedItems.value = selection;
};

const handleSelectAll = (selection: any[]) => {
  allSelectedItems.value = selection;
};

const getStatusType = (status: string) => {
  switch (status) {
    case "无需下发":
      return "info";
    case "待下发":
      return "warning";
    case "正在下发":
      return "primary";
    case "下发成功":
      return "success";
    case "下发失败":
      return "danger";
    default:
      return "info";
  }
};

// 任务管理方法
const stopCurrentTask = () => {
  if (currentTaskPoller.value) {
    currentTaskPoller.value.stop();
    currentTaskPoller.value = null;
  }
  currentTaskId.value = "";
  taskProgress.value = 0;
  taskCurrentStep.value = "";
};

// 业务方法
const onGenerate = async () => {
  if (allSelectedItems.value.length === 0) {
    ElMessage.warning("请先选择监控单元");
    return;
  }

  try {
    generating.value = true;
    logDetail.value = "";
    canGenerate.value = false;

    const ids = allSelectedItems.value.map(item => item.monitorUnitId);

    // 如果包含特定类型的监控单元，自动设置密码
    allSelectedItems.value.forEach(item => {
      if (item.monitorUnitCategory === 18 || item.monitorUnitCategory === 17) {
        distributeForm.passWord = "0202@smsP";
      }
    });

    // 1. 创建配置生成任务
    const response = await createConfigGenerationTask({
      monitorUnitIds: ids,
      userId: 'admin'
    });

    const task = response.data;
    currentTaskId.value = task.taskId;

    // 2. 开始任务轮询
    currentTaskPoller.value = new TaskPoller(task.taskId, {
      interval: 3000,
      timeout: 300000, // 5分钟超时
      onProgress: (status: TaskStatusResponse) => {
        // 更新进度和当前步骤
        taskProgress.value = status.progress;
        taskCurrentStep.value = status.currentStep;
        
        // 更新日志显示
        if (status.message) {
          logDetail.value += `[${new Date().toLocaleTimeString()}] ${status.message}\n`;
        }
        
        // 滚动到底部
        nextTick(() => {
          const textarea = document.querySelector('.log-textarea textarea');
          if (textarea) {
            textarea.scrollTop = textarea.scrollHeight;
          }
        });
      },
      onCompleted: (status: TaskStatusResponse) => {
        taskProgress.value = 100;
        taskCurrentStep.value = '配置生成完成';
        logDetail.value += `[${new Date().toLocaleTimeString()}] 任务完成: ${status.message}\n`;
        canGenerate.value = true;
        generating.value = false;
        ElMessage.success('配置生成完成');
        
        // 3秒后开始刷新监控单元状态
        setTimeout(() => {
          refreshStatus(ids);
        }, 3000);
      },
      onFailed: (status: TaskStatusResponse) => {
        taskCurrentStep.value = '配置生成失败';
        logDetail.value += `[${new Date().toLocaleTimeString()}] 任务失败: ${status.errorMessage || status.message}\n`;
        generating.value = false;
        ElMessage.error(status.errorMessage || '配置生成失败');
      }
    });

    await currentTaskPoller.value.start();
    
  } catch (error: any) {
    generating.value = false;
    const errorMsg = error.message || '配置生成失败';
    logDetail.value += `错误: ${errorMsg}\n`;
    ElMessage.error(errorMsg);
    console.error('配置生成失败:', error);
  }
};

const onDownFile = async () => {
  if (allSelectedItems.value.length === 0) return;

  try {
    const ids = allSelectedItems.value.map(item => item.monitorUnitId);
    await downloadGeneratedConfigs(ids);
    ElMessage.success("下载成功！");
  } catch (error: any) {
    console.error("下载失败:", error);
    ElMessage.error(error.message || "下载失败");
  }
};

const onDistribute = async () => {
  try {
    await distributeFormRef.value?.validate();

    if (allSelectedItems.value.length === 0) {
      ElMessage.warning("请先选择监控单元");
      return;
    }

    distributing.value = true;
    logDetail.value = "";
    completed.value = false;

    const ids = allSelectedItems.value.map(item => item.monitorUnitId);
    
    // 1. 创建配置下发任务
    const response = await createConfigDistributionTask({
      monitorUnitIds: ids,
      username: distributeForm.userName,
      password: distributeForm.passWord,
      port: distributeForm.port,
      protocol: (distributeForm.protocol ? "sftp" : "ftp") as ProtocolType,
      userId: 'admin'
    });

    const task = response.data;
    currentTaskId.value = task.taskId;

    // 2. 开始任务轮询
    currentTaskPoller.value = new TaskPoller(task.taskId, {
      interval: 3000,
      timeout: 300000, // 5分钟超时
      onProgress: (status: TaskStatusResponse) => {
        // 更新进度和当前步骤
        taskProgress.value = status.progress;
        taskCurrentStep.value = status.currentStep;
        
        // 更新日志显示
        if (status.message) {
          logDetail.value += `[${new Date().toLocaleTimeString()}] ${status.message}\n`;
        }
        
        // 滚动到底部
        nextTick(() => {
          const textarea = document.querySelector('.log-textarea textarea');
          if (textarea) {
            textarea.scrollTop = textarea.scrollHeight;
          }
        });
      },
      onCompleted: (status: TaskStatusResponse) => {
        taskProgress.value = 100;
        taskCurrentStep.value = '配置下发完成';
        logDetail.value += `[${new Date().toLocaleTimeString()}] 任务完成: ${status.message}\n`;
        completed.value = true;
        distributing.value = false;
        ElMessage.success('配置下发完成');
      },
      onFailed: (status: TaskStatusResponse) => {
        taskCurrentStep.value = '配置下发失败';
        logDetail.value += `[${new Date().toLocaleTimeString()}] 任务失败: ${status.errorMessage || status.message}\n`;
        distributing.value = false;
        ElMessage.error(status.errorMessage || '配置下发失败');
      }
    });

    await currentTaskPoller.value.start();

  } catch (error: any) {
    distributing.value = false;
    const errorMsg = error.message || '配置下发失败';
    logDetail.value += `错误: ${errorMsg}\n`;
    ElMessage.error(errorMsg);
    console.error("配置下发失败:", error);
  }
};

const refreshStatus = async (ids: number[]) => {
  try {
    const statusList = await refreshMonitorUnitStatus(ids);
    muStatusList.value = statusList || [];

    // 更新状态
    allSelectedItems.value.forEach(mu => {
      const status = muStatusList.value.find(
        s => s.monitorUnitId === mu.monitorUnitId
      );
      if (status) {
        switch (status.state) {
          case 0:
            mu.statusTxt = "无需下发";
            break;
          case 1:
            mu.statusTxt = "待下发";
            break;
          case 2:
            mu.statusTxt = "正在下发";
            break;
          case 3:
            mu.statusTxt = "下发成功";
            break;
          case 4:
            mu.statusTxt = "下发失败";
            break;
          default:
            mu.statusTxt = "未知";
            break;
        }
      }
    });

    // 继续轮询
    if (refreshTimer) {
      clearTimeout(refreshTimer);
    }
    refreshTimer = setTimeout(() => {
      refreshStatus(ids);
    }, 3000);
  } catch (error) {
    console.error("刷新状态失败:", error);
  }
};

// 步骤控制
const pre = () => {
  current.value -= 1;
  // 不需要特殊的日志处理，logDetail保持原状态
};

const next = () => {
  current.value += 1;
  if (current.value === 1) {
    showAllSelectedItems.value = _.cloneDeep(allSelectedItems.value);
  }
  if (current.value === 2) {
    // 清空日志，准备显示分发日志
    logDetail.value = "";

    // 检查是否全部是GFSU V3类型，如果是则启用SFTP
    const isAllGfsuV3 = allSelectedItems.value.every(
      item => item.monitorUnitCategory === 18
    );
    if (isAllGfsuV3) {
      distributeForm.protocol = true;
    }
  }
};

const done = () => {
  handleClose();
};

const handleClose = () => {
  // 停止当前任务轮询
  stopCurrentTask();

  // 清理定时器
  if (refreshTimer) {
    clearTimeout(refreshTimer);
    refreshTimer = null;
  }

  // 重置所有状态
  current.value = 0;
  allSelectedItems.value = [];
  showAllSelectedItems.value = [];
  logDetail.value = "";
  canGenerate.value = false;
  completed.value = false;
  generating.value = false;
  distributing.value = false;
  passwordVisible.value = false;
  taskProgress.value = 0;
  taskCurrentStep.value = "";

  // 重置筛选条件
  filters.name = "";
  filters.type = "";
  filters.address = "";
  statusFilters.name = "";
  statusFilters.type = "";
  statusFilters.address = "";
  statusFilters.status = "";

  // 重置表格选择
  if (tableRef.value) {
    tableRef.value.clearSelection();
  }

  // 重置表单
  if (distributeFormRef.value) {
    distributeFormRef.value.clearValidate();
  }

  // 最后发送关闭事件
  emit("update:visible", false);
  emit("close");
};

// 监听props变化
watch(
  () => props.visible,
  (newVal, oldVal) => {
    if (newVal && !oldVal) {
      // 弹框打开时初始化
      nextTick(() => {
        initData();
      });
    }
  },
  { immediate: false }
);

// 监听数据变化，确保数据加载后能够正确显示
watch(
  () => [props.muList, props.monitorUnitCategories],
  ([newMuList, newCategories]) => {
    if (props.visible && newMuList && newMuList.length > 0) {
      initData();
    }
  },
  { deep: true, immediate: true }
);

// 组件挂载时初始化
onMounted(() => {
  if (props.visible) {
    initData();
  }
});

// 组件卸载时清理
onUnmounted(() => {
  // 停止任务轮询
  stopCurrentTask();
  
  // 清理定时器
  if (refreshTimer) {
    clearTimeout(refreshTimer);
  }
});
</script>

<style scoped>
.mu-distribution-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.mu-distribution-dialog :deep(.el-dialog__footer) {
  padding: 0;
}

.mu-distribution-dialog :deep(.el-dialog__header) {
  padding: 16px 20px 12px;
}

.modern-table {
  border-radius: 6px;
  overflow: hidden;
}

.modern-table :deep(.el-table__header) {
  background-color: var(--el-fill-color-lighter);
}

.modern-table :deep(.el-table th) {
  background-color: var(--el-fill-color-lighter);
  font-weight: 600;
  color: var(--el-text-color-primary);
  padding: 8px 12px;
}

.modern-table :deep(.el-table td) {
  border-bottom: 1px solid var(--el-border-color-lighter);
  padding: 6px 12px;
}

.modern-table :deep(.el-table tr:hover td) {
  background-color: var(--el-fill-color-light);
}

.modern-table :deep(.el-table__row) {
  height: 38px;
}

.log-textarea :deep(.el-textarea__inner) {
  background-color: var(--el-fill-color-darker);
  border: none;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 11px;
  line-height: 1.4;
  color: var(--el-text-color-primary);
  padding: 8px 12px;
}

.distribute-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--el-text-color-regular);
  font-size: 12px;
}

.distribute-form :deep(.el-form-item) {
  margin-bottom: 16px;
}

.distribution-tabs :deep(.el-tabs__header) {
  margin-bottom: 12px;
}

.distribution-tabs :deep(.el-tabs__nav-wrap) {
  background-color: transparent;
}

.distribution-tabs :deep(.el-tabs__item) {
  font-weight: 500;
  font-size: 12px;
  padding: 0 16px;
}

.distribution-tabs :deep(.el-tabs__content) {
  padding: 0;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .mu-distribution-dialog {
    width: 90% !important;
  }
}

@media (max-width: 1200px) {
  .mu-distribution-dialog {
    width: 95% !important;
  }
}

@media (max-width: 992px) {
  .grid-cols-4 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (max-width: 768px) {
  .grid-cols-4 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .mu-distribution-dialog {
    width: 98% !important;
  }
}
</style>
