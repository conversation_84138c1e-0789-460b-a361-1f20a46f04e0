package com.siteweb.tcs.south.omc.web.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.siteweb.tcs.siteweb.entity.Signal;
import com.siteweb.tcs.siteweb.entity.Control;
import com.siteweb.tcs.siteweb.entity.Event;
import com.siteweb.tcs.siteweb.mapper.EquipmentTemplateMapper;
import com.siteweb.tcs.siteweb.mapper.SignalMapper;
import com.siteweb.tcs.siteweb.mapper.ControlMapper;
import com.siteweb.tcs.siteweb.mapper.EventMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 设备模板标准化服务
 */
@Slf4j
@Service
public class EquipmentTemplateStandardizationService {

    @Autowired
    private EquipmentTemplateMapper equipmentTemplateMapper;

    @Autowired
    private SignalMapper signalMapper;

    @Autowired
    private ControlMapper controlMapper;

    @Autowired
    private EventMapper eventMapper;

    /**
     * 应用标准化到子模版
     * 将父模板的信号、告警、控制的描述字段复制到所有子模版的相同ID项目上
     * 
     * @param parentTemplateId 父模板ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean applyStandardizationToChildren(Integer parentTemplateId) {
        try {
            log.info("开始应用标准化到子模版，父模板ID: {}", parentTemplateId);

            // 1. 查找所有子模版ID
            List<Integer> childTemplateIds = equipmentTemplateMapper.findAllChildId(parentTemplateId);
            if (childTemplateIds == null || childTemplateIds.isEmpty()) {
                log.warn("未找到父模板 {} 的子模版", parentTemplateId);
                return true; // 没有子模版也算成功
            }

            log.info("找到 {} 个子模版: {}", childTemplateIds.size(), childTemplateIds);

            // 2. 获取父模板的信号数据（只获取有描述的信号）
            List<Signal> parentSignals = signalMapper.selectList(
                new QueryWrapper<Signal>()
                    .eq("EquipmentTemplateId", parentTemplateId)
                    .isNotNull("Description")
                    .ne("Description", "")
            );
            
            // 3. 获取父模板的控制数据（只获取有描述的控制）
            List<Control> parentControls = controlMapper.selectList(
                new QueryWrapper<Control>()
                    .eq("EquipmentTemplateId", parentTemplateId)
                    .isNotNull("Description")
                    .ne("Description", "")
            );

            // 4. 获取父模板的事件数据（只获取有描述的事件）
            List<Event> parentEvents = eventMapper.selectList(
                new QueryWrapper<Event>()
                    .eq("EquipmentTemplateId", parentTemplateId)
                    .isNotNull("Description")
                    .ne("Description", "")
            );

            log.info("父模板数据统计 - 信号: {}, 控制: {}, 事件: {}", 
                    parentSignals.size(), parentControls.size(), parentEvents.size());

            // 5. 对每个子模版应用标准化
            for (Integer childTemplateId : childTemplateIds) {
                log.info("正在处理子模版: {}", childTemplateId);
                
                // 更新信号描述
                updateSignalDescriptions(childTemplateId, parentSignals);
                
                // 更新控制描述
                updateControlDescriptions(childTemplateId, parentControls);
                
                // 更新事件描述
                updateEventDescriptions(childTemplateId, parentEvents);
            }

            log.info("标准化应用完成，父模板ID: {}", parentTemplateId);
            return true;

        } catch (Exception e) {
            log.error("应用标准化到子模版失败，父模板ID: {}", parentTemplateId, e);
            throw new RuntimeException("应用标准化失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新子模版的信号描述
     */
    private void updateSignalDescriptions(Integer childTemplateId, List<Signal> parentSignals) {
        if (parentSignals.isEmpty()) {
            return;
        }

        // 转换为Map便于查找
        Map<Integer, String> signalDescMap = parentSignals.stream()
            .collect(Collectors.toMap(Signal::getSignalId, Signal::getDescription));

        // 批量更新
        for (Map.Entry<Integer, String> entry : signalDescMap.entrySet()) {
            Integer signalId = entry.getKey();
            String description = entry.getValue();
            
            int updated = signalMapper.update(null, 
                new UpdateWrapper<Signal>()
                    .eq("EquipmentTemplateId", childTemplateId)
                    .eq("SignalId", signalId)
                    .set("Description", description)
            );
            
            if (updated > 0) {
                log.debug("子模版 {} 的信号 {} 描述已更新", childTemplateId, signalId);
            }
        }
    }

    /**
     * 更新子模版的控制描述
     */
    private void updateControlDescriptions(Integer childTemplateId, List<Control> parentControls) {
        if (parentControls.isEmpty()) {
            return;
        }

        // 转换为Map便于查找
        Map<Integer, String> controlDescMap = parentControls.stream()
            .collect(Collectors.toMap(Control::getControlId, Control::getDescription));

        // 批量更新
        for (Map.Entry<Integer, String> entry : controlDescMap.entrySet()) {
            Integer controlId = entry.getKey();
            String description = entry.getValue();
            
            int updated = controlMapper.update(null, 
                new UpdateWrapper<Control>()
                    .eq("EquipmentTemplateId", childTemplateId)
                    .eq("ControlId", controlId)
                    .set("Description", description)
            );
            
            if (updated > 0) {
                log.debug("子模版 {} 的控制 {} 描述已更新", childTemplateId, controlId);
            }
        }
    }

    /**
     * 更新子模版的事件描述
     */
    private void updateEventDescriptions(Integer childTemplateId, List<Event> parentEvents) {
        if (parentEvents.isEmpty()) {
            return;
        }

        // 转换为Map便于查找
        Map<Integer, String> eventDescMap = parentEvents.stream()
            .collect(Collectors.toMap(Event::getEventId, Event::getDescription));

        // 批量更新
        for (Map.Entry<Integer, String> entry : eventDescMap.entrySet()) {
            Integer eventId = entry.getKey();
            String description = entry.getValue();
            
            int updated = eventMapper.update(null, 
                new UpdateWrapper<Event>()
                    .eq("EquipmentTemplateId", childTemplateId)
                    .eq("EventId", eventId)
                    .set("Description", description)
            );
            
            if (updated > 0) {
                log.debug("子模版 {} 的事件 {} 描述已更新", childTemplateId, eventId);
            }
        }
    }
} 