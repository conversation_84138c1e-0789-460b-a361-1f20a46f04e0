package com.siteweb.tcs.south.omc.web.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description:
 */
@Slf4j
@RestController
@RequestMapping("/equipmentcategorymap")
public class EquipmentCategoryMapController {

    @Qualifier("omcSitewebPersistentService")
    @Autowired
    private SitewebPersistentService sitewebPersistentService;

    @GetMapping(value = "/list/{equipmentCategory}" , produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> list(@PathVariable Integer equipmentCategory) {
        return ResponseHelper.successful(sitewebPersistentService.getConfigAPI().findEquipmentCategoryMapDTOForEquipmentCategoryMap(equipmentCategory));
    }
}
