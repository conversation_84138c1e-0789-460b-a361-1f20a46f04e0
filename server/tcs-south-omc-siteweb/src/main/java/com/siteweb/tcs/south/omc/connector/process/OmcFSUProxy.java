package com.siteweb.tcs.south.omc.connector.process;

import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.Props;

@Slf4j
public class OmcFSUProxy extends AbstractActor {
    public static Props props() {
        return Props.create(OmcFSUProxy.class);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .matchAny(message -> log.debug("OmcFSUProxy 接收到消息: {}", message))
                .build();
    }
} 