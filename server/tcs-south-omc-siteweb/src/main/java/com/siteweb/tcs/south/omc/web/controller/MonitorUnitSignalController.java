package com.siteweb.tcs.south.omc.web.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.siteweb.tcs.siteweb.entity.TslMonitorUnitSignal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 监控单元信号Controller
 * 对应前端device-management模块的监控单元信号相关接口
 */
@Slf4j
@RestController
@RequestMapping("/monitorunitsignal")
public class MonitorUnitSignalController {

    @Qualifier("omcSitewebPersistentService")
    @Autowired
    private SitewebPersistentService sitewebPersistentService;

    /**
     * 获取信号实例信息
     * 对应前端: api/config/monitorunitsignal/condition
     */
    @GetMapping("/condition")
    public ResponseEntity<ResponseResult> getMonitorUnitSignalCondition(
            @RequestParam Integer equipmentId,
            @RequestParam Integer signalId) {
        try {
            List<TslMonitorUnitSignal> result = sitewebPersistentService.getConfigAPI()
                    .getMonitorUnitSignalConditionForDeviceManagement(equipmentId, signalId);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to get monitor unit signal condition", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 添加信号实例
     * 对应前端: api/config/monitorunitsignal/add
     */
    @PostMapping("/add")
    public ResponseEntity<ResponseResult> addMonitorUnitSignal(@RequestBody TslMonitorUnitSignal signal) {
        try {
            boolean result = sitewebPersistentService.getConfigAPI()
                    .addMonitorUnitSignalForDeviceManagement(signal);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to add monitor unit signal", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 删除信号实例
     * 对应前端: api/config/monitorunitsignal/delete
     */
    @DeleteMapping("/delete")
    public ResponseEntity<ResponseResult> deleteMonitorUnitSignal(
            @RequestParam Integer equipmentId,
            @RequestParam Integer signalId) {
        try {
            boolean result = sitewebPersistentService.getConfigAPI()
                    .deleteMonitorUnitSignalForDeviceManagement(equipmentId, signalId);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to delete monitor unit signal", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
