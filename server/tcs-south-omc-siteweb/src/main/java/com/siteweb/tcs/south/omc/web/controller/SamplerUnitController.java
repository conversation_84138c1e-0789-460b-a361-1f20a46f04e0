package com.siteweb.tcs.south.omc.web.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.siteweb.tcs.siteweb.entity.SamplerUnit;
import com.siteweb.tcs.siteweb.vo.SamplerUnitWithPortVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 采样器单元控制器
 * 通过 SitewebPersistentService.getConfigAPI() 调用相关方法
 */
@Slf4j
@RestController
@RequestMapping("/sampler-unit")
public class SamplerUnitController {

    @Qualifier("omcSitewebPersistentService")
    @Autowired
    private SitewebPersistentService sitewebPersistentService;

    /**
     * 新增采样器单元配置
     *
     * @param samplerUnit 采样器单元实体
     * @return 创建结果
     */
    @PostMapping("/config")
    public ResponseEntity<ResponseResult> createConfig(@RequestBody SamplerUnit samplerUnit) {
        try {
            // 设置默认值（去掉局站概念）
            if (samplerUnit.getMonitorUnitId() == null) {
                return ResponseHelper.failed("监控单元ID不能为空");
            }

            SamplerUnit createdSamplerUnit = sitewebPersistentService.getConfigAPI()
                    .createForSamplerUnit(samplerUnit);
            
            if (createdSamplerUnit != null) {
                return ResponseHelper.successful(createdSamplerUnit);
            } else {
                return ResponseHelper.failed("采样器单元创建失败");
            }
        } catch (Exception e) {
            log.error("创建采样器单元配置失败", e);
            return ResponseHelper.failed("创建采样器单元配置失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取采样器单元配置
     *
     * @param samplerUnitId 采样器单元ID
     * @return 采样器单元配置
     */
    @GetMapping("/config/{samplerUnitId}")
    public ResponseEntity<ResponseResult> getConfig(@PathVariable("samplerUnitId") Integer samplerUnitId) {
        try {
            SamplerUnit samplerUnit = sitewebPersistentService.getConfigAPI()
                    .findByIdForSamplerUnit(samplerUnitId);
            
            if (samplerUnit != null) {
                return ResponseHelper.successful(samplerUnit);
            } else {
                return ResponseHelper.failed("采样器单元不存在");
            }
        } catch (Exception e) {
            log.error("获取采样器单元配置失败, ID: {}", samplerUnitId, e);
            return ResponseHelper.failed("获取采样器单元配置失败: " + e.getMessage());
        }
    }

    /**
     * 更新采样器单元配置
     *
     * @param samplerUnit 采样器单元实体
     * @return 更新结果
     */
    @PutMapping("/config")
    public ResponseEntity<ResponseResult> updateConfig(@RequestBody SamplerUnit samplerUnit) {
        try {
            if (samplerUnit.getSamplerUnitId() == null) {
                return ResponseHelper.failed("采样器单元ID不能为空");
            }

            boolean success = sitewebPersistentService.getConfigAPI()
                    .updateForSamplerUnit(samplerUnit);
            
            if (success) {
                return ResponseHelper.successful();
            } else {
                return ResponseHelper.failed("采样器单元更新失败");
            }
        } catch (Exception e) {
            log.error("更新采样器单元配置失败", e);
            return ResponseHelper.failed("更新采样器单元配置失败: " + e.getMessage());
        }
    }

    /**
     * 删除采样器单元配置
     *
     * @param samplerUnitId 采样器单元ID
     * @return 删除结果
     */
    @DeleteMapping("/config/{samplerUnitId}")
    public ResponseEntity<ResponseResult> deleteConfig(@PathVariable("samplerUnitId") Integer samplerUnitId) {
        try {
            boolean success = sitewebPersistentService.getConfigAPI()
                    .deleteForSamplerUnit(samplerUnitId);
            
            if (success) {
                return ResponseHelper.successful();
            } else {
                return ResponseHelper.failed("采样器单元删除失败");
            }
        } catch (Exception e) {
            log.error("删除采样器单元配置失败, ID: {}", samplerUnitId, e);
            return ResponseHelper.failed("删除采样器单元配置失败: " + e.getMessage());
        }
    }

    /**
     * 根据监控单元ID和端口ID获取采样器单元列表
     *
     * @param monitorUnitId 监控单元ID
     * @param portId 端口ID
     * @return 采样器单元列表
     */
    @GetMapping
    public ResponseEntity<ResponseResult> getSamplerUnits(
            @RequestParam("monitorUnitId") Integer monitorUnitId,
            @RequestParam("portId") Integer portId) {
        try {
            java.util.List<SamplerUnit> samplerUnits = sitewebPersistentService.getConfigAPI()
                    .findByMonitorUnitIdAndPortIdForSamplerUnit(monitorUnitId, portId);

            return ResponseHelper.successful(samplerUnits);
        } catch (Exception e) {
            log.error("获取采样器单元列表失败, monitorUnitId: {}, portId: {}", monitorUnitId, portId, e);
            return ResponseHelper.failed("获取采样器单元列表失败: " + e.getMessage());
        }
    }

    // ==================== 设备管理相关接口 ====================

    /**
     * 获取采样单元列表（带端口信息）
     * 对应前端: api/config/samplerunit/samplerunitwithport
     */
    @GetMapping("/samplerunitwithport")
    public ResponseEntity<List<SamplerUnitWithPortVO>> getSamplerUnitWithPort() {
        try {
            List<SamplerUnitWithPortVO> result = sitewebPersistentService.getConfigAPI()
                    .getSamplerUnitWithPortForDeviceManagement();
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Failed to get sampler unit with port", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
