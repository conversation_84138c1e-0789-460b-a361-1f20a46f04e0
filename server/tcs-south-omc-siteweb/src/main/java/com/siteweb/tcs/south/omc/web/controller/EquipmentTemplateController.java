package com.siteweb.tcs.south.omc.web.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.siteweb.tcs.siteweb.entity.EquipmentTemplate;
import com.siteweb.tcs.siteweb.dto.CopyEquipmentTemplateDTO;
import com.siteweb.tcs.siteweb.vo.EquipmentTemplateVO;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.multipart.MultipartFile;
import cn.hutool.core.exceptions.ExceptionUtil;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 设备模板控制器（仅支持协议管理页面需要的接口）
 * 通过 SitewebPersistentService.getConfigAPI() 调用相关方法
 */
@Slf4j
@RestController
@RequestMapping("/equipmenttemplate")
public class EquipmentTemplateController {

    @Qualifier("omcSitewebPersistentService")
    @Autowired
    private SitewebPersistentService sitewebPersistentService;


    /**
     * 查询所有设备模板（用于协议管理页面显示）
     */
    @GetMapping
    public ResponseEntity<ResponseResult> findAll() {
        try {
            List<?> result = sitewebPersistentService.getConfigAPI().findVoAllForEquipmentTemplate();
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to find all equipment templates", e);
            return ResponseHelper.failed("查询设备模板失败: " + e.getMessage());
        }
    }

    /**
     * 导入设备模板（协议上传功能）
     */
    @PostMapping("/import")
    public ResponseEntity<ResponseResult> importEquipmentTemplate(MultipartFile file) {
        if (Objects.isNull(file) || file.isEmpty()) {
            return ResponseHelper.failed("上传文件不能为空");
        }

        Document document = null;
        try {
            byte[] fileBytes = file.getBytes();
            // 使用字节数组创建输入流
            InputStream templateInputXmlStream = new ByteArrayInputStream(fileBytes);

            // Use dom4j to read XML
            SAXReader reader = new SAXReader();
            document = reader.read(templateInputXmlStream);

        } catch (Exception e) {
            log.error("导入设备模板错误，:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseHelper.failed("上传文件格式错误");
        }

        try {
            // Get root element using dom4j
            Element equipmentTemplatesElement = document.getRootElement();

            EquipmentTemplate equipmentTemplate = sitewebPersistentService.getConfigAPI()
                    .importTemplateForEquipmentTemplate(equipmentTemplatesElement);

            if (equipmentTemplate != null) {
                return ResponseHelper.successful(equipmentTemplate);
            }
            return ResponseHelper.failed("导入设备模板失败");
        } catch (Exception e) {
            log.error("Failed to import equipment template", e);
            return ResponseHelper.failed("导入设备模板失败: " + e.getMessage());
        }
    }

    /**
     * 获取设备模板树 (隐藏动态配置模板)
     */
    @GetMapping("/tree")
    public ResponseEntity<ResponseResult> findTree(@RequestParam(required = false, defaultValue = "true") Boolean hideDynamicConfigTemplate) {
        try {
            return ResponseHelper.successful(sitewebPersistentService.getConfigAPI().findTreeForEquipmentTemplate(hideDynamicConfigTemplate));
        } catch (Exception e) {
            log.error("Failed to find equipment template tree", e);
            return ResponseHelper.failed("查询设备模板树失败: " + e.getMessage());
        }
    }

    /**
     * 根据 ID 获取单个模板的详细信息
     */
    @GetMapping("/{id}")
    public ResponseEntity<ResponseResult> findById(@PathVariable Integer id) {
        try {
            Object result = sitewebPersistentService.getConfigAPI().findVoByIdForEquipmentTemplate(id);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to find equipment template by id: {}", id, e);
            return ResponseHelper.failed("查询设备模板详情失败: " + e.getMessage());
        }
    }

    /**
     * 更新模板的设备类型 (继承更新)
     */
    @PutMapping("/inheritupdate")
    public ResponseEntity<ResponseResult> inheritUpdate(@RequestBody EquipmentTemplate equipmentTemplate) {
        try {
            boolean result = sitewebPersistentService.getConfigAPI().inheritUpdateForEquipmentTemplate(equipmentTemplate);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to inherit update equipment template", e);
            return ResponseHelper.failed("继承更新设备模板失败: " + e.getMessage());
        }
    }

    /**
     * 传入完整模板信息以进行更新
     */
    @PutMapping
    public ResponseEntity<ResponseResult> update(@RequestBody EquipmentTemplateVO equipmentTemplateVO) {
        try {
            boolean result = sitewebPersistentService.getConfigAPI().updateForEquipmentTemplate(equipmentTemplateVO.toEntity());
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to update equipment template", e);
            return ResponseHelper.failed("更新设备模板失败: " + e.getMessage());
        }
    }

    /**
     * 复制一个设备模板
     */
    @PostMapping("/copy")
    public ResponseEntity<ResponseResult> copyTemplate(@RequestBody CopyEquipmentTemplateDTO copyEquipmentTemplateDTO) {
        try {
            Object result = sitewebPersistentService.getConfigAPI().copyForEquipmentTemplate(copyEquipmentTemplateDTO);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to copy equipment template", e);
            return ResponseHelper.failed("复制设备模板失败: " + e.getMessage());
        }
    }

    /**
     * 根据 ID 删除指定模板
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseResult> deleteById(@PathVariable Integer id) {
        try {
            boolean result = sitewebPersistentService.getConfigAPI().deleteByIdForEquipmentTemplate(id);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to delete equipment template: {}", id, e);
            return ResponseHelper.failed("删除设备模板失败: " + e.getMessage());
        }
    }

    /**
     * 根据模板 ID 导出模板文件
     */
    @PostMapping("/export")
    public ResponseEntity<ResponseResult> exportTemplate(@RequestParam Integer equipmentTemplateId) {
        try {
            String result = sitewebPersistentService.getConfigAPI().exportForEquipmentTemplate(equipmentTemplateId);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to export equipment template: {}", equipmentTemplateId, e);
            return ResponseHelper.failed("导出设备模板失败: " + e.getMessage());
        }
    }

    /**
     * 将指定模板升级为根模板
     */
    @PutMapping("/upgradetoroottemplate")
    public ResponseEntity<ResponseResult> upgradeToRootTemplate(@RequestParam Integer equipmentTemplateId) {
        try {
            boolean result = sitewebPersistentService.getConfigAPI().upgradeToRootTemplateForEquipmentTemplate(equipmentTemplateId);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to upgrade to root template: {}", equipmentTemplateId, e);
            return ResponseHelper.failed("升级为根模板失败: " + e.getMessage());
        }
    }

    /**
     * 新增一个设备模板
     */
    @PostMapping
    public ResponseEntity<ResponseResult> createTemplate(@RequestBody EquipmentTemplate equipmentTemplate) {
        try {
            Object result = sitewebPersistentService.getConfigAPI().createForEquipmentTemplate(equipmentTemplate);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to create equipment template", e);
            return ResponseHelper.failed("创建设备模板失败: " + e.getMessage());
        }
    }

    /**
     * 根据条件 (设备分类、模板名称) 筛选模板树
     */
    @GetMapping("/tree/excludecategory")
    public ResponseEntity<ResponseResult> findTreeExcludeCategory(
            @RequestParam(required = false) Integer equipmentCategory,
            @RequestParam(required = false) String protocolCode,
            @RequestParam(required = false) String equipmentTemplateName) {
        try {
            List<?> result = sitewebPersistentService.getConfigAPI()
                    .findTreeExcludeCategoryForEquipmentTemplate(equipmentCategory, protocolCode, equipmentTemplateName);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to find equipment template tree by condition", e);
            return ResponseHelper.failed("根据条件查询设备模板树失败: " + e.getMessage());
        }
    }

    /**
     * 根据模板 ID 列表获取相关的 DLL 路径
     */
    @GetMapping("/dllpath")
    public ResponseEntity<ResponseResult> getDllPath(@RequestParam String equipmentTemplateIds) {
        try {
            // 解析ID列表
            List<Integer> idList = java.util.Arrays.stream(equipmentTemplateIds.split(","))
                    .map(String::trim)
                    .map(Integer::parseInt)
                    .toList();
            List<String> result = sitewebPersistentService.getConfigAPI().getDllPathForEquipmentTemplate(idList);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to get DLL paths for equipment templates: {}", equipmentTemplateIds, e);
            return ResponseHelper.failed("获取DLL路径失败: " + e.getMessage());
        }
    }

    /**
     * 根据模板 ID 获取其关联的信号、事件和控制
     */
    @GetMapping("/equipmentTemplateId")
    public ResponseEntity<ResponseResult> getAssociatedData(@RequestParam Integer equipmentTemplateId) {
        try {
            Object result = sitewebPersistentService.getConfigAPI().getAssociatedDataForEquipmentTemplate(equipmentTemplateId);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to get associated data for equipment template: {}", equipmentTemplateId, e);
            return ResponseHelper.failed("获取设备模板关联数据失败: " + e.getMessage());
        }
    }

    /**
     * 根据模板 ID 导出其详细配置为 Excel 文件
     */
    @PostMapping("/excel/export")
    public void exportExcel(HttpServletResponse response, @RequestParam Integer equipmentTemplateId) {
        try {
            sitewebPersistentService.getConfigAPI().exportExcelForEquipmentTemplate(response, equipmentTemplateId);
        } catch (Exception e) {
            log.error("Failed to export equipment template to Excel: {}", equipmentTemplateId, e);
            // 设置错误响应
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    @PutMapping(value = "/updatecategory")
    public ResponseEntity<ResponseResult> updateEquipmentCategory(Integer equipmentTemplateId, Integer equipmentCategory) {
        try {
            return ResponseHelper.successful(sitewebPersistentService.getConfigAPI().updateEquipmentCategoryForEquipmentTemplate(equipmentTemplateId,equipmentCategory));
        } catch (Exception e) {
            log.error("Failed to export equipment template to Excel: {}", equipmentTemplateId, e);
            return ResponseHelper.failed("更新设备模板分类失败: " + e.getMessage());
        }
    }

    /**
     * 应用标准化到子模版
     * 将父模板的信号、告警、控制的描述字段复制到所有子模版的相同ID项目上
     */
    @PostMapping("/apply-standardization-to-children")
    public ResponseEntity<ResponseResult> applyStandardizationToChildren(@RequestParam Integer equipmentTemplateId) {
        try {
            boolean result = sitewebPersistentService.getConfigAPI().applyStandardizationToChildrenForEquipmentTemplate(equipmentTemplateId);
            if (result) {
                return ResponseHelper.successful("标准化应用成功");
            } else {
                return ResponseHelper.failed("标准化应用失败");
            }
        } catch (Exception e) {
            log.error("Failed to apply standardization to child templates: {}", equipmentTemplateId, e);
            return ResponseHelper.failed("应用标准化到子模版失败: " + e.getMessage());
        }
    }

}
