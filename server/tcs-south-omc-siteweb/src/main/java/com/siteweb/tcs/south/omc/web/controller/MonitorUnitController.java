package com.siteweb.tcs.south.omc.web.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.siteweb.tcs.siteweb.dto.CreateMonitorUnitDTO;
import com.siteweb.tcs.siteweb.dto.MonitorUnitDTO;
import com.siteweb.tcs.siteweb.dto.TypeItemDTO;
import com.siteweb.tcs.siteweb.entity.DataItem;
import com.siteweb.tcs.siteweb.entity.Equipment;
import com.siteweb.tcs.siteweb.entity.Port;
import com.siteweb.tcs.siteweb.vo.PortTreeVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 监控单元控制器
 * 负责监控单元的增删改查和相关业务操作
 */
@Slf4j
@RestController
@RequestMapping("/monitor-unit")
public class MonitorUnitController {

    @Qualifier("omcSitewebPersistentService")
    @Autowired
    private SitewebPersistentService sitewebPersistentService;

    /**
     * 根据ID获取监控单元配置
     *
     * @param id 监控单元ID
     * @return 监控单元配置信息
     */
    @GetMapping("/config/{id}")
    public ResponseEntity<ResponseResult> getConfig(@PathVariable("id") Integer id) {
        try {
            MonitorUnitDTO monitorUnit = sitewebPersistentService.getConfigAPI().findByIdForMonitorUnit(id);
            if (monitorUnit != null) {
                return ResponseHelper.successful(monitorUnit);
            } else {
                return ResponseHelper.failed("监控单元不存在");
            }
        } catch (Exception e) {
            log.error("获取监控单元配置失败, ID: {}", id, e);
            return ResponseHelper.failed("获取监控单元配置失败: " + e.getMessage());
        }
    }

    /**
     * 创建监控单元配置（按照原配置工具的 createStationMonitorUnit 逻辑，去掉v3字样）
     *
     * @param createMonitorUnitDTO 创建监控单元DTO
     * @return 创建结果
     */
    @PostMapping("/config")
    public ResponseEntity<ResponseResult> createConfig(@RequestBody CreateMonitorUnitDTO createMonitorUnitDTO) {
        try {
            // 去掉局站概念，设置stationId为0
            MonitorUnitDTO monitorUnit = createMonitorUnitDTO.toMonitorUnit(0);

            // 按照原配置工具的逻辑进行验证
            boolean success = sitewebPersistentService.getConfigAPI().createV3ForMonitorUnit(monitorUnit);

            if (success) {
                return ResponseHelper.successful(monitorUnit);
            } else {
                return ResponseHelper.failed("监控单元创建失败");
            }
        } catch (Exception e) {
            log.error("创建监控单元配置失败", e);
            return ResponseHelper.failed("创建监控单元配置失败: " + e.getMessage());
        }
    }

    /**
     * 更新监控单元配置
     *
     * @param monitorUnit 监控单元数据
     * @return 更新结果
     */
    @PutMapping("/config")
    public ResponseEntity<ResponseResult> updateConfig(@RequestBody MonitorUnitDTO monitorUnit) {
        try {
            // 确保stationId为0（去掉局站概念）
            if (monitorUnit.getStationId() == null) {
                monitorUnit.setStationId(0);
            }
            boolean success = sitewebPersistentService.getConfigAPI().updateForMonitorUnit(monitorUnit);
            if (success) {
                return ResponseHelper.successful(monitorUnit);
            } else {
                return ResponseHelper.failed("监控单元更新失败");
            }
        } catch (Exception e) {
            log.error("更新监控单元配置失败", e);
            return ResponseHelper.failed("更新监控单元配置失败: " + e.getMessage());
        }
    }

    /**
     * 删除监控单元配置
     *
     * @param id 监控单元ID
     * @param isDelEqs 是否删除关联设备
     * @return 删除结果
     */
    @DeleteMapping("/config/{id}")
    public ResponseEntity<ResponseResult> deleteConfig(@PathVariable("id") Integer id,
                              @RequestParam(value = "isDelEqs", defaultValue = "false") Boolean isDelEqs) {
        try {
            boolean success = sitewebPersistentService.getConfigAPI().deleteForMonitorUnit(id, isDelEqs);
            if (success) {
                return ResponseHelper.successful();
            } else {
                return ResponseHelper.failed("监控单元删除失败");
            }
        } catch (Exception e) {
            log.error("删除监控单元配置失败, ID: {}", id, e);
            return ResponseHelper.failed("删除监控单元配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取监控单元类型列表
     *
     * @return 监控单元类型列表
     */
    @GetMapping("/types")
    public ResponseEntity<ResponseResult> getTypes() {
        try {
            List<TypeItemDTO> types = sitewebPersistentService.getConfigAPI().findTypesForMonitorUnit();
            return ResponseHelper.successful(types);
        } catch (Exception e) {
            log.error("获取监控单元类型失败", e);
            return ResponseHelper.failed("获取监控单元类型失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有监控单元列表（替代原来的局站监控单元接口）
     *
     * @return 监控单元列表
     */
    @GetMapping
    public ResponseEntity<ResponseResult> getAllMonitorUnits() {
        try {
            List<MonitorUnitDTO> monitorUnits = sitewebPersistentService.getConfigAPI().findByIdsForMonitorUnit(null);
            return ResponseHelper.successful(monitorUnits);
        } catch (Exception e) {
            log.error("获取所有监控单元失败", e);
            return ResponseHelper.failed("获取监控单元列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取监控单元下的端口列表
     *
     * @param monitorUnitId 监控单元ID
     * @return 端口列表
     */
    @GetMapping("/ports/{monitorUnitId}")
    public ResponseEntity<ResponseResult> getPorts(@PathVariable("monitorUnitId") Integer monitorUnitId) {
        try {
            List<Port> ports = sitewebPersistentService.getConfigAPI().findByMonitorUnitIdForPort(monitorUnitId);
            return ResponseHelper.successful(ports);
        } catch (Exception e) {
            log.error("获取监控单元端口失败, monitorUnitId: {}", monitorUnitId, e);
            return ResponseHelper.failed("获取端口列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取监控单元的采样器树结构
     *
     * @param monitorUnitId 监控单元ID
     * @return 采样器树结构
     */
    @GetMapping("/sampler-tree/{monitorUnitId}")
    public ResponseEntity<ResponseResult> getSamplerTree(@PathVariable("monitorUnitId") Integer monitorUnitId) {
        try {
            List<PortTreeVO> samplerTree = sitewebPersistentService.getConfigAPI().getSamplerTreeForMonitorUnit(monitorUnitId);
            return ResponseHelper.successful(samplerTree);
        } catch (Exception e) {
            log.error("获取采样器树失败, monitorUnitId: {}", monitorUnitId, e);
            return ResponseHelper.failed("获取采样器树失败: " + e.getMessage());
        }
    }

    /**
     * 根据监控单元ID获取设备列表
     *
     * @param monitorUnitId 监控单元ID
     * @return 设备列表
     */
    @GetMapping("/equipments/{monitorUnitId}")
    public ResponseEntity<ResponseResult> getByMonitorUnitId(@PathVariable("monitorUnitId") Integer monitorUnitId) {
        try {
            List<Equipment> equipments = sitewebPersistentService.getConfigAPI().findByMonitorUnitIdForEquipment(monitorUnitId);
            return ResponseHelper.successful(equipments);
        } catch (Exception e) {
            log.error("根据监控单元ID获取设备失败, monitorUnitId: {}", monitorUnitId, e);
            return ResponseHelper.failed("获取设备列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID列表批量获取监控单元
     *
     * @param ids 监控单元ID列表
     * @return 监控单元列表
     */
    @PostMapping("/batch")
    public ResponseEntity<ResponseResult> getByIds(@RequestBody List<Integer> ids) {
        try {
            List<MonitorUnitDTO> monitorUnits = sitewebPersistentService.getConfigAPI().findByIdsForMonitorUnit(ids);
            return ResponseHelper.successful(monitorUnits);
        } catch (Exception e) {
            log.error("批量获取监控单元失败", e);
            return ResponseHelper.failed("批量获取监控单元失败: " + e.getMessage());
        }
    }
}
