package com.siteweb.tcs.south.omc.web.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.siteweb.tcs.siteweb.entity.TslMonitorUnitEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 监控单元事件Controller
 * 对应前端device-management模块的监控单元事件相关接口
 */
@Slf4j
@RestController
@RequestMapping("/monitorunitevent")
public class MonitorUnitEventController {

    @Qualifier("omcSitewebPersistentService")
    @Autowired
    private SitewebPersistentService sitewebPersistentService;

    /**
     * 获取事件实例信息
     * 对应前端: api/config/monitorunitevent/condition
     */
    @GetMapping("/condition")
    public ResponseEntity<ResponseResult> getMonitorUnitEventCondition(
            @RequestParam Integer equipmentId,
            @RequestParam Integer eventId) {
        try {
            List<TslMonitorUnitEvent> result = sitewebPersistentService.getConfigAPI()
                    .getMonitorUnitEventConditionForDeviceManagement(equipmentId, eventId);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to get monitor unit event condition", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 添加事件实例
     * 对应前端: api/config/monitorunitevent/add
     */
    @PostMapping("/add")
    public ResponseEntity<ResponseResult> addMonitorUnitEvent(@RequestBody TslMonitorUnitEvent event) {
        try {
            boolean result = sitewebPersistentService.getConfigAPI()
                    .addMonitorUnitEventForDeviceManagement(event);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to add monitor unit event", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 删除事件实例
     * 对应前端: api/config/monitorunitevent/delete
     */
    @DeleteMapping("/delete")
    public ResponseEntity<ResponseResult> deleteMonitorUnitEvent(
            @RequestParam Integer equipmentId,
            @RequestParam Integer eventId) {
        try {
            boolean result = sitewebPersistentService.getConfigAPI()
                    .deleteMonitorUnitEventForDeviceManagement(equipmentId, eventId);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to delete monitor unit event", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
