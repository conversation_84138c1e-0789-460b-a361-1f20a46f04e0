package com.siteweb.tcs.south.omc.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.siteweb.tcs.siteweb.dto.*;
import com.siteweb.tcs.siteweb.entity.TaskStatus;
import com.siteweb.tcs.siteweb.vo.TaskStatusVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * 配置任务管理Controller
 * 提供配置生成、下发、远程配置拉取和下载的RESTful API
 * 合并了原MonitorUnitXmlController的功能
 */
@Slf4j
@RestController
@RequestMapping("/config-tasks")
@Tag(name = "配置任务管理", description = "监控单元配置文件生成、下发、远程配置拉取和下载API")
@Validated
public class ConfigTaskController {

    @Qualifier("omcSitewebPersistentService")
    @Autowired
    private SitewebPersistentService sitewebPersistentService;

    /**
     * 创建配置生成任务
     */
    @PostMapping("/generation")
    @Operation(summary = "创建配置生成任务", description = "为指定的监控单元创建配置生成任务")
    public ResponseEntity<ResponseResult> createGenerationTask(
            @RequestBody ConfigGenerationTaskRequest request) {
        try {
            // 生成唯一任务ID
            String taskId = UUID.randomUUID().toString();

            // 创建任务
            TaskStatus task = sitewebPersistentService.getConfigAPI()
                    .createConfigGenerationTaskForConfigTask(taskId, request.getMonitorUnitIds(), request.getUserId());

            // 构建响应
            TaskResponse response = new TaskResponse();
            response.setTaskId(taskId);
            response.setStatus(task.getStatus());

            log.info("Created config generation task: {} for monitor units: {}", taskId, request.getMonitorUnitIds());
            return ResponseHelper.successful(response);

        } catch (Exception e) {
            log.error("Failed to create config generation task", e);
            return ResponseHelper.failed("创建配置生成任务失败: " + e.getMessage());
        }
    }

    /**
     * 创建配置下发任务
     */
    @PostMapping("/distribution")
    @Operation(summary = "创建配置下发任务", description = "为指定的监控单元创建配置下发任务")
    public ResponseEntity<ResponseResult> createDistributionTask(
            @RequestBody ConfigDistributionTaskRequest request) {
        try {
            // 生成唯一任务ID
            String taskId = UUID.randomUUID().toString();

            // 创建任务
            TaskStatus task = sitewebPersistentService.getConfigAPI()
                    .createConfigDistributionTaskForConfigTask(
                            taskId,
                            request.getMonitorUnitIds(),
                            request.getUsername(),
                            request.getPassword(),
                            request.getPort(),
                            request.getProtocol(),
                            request.getUserId()
                    );

            // 构建响应
            TaskResponse response = new TaskResponse();
            response.setTaskId(taskId);
            response.setStatus(task.getStatus());

            log.info("Created config distribution task: {} for monitor units: {}", taskId, request.getMonitorUnitIds());
            return ResponseHelper.successful(response);

        } catch (Exception e) {
            log.error("Failed to create config distribution task", e);
            return ResponseHelper.failed("创建配置下发任务失败: " + e.getMessage());
        }
    }

    /**
     * 查询任务状态
     */
    @GetMapping("/{taskId}")
    @Operation(summary = "查询任务状态", description = "根据任务ID查询任务的当前状态和进度")
    public ResponseEntity<ResponseResult> getTaskStatus(
            @Parameter(description = "任务ID") @PathVariable String taskId) {
        try {
            TaskStatusVO taskStatus = sitewebPersistentService.getConfigAPI()
                    .getTaskStatusForConfigTask(taskId);

            if (taskStatus == null) {
                return ResponseHelper.failed("任务不存在: " + taskId);
            }

            return ResponseHelper.successful(taskStatus);

        } catch (Exception e) {
            log.error("Failed to get task status: {}", taskId, e);
            return ResponseHelper.failed("查询任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 查询任务历史
     */
    @GetMapping
    @Operation(summary = "查询任务历史", description = "分页查询任务历史记录")
    public ResponseEntity<ResponseResult> getTaskHistory(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "任务类型，可选值：CONFIGURATION_GENERATION, CONFIGURATION_DISTRIBUTION, CONFIGURATION_BACKUP")
            @RequestParam(required = false) String taskType) {
        try {
            Page<TaskStatusVO> taskHistory = sitewebPersistentService.getConfigAPI()
                    .getTaskHistoryForConfigTask(page, size, taskType);

            return ResponseHelper.successful(taskHistory);

        } catch (Exception e) {
            log.error("Failed to get task history, page: {}, size: {}, taskType: {}", page, size, taskType, e);
            return ResponseHelper.failed("查询任务历史失败: " + e.getMessage());
        }
    }

    /**
     * 取消任务
     */
    @DeleteMapping("/{taskId}")
    @Operation(summary = "取消任务", description = "取消指定的任务")
    public ResponseEntity<ResponseResult> cancelTask(
            @Parameter(description = "任务ID") @PathVariable String taskId) {
        try {
            sitewebPersistentService.getConfigAPI().cancelTaskForConfigTask(taskId);

            log.info("Cancelled task: {}", taskId);
            return ResponseHelper.successful("任务已取消");

        } catch (Exception e) {
            log.error("Failed to cancel task: {}", taskId, e);
            return ResponseHelper.failed("取消任务失败: " + e.getMessage());
        }
    }

    /**
     * 拉取远端配置（下载到服务器）
     */
    @PostMapping("/remote-config/pull")
    @Operation(summary = "拉取远端配置", description = "从远程采集器下载配置文件到服务器")
    public ResponseEntity<ResponseResult> pullRemoteConfig(
            @RequestBody @Validated RemoteConfigDownloadTaskRequest request) {
        try {
            // 生成唯一任务ID
            String taskId = UUID.randomUUID().toString();

            // 创建远程配置下载任务
            TaskStatus task = sitewebPersistentService.getConfigAPI()
                    .createRemoteConfigDownloadTaskForConfigTask(
                            taskId,
                            request.getMonitorUnitId(),
                            request.getProtocol(),
                            request.getPort(),
                            request.getUsername(),
                            request.getPassword(),
                            request.getUserId()
                    );

            // 构建响应
            TaskResponse response = new TaskResponse();
            response.setTaskId(taskId);
            response.setStatus(task.getStatus());

            log.info("Created remote config download task: {} for monitor unit: {}", taskId, request.getMonitorUnitId());
            return ResponseHelper.successful(response);

        } catch (Exception e) {
            log.error("Failed to create remote config download task", e);
            return ResponseHelper.failed("创建远程配置下载任务失败: " + e.getMessage());
        }
    }

    /**
     * 下载远端配置（将服务器上的文件提供给前端下载）
     */
    @GetMapping("/remote-config/download/{monitorUnitId}")
    @Operation(summary = "下载远端配置", description = "下载已拉取到服务器的远程配置文件")
    public ResponseEntity<Resource> downloadRemoteConfig(
            @Parameter(description = "监控单元ID") @PathVariable Integer monitorUnitId) {
        try {
            // 直接通过监控单元ID获取最新的远程配置文件
            File file = sitewebPersistentService.getConfigAPI()
                    .getLatestRemoteConfigFileForMonitorUnitXml(monitorUnitId);

            if (file == null || !file.exists()) {
                log.warn("监控单元[{}]没有找到远程配置文件", monitorUnitId);
                return ResponseEntity.notFound().build();
            }

            Resource resource = new FileSystemResource(file);

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + file.getName() + "\"");
            headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);

            log.info("Downloading remote config file: {} for monitor unit: {}", file.getName(), monitorUnitId);
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(file.length())
                    .body(resource);

        } catch (Exception e) {
            log.error("Failed to download remote config for monitor unit: {}", monitorUnitId, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 创建配置备份任务
     */
    @PostMapping("/backup")
    @Operation(summary = "创建配置备份任务", description = "为指定的监控单元创建配置备份任务")
    public ResponseEntity<ResponseResult> createBackupTask(
            @RequestBody @Validated ConfigBackupTaskRequest request) {
        try {
            // 生成唯一任务ID
            String taskId = UUID.randomUUID().toString();

            // 创建任务
            TaskStatus task = sitewebPersistentService.getConfigAPI()
                    .createConfigBackupTaskForConfigTask(taskId, request.getMonitorUnitId(), request.getUserId());

            // 构建响应
            TaskResponse response = new TaskResponse();
            response.setTaskId(taskId);
            response.setStatus(task.getStatus());

            log.info("Created config backup task: {} for monitor unit: {}", taskId, request.getMonitorUnitId());
            return ResponseHelper.successful(response);

        } catch (Exception e) {
            log.error("Failed to create config backup task", e);
            return ResponseHelper.failed("创建配置备份任务失败: " + e.getMessage());
        }
    }

    /**
     * 下载配置备份文件
     */
    @GetMapping("/backup/download/{monitorUnitId}")
    @Operation(summary = "下载配置备份文件", description = "下载指定监控单元的最新备份文件")
    public ResponseEntity<Resource> downloadBackupFile(
            @Parameter(description = "监控单元ID") @PathVariable Integer monitorUnitId) {
        try {
            // 获取最新的备份文件
            File file = sitewebPersistentService.getConfigAPI()
                    .getLatestBackupFileForConfigTask(monitorUnitId);

            if (file == null || !file.exists()) {
                log.warn("监控单元[{}]没有找到备份文件", monitorUnitId);
                return ResponseEntity.notFound().build();
            }

            Resource resource = new FileSystemResource(file);

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + file.getName() + "\"");
            headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);

            log.info("Downloading backup file: {} for monitor unit: {}", file.getName(), monitorUnitId);
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(file.length())
                    .body(resource);

        } catch (Exception e) {
            log.error("Failed to download backup file for monitor unit: {}", monitorUnitId, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 检查是否近期备份过
     */
    @GetMapping("/backup/recent/{monitorUnitId}")
    @Operation(summary = "检查是否近期备份过", description = "检查指定监控单元是否在10分钟内备份过")
    public ResponseEntity<ResponseResult> checkRecentBackup(
            @Parameter(description = "监控单元ID") @PathVariable Integer monitorUnitId,
            @Parameter(description = "时间范围（分钟），默认10分钟") @RequestParam(defaultValue = "10") int minutes) {
        try {
            boolean hasRecent = sitewebPersistentService.getConfigAPI()
                    .hasRecentBackupForConfigTask(monitorUnitId, minutes);

            Map<String, Object> result = new HashMap<>();
            result.put("hasRecentBackup", hasRecent);
            result.put("monitorUnitId", monitorUnitId);
            result.put("checkMinutes", minutes);

            log.debug("Check recent backup for monitor unit: {}, hasRecent: {}", monitorUnitId, hasRecent);
            return ResponseHelper.successful(result);

        } catch (Exception e) {
            log.error("Failed to check recent backup for monitor unit: {}", monitorUnitId, e);
            return ResponseHelper.failed("检查近期备份状态失败: " + e.getMessage());
        }
    }

    // ==================== 监控单元配置文件管理 ====================

    /**
     * 下载多个监控单元配置文件
     */
    @GetMapping(value = "/monitor-unit-configs/download", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    @Operation(summary = "下载监控单元配置文件", description = "根据监控单元ID列表下载对应的配置文件")
    public ResponseEntity<byte[]> downloadMonitorUnitConfigs(
            @Parameter(description = "监控单元ID列表，逗号分隔", example = "1,2,3")
            @RequestParam("monitorUnitIds") String monitorUnitIds) {
        try {
            log.info("Downloading monitor unit config files for monitorUnits: {}", monitorUnitIds);

            // 调用ConfigAPI下载配置文件
            byte[] zipBytes = sitewebPersistentService.getConfigAPI()
                    .downloadMultipleMonitorUnitConfigXMLForMonitorUnitXml(monitorUnitIds);

            // 设置HTTP头信息
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDisposition(ContentDisposition.builder("attachment")
                    .filename("MonitorUnitConfigs.zip")
                    .build());

            log.info("Successfully created MonitorUnitConfigs.zip for monitorUnits: {}", monitorUnitIds);
            return new ResponseEntity<>(zipBytes, headers, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Failed to download monitor unit config files for monitorUnits: {}", monitorUnitIds, e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 创建配置导入任务（直接导入，不进行差异对比）
     */
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "创建配置导入任务", description = "上传zip文件并直接创建配置导入任务（不进行差异对比）")
    public ResponseEntity<ResponseResult> createImportTask(
            @Parameter(description = "配置文件zip包") @RequestParam("file") MultipartFile file,
            @Parameter(description = "用户ID") @RequestParam(defaultValue = "admin") String userId,
            @Parameter(description = "是否覆盖现有配置") @RequestParam(defaultValue = "true") Boolean overwrite,
            @Parameter(description = "导入模式") @RequestParam(defaultValue = "FULL") String importMode,
            @Parameter(description = "备注信息") @RequestParam(required = false) String remarks) {

        try {
            // 验证文件
            if (file.isEmpty()) {
                return ResponseHelper.failed("上传文件不能为空");
            }

            if (!Objects.requireNonNull(file.getOriginalFilename()).toLowerCase().endsWith(".zip")) {
                return ResponseHelper.failed("只支持zip格式的配置文件");
            }

            // 生成唯一任务ID
            String taskId = UUID.randomUUID().toString();

            // 构建请求对象
            ConfigImportTaskRequest request = new ConfigImportTaskRequest();
            request.setUserId(userId);
            request.setOverwrite(overwrite);
            request.setImportMode(importMode);
            request.setRemarks(remarks);

            // 创建任务
            TaskStatus task = sitewebPersistentService.getConfigAPI()
                    .createConfigImportTaskForConfigTask(taskId, file, request);

            // 构建响应
            TaskResponse response = new TaskResponse();
            response.setTaskId(taskId);
            response.setStatus(task.getStatus());

            log.info("Created config import task: {} for file: {}", taskId, file.getOriginalFilename());
            return ResponseHelper.successful(response);

        } catch (Exception e) {
            log.error("Failed to create config import task", e);
            return ResponseHelper.failed("创建配置导入任务失败: " + e.getMessage());
        }
    }

    /**
     * 上传配置文件并进行差异对比（推荐使用的新流程）
     */
    @PostMapping(value = "/upload-and-compare", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "上传配置文件并进行差异对比",
            description = "上传配置文件，自动解析监控单元信息并与数据库进行差异对比，返回差异结果供用户确认")
    public ResponseEntity<ResponseResult> uploadAndCompareConfig(
            @Parameter(description = "配置文件(ZIP格式)", required = true)
            @RequestParam("file") MultipartFile file) {

        try {
            log.info("开始上传配置文件并进行差异对比，文件名: {}", file.getOriginalFilename());

            // 验证文件
            if (file.isEmpty()) {
                return ResponseHelper.failed("上传的文件不能为空");
            }

            if (!Objects.requireNonNull(file.getOriginalFilename()).toLowerCase().endsWith(".zip")) {
                return ResponseHelper.failed("只支持ZIP格式的配置文件");
            }

            // 执行上传和差异对比
            var result = sitewebPersistentService.getConfigAPI()
                    .uploadAndCompareConfigForConfigTask(file);

            if (!result.isSuccess()) {
                return ResponseHelper.failed("上传和差异对比失败: " + result.getErrorMessage());
            }

            log.info("配置文件上传和差异对比完成，任务ID: {}, 监控单元ID: {}, 是否新建: {}",
                    result.getUploadTaskId(), result.getDetectedMonitorUnitId(), result.isNewMonitorUnit());

            return ResponseHelper.successful(result);

        } catch (Exception e) {
            log.error("配置文件上传和差异对比失败，文件名: {}", file.getOriginalFilename(), e);
            return ResponseHelper.failed("配置文件上传和差异对比失败: " + e.getMessage());
        }
    }

    /**
     * 确认导入配置（基于之前上传的文件）
     */
    @PostMapping("/confirm-import/{uploadTaskId}")
    @Operation(summary = "确认导入配置",
            description = "基于之前上传的配置文件确认执行导入操作")
    public ResponseEntity<ResponseResult> confirmImport(
            @Parameter(description = "上传任务ID", required = true)
            @PathVariable String uploadTaskId,
            @Parameter(description = "用户ID") @RequestParam(defaultValue = "admin") String userId,
            @Parameter(description = "是否覆盖现有配置") @RequestParam(defaultValue = "true") Boolean overwrite,
            @Parameter(description = "导入模式") @RequestParam(defaultValue = "FULL") String importMode,
            @Parameter(description = "备注信息") @RequestParam(required = false) String remarks) {

        try {
            log.info("开始确认导入配置，上传任务ID: {}", uploadTaskId);

            // 构建导入请求
            ConfigImportTaskRequest request = new ConfigImportTaskRequest();
            request.setUserId(userId);
            request.setOverwrite(overwrite);
            request.setImportMode(importMode);
            request.setRemarks(remarks);

            // 执行确认导入
            var importReport = sitewebPersistentService.getConfigAPI()
                    .confirmImportFromUploadForConfigTask(uploadTaskId, request);

            if (!importReport.isSuccess()) {
                return ResponseHelper.failed("配置导入失败: " + importReport.getErrorMessage());
            }

            log.info("配置导入完成，上传任务ID: {}", uploadTaskId);
            return ResponseHelper.successful(importReport);

        } catch (Exception e) {
            log.error("确认导入配置失败，上传任务ID: {}", uploadTaskId, e);
            return ResponseHelper.failed("确认导入配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取差异对比结果的统计信息
     */
    @PostMapping("/diff-summary")
    @Operation(summary = "获取差异对比统计信息",
            description = "根据差异对比结果生成统计摘要")
    public ResponseEntity<ResponseResult> getDiffSummary(
            @Parameter(description = "差异对比结果", required = true)
            @RequestBody ConfigUploadAndDiffResult diffResult) {

        try {
            if (diffResult == null || diffResult.getDiffSummary() == null) {
                return ResponseHelper.failed("差异对比结果不能为空");
            }

            var summary = diffResult.getDiffSummary();

            // 构建详细的统计信息
            var detailedSummary = new java.util.HashMap<String, Object>();
            detailedSummary.put("totalChanges", summary.getTotalChanges());
            detailedSummary.put("hasChanges", summary.hasChanges());
            detailedSummary.put("monitorUnitChanges", summary.getMonitorUnitChanges());
            detailedSummary.put("equipmentTemplateChanges", summary.getEquipmentTemplateChanges());
            detailedSummary.put("newEquipmentTemplates", summary.getNewEquipmentTemplates());
            detailedSummary.put("deletedEquipmentTemplates", summary.getDeletedEquipmentTemplates());
            detailedSummary.put("modifiedEquipmentTemplates", summary.getModifiedEquipmentTemplates());
            detailedSummary.put("signalChanges", summary.getSignalChanges());
            detailedSummary.put("eventChanges", summary.getEventChanges());
            detailedSummary.put("controlChanges", summary.getControlChanges());
            detailedSummary.put("portChanges", summary.getPortChanges());
            detailedSummary.put("samplerUnitChanges", summary.getSamplerUnitChanges());
            detailedSummary.put("equipmentChanges", summary.getEquipmentChanges());

            // 添加监控单元信息
            detailedSummary.put("monitorUnitId", diffResult.getDetectedMonitorUnitId());
            detailedSummary.put("monitorUnitName", diffResult.getDetectedMonitorUnitName());
            detailedSummary.put("isNewMonitorUnit", diffResult.isNewMonitorUnit());

            return ResponseHelper.successful(detailedSummary);

        } catch (Exception e) {
            log.error("获取差异统计信息失败", e);
            return ResponseHelper.failed("获取差异统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 取消上传任务（清理临时文件）
     */
    @DeleteMapping("/cancel-upload/{uploadTaskId}")
    @Operation(summary = "取消上传任务",
            description = "取消上传任务并清理相关的临时文件")
    public ResponseEntity<ResponseResult> cancelUploadTask(
            @Parameter(description = "上传任务ID", required = true)
            @PathVariable String uploadTaskId) {

        try {
            log.info("取消上传任务: {}", uploadTaskId);

            // 这里可以添加清理逻辑，比如删除临时文件等
            // 由于我们在ConfigImportServiceImpl中使用了缓存，可以考虑添加清理方法

            log.info("上传任务已取消: {}", uploadTaskId);
            return ResponseHelper.successful("上传任务已取消");

        } catch (Exception e) {
            log.error("取消上传任务失败: {}", uploadTaskId, e);
            return ResponseHelper.failed("取消上传任务失败: " + e.getMessage());
        }
    }
}
