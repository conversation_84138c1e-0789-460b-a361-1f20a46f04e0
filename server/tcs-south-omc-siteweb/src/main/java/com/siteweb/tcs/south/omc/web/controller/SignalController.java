package com.siteweb.tcs.south.omc.web.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.siteweb.tcs.siteweb.dto.SignalConfigItem;
import com.siteweb.tcs.siteweb.dto.SignalFieldCopyDTO;
import com.siteweb.tcs.siteweb.dto.SimplifySignalDTO;
import com.siteweb.tcs.siteweb.entity.AcrossMonitorUnitSignal;
import com.siteweb.tcs.siteweb.entity.Signal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 信号管理Controller
 * 对应前端device-management模块的信号相关接口
 */
@Slf4j
@RestController
@RequestMapping("/signal")
public class SignalController {

    @Qualifier("omcSitewebPersistentService")
    @Autowired
    private SitewebPersistentService sitewebPersistentService;

    /**
     * 获取跨站信号信息
     * 对应前端: api/config/signal/crossmonitorunitsignal/condition
     */
    @GetMapping("/crossmonitorunitsignal/condition")
    public ResponseEntity<ResponseResult> getCrossMonitorUnitSignalCondition(
            @RequestParam Map<String, Object> condition) {
        try {
            List<AcrossMonitorUnitSignal> result = sitewebPersistentService.getConfigAPI()
                    .getCrossMonitorUnitSignalForSignal(condition);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to get cross monitor unit signal condition", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 创建跨站信号
     * 对应前端: api/config/signal/createacrossmonitorunitsignal
     */
    @PostMapping("/createacrossmonitorunitsignal")
    public ResponseEntity<ResponseResult> createAcrossMonitorUnitSignal(
            @RequestBody AcrossMonitorUnitSignal signal) {
        try {
            boolean result = sitewebPersistentService.getConfigAPI()
                    .createAcrossMonitorUnitSignalForSignal(signal);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to create across monitor unit signal", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取简化信号列表
     * 对应前端: api/config/signal/simplifysignals
     */
    @GetMapping("/simplifysignals")
    public ResponseEntity<ResponseResult> getSimplifySignals(Integer equipmentTemplateId) {
        try {
            List<SimplifySignalDTO> result = sitewebPersistentService.getConfigAPI()
                    .getSimplifySignalsForSignal(equipmentTemplateId);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to get simplify signals", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 根据设备模板和设备ID获取信号列表
     * 对应前端: api/config/signal
     */
    @GetMapping(params = {"equipmentTemplateId", "equipmentId"})
    public ResponseEntity<ResponseResult> getSignalList(
            @RequestParam Integer equipmentTemplateId,
            @RequestParam(required = false) Integer equipmentId) {
        try {
            List<SignalConfigItem> result = sitewebPersistentService.getConfigAPI()
                    .getSignalListForSignal(equipmentTemplateId, equipmentId);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to get signal list", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 新增信号
     * 对应前端: api/config/signal/create
     */
    @PostMapping("/create")
    public ResponseEntity<ResponseResult> createSignal(@RequestBody SignalConfigItem signalConfigItem) {
        try {
            Signal result = sitewebPersistentService.getConfigAPI()
                    .createForSignal(signalConfigItem);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to create signal", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 更新信号
     * 对应前端: api/config/signal/update
     */
    @PutMapping("/update")
    public ResponseEntity<ResponseResult> updateSignal(@RequestBody SignalConfigItem signalConfigItem) {
        try {
            Signal result = sitewebPersistentService.getConfigAPI()
                    .updateForSignal(signalConfigItem);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to update signal", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 批量删除信号
     * 对应前端: api/config/signal/batchdelete
     */
    @DeleteMapping("/batchdelete")
    public ResponseEntity<ResponseResult> batchDeleteSignal(
            @RequestParam Integer equipmentTemplateId,
            @RequestBody List<Integer> signalIds) {
        try {
            boolean result = sitewebPersistentService.getConfigAPI()
                    .batchDeleteForSignal(equipmentTemplateId, signalIds);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to batch delete signal", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 信号字段复制
     * 对应前端: api/config/signal/field/copy
     */
    @PutMapping("/field/copy")
    @Transactional
    public ResponseEntity<ResponseResult> fieldCopySignal(@RequestBody List<SignalFieldCopyDTO> signalFieldCopyList) {
        try {
            boolean result = sitewebPersistentService.getConfigAPI()
                    .fieldCopyForSignal(signalFieldCopyList);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to field copy signal", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
