package com.siteweb.tcs.south.omc.config;

import org.flywaydb.core.Flyway;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;

@Configuration
public class FlywayConfig {

    @Value("${plugin.database.fromhop:false}")
    private Boolean fromHop;

    @Bean(name = "omcFlyway")
    @DependsOn("omcDataSource")
    public Flyway flyway(@Qualifier("omcDataSource") DataSource dataSource) throws SQLException {
        String databaseProductName;
        try (Connection conn = dataSource.getConnection()) {
            databaseProductName = conn.getMetaData().getDatabaseProductName().toLowerCase();
        }
        databaseProductName = switch (databaseProductName.toLowerCase()){
            case "postgresql" -> "postgresql";
            case "mysql" -> "h2";
            case "h2" -> "h2";
            default -> "h2";
        };
        String flywaylocation = "classpath:db/south-omc-siteweb/migration/" + databaseProductName.toLowerCase() + (Boolean.TRUE.equals(fromHop) ? "_hop" : "");
        Flyway flyway = Flyway.configure()
            .dataSource(dataSource)
            .locations(flywaylocation)
            .baselineOnMigrate(true)
            .baselineVersion("0")
            .validateOnMigrate(true)
            .table("omc_schema_history")
            .load();
        flyway.migrate();
        return flyway;
    }
} 