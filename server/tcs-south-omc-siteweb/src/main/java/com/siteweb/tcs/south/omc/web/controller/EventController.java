package com.siteweb.tcs.south.omc.web.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.siteweb.tcs.siteweb.dto.BatchEventConfigItem;
import com.siteweb.tcs.siteweb.dto.EventConfigItem;
import com.siteweb.tcs.siteweb.dto.EventFieldCopyDTO;
import com.siteweb.tcs.siteweb.entity.Event;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 事件管理Controller
 * 对应前端device-management模块的事件相关接口
 */
@Slf4j
@RestController
@RequestMapping("/event")
public class EventController {

    @Qualifier("omcSitewebPersistentService")
    @Autowired
    private SitewebPersistentService sitewebPersistentService;

    /**
     * 新增事件
     * 对应前端: api/config/event/create
     */
    @PostMapping("/create")
    public ResponseEntity<ResponseResult> createEvent(@RequestBody EventConfigItem eventConfigItem) {
        try {
            Event result = sitewebPersistentService.getConfigAPI()
                    .createForEvent(eventConfigItem);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to create event", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 更新事件
     * 对应前端: api/config/event/update
     */
    @PutMapping("/update")
    public ResponseEntity<ResponseResult> updateEvent(@RequestBody EventConfigItem eventConfigItem) {
        try {
            Event result = sitewebPersistentService.getConfigAPI()
                    .updateForEvent(eventConfigItem);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to update event", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 批量更新事件
     * 对应前端: api/config/event/update/batch
     */
    @PutMapping("/update/batch")
    public ResponseEntity<ResponseResult> batchUpdateEvent(@RequestBody BatchEventConfigItem batchEventConfigItem) {
        try {
            boolean result = sitewebPersistentService.getConfigAPI()
                    .batchUpdateForEvent(batchEventConfigItem);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to batch update event", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 删除事件
     * 对应前端: api/config/event/delete
     */
    @DeleteMapping("/delete")
    public ResponseEntity<ResponseResult> deleteEvent(
            @RequestParam Integer equipmentTemplateId,
            @RequestParam Integer eventId) {
        try {
            boolean result = sitewebPersistentService.getConfigAPI()
                    .deleteForEvent(equipmentTemplateId, eventId);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to delete event", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping(params = "equipmentTemplateId")
    public ResponseEntity<ResponseResult> findByEquipmentTemplateId(Integer equipmentTemplateId) {
        return ResponseHelper.successful(sitewebPersistentService.getConfigAPI().findEventItemByEquipmentTemplateIdForEvent(equipmentTemplateId));
    }

    /**
     * 事件字段复制
     * 对应前端: api/config/event/field/copy
     */
    @PutMapping("/field/copy")
    @Transactional
    public ResponseEntity<ResponseResult> fieldCopyEvent(@RequestBody List<EventFieldCopyDTO> eventFieldCopyList) {
        try {
            boolean result = sitewebPersistentService.getConfigAPI()
                    .fieldCopyForEvent(eventFieldCopyList);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to field copy event", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 事件关联信号
     * 对应前端: api/config/event/linkevent
     */
    @PostMapping("/linkevent")
    @Transactional
    public ResponseEntity<ResponseResult> linkEvent(
            @RequestParam Integer equipmentTemplateId,
            @RequestParam Integer signalId) {
        try {
            boolean result = sitewebPersistentService.getConfigAPI()
                    .linkEventForEvent(equipmentTemplateId, signalId);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to link event", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
