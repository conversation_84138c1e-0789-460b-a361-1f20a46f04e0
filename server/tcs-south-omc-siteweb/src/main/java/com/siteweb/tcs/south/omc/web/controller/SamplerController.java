package com.siteweb.tcs.south.omc.web.controller;

import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.util.CharsetUtil;
import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.siteweb.entity.Sampler;
import com.siteweb.tcs.siteweb.enums.ProtocolTypeEnum;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.Objects;

/**
 * @Description:
 */
@Slf4j
@RestController
@RequestMapping("/sampler")
public class SamplerController {
    @Qualifier("omcSitewebPersistentService")
    @Autowired
    private SitewebPersistentService sitewebPersistentService;

    @GetMapping
    public ResponseEntity<ResponseResult> findSamplerList(){
//        List<Sampler> allSamplers = sitewebPersistentService.getAllSamplers();
        return ResponseHelper.successful(sitewebPersistentService.getConfigAPI().findAllVoForSampler());
    }

    @GetMapping("/{samplerId}")
    public ResponseEntity<ResponseResult> findById(@PathVariable("samplerId") Integer samplerId){
        return ResponseHelper.successful(sitewebPersistentService.getConfigAPI().findByIdForSampler(samplerId));
    }

    @PutMapping
    public ResponseEntity<ResponseResult> update(@RequestBody Sampler sampler) {
        return ResponseHelper.successful(sitewebPersistentService.getConfigAPI().updateForSampler(sampler));
    }

    @DeleteMapping(params = "samplerIds")
    public ResponseEntity<ResponseResult> batchDelete(String samplerIds){
        String result = sitewebPersistentService.getConfigAPI().validateBatchDeleteForSampler(samplerIds);
        if (StringUtils.isNotEmpty(result)){
            return ResponseHelper.failed(result);
        }
        return ResponseHelper.successful(sitewebPersistentService.getConfigAPI().batchDeleteForSampler(samplerIds));
    }

    @DeleteMapping("/{samplerId}")
    public ResponseEntity<ResponseResult> deleteSampler(@PathVariable("samplerId") Integer samplerId) {
        String result = sitewebPersistentService.getConfigAPI().validateDeleteByIdForSampler(samplerId);
        if (StringUtils.isNotEmpty(result)){
            return ResponseHelper.failed(result);
        }
        return ResponseHelper.successful(sitewebPersistentService.getConfigAPI().deleteByIdForSampler(samplerId));
    }

    @PostMapping("/upload/dllpath")
    public ResponseEntity<ResponseResult> dllPathUpload(@RequestParam("file") MultipartFile file, @RequestParam("protocolCode") String protocolCode, @RequestParam("protocolType") Integer protocolType) throws IOException {
        ProtocolTypeEnum protocolTypeEnum = ProtocolTypeEnum.getByProtocolType(protocolType);
        if (Objects.isNull(protocolTypeEnum)) {
            return ResponseHelper.failed("uploadFile.protocolType.notExist");
        }
        if (Objects.isNull(file) || file.isEmpty()) {
            return ResponseHelper.failed("uploadFile.notEmpty");
        }
        Sampler sampler = sitewebPersistentService.getConfigAPI().findByProtocolCodeForSampler(protocolCode);
        if (Objects.isNull(sampler)) {
            return ResponseHelper.failed("monitor.protocol.noExists"+ protocolCode);
        }
        sitewebPersistentService.getConfigAPI().uploadProtocolFileForSampler(sampler, protocolTypeEnum, file.getBytes(), sampler.getDllPath(),"south-omc-siteweb/workspace/");
        return ResponseHelper.successful();
    }

    @DeleteMapping("/protocolfile")
    public ResponseEntity<ResponseResult> deleteProtocolFile(String protocolCode,Integer protocolType) {
        ProtocolTypeEnum protocolTypeEnum = ProtocolTypeEnum.getByProtocolType(protocolType);
        if (Objects.isNull(protocolTypeEnum)) {
            return ResponseHelper.failed("uploadFile.protocolType.notExist");
        }
        sitewebPersistentService.getConfigAPI().deleteProtocolFileForSampler(protocolCode, protocolTypeEnum);
        return ResponseHelper.successful();
    }

    @GetMapping(value = "/download/dllpath",params = "protocolCode")
    public ResponseEntity<ResponseResult> dllPathDownload(String protocolCode, HttpServletResponse response, Integer protocolType) throws IOException {
        File file = sitewebPersistentService.getConfigAPI().downloadProtocolFileForSampler(protocolCode, protocolType);
        // 设置响应头
        response.setContentType(Files.probeContentType(file.toPath()));
        //使用URLEncode编码，避免中文乱码
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + URLEncodeUtil.encode(file.getName(), CharsetUtil.parse("UTF-8")));
        response.setHeader(HttpHeaders.CONTENT_LENGTH, String.valueOf(file.length()));
        // 将文件写入响应输出流
        try (InputStream inputStream = new FileInputStream(file)) {
            FileCopyUtils.copy(inputStream, response.getOutputStream());
        }
        return ResponseHelper.successful();
    }

    @GetMapping(value = "/getsamplerbyprotocolpath",params = "protocolCode")
    public ResponseEntity<ResponseResult> getSamplerByProtocolCode(String protocolCode){
        if(StringUtils.isBlank(protocolCode)) {
            return ResponseHelper.failed("can't find sampler");
        }
        Sampler sampler = sitewebPersistentService.getConfigAPI().findByProtocolCodeForSampler(protocolCode);
        return ResponseHelper.successful(sampler);
    }
}
