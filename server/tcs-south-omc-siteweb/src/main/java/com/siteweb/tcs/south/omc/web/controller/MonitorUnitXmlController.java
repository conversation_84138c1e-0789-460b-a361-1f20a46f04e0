package com.siteweb.tcs.south.omc.web.controller;

import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 监控单元XML配置文件管理Controller
 * 迁移自tcs-config项目的MonitorUnitXmlController
 */
@Slf4j
@RestController
@RequestMapping("/monitorunitxml")
@Tag(name = "监控单元XML配置管理", description = "监控单元配置文件生成和下载API")
public class MonitorUnitXmlController {

    @Qualifier("omcSitewebPersistentService")
    @Autowired
    private SitewebPersistentService sitewebPersistentService;

    /**
     * 通过workStationId生成监控单元配置文件(RMU)，并将生成的文件打包并作为HTTP响应返回给前端
     * 
     * 迁移自：tcs-config/MonitorUnitXmlController.createAndDownloadMonitorUnitConfigXML
     *
     * @param workStationIds 工作站ID列表，逗号分隔
     * @return ZIP文件流
     */
    @GetMapping(value = "createAndDownloadMonitorUnitConfigXML", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    @Operation(summary = "生成并下载监控单元配置文件", description = "通过工作站ID生成监控单元配置文件并打包下载")
    public ResponseEntity<byte[]> createAndDownloadMonitorUnitConfigXML(
            @Parameter(description = "工作站ID列表，逗号分隔", example = "1,2,3") 
            @RequestParam String workStationIds) {
        try {
            log.info("Creating and downloading monitor unit config XML for workStations: {}", workStationIds);
            
            // 调用ConfigAPI生成并打包配置文件
            byte[] zipBytes = sitewebPersistentService.getConfigAPI()
                    .createAndDownloadMonitorUnitConfigXMLForMonitorUnitXml(workStationIds);

            // 设置HTTP头信息
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDisposition(ContentDisposition.builder("attachment")
                    .filename("RMU.zip")
                    .build());

            log.info("Successfully created RMU.zip for workStations: {}", workStationIds);
            return new ResponseEntity<>(zipBytes, headers, HttpStatus.OK);
            
        } catch (Exception e) {
            log.error("Failed to create and download monitor unit config XML for workStations: {}", workStationIds, e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 下载多个监控单元配置文件
     * 
     * 迁移自：tcs-config/MonitorUnitXmlController.downloadMultipleMonitorUnitConfigXML
     *
     * @param monitorUnitIds 监控单元ID列表，逗号分隔
     * @return ZIP文件流
     */
    @GetMapping(value = "downloadMultipleMonitorUnitConfigXML", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    @Operation(summary = "下载多个监控单元配置文件", description = "根据监控单元ID列表下载对应的配置文件")
    public ResponseEntity<byte[]> downloadMultipleMonitorUnitConfigXML(
            @Parameter(description = "监控单元ID列表，逗号分隔", example = "1,2,3") 
            @RequestParam("monitorUnitIds") String monitorUnitIds) {
        try {
            log.info("Downloading multiple monitor unit config XML for monitorUnits: {}", monitorUnitIds);
            
            // 调用ConfigAPI下载配置文件
            byte[] zipBytes = sitewebPersistentService.getConfigAPI()
                    .downloadMultipleMonitorUnitConfigXMLForMonitorUnitXml(monitorUnitIds);

            // 设置HTTP头信息
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDisposition(ContentDisposition.builder("attachment")
                    .filename("MonitorUnitConfigs.zip")
                    .build());

            log.info("Successfully created MonitorUnitConfigs.zip for monitorUnits: {}", monitorUnitIds);
            return new ResponseEntity<>(zipBytes, headers, HttpStatus.OK);
            
        } catch (Exception e) {
            log.error("Failed to download multiple monitor unit config XML for monitorUnits: {}", monitorUnitIds, e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
