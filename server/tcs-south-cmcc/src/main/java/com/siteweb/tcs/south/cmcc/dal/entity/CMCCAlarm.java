package com.siteweb.tcs.south.cmcc.dal.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.tcs.cmcc.common.protocol.EnumState;
import com.siteweb.tcs.hub.domain.letter.ForeignAlarmConfigChange;
import com.siteweb.tcs.hub.domain.letter.enums.EventLevelEnum;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventType;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * CMCC告警信息实体
 * <AUTHOR> (2025-05-22)
 **/
@Data
@NoArgsConstructor
@TableName("cmcc_alarms")
public class CMCCAlarm implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * FSU ID
     */
    @TableField("fsu_id")
    private String fsuId;

    /**
     * 设备ID
     */
    @TableField("device_id")
    private String deviceId;

    /**
     * 告警点ID
     */
    @TableField("sp_id")
    private String spId;

    /**
     * 告警级别: 0=正常数据, 1=一级告警, 2=二级告警, 3=三级告警, 4=四级告警, 5=操作事件, 6=无效数据
     */
    @TableField("alarm_level")
    private EnumState alarmLevel;

    /**
     * 告警名称
     */
    @TableField("alarm_name")
    private String alarmName;

    /**
     * 标识告警类别
     */
    private String unit;

    /**
     * 操作类型
     */
    @TableField("operate_type")
    private Integer operateType;

    public ForeignAlarmConfigChange toForeignAlarmConfigChange(){
        ForeignAlarmConfigChange foreignAlarmConfigChange = new ForeignAlarmConfigChange();
        switch (alarmLevel) {
            case CRITICAL:
                foreignAlarmConfigChange.setAlarmLevel(EventLevelEnum.LEVEL_1.getValue());
                break;
            case MAJOR:
                foreignAlarmConfigChange.setAlarmLevel(EventLevelEnum.LEVEL_2.getValue());
                break;
            case MINOR:
                foreignAlarmConfigChange.setAlarmLevel(EventLevelEnum.LEVEL_3.getValue());
                break;
            case HINT:
                foreignAlarmConfigChange.setAlarmLevel(EventLevelEnum.LEVEL_4.getValue());
                break;
        }
        foreignAlarmConfigChange.setForeignAlarmName(alarmName);
        foreignAlarmConfigChange.setForeignSignalId(spId);
        foreignAlarmConfigChange.setForeignAlarmId(spId);
        // todo xsx 2025-06-22 后续需要实现事件类型以及基类
//        foreignAlarmConfigChange.setEventType(ConfigToCTCCProvider.convertToLifeCycleEventType(operateType));
//        foreignAlarmConfigChange.setBaseTypeId(StdSignalProvider.getStdSignalByDeviceIdAndSpId(deviceId, spId).getEventBaseTypeId());
        foreignAlarmConfigChange.setEventType(LifeCycleEventType.CREATE);
        foreignAlarmConfigChange.setDescription(spId);
        return foreignAlarmConfigChange;

    }
}