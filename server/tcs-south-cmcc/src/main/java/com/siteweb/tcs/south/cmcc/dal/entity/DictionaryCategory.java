package com.siteweb.tcs.south.cmcc.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;

/**
 * 字典类别实体类
 * 对应表：dictionary_category
 */
@Data
@TableName("dictionary_category")
public class DictionaryCategory implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 字典类别ID
     */
    @TableId(value = "DictionaryCategoryId", type = IdType.AUTO)
    private Integer dictionaryCategoryId;

    /**
     * 类别ID
     */
    @TableField("CategoryId")
    private Integer categoryId;

    /**
     * 类别值
     */
    @TableField("CategoryValue")
    private String categoryValue;

    /**
     * 翻译键
     */
    @TableField("TranslateKey")
    private String translateKey;

    /**
     * 是否启用
     */
    @TableField("Enable")
    private Integer enable;

    /**
     * 是否过期
     */
    @TableField("IsExpired")
    private Integer isExpired;

    /**
     * 扩展字段
     */
    @TableField("ExtendField")
    private String extendField;
} 