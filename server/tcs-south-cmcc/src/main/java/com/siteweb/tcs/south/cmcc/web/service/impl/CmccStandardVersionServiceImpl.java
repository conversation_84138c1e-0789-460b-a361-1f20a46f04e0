package com.siteweb.tcs.south.cmcc.web.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.south.cmcc.dal.entity.CmccStandardVersion;
import com.siteweb.tcs.south.cmcc.dal.mapper.CmccStandardVersionMapper;
import com.siteweb.tcs.south.cmcc.web.service.ICmccStandardVersionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * CMCC标准版本Service实现类
 */
@Service
public class CmccStandardVersionServiceImpl extends ServiceImpl<CmccStandardVersionMapper, CmccStandardVersion> implements ICmccStandardVersionService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveStandardVersion(CmccStandardVersion cmccStandardVersion) {
        return save(cmccStandardVersion);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStandardVersion(CmccStandardVersion cmccStandardVersion) {
        return updateById(cmccStandardVersion);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteStandardVersion(Integer id) {
        return removeById(id);
    }
} 