package com.siteweb.tcs.south.cmcc.connector.process;


import cn.hutool.core.util.ObjectUtil;

import cn.hutool.json.JSONUtil;
import com.siteweb.stream.common.messages.ShapeRouteMessage;
import com.siteweb.stream.core.manager.StreamGraphInstanceManager;
import com.siteweb.stream.core.provider.StreamGraphProvider;
import com.siteweb.tcs.cmcc.common.message.MobileBRawMessage;
import com.siteweb.tcs.cmcc.common.protocol.PK_TypeName;
import com.siteweb.tcs.common.runtime.PluginScope;
import com.siteweb.tcs.common.system.AbstractGatewayProxy;

import com.siteweb.tcs.hub.domain.letter.ForeignDeviceConfigChange;
import com.siteweb.tcs.hub.domain.letter.ForeignGatewayConfigChange;
import com.siteweb.tcs.hub.domain.letter.ForeignSignalConfigChange;
import com.siteweb.tcs.hub.domain.letter.LifeCycleEvent;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventType;
import com.siteweb.tcs.hub.domain.letter.enums.ThingType;
import com.siteweb.tcs.hub.domain.process.lifecycle.GatewayPipelineProxy;
import com.siteweb.tcs.south.cmcc.connector.FailureCauses;
import com.siteweb.tcs.south.cmcc.connector.letter.FSULifeCycleEvent;
import com.siteweb.tcs.south.cmcc.connector.letter.LoginMessage;
import com.siteweb.tcs.south.cmcc.connector.letter.SendDevConfDataMessage;
import com.siteweb.tcs.south.cmcc.connector.letter.UserControlCommand;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCDevice;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCFsu;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCPendingFsu;
import com.siteweb.tcs.south.cmcc.dal.provider.DeviceProvider;
import com.siteweb.tcs.south.cmcc.dal.provider.FSUProvider;
import com.siteweb.tcs.south.cmcc.dal.provider.PendingFSUProvider;
import com.siteweb.tcs.south.cmcc.dal.services.DeviceDiffService;
import com.siteweb.tcs.south.cmcc.util.MobileXmlUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> (2025-05-09)
 **/
@Slf4j
public class CmccFSUProxy extends AbstractGatewayProxy {

    private final FSUProvider fsuProvider;
    private final PendingFSUProvider pendingFSUProvider;
    private final DeviceProvider deviceProvider;

    private final DeviceDiffService deviceDiffService;
    private final FSUState state = new FSUState();
    private final StreamGraphProvider streamGraphProvider;
    private ActorRef graphActor;
    private ActorRef pipeline;
    private StreamGraphInstanceManager streamGraphInstanceManager = StreamGraphInstanceManager.getInstance();
    private final String fsuId;
    //todo xsx 后续需要替换
    private String pluginInstanceId = "tcs-south-cmcc";

    public CmccFSUProxy() {
        fsuId = getSelf().path().name();
        deviceDiffService = new DeviceDiffService(fsuId, this::onDeviceAdded, this::onDeviceDeleted, this::onDeviceModify);
        fsuProvider = PluginScope.getBean(FSUProvider.class);
        pendingFSUProvider = PluginScope.getBean(PendingFSUProvider.class);
        deviceProvider = PluginScope.getBean(DeviceProvider.class);
        streamGraphProvider = PluginScope.getBean(StreamGraphProvider.class);
    }


    @Override
    public void preStart() throws Exception {
        for (CMCCFsu cmccFsu : fsuProvider.getAllFsu()) {
            tryLoadFsu(cmccFsu);
        }
    }


    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(FSULifeCycleEvent.class, this::onProcessLifeCycleEvent)
                .match(UserControlCommand.class, this::onProcessUserControlCommand)
                .match(MobileBRawMessage.class, this::onProcessRawMessage)  // 从Sharding过来的消息
                .build();
    }


    private void onProcessUserControlCommand(UserControlCommand command) {

    }


    private void onProcessLifeCycleEvent(FSULifeCycleEvent message) {
        var event = new LifeCycleEvent();
        // TODO full PluginInstanceId
        event.setPluginInstanceId(pluginInstanceId);
        switch (message.getEvent()) {
            case CREATE:
                var fsuInfo = message.getFsuInfo();
                tryLoadFsu(fsuInfo);
                // Send LifeCycleEvent to Pipeline
                event.setThingType(ThingType.GATEWAY);
                event.setEventType(LifeCycleEventType.CREATE);
                event.setForeignGatewayId(fsuId);
                ForeignGatewayConfigChange foreignGatewayConfigChange = fsuInfo.toForeignGatewayConfigChange();
                event.setForeignConfigChange(foreignGatewayConfigChange);
                pipeline.tell(event, self());
                break;
            case FIELD_UPDATE:
                // Send LifeCycleEvent to Pipeline
                event.setThingType(ThingType.GATEWAY);
                event.setEventType(LifeCycleEventType.UNLOAD);
                event.setForeignConfigChange(message.getFsuInfo());
                event.setForeignGatewayId(fsuId);
                pipeline.tell(event, self());
                // 通知。。
                break;
            case DELETE:
                // 销毁
                shutdownFsu();
                // Send LifeCycleEvent to Pipeline
                event.setThingType(ThingType.GATEWAY);
                event.setEventType(LifeCycleEventType.DELETE);
                event.setForeignConfigChange(message.getFsuInfo());
                event.setForeignGatewayId(fsuId);
                pipeline.tell(event, self());
                state.clean();
                break;
            default:
                log.warn("未知的生命周期事件类型: {}", message.getEvent());
        }
    }

    /**
     * 尝试加载FSU 数据
     *
     * @param fsuInfo 如果是FSU 的 LifeCycleEvent 会携带数据过来，无需重新load
     */
    private void tryLoadFsu(CMCCFsu fsuInfo) {
        // TODO FSUProvider  改为Cache方式获取
        if (fsuInfo == null) {
            fsuInfo = fsuProvider.getFsuByFsuid(fsuId);
        }
        CMCCPendingFsu paddingInfo = null;
        if (fsuInfo == null) {
            // 从待审核列表查找
            paddingInfo = pendingFSUProvider.getFsuByFsuid(fsuId);
        }
        state.updateState(fsuInfo, paddingInfo);
        // 判断是否应该加载数据
        if (state.getStatus().equals(FSUStatus.UNKNOWN) || state.getStatus().equals(FSUStatus.PENDING)) {
            return;
        }
        // 加载设备列表
        state.setDevices(deviceProvider.getAllDevice(fsuId));
        // 创建管道
        pipeline = this.context().actorOf(Props.create(GatewayPipelineProxy.class, fsuId,pluginInstanceId));
        // 启动流计算
        if (state.getFsuInfo().getGraphId() != null) {
            var streamGraph = streamGraphProvider.findGraph(state.getFsuInfo().getGraphId());
            if (streamGraph != null) {
                var graphInstance = streamGraphInstanceManager.createGraph(context(), streamGraph, streamGraph.getGraphOption());
                streamGraphInstanceManager.startGraph(state.getFsuInfo().getGraphId());
                graphActor = graphInstance.getGraphActorRef();
            }
        }
        // Send Load LifeCycleEvent to Pipeline
        var event = new LifeCycleEvent();
        event.setThingType(ThingType.GATEWAY);
        event.setEventType(LifeCycleEventType.LOAD);
        event.setForeignConfigChange(state.getFsuInfo());
        event.setForeignGatewayId(state.getFsuInfo().getFsuId());
        pipeline.tell(event, self());
    }

    /**
     * 停掉FSU相关的所有内存对象
     */
    private void shutdownFsu() {

        // 停掉流程图
        if (state.getFsuInfo().getGraphId() != null) {
            streamGraphInstanceManager.stopGraph(state.getFsuInfo().getGraphId());
        }
        // 设备相关资源清理
        // TODO Pipeline 毒丸消息清理？
        //  pipeline.tell();
    }

    private void syncToLocalAndLifeCycle(LoginMessage.Info fsu) {
        var local = state.getFsuInfo();
        var change = false;
        if (!ObjectUtil.equal(fsu.getFsuIp(), local.getIpAddress())) {
            local.setIpAddress(fsu.getFsuIp());
            change = true;
        }

        if (!ObjectUtil.equal(fsu.getFsuMac(), local.getMac())) {
            local.setMac(fsu.getFsuMac());
            change = true;
        }

        if (!ObjectUtil.equal(fsu.getFsuVer(), local.getVendor())) {
            local.setVersion(fsu.getFsuVer());
            change = true;
        }
        if (change) {
            fsuProvider.updateFsu(local);
            // FSU上送导致数据变更，发送至LifeCycle
            var event = new FSULifeCycleEvent();
            event.setFsuId(local.getFsuId());
            event.setEvent(LifeCycleEventType.FIELD_UPDATE);
            event.setFsuInfo(local);
            self().tell(event, ActorRef.noSender());
        }
    }


    /***
     * 解析登录报文,并进行身份验证
     * <AUTHOR> (2025/5/15)
     * @param message
     */
    private void handleLogin(MobileBRawMessage message) {
        var loginMessage = MobileXmlUtil.parseLoginMessage(message);
        if (loginMessage != null) {
            var pkgInfo = loginMessage.getInfo();
            log.info("Handle FSU:{} login request username:{}, password:{} .", pkgInfo.getFsuId(), pkgInfo.getUserName(), pkgInfo.getPassword());
            if (pkgInfo.getUserName().equals(state.getFsuInfo().getUsername())) {
                if (pkgInfo.getPassword().equals(state.getFsuInfo().getPassword())) {
                    state.setStatus(FSUStatus.ONLINE);
                    log.info("FSU {} login success.", fsuId);
                    loginMessage.responseSuccess();
                    // 同步变更的数据至数据库
                    syncToLocalAndLifeCycle(pkgInfo);
                    // TODO 向管道发送FSU上线
                    var event = new LifeCycleEvent();
                    event.setThingType(ThingType.GATEWAY);
                    event.setEventType(LifeCycleEventType.NOT_CHANGE);
                    event.setForeignConfigChange(state.getFsuInfo());
                    event.setForeignGatewayId(state.getFsuInfo().getFsuId());
                    pipeline.tell(event, self());
                } else {
                    log.warn("FSU {} login failure, cause:{}", pkgInfo.getFsuId(), FailureCauses.PASSWORD_ERROR);
                    message.responseFail(FailureCauses.PASSWORD_ERROR);
                }
            } else {
                log.warn("FSU {} login failure, cause:{}", pkgInfo.getFsuId(), FailureCauses.USERNAME_ERROR);
                message.responseFail(FailureCauses.USERNAME_ERROR);
            }
        }

    }

    private void handleLoginToPending(MobileBRawMessage message) {
        var loginMessage = MobileXmlUtil.parseLoginMessage(message);
        if (loginMessage != null) {
            var pending = new CMCCPendingFsu();
            pending.setFsuId(message.getFsuId());
            pending.setMac(loginMessage.getInfo().getFsuMac());
            pending.setVersion(loginMessage.getInfo().getFsuVer());
            pending.setIpAddress(loginMessage.getInfo().getFsuIp());
            pending.setUsername(loginMessage.getInfo().getUserName());
            pending.setPassword(loginMessage.getInfo().getPassword());
            pending.setCreateTime(LocalDateTime.now());
            pendingFSUProvider.save(pending);
            state.updateState(null, pending);
        }
        // 返回未授权
        message.responseFail(FailureCauses.FORBIDDEN_ACCESS);
    }

    /**
     * 处理Http接口发送过来的移动B接口原始报文
     *
     * @param message
     */
    private void onProcessRawMessage(MobileBRawMessage message) {
        try {
            onProcessRawMessageCore(message);
        } catch (Throwable throwable) {
            message.responseFail(FailureCauses.INTERNAL_SERVER_ERROR);
        }
    }


    private void onProcessRawMessageCore(MobileBRawMessage message) {

        /**
         * xsx 2025/6/9 测试配置先注释掉拦截代码
         */
//        if (state.getStatus().equals(FSUStatus.UNKNOWN)) {
//            // FSU未记录在案
//            if (message.getPkType().equals(PK_TypeName.LOGIN)) {
//                // 当前FSU发来登录请求
//                handleLoginToPending(message);
//            }
//            // 返回未授权
//            message.responseFail(FailureCauses.FORBIDDEN_ACCESS);
//            return;
//        } else if (state.getStatus().equals(FSUStatus.PENDING)) {
//            // 返回未授权
//            message.responseFail(FailureCauses.FORBIDDEN_ACCESS);
//            return;
//        }
//        if (!state.isLoginAuthentication()) {
//            // 未认证的
//            if (message.getPkType().equals(PK_TypeName.LOGIN)) {
//                handleLogin(message);
//            } else {
//                // 对于未登录时收到除LOGIN之外的包全部丢掉。
//                message.responseFail(FailureCauses.NOT_AUTHORIZED);
//            }
//            return;
//        }
        // 配置上送
        if (message.getPkType().equals(PK_TypeName.SEND_DEV_CONF_DATA)) {
            var confMsg = MobileXmlUtil.parseDevConfMessage(message);
            if (confMsg != null) {
                handleDevConfData(confMsg);
                confMsg.responseSuccess();
            } else {
                message.responseFail(FailureCauses.INVALID_MESSAGE_DATA);
            }
            return;
        }
        if (graphActor != null) {
            //转发到 Streams 处理
            message.setOriginalAsker(getSender());
            graphActor.tell(new ShapeRouteMessage(message), self());
            return;
        }
        //TODO  消息未被处理，如何做？ 回复成功还是失败？ 先打印日志 不回复。
        log.warn("Unprocessed FSU request message {}", message.getPkType().name());
    }

    /**
     * 处理移动B接口的配置上送
     * 此事件应保持同步，以保证后续的数据告警与配置一致性
     *
     * @param message
     */
    private void handleDevConfData(SendDevConfDataMessage message) {
        var devices = message.getInfo().getDeviceList();
        List<CMCCDevice> selfDevice = new ArrayList<>();
        deviceDiffService.compare(devices, selfDevice);
    }


    /***
     * 配置上送，新设备添加,同时处理设备信号
     * @param devices
     */
    private void onDeviceAdded(List<CMCCDevice> devices) {
        // TODO  Device 持久化入库
        // 测试数据
        String testData = "{\n" +
                "\t\"id\": null,\n" +
                "\t\"fsuId\": \"FSU123456\",\n" +
                "\t\"fsuName\": \"xsx测试\",\n" +
                "\t\"fsuPort\": null,\n" +
                "\t\"graphId\": null,\n" +
                "\t\"ipAddress\": \"*************\",\n" +
                "\t\"mac\": \"00:1A:2B:3C:4D:5E\",\n" +
                "\t\"version\": \"v1.0.3\",\n" +
                "\t\"username\": \"admin\",\n" +
                "\t\"password\": \"pass1234\",\n" +
                "\t\"vendor\": null,\n" +
                "\t\"model\": null,\n" +
                "\t\"regionId\": null,\n" +
                "\t\"createTime\": \"2025-07-02T09:36:53.352438\",\n" +
                "\t\"updateTime\": \"2025-07-02T09:36:53.357425\",\n" +
                "\t\"firmwareVersion\": null,\n" +
                "\t\"softwareVersion\": null,\n" +
                "\t\"firstLoginDate\": null,\n" +
                "\t\"lastLoginDate\": null,\n" +
                "\t\"lastOtaDate\": null,\n" +
                "\t\"lastSyncFactoryDate\": null,\n" +
                "\t\"lastSyncSchemeDate\": null,\n" +
                "\t\"lastSuReadyDate\": null,\n" +
                "\t\"ftpType\": 0,\n" +
                "\t\"ftpPort\": null,\n" +
                "\t\"ftpUserName\": null,\n" +
                "\t\"ftpPassword\": null,\n" +
                "\t\"deviceList\": [\n" +
                "\t\t{\n" +
                "\t\t\t\"id\": null,\n" +
                "\t\t\t\"fsuId\": \"FSU123456\",\n" +
                "\t\t\t\"deviceId\": \"DEV001\",\n" +
                "\t\t\t\"deviceName\": \"Device 1\",\n" +
                "\t\t\t\"siteName\": \"Site A\",\n" +
                "\t\t\t\"roomName\": \"Room 101\",\n" +
                "\t\t\t\"deviceType\": \"Sensor\",\n" +
                "\t\t\t\"deviceSubType\": \"Temperature\",\n" +
                "\t\t\t\"model\": \"TEMP-100\",\n" +
                "\t\t\t\"brand\": \"BrandX\",\n" +
                "\t\t\t\"ratedCapacity\": 100.0,\n" +
                "\t\t\t\"version\": \"1.0\",\n" +
                "\t\t\t\"beginRunTime\": \"2025-07-01T00:00:00\",\n" +
                "\t\t\t\"devDescribe\": \"Temperature sensor in main hall\",\n" +
                "\t\t\t\"description\": \"Monitors ambient temperature\",\n" +
                "\t\t\t\"signalList\": [\n" +
                "\t\t\t\t{\n" +
                "\t\t\t\t\t\"id\": null,\n" +
                "\t\t\t\t\t\"fsuId\": \"FSU123456\",\n" +
                "\t\t\t\t\t\"deviceId\": \"DEV001\",\n" +
                "\t\t\t\t\t\"spId\": \"SIG001\",\n" +
                "\t\t\t\t\t\"spName\": \"Temperature Reading\",\n" +
                "\t\t\t\t\t\"spType\": \"AI\",\n" +
                "\t\t\t\t\t\"alarmMeanings\": \"High temperature alert\",\n" +
                "\t\t\t\t\t\"normalMeanings\": \"Normal temperature\",\n" +
                "\t\t\t\t\t\"unit\": \"Celsius\",\n" +
                "\t\t\t\t\t\"nmAlarmId\": \"ALARM001\",\n" +
                "\t\t\t\t\t\"alarmLevel\": 2,\n" +
                "\t\t\t\t\t\"deviceHlType\": \"TEMP\",\n" +
                "\t\t\t\t\t\"meanings\": {\n" +
                "\t\t\t\t\t\t\"0\": \"Normal\",\n" +
                "\t\t\t\t\t\t\"1\": \"Warning\"\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"operateType\": 1,\n" +
                "\t\t\t\t\t\"visible\": true\n" +
                "\t\t\t\t}\n" +
                "\t\t\t],\n" +
                "\t\t\t\"alarmList\": [\n" +
                "\t\t\t\t{\n" +
                "\t\t\t\t\t\"fsuId\": \"FSU123456\",\n" +
                "\t\t\t\t\t\"deviceId\": \"DEV001\",\n" +
                "\t\t\t\t\t\"spId\": \"ALARM001\",\n" +
                "\t\t\t\t\t\"alarmLevel\": 2,\n" +
                "\t\t\t\t\t\"alarmName\": \"High Temperature Alarm\",\n" +
                "\t\t\t\t\t\"unit\": \"Celsius\",\n" +
                "\t\t\t\t\t\"operateType\": 1\n" +
                "\t\t\t\t}\n" +
                "\t\t\t],\n" +
                "\t\t\t\"controlList\": [\n" +
                "\t\t\t\t{\n" +
                "\t\t\t\t\t\"fsuId\": \"FSU123456\",\n" +
                "\t\t\t\t\t\"deviceId\": \"DEV001\",\n" +
                "\t\t\t\t\t\"spId\": \"CTRL001\",\n" +
                "\t\t\t\t\t\"controlName\": \"Cooling Control\",\n" +
                "\t\t\t\t\t\"controlMeanings\": \"Activate cooling\",\n" +
                "\t\t\t\t\t\"alarmMeanings\": 1,\n" +
                "\t\t\t\t\t\"spType\": \"DO\",\n" +
                "\t\t\t\t\t\"maxValue\": 1.0,\n" +
                "\t\t\t\t\t\"minValue\": 0.0,\n" +
                "\t\t\t\t\t\"operateType\": 1\n" +
                "\t\t\t\t}\n" +
                "\t\t\t]\n" +
                "\t\t},\n" +
                "\t\t{\n" +
                "\t\t\t\"id\": null,\n" +
                "\t\t\t\"fsuId\": \"FSU123456\",\n" +
                "\t\t\t\"deviceId\": \"DEV002\",\n" +
                "\t\t\t\"deviceName\": \"Device 2\",\n" +
                "\t\t\t\"siteName\": \"Site A\",\n" +
                "\t\t\t\"roomName\": \"Room 102\",\n" +
                "\t\t\t\"deviceType\": \"Sensor\",\n" +
                "\t\t\t\"deviceSubType\": \"Humidity\",\n" +
                "\t\t\t\"model\": \"HUM-200\",\n" +
                "\t\t\t\"brand\": \"BrandY\",\n" +
                "\t\t\t\"ratedCapacity\": 50.0,\n" +
                "\t\t\t\"version\": \"1.1\",\n" +
                "\t\t\t\"beginRunTime\": \"2025-07-01T00:00:00\",\n" +
                "\t\t\t\"devDescribe\": \"Humidity sensor in storage\",\n" +
                "\t\t\t\"description\": \"Monitors humidity levels\",\n" +
                "\t\t\t\"signalList\": [\n" +
                "\t\t\t\t{\n" +
                "\t\t\t\t\t\"id\": null,\n" +
                "\t\t\t\t\t\"fsuId\": \"FSU123456\",\n" +
                "\t\t\t\t\t\"deviceId\": \"DEV002\",\n" +
                "\t\t\t\t\t\"spId\": \"SIG002\",\n" +
                "\t\t\t\t\t\"spName\": \"Humidity Reading\",\n" +
                "\t\t\t\t\t\"spType\": \"AI\",\n" +
                "\t\t\t\t\t\"alarmMeanings\": \"High humidity alert\",\n" +
                "\t\t\t\t\t\"normalMeanings\": \"Normal humidity\",\n" +
                "\t\t\t\t\t\"unit\": \"Percentage\",\n" +
                "\t\t\t\t\t\"nmAlarmId\": \"ALARM002\",\n" +
                "\t\t\t\t\t\"alarmLevel\": 3,\n" +
                "\t\t\t\t\t\"deviceHlType\": \"HUM\",\n" +
                "\t\t\t\t\t\"meanings\": {\n" +
                "\t\t\t\t\t\t\"0\": \"Normal\",\n" +
                "\t\t\t\t\t\t\"1\": \"High\"\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"operateType\": 1,\n" +
                "\t\t\t\t\t\"visible\": true\n" +
                "\t\t\t\t}\n" +
                "\t\t\t],\n" +
                "\t\t\t\"alarmList\": [\n" +
                "\t\t\t\t{\n" +
                "\t\t\t\t\t\"fsuId\": \"FSU123456\",\n" +
                "\t\t\t\t\t\"deviceId\": \"DEV002\",\n" +
                "\t\t\t\t\t\"spId\": \"ALARM002\",\n" +
                "\t\t\t\t\t\"alarmLevel\": 3,\n" +
                "\t\t\t\t\t\"alarmName\": \"High Humidity Alarm\",\n" +
                "\t\t\t\t\t\"unit\": \"Percentage\",\n" +
                "\t\t\t\t\t\"operateType\": 1\n" +
                "\t\t\t\t}\n" +
                "\t\t\t],\n" +
                "\t\t\t\"controlList\": [\n" +
                "\t\t\t\t{\n" +
                "\t\t\t\t\t\"fsuId\": \"FSU123456\",\n" +
                "\t\t\t\t\t\"deviceId\": \"DEV002\",\n" +
                "\t\t\t\t\t\"spId\": \"CTRL002\",\n" +
                "\t\t\t\t\t\"controlName\": \"Dehumidifier Control\",\n" +
                "\t\t\t\t\t\"controlMeanings\": \"Activate dehumidifier\",\n" +
                "\t\t\t\t\t\"alarmMeanings\": 1,\n" +
                "\t\t\t\t\t\"spType\": \"DO\",\n" +
                "\t\t\t\t\t\"maxValue\": 1.0,\n" +
                "\t\t\t\t\t\"minValue\": 0.0,\n" +
                "\t\t\t\t\t\"operateType\": 1\n" +
                "\t\t\t\t}\n" +
                "\t\t\t]\n" +
                "\t\t},\n" +
                "\t\t{\n" +
                "\t\t\t\"id\": null,\n" +
                "\t\t\t\"fsuId\": \"FSU123456\",\n" +
                "\t\t\t\"deviceId\": \"DEV003\",\n" +
                "\t\t\t\"deviceName\": \"Device 3\",\n" +
                "\t\t\t\"siteName\": \"Site A\",\n" +
                "\t\t\t\"roomName\": \"Room 103\",\n" +
                "\t\t\t\"deviceType\": \"Actuator\",\n" +
                "\t\t\t\"deviceSubType\": \"Light\",\n" +
                "\t\t\t\"model\": \"LIGHT-300\",\n" +
                "\t\t\t\"brand\": \"BrandZ\",\n" +
                "\t\t\t\"ratedCapacity\": 200.0,\n" +
                "\t\t\t\"version\": \"1.2\",\n" +
                "\t\t\t\"beginRunTime\": \"2025-07-01T00:00:00\",\n" +
                "\t\t\t\"devDescribe\": \"Lighting control in office\",\n" +
                "\t\t\t\"description\": \"Controls office lighting\",\n" +
                "\t\t\t\"signalList\": [\n" +
                "\t\t\t\t{\n" +
                "\t\t\t\t\t\"id\": null,\n" +
                "\t\t\t\t\t\"fsuId\": \"FSU123456\",\n" +
                "\t\t\t\t\t\"deviceId\": \"DEV003\",\n" +
                "\t\t\t\t\t\"spId\": \"SIG003\",\n" +
                "\t\t\t\t\t\"spName\": \"Light Status\",\n" +
                "\t\t\t\t\t\"spType\": \"DI\",\n" +
                "\t\t\t\t\t\"alarmMeanings\": \"Light failure\",\n" +
                "\t\t\t\t\t\"normalMeanings\": \"Light operational\",\n" +
                "\t\t\t\t\t\"unit\": \"Status\",\n" +
                "\t\t\t\t\t\"nmAlarmId\": \"ALARM003\",\n" +
                "\t\t\t\t\t\"alarmLevel\": 1,\n" +
                "\t\t\t\t\t\"deviceHlType\": \"LIGHT\",\n" +
                "\t\t\t\t\t\"meanings\": {\n" +
                "\t\t\t\t\t\t\"0\": \"Off\",\n" +
                "\t\t\t\t\t\t\"1\": \"On\"\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"operateType\": 1,\n" +
                "\t\t\t\t\t\"visible\": true\n" +
                "\t\t\t\t}\n" +
                "\t\t\t],\n" +
                "\t\t\t\"alarmList\": [\n" +
                "\t\t\t\t{\n" +
                "\t\t\t\t\t\"fsuId\": \"FSU123456\",\n" +
                "\t\t\t\t\t\"deviceId\": \"DEV003\",\n" +
                "\t\t\t\t\t\"spId\": \"ALARM003\",\n" +
                "\t\t\t\t\t\"alarmLevel\": 1,\n" +
                "\t\t\t\t\t\"alarmName\": \"Light Failure Alarm\",\n" +
                "\t\t\t\t\t\"unit\": \"Status\",\n" +
                "\t\t\t\t\t\"operateType\": 1\n" +
                "\t\t\t\t}\n" +
                "\t\t\t],\n" +
                "\t\t\t\"controlList\": [\n" +
                "\t\t\t\t{\n" +
                "\t\t\t\t\t\"fsuId\": \"FSU123456\",\n" +
                "\t\t\t\t\t\"deviceId\": \"DEV003\",\n" +
                "\t\t\t\t\t\"spId\": \"CTRL003\",\n" +
                "\t\t\t\t\t\"controlName\": \"Light Switch\",\n" +
                "\t\t\t\t\t\"controlMeanings\": \"Toggle light\",\n" +
                "\t\t\t\t\t\"alarmMeanings\": 1,\n" +
                "\t\t\t\t\t\"spType\": \"DO\",\n" +
                "\t\t\t\t\t\"maxValue\": 1.0,\n" +
                "\t\t\t\t\t\"minValue\": 0.0,\n" +
                "\t\t\t\t\t\"operateType\": 1\n" +
                "\t\t\t\t}\n" +
                "\t\t\t]\n" +
                "\t\t}\n" +
                "\t]\n" +
                "}";
        // xsx 通知hub
        CMCCFsu cmccFsu = JSONUtil.toBean(testData, CMCCFsu.class);
        devices = cmccFsu.getDeviceList();
        List<ForeignDeviceConfigChange> foreignDeviceConfigChangeList = new ArrayList<>();
        devices.forEach(e->{
            ForeignDeviceConfigChange foreignDeviceConfigChange = e.toForeignDeviceConfigChange();
            foreignDeviceConfigChange.setEventType(LifeCycleEventType.CREATE);
            foreignDeviceConfigChangeList.add(foreignDeviceConfigChange);
        });
        LifeCycleEvent lifeCycleEvent = new LifeCycleEvent();
        lifeCycleEvent.setEventType(LifeCycleEventType.CREATE);
        lifeCycleEvent.setPluginInstanceId(pluginInstanceId);
        lifeCycleEvent.setForeignGatewayId(fsuId);
        lifeCycleEvent.setThingType(ThingType.DEVICE);
        lifeCycleEvent.setForeignConfigChange(foreignDeviceConfigChangeList);
        pipeline.tell(lifeCycleEvent, ActorRef.noSender());
    }

    /**
     * 配置上送，设备被删除，同时处理设备信号
     *
     * @param devices
     */
    private void onDeviceDeleted(List<CMCCDevice> devices) {
        // TODO  Device 持久化入库
    }

    /**
     * 配置上送，设备信息被修改
     *
     * @param devices
     */
    private void onDeviceModify(List<CMCCDevice> devices) {
        // TODO  Device 持久化入库
    }


}
