package com.siteweb.tcs.south.cmcc.web.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.south.cmcc.dal.entity.DictionaryItem;
import com.siteweb.tcs.south.cmcc.dal.mapper.DictionaryItemMapper;
import com.siteweb.tcs.south.cmcc.web.service.IDictionaryItemService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 字典项Service实现类
 */
@Service
public class DictionaryItemServiceImpl extends ServiceImpl<DictionaryItemMapper, DictionaryItem> implements IDictionaryItemService {

    @Resource
    private DictionaryItemMapper dictionaryItemMapper;

    @Override
    public List<DictionaryItem> listByCategoryId(Integer categoryId) {
        return dictionaryItemMapper.selectByCategoryId(categoryId);
    }

    @Override
    public List<DictionaryItem> listByParentCategoryId(Integer parentCategoryId) {
        return dictionaryItemMapper.selectByParentCategoryId(parentCategoryId);
    }

    @Override
    public List<DictionaryItem> listByEnable(Integer enable) {
        return dictionaryItemMapper.selectByEnable(enable);
    }

    @Override
    public List<DictionaryItem> listByIsSystem(Integer isSystem) {
        return dictionaryItemMapper.selectByIsSystem(isSystem);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDictionaryItem(DictionaryItem dictionaryItem) {
        return save(dictionaryItem);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDictionaryItem(DictionaryItem dictionaryItem) {
        return updateById(dictionaryItem);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDictionaryItem(Integer id) {
        return removeById(id);
    }

    @Override
    public List<DictionaryItem> getAlarmTypesByDeviceType(Integer deviceTypeItemId) {
        return dictionaryItemMapper.selectAlarmTypesByDeviceType(deviceTypeItemId);
    }

    @Override
    public List<DictionaryItem> getAllDeviceTypes() {
        return dictionaryItemMapper.selectAllDeviceTypes();
    }
} 