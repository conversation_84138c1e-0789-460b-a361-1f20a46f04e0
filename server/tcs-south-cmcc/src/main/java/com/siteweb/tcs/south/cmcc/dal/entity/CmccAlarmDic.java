package com.siteweb.tcs.south.cmcc.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * CMCC告警字典实体类
 * 对应表：cmcc_alarm_dic
 */
@Data
@TableName("cmcc_alarm_dic")
public class CmccAlarmDic implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 标准版本
     */
    @TableField("StandardVersion")
    private Integer standardVersion;

    /**
     * 告警标准ID
     */
    @TableField("AlarmStandardId")
    private String alarmStandardId;

    /**
     * 设备类型
     */
    @TableField("DeviceType")
    private Integer deviceType;

    /**
     * 告警逻辑类别
     */
    @TableField("AlarmLogicClass")
    private Integer alarmLogicClass;

    /**
     * 告警逻辑子类别
     */
    @TableField("AlarmLogicSubclass")
    private String alarmLogicSubclass;

    /**
     * 告警标准名称
     */
    @TableField("AlarmStandardName")
    private String alarmStandardName;

    /**
     * 含义
     */
    @TableField("Meaning")
    private String meaning;

    /**
     * 通信楼宇告警级别
     */
    @TableField("CommunicationBuildingAlarmLevel")
    private Integer communicationBuildingAlarmLevel;

    /**
     * 传输节点告警级别
     */
    @TableField("TransmissionNodeAlarmLevel")
    private Integer transmissionNodeAlarmLevel;

    /**
     * 通信基站告警级别
     */
    @TableField("CommunicationBaseStationAlarmLevel")
    private Integer communicationBaseStationAlarmLevel;

    /**
     * IDC告警级别
     */
    @TableField("IDCAlarmLevel")
    private Integer idcAlarmLevel;

    /**
     * 描述
     */
    @TableField("Description")
    private String description;

    /**
     * 扩展字段
     */
    @TableField("ExtendFiled")
    private String extendFiled;
} 