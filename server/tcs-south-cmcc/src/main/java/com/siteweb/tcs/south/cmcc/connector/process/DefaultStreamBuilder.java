package com.siteweb.tcs.south.cmcc.connector.process;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.siteweb.stream.core.entity.StreamGraph;
import org.apache.commons.lang.math.RandomUtils;
import java.security.InvalidParameterException;
/**
 * <AUTHOR> (2025-05-14)
 **/
public class DefaultStreamBuilder {
    private static final ObjectMapper objectMapper = new ObjectMapper();
    public static StreamGraph buildDefault(String fsuId) {
        var json = GRAPH_TEMPLATE
                .replace("{FSU:ID}", fsuId);
        try {
            return objectMapper.readValue(json, StreamGraph.class);
        } catch (JsonProcessingException e) {
            throw new InvalidParameterException();
        }
    }
    private static final String GRAPH_TEMPLATE =
            """
                    {
                        "streamGraphId": null,
                        "streamGraphName": "CMCC Fsu - DataFlow Graph",
                        "graphOption": {
                            "properties": {
                                "Author": "xsx",
                                "runMode": "RUN",
                                "fsuId": "{FSU:ID}"
                            }
                        },
                        "flows": [
                            {
                                "streamFlowId": 1000000,
                                "streamFlowName": "Get Data Flow",
                                "streamFlowOption": null,
                                "nodes": [
                                    {
                                        "streamNodeId": 1000,
                                        "shapeType": "fixed-timer",
                                        "option": {
                                            "type": "TimerShapeOption",
                                            "version": null,
                                            "name": "",
                                            "bkColor": "",
                                            "iconColor": "",
                                            "x": 0,
                                            "y": 0,
                                            "icon": null,
                                            "comment": null,
                                            "enabled": true,
                                            "dynamicOutlets": [],
                                            "firstDelay": 0,
                                            "interval": 2000,
                                            "defaultState": true
                                        }
                                    },
                                    {
                                        "streamNodeId": 2000,
                                        "shapeType": "cmcc-get-data",
                                        "option": {
                                            "type": "GetDataShapeOption",
                                            "version": null,
                                            "name": "",
                                            "bkColor": "",
                                            "iconColor": "",
                                            "x": 0,
                                            "y": 0,
                                            "icon": null,
                                            "comment": null,
                                            "enabled": true,
                                            "dynamicOutlets": []
                                        }
                                    },
                                    {
                                        "streamNodeId": 3000,
                                        "shapeType": "http-request",
                                        "option": {
                                            "type": "HTTPRequestShapeOption",
                                            "version": null,
                                            "name": "",
                                            "bkColor": "",
                                            "iconColor": "",
                                            "x": 0,
                                            "y": 0,
                                            "icon": null,
                                            "comment": null,
                                            "enabled": true,
                                            "dynamicOutlets": [],
                                            "serveUrl": "",
                                            "connectTimeout": 10000,
                                            "writeTimeout": 5000,
                                            "header": null
                                        }
                                    }
                                ],
                                "links": [
                                    {
                                        "streamLinkId": 4129218799920347136,
                                        "outNodeId": 1000,
                                        "outletId": 65536,
                                        "inletId": 65536,
                                        "inNodeId": 2000
                                    },
                                    {
                                        "streamLinkId": 6431946900754908160,
                                        "outNodeId": 2000,
                                        "outletId": 65536,
                                        "inletId": 65536,
                                        "inNodeId": 3000
                                    },
                                    {
                                        "streamLinkId": 307358236869909504,
                                        "outNodeId": 3000,
                                        "outletId": 65536,
                                        "inletId": 131072,
                                        "inNodeId": 2000
                                    }
                                ]
                            },
                            {
                                "streamFlowId": 2000000,
                                "streamFlowName": "自下往上流",
                                "streamFlowOption": null,
                                "nodes": [
                                    {
                                        "streamNodeId": 2400000,
                                        "shapeType": "cmcc-package-parser",
                                        "option": {
                                            "type": "MobileParserShapeOption",
                                            "version": null,
                                            "name": "",
                                            "bkColor": "",
                                            "iconColor": "",
                                            "x": 0,
                                            "y": 0,
                                            "icon": null,
                                            "comment": null,
                                            "enabled": true,
                                            "dynamicOutlets": [],
                                        }
                                    },
                                    {
                                        "streamNodeId": 2500000,
                                        "shapeType": "data-switch",
                                        "option": {
                                            "type": "SwitchShapeOption",
                                            "version": null,
                                            "name": "",
                                            "bkColor": "",
                                            "iconColor": "",
                                            "x": 0,
                                            "y": 0,
                                            "icon": null,
                                            "comment": null,
                                            "enabled": true,
                                            "dynamicOutlets": [
                                                65537,
                                                65538
                                            ],
                                            "property": {
                                                "scope": "msg",
                                                "property": "getPkType().getName()"
                                            },
                                            "branches": [
                                                {
                                                    "operator": "eq",
                                                    "property": {
                                                        "scope": "text",
                                                        "text": "SEND_DATA"
                                                    },
                                                    "output": 65537
                                                },
                                                {
                                                    "operator": "eq",
                                                    "property": {
                                                        "scope": "text",
                                                        "text": "SEND_ALARM"
                                                    },
                                                    "output": 65538
                                                }
                                            ],
                                            "matchAll": true
                                        }
                                    },
                                    {
                                        "streamNodeId": 2600000,
                                        "shapeType": "cmcc-send-data",
                                        "option": {
                                            "type": "SendDataShapeOption",
                                            "version": null,
                                            "name": "",
                                            "bkColor": "",
                                            "iconColor": "",
                                            "x": 0,
                                            "y": 0,
                                            "icon": null,
                                            "comment": null,
                                            "enabled": true,
                                            "dynamicOutlets": []
                                        }
                                    },
                                    {
                                        "streamNodeId": 2700000,
                                        "shapeType": "cmcc-send-alarm",
                                        "option": {
                                            "type": "SendAlarmShapeOption",
                                            "version": null,
                                            "name": "",
                                            "bkColor": "",
                                            "iconColor": "",
                                            "x": 0,
                                            "y": 0,
                                            "icon": null,
                                            "comment": null,
                                            "logging": {
                                                "logLevel": "INFO",
                                                "enableDetailedLogging": "false"
                                            }
                                            "enabled": true,
                                            "dynamicOutlets": []
                                        }
                                    }
                                ],
                                "links": [
                                    {
                                        "streamLinkId": 560526772782505984,
                                        "outNodeId": 2400000,
                                        "outletId": 65536,
                                        "inletId": 65536,
                                        "inNodeId": 2500000
                                    },
                                    {
                                        "streamLinkId": 2143772124606545920,
                                        "outNodeId": 2500000,
                                        "outletId": 65537,
                                        "inletId": 65536,
                                        "inNodeId": 2600000
                                    },
                                    {
                                        "streamLinkId": 6021212459204377654,
                                        "outNodeId": 2500000,
                                        "outletId": 65538,
                                        "inletId": 65536,
                                        "inNodeId": 2700000
                                    }
                                ]
                            }
                        ]
                    }""";
}