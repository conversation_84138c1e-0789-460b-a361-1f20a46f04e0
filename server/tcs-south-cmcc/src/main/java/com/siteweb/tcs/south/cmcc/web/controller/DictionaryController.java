package com.siteweb.tcs.south.cmcc.web.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.south.cmcc.dal.entity.DictionaryCategory;
import com.siteweb.tcs.south.cmcc.dal.entity.DictionaryItem;
import com.siteweb.tcs.south.cmcc.web.service.IDictionaryCategoryService;
import com.siteweb.tcs.south.cmcc.web.service.IDictionaryItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 字典管理Controller
 */
@Slf4j
@Api(tags = "字典管理")
@RestController
@RequestMapping("/dictionary")
public class DictionaryController {

    @Resource
    private IDictionaryCategoryService dictionaryCategoryService;

    @Resource
    private IDictionaryItemService dictionaryItemService;

    // ==================== 字典类别相关接口 ====================

    /**
     * 分页查询字典类别
     */
    @ApiOperation("分页查询字典类别")
    @GetMapping("/category/page")
    public ResponseEntity<ResponseResult> categoryPage(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("是否启用") @RequestParam(required = false) Integer enable) {
        
        log.info("分页查询字典类别: current={}, size={}, enable={}", current, size, enable);
        
        Page<DictionaryCategory> page = new Page<>(current, size);
        QueryWrapper<DictionaryCategory> queryWrapper = new QueryWrapper<>();
        
        if (enable != null) {
            queryWrapper.eq("Enable", enable);
        }
        
        Page<DictionaryCategory> result = dictionaryCategoryService.page(page, queryWrapper);
        return ResponseHelper.successful(result);
    }

    /**
     * 根据ID查询字典类别
     */
    @ApiOperation("根据ID查询字典类别")
    @GetMapping("/category/{id}")
    public ResponseEntity<ResponseResult> getCategoryById(@ApiParam("主键ID") @PathVariable Integer id) {
        log.info("根据ID查询字典类别: {}", id);
        DictionaryCategory result = dictionaryCategoryService.getById(id);
        return ResponseHelper.successful(result);
    }

    /**
     * 根据类别ID查询字典类别
     */
    @ApiOperation("根据类别ID查询字典类别")
    @GetMapping("/category/category-id/{categoryId}")
    public ResponseEntity<ResponseResult> getByCategoryId(@ApiParam("类别ID") @PathVariable Integer categoryId) {
        log.info("根据类别ID查询字典类别: {}", categoryId);
        DictionaryCategory result = dictionaryCategoryService.getByCategoryId(categoryId);
        return ResponseHelper.successful(result);
    }

    /**
     * 新增字典类别
     */
    @ApiOperation("新增字典类别")
    @PostMapping("/category")
    public ResponseEntity<ResponseResult> saveCategory(@RequestBody DictionaryCategory dictionaryCategory) {
        log.info("新增字典类别: {}", dictionaryCategory);
        boolean result = dictionaryCategoryService.saveDictionaryCategory(dictionaryCategory);
        return ResponseHelper.successful(result);
    }

    /**
     * 更新字典类别
     */
    @ApiOperation("更新字典类别")
    @PutMapping("/category")
    public ResponseEntity<ResponseResult> updateCategory(@RequestBody DictionaryCategory dictionaryCategory) {
        log.info("更新字典类别: {}", dictionaryCategory);
        boolean result = dictionaryCategoryService.updateDictionaryCategory(dictionaryCategory);
        return ResponseHelper.successful(result);
    }

    /**
     * 删除字典类别
     */
    @ApiOperation("删除字典类别")
    @DeleteMapping("/category/{id}")
    public ResponseEntity<ResponseResult> deleteCategory(@ApiParam("主键ID") @PathVariable Integer id) {
        log.info("删除字典类别: {}", id);
        boolean result = dictionaryCategoryService.deleteDictionaryCategory(id);
        return ResponseHelper.successful(result);
    }

    // ==================== 字典项相关接口 ====================

    /**
     * 分页查询字典项
     */
    @ApiOperation("分页查询字典项")
    @GetMapping("/item/page")
    public ResponseEntity<ResponseResult> itemPage(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("类别ID") @RequestParam(required = false) Integer categoryId,
            @ApiParam("是否启用") @RequestParam(required = false) Integer enable) {
        
        log.info("分页查询字典项: current={}, size={}, categoryId={}, enable={}", current, size, categoryId, enable);
        
        Page<DictionaryItem> page = new Page<>(current, size);
        QueryWrapper<DictionaryItem> queryWrapper = new QueryWrapper<>();
        
        if (categoryId != null) {
            queryWrapper.eq("CategoryId", categoryId);
        }
        if (enable != null) {
            queryWrapper.eq("Enable", enable);
        }
        
        Page<DictionaryItem> result = dictionaryItemService.page(page, queryWrapper);
        return ResponseHelper.successful(result);
    }

    /**
     * 根据ID查询字典项
     */
    @ApiOperation("根据ID查询字典项")
    @GetMapping("/item/{id}")
    public ResponseEntity<ResponseResult> getItemById(@ApiParam("主键ID") @PathVariable Integer id) {
        log.info("根据ID查询字典项: {}", id);
        DictionaryItem result = dictionaryItemService.getById(id);
        return ResponseHelper.successful(result);
    }

    /**
     * 根据类别ID查询字典项列表
     */
    @ApiOperation("根据类别ID查询字典项列表")
    @GetMapping("/item/category/{categoryId}")
    public ResponseEntity<ResponseResult> listItemByCategoryId(@ApiParam("类别ID") @PathVariable Integer categoryId) {
        log.info("根据类别ID查询字典项列表: {}", categoryId);
        List<DictionaryItem> result = dictionaryItemService.listByCategoryId(categoryId);
        return ResponseHelper.successful(result);
    }

    /**
     * 根据父类别ID查询字典项列表
     */
    @ApiOperation("根据父类别ID查询字典项列表")
    @GetMapping("/item/parent-category/{parentCategoryId}")
    public ResponseEntity<ResponseResult> listItemByParentCategoryId(@ApiParam("父类别ID") @PathVariable Integer parentCategoryId) {
        log.info("根据父类别ID查询字典项列表: {}", parentCategoryId);
        List<DictionaryItem> result = dictionaryItemService.listByParentCategoryId(parentCategoryId);
        return ResponseHelper.successful(result);
    }

    /**
     * 新增字典项
     */
    @ApiOperation("新增字典项")
    @PostMapping("/item")
    public ResponseEntity<ResponseResult> saveItem(@RequestBody DictionaryItem dictionaryItem) {
        log.info("新增字典项: {}", dictionaryItem);
        boolean result = dictionaryItemService.saveDictionaryItem(dictionaryItem);
        return ResponseHelper.successful(result);
    }

    /**
     * 更新字典项
     */
    @ApiOperation("更新字典项")
    @PutMapping("/item")
    public ResponseEntity<ResponseResult> updateItem(@RequestBody DictionaryItem dictionaryItem) {
        log.info("更新字典项: {}", dictionaryItem);
        boolean result = dictionaryItemService.updateDictionaryItem(dictionaryItem);
        return ResponseHelper.successful(result);
    }

    /**
     * 删除字典项
     */
    @ApiOperation("删除字典项")
    @DeleteMapping("/item/{id}")
    public ResponseEntity<ResponseResult> deleteItem(@ApiParam("主键ID") @PathVariable Integer id) {
        log.info("删除字典项: {}", id);
        boolean result = dictionaryItemService.deleteDictionaryItem(id);
        return ResponseHelper.successful(result);
    }

    /**
     * 批量删除字典项
     */
    @ApiOperation("批量删除字典项")
    @DeleteMapping("/item/batch")
    public ResponseEntity<ResponseResult> deleteItemBatch(@RequestBody List<Integer> ids) {
        log.info("批量删除字典项: {}", ids);
        boolean result = dictionaryItemService.removeByIds(ids);
        return ResponseHelper.successful(result);
    }

    /**
     * 批量删除字典项
     */
    @ApiOperation("根据设备类型获取该设备类型的所有告警类型")
    @GetMapping("/alarm-type-by-device-type")
    public ResponseEntity<ResponseResult> getAlarmTypesByDeviceType(@ApiParam("主键ID") Integer deviceTypeId) {
        List<DictionaryItem> alarmTypesByDeviceType = dictionaryItemService.getAlarmTypesByDeviceType(deviceTypeId);
        return ResponseHelper.successful(alarmTypesByDeviceType);
    }

    /**
     * 根据父类别ID查询字典项列表
     */
    @ApiOperation("获取所有设备类型")
    @GetMapping("/device-type")
    public ResponseEntity<ResponseResult> getDeviceType() {
        List<DictionaryItem> result = dictionaryItemService.listByCategoryId(2);
        return ResponseHelper.successful(result);
    }

    /**
     * 获取信号类型
     */
    @ApiOperation("获取信号类型")
    @GetMapping("/semaphore-type")
    public ResponseEntity<ResponseResult> getSemaphoreType() {
        List<DictionaryItem> result = dictionaryItemService.listByCategoryId(6);
        return ResponseHelper.successful(result);
    }

    /**
     * 获取告警级别
     */
    @ApiOperation("获告警级别")
    @GetMapping("/event-severity")
    public ResponseEntity<ResponseResult> getEventSeverity() {
        List<DictionaryItem> result = dictionaryItemService.listByCategoryId(7);
        return ResponseHelper.successful(result);
    }
} 