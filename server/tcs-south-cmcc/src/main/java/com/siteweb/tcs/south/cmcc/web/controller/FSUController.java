package com.siteweb.tcs.south.cmcc.web.controller;

/**
 * <AUTHOR> (2025-05-21)
 **/

import com.siteweb.stream.core.provider.StreamGraphProvider;
import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventType;
import com.siteweb.tcs.south.cmcc.connector.letter.FSULifeCycleEvent;
import com.siteweb.tcs.south.cmcc.connector.process.DefaultStreamBuilder;

import com.siteweb.tcs.south.cmcc.dal.entity.CMCCFsu;
import com.siteweb.tcs.south.cmcc.dal.provider.FSUProvider;
import com.siteweb.tcs.south.cmcc.dal.provider.PendingFSUProvider;
import com.siteweb.tcs.south.cmcc.web.dto.CreateCmccFSUDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("api/cmcc/2016/fsu/")
public class FSUController {

    @Autowired
    private PendingFSUProvider pendingFSUProvider;

    @Autowired
    private FSUProvider fsuProvider;


    @Autowired
    @Qualifier("cmcc-gateway-sharding")
    private ActorRef cmccFsuShading;

    @Autowired
    private StreamGraphProvider streamGraphProvider;

    /**
     * FSU向SC注册
     */
    @GetMapping(value = "pending/list", produces = MediaType.APPLICATION_XML_VALUE, consumes = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<ResponseResult> pendingList() {
        log.info("获取待注册FSU列表");
        var pendingList = pendingFSUProvider.getPendingList();
        // TODO 需要对pendingList的账号密码进行隐藏
        return ResponseHelper.successful(pendingList);
    }


    /**
     * FSU向SC注册
     */
    @GetMapping(value = "list", produces = MediaType.APPLICATION_XML_VALUE, consumes = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<ResponseResult> fsuList() {
        log.info("获取FSU列表");
        // TODO 需要对fsuList的账号密码进行隐藏
        return ResponseHelper.successful(fsuProvider.getAllFsu());
    }


    /**
     * FSU向SC注册
     */
    @PostMapping(value = "create", produces = MediaType.APPLICATION_XML_VALUE, consumes = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<ResponseResult> createFromPending(@RequestBody CreateCmccFSUDTO createCmccFSUDTO) {
        log.info("从待注册FSU创建FSU: {}", createCmccFSUDTO.getFsuId());
        // TODO 需要对fsuList的账号密码进行隐藏
        var pendingFsu = pendingFSUProvider.getFsuByFsuid(createCmccFSUDTO.getFsuId());
        if (pendingFsu == null) return ResponseHelper.failed("not found pending fsu");
        var fsu = CMCCFsu.fromPending(pendingFsu);
        fsu.setFsuName(createCmccFSUDTO.getFsuName());
        if (fsuProvider.saveFsu(fsu)) {
            // 创建FSU的流图
//            var streamGraph = DefaultStreamBuilder.buildDefault(fsu.getFsuId());
//            streamGraphProvider.createGraph(streamGraph);
//            fsu.setGraphId(streamGraph.getStreamGraphId());
            fsuProvider.updateFsu(fsu);
            // 发送FSULifeCycle至Proxy
            var event = new FSULifeCycleEvent();
            event.setFsuId(fsu.getFsuId());
            event.setEvent(LifeCycleEventType.CREATE);
            event.setFsuInfo(fsu);
            cmccFsuShading.tell(event, ActorRef.noSender());
        }
        return ResponseHelper.successful();
    }


    @PutMapping(value = "update", produces = MediaType.APPLICATION_XML_VALUE, consumes = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<ResponseResult> updateFsu(@RequestBody CMCCFsu fsu) {
        log.info("更新FSU: {}", fsu.getFsuId());
        if (fsuProvider.updateFsu(fsu)) {
            var event = new FSULifeCycleEvent();
            event.setFsuId(fsu.getFsuId());
            event.setEvent(LifeCycleEventType.FIELD_UPDATE);
            event.setFsuInfo(fsu);
            cmccFsuShading.tell(event, ActorRef.noSender());
            return ResponseHelper.successful();
        }
        return ResponseHelper.failed("update failure.");
    }


    @DeleteMapping(value = "delete/{fsuid}", produces = MediaType.APPLICATION_XML_VALUE, consumes = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<ResponseResult> deleteFsu(@PathVariable("fsuid") String fsuId) {
        log.info("删除FSU: {}", fsuId);
        var fsu= fsuProvider.getFsuByFsuid(fsuId);
        if (fsu == null) return  ResponseHelper.failed("not found fsu.");
        if (fsuProvider.deleteFsu(fsuId)) {
            streamGraphProvider.deleteGraph(fsu.getGraphId());
            var event = new FSULifeCycleEvent();
            event.setFsuId(fsuId);
            event.setEvent(LifeCycleEventType.DELETE);
            event.setFsuInfo(null);
            cmccFsuShading.tell(event, ActorRef.noSender());
            return ResponseHelper.successful();
        }
        return ResponseHelper.failed("delete fsu failure.");
    }


}
