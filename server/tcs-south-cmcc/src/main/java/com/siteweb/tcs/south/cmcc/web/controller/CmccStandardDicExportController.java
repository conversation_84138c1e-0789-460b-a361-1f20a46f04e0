package com.siteweb.tcs.south.cmcc.web.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.south.cmcc.web.service.ICmccStandardDicExportService;
import com.siteweb.tcs.south.cmcc.web.service.ICmccStandardVersionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * CMCC标准字典导出Controller
 */
@Slf4j
@Api(tags = "CMCC标准字典导出管理")
@RestController
@RequestMapping("/standard-dic-export")
public class CmccStandardDicExportController {

    @Autowired
    private ICmccStandardDicExportService cmccStandardDicExportService;

    @Autowired
    private ICmccStandardVersionService cmccStandardVersionService;

    /**
     * 导出XML字符串
     */
    @ApiOperation("导出XML字符串")
    @GetMapping("/xml-string")
    public ResponseEntity<ResponseResult> exportXmlString(
            @ApiParam("标准版本") @RequestParam(required = false) Integer standardVersion) {

        log.info("导出CMCC标准字典XML字符串: standardVersion={}", standardVersion);

        try {
            // 如果没有指定版本，使用默认版本（假设为1）
            if (standardVersion == null) {
                standardVersion = getDefaultStandardVersion();
            }

            String xmlContent = cmccStandardDicExportService.exportXmlStr(standardVersion);

            return ResponseHelper.successful(xmlContent);
        } catch (Exception e) {
            log.error("导出CMCC标准字典XML字符串失败", e);
            return ResponseHelper.failed("导出XML字符串失败: " + e.getMessage());
        }
    }

    /**
     * 生成并下载XML文件
     */
    @ApiOperation("生成并下载XML文件")
    @GetMapping("/download")
    public ResponseEntity<Resource> downloadXmlFile(
            @ApiParam("标准版本") @RequestParam(required = false) Integer standardVersion) {
        
        log.info("生成并下载CMCC标准字典XML文件: standardVersion={}", standardVersion);
        
        try {
            // 如果没有指定版本，使用默认版本
            if (standardVersion == null) {
                standardVersion = getDefaultStandardVersion();
            }
            
            // 生成XML文件
            String filePath = cmccStandardDicExportService.generateXmlFile(standardVersion);
            File file = new File(filePath);
            
            if (!file.exists()) {
                log.error("XML文件不存在: {}", filePath);
                return ResponseEntity.notFound().build();
            }
            
            // 构建文件名
            String fileName = "cmb_dictionary.xml";
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());
            
            // 创建文件资源
            Resource resource = new FileSystemResource(file);
            
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .header(HttpHeaders.CONTENT_DISPOSITION, 
                            "attachment; filename=\"" + fileName + "\"; filename*=UTF-8''" + encodedFileName)
                    .body(resource);
                    
        } catch (Exception e) {
            log.error("下载CMCC标准字典XML文件失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 生成XML文件（不下载）
     */
    @ApiOperation("生成XML文件")
    @PostMapping("/generate")
    public ResponseEntity<ResponseResult> generateXmlFile(
            @ApiParam("标准版本") @RequestParam(required = false) Integer standardVersion) {

        log.info("生成CMCC标准字典XML文件: standardVersion={}", standardVersion);

        try {
            // 如果没有指定版本，使用默认版本
            if (standardVersion == null) {
                standardVersion = getDefaultStandardVersion();
            }

            String filePath = cmccStandardDicExportService.generateXmlFile(standardVersion);

            return ResponseHelper.successful("XML文件生成成功: " + filePath);

        } catch (Exception e) {
            log.error("生成CMCC标准字典XML文件失败", e);
            return ResponseHelper.failed("生成XML文件失败: " + e.getMessage());
        }
    }

    /**
     * 检查XML文件是否存在
     */
    @ApiOperation("检查XML文件是否存在")
    @GetMapping("/exists")
    public ResponseEntity<ResponseResult> checkXmlFileExists(
            @ApiParam("标准版本") @RequestParam(required = false) Integer standardVersion) {

        log.info("检查CMCC标准字典XML文件是否存在: standardVersion={}", standardVersion);

        try {
            // 如果没有指定版本，使用默认版本
            if (standardVersion == null) {
                standardVersion = getDefaultStandardVersion();
            }

            String filePath = cmccStandardDicExportService.getXmlFilePath(standardVersion);
            File file = new File(filePath);

            return ResponseHelper.successful(file.exists());
        } catch (Exception e) {
            log.error("检查CMCC标准字典XML文件是否存在失败", e);
            return ResponseHelper.failed("检查文件失败: " + e.getMessage());
        }
    }

    /**
     * 获取默认标准版本
     * @return 默认标准版本
     */
    private Integer getDefaultStandardVersion() {
        // 尝试获取第一个可用的标准版本，如果没有则返回1
        return cmccStandardVersionService.list().stream()
                .findFirst()
                .map(version -> version.getIdcmccStandardVersion())
                .orElse(null);
    }
}
