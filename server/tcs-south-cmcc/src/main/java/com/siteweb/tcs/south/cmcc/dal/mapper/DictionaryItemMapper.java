package com.siteweb.tcs.south.cmcc.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.south.cmcc.dal.entity.DictionaryItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 字典项Mapper接口
 */
@Mapper
@Repository
public interface DictionaryItemMapper extends BaseMapper<DictionaryItem> {

    /**
     * 根据类别ID查询字典项列表
     * @param categoryId 类别ID
     * @return 字典项列表
     */
    List<DictionaryItem> selectByCategoryId(@Param("categoryId") Integer categoryId);

    /**
     * 根据父类别ID查询字典项列表
     * @param parentCategoryId 父类别ID
     * @return 字典项列表
     */
    List<DictionaryItem> selectByParentCategoryId(@Param("parentCategoryId") Integer parentCategoryId);

    /**
     * 根据启用状态查询字典项列表
     * @param enable 启用状态
     * @return 字典项列表
     */
    List<DictionaryItem> selectByEnable(@Param("enable") Integer enable);

    /**
     * 根据系统标识查询字典项列表
     * @param isSystem 是否系统项
     * @return 字典项列表
     */
    List<DictionaryItem> selectByIsSystem(@Param("isSystem") Integer isSystem);

    /**
     * 根据设备类型查询该设备类型下的所有告警类型
     * @param deviceTypeItemId 设备类型的ItemId
     * @return 告警类型列表
     */
    List<DictionaryItem> selectAlarmTypesByDeviceType(@Param("deviceTypeItemId") Integer deviceTypeItemId);

    /**
     * 查询所有设备类型
     * @return 设备类型列表
     */
    List<DictionaryItem> selectAllDeviceTypes();
} 