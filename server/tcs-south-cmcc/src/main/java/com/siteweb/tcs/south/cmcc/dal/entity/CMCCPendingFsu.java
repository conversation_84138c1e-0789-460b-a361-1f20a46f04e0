package com.siteweb.tcs.south.cmcc.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * CMCC待处理FSU信息实体
 * <AUTHOR> (2025-05-15)
 **/
@Data
@TableName("cmcc_pending_fsus")
public class CMCCPendingFsu implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * FSU ID号 (主键)
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String fsuId;


    /**
     * FSU的内网IP
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * FSU的内网IP
     */
    @TableField("fsu_port")
    private String fsuPort;

    /**
     * FSU的MAC地址
     */
    private String mac;

    /**
     * FSU版本号
     */
    private String version;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}
