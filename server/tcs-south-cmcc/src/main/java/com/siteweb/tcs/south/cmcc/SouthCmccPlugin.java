package com.siteweb.tcs.south.cmcc;

import cn.hutool.json.JSONUtil;
import com.siteweb.tcs.cmcc.common.message.MobileBMessage;
import com.siteweb.tcs.cmcc.common.message.MobileBRawMessage;
import com.siteweb.tcs.cmcc.common.protocol.PK_TypeName;
import com.siteweb.tcs.common.runtime.PluginContext;
import com.siteweb.tcs.common.runtime.SouthPlugin;
import com.siteweb.tcs.middleware.common.registry.ServiceRegistry;
import com.siteweb.tcs.south.cmcc.connector.ConnectorDataHolder;
import com.siteweb.tcs.south.cmcc.util.MobileXmlUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.pattern.Patterns;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import scala.concurrent.Future;
import scala.util.Try;

/**
 * 插件主类
 * <p>
 * 中国移动南向接入插件的入口类，负责插件的生命周期管理
 * </p>
 */
@Slf4j
public class SouthCmccPlugin extends SouthPlugin {

    @Autowired
    private ConnectorDataHolder dataHolder;

    @Value("${plugin.middleware.database.primary}")
    private String dbResourceId;

    @Autowired
    private MessageSource messageSource;

    @Autowired
    private ServiceRegistry serviceRegistry;

    // todo 测试代码，后续删掉 xsx
    @Autowired
    @Qualifier("cmcc-gateway-sharding")
    private ActorRef cmccFsuShading;

    public SouthCmccPlugin(PluginContext context) {
        super(context);
    }

    @Override
    public void onStart() {
        try {
            log.info("Starting SouthCmccPlugin");
            // 设置插件ID和创建根Actor
            dataHolder.setPluginId(this.getPluginId());
            // 初始化组件和服务
            String config = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                    "<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:ns=\"http://FSUService.chinamobile.com\" xmlns:soapenc=\"http://schemas.xmlsoap.org/soap/encoding/\">\n" +
                    "\t<soap:Body>\n" +
                    "\t\t<!-- SOAP Request -->\n" +
                    "\t\t<ns:invoke soap:encodingStyle=\"http://schemas.xmlsoap.org/soap/encoding/\">\n" +
                    "\t\t\t<xmlData>\n" +
                    "\t\t\t\t<Request>\n" +
                    "\t\t\t\t\t<PK_Type>\n" +
                    "\t\t\t\t\t\t<Name>SEND_DEV_CONF_DATA</Name>\n" +
                    "\t\t\t\t\t</PK_Type>\n" +
                    "\t\t\t\t\t<Info>\n" +
                    "\t\t\t\t\t\t<FSUID>\tFSU123456</FSUID>\n" +
                    "\t\t\t\t\t\t<Values>\n" +
                    "\t\t\t\t\t\t\t<Device DeviceID=\"060200001000001\" DeviceName=\"艾默生组合开关电源001/001\" SiteID=\"4301053000001\" RoomID=\"020200540\" RoomName=\"测试电力机房\" SiteName=\"长沙市一中\" DeviceType=\"6\" DeviceSubType=\"2\" Model=\"开关电源\" Brand=\"艾默生\" RatedCapacity=\"480\" Version=\"1\" BeginRunTime=\"2018-10-31 14:01:40\" DevDescribe=\"13\" ConfRemark=\"\">\n" +
                    "\t\t\t\t\t\t\t\t<Signals Count=\"54\">\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"3\" ID=\"006301\" SignalName=\"输入相电压Ua\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-00-006301\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"3\" ID=\"006302\" SignalName=\"输入相电压Ub\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-00-006302\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"3\" ID=\"006303\" SignalName=\"输入相电压Uc\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-00-006303\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"3\" ID=\"006304\" SignalName=\"输入相电流Ia\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-00-006304\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"3\" ID=\"006305\" SignalName=\"输入相电流Ib\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-00-006305\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"3\" ID=\"006306\" SignalName=\"输入相电流Ic\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-00-006306\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"3\" ID=\"006307\" SignalName=\"频率\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-00-006307\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"3\" ID=\"006308\" SignalName=\"直流输出电压\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-00-006308\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"3\" ID=\"006309\" SignalName=\"负载总电流\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-00-006309\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"3\" ID=\"006310\" SignalName=\"模块00电流\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-00-006310\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"3\" ID=\"006311\" SignalName=\"模块00温度\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-00-006311\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"3\" ID=\"006312\" SignalName=\"电池组00电流\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-00-006312\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"3\" ID=\"006313\" SignalName=\"输入00线电压Uab\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-00-006313\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"3\" ID=\"006314\" SignalName=\"输入00线电压Ubc\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-00-006314\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"3\" ID=\"006315\" SignalName=\"输入00线电压Uca\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-00-006315\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"3\" ID=\"006316\" SignalName=\"分路00电流\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-00-006316\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"3\" ID=\"006318\" SignalName=\"配置模块数量\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-00-006318\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"3\" ID=\"006320\" SignalName=\"蓄电池组数\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-00-006320\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"3\" ID=\"006321\" SignalName=\"电池组00额定容量\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-00-006321\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"3\" ID=\"006322\" SignalName=\"电池组额定总容量\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-00-006322\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"3\" ID=\"006323\" SignalName=\"电池组实际总容量\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-00-006323\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"3\" ID=\"006324\" SignalName=\"电池组剩余容量百分比\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-00-006324\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"4\" ID=\"006401\" SignalName=\"模块00开关状态\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-00-006401\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"1\" ID=\"006101\" SignalName=\"模块00开机\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-00-006101\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"1\" ID=\"006102\" SignalName=\"模块00关机\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-00-006102\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"1\" ID=\"006103\" SignalName=\"遥控均浮充\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-00-006103\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"2\" ID=\"006201\" SignalName=\"均充电压设定值\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-00-006201\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"2\" ID=\"006202\" SignalName=\"浮充电压设定值\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-00-006202\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"2\" ID=\"006207\" SignalName=\"充电限流设定\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-00-006207\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"0\" ID=\"006001\" SignalName=\"电池熔丝故障告警\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-10-006001\" AlarmLevel=\"1\" Threshold=\"1\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"0\" ID=\"006002\" SignalName=\"电池充电过流告警\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-10-006002\" AlarmLevel=\"3\" Threshold=\"1\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"0\" ID=\"006003\" SignalName=\"电池组温度过高告警\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-10-006003\" AlarmLevel=\"3\" Threshold=\"1\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"0\" ID=\"006004\" SignalName=\"电池放电不平衡告警\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-10-006004\" AlarmLevel=\"4\" Threshold=\"1\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"0\" ID=\"006010\" SignalName=\"负载电流过高告警\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-10-006010\" AlarmLevel=\"4\" Threshold=\"1\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"0\" ID=\"006011\" SignalName=\"负载熔丝故障告警\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-10-006011\" AlarmLevel=\"1\" Threshold=\"1\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"0\" ID=\"006012\" SignalName=\"输出电压过低告警\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-10-006012\" AlarmLevel=\"2\" Threshold=\"1\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"0\" ID=\"006013\" SignalName=\"输出电压过高告警\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-10-006013\" AlarmLevel=\"2\" Threshold=\"1\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"0\" ID=\"006014\" SignalName=\"直流屏通讯中断告警\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-10-006014\" AlarmLevel=\"2\" Threshold=\"1\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"0\" ID=\"006015\" SignalName=\"监控模块故障告警\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-10-006015\" AlarmLevel=\"2\" Threshold=\"1\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"0\" ID=\"006018\" SignalName=\"一级低压脱离告警\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-10-006018\" AlarmLevel=\"1\" Threshold=\"1\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"0\" ID=\"006019\" SignalName=\"二级低压脱离告警\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-10-006019\" AlarmLevel=\"1\" Threshold=\"1\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"0\" ID=\"006020\" SignalName=\"输出中断告警\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-10-006020\" AlarmLevel=\"1\" Threshold=\"1\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"0\" ID=\"006032\" SignalName=\"交流输入频率告警\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-10-006032\" AlarmLevel=\"3\" Threshold=\"1\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"0\" ID=\"006033\" SignalName=\"市电切换失败故障告警\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-10-006033\" AlarmLevel=\"3\" Threshold=\"1\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"0\" ID=\"006034\" SignalName=\"防雷器空开断开告警\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-10-006034\" AlarmLevel=\"3\" Threshold=\"1\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"0\" ID=\"006035\" SignalName=\"防雷器故障告警\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-10-006035\" AlarmLevel=\"3\" Threshold=\"1\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"0\" ID=\"006036\" SignalName=\"交流屏通讯中断告警\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-10-006036\" AlarmLevel=\"3\" Threshold=\"1\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"0\" ID=\"006037\" SignalName=\"交流输入00电压过高告警\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-10-006037\" AlarmLevel=\"3\" Threshold=\"1\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"0\" ID=\"006038\" SignalName=\"交流输入00电压过低告警\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-10-006038\" AlarmLevel=\"3\" Threshold=\"1\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"0\" ID=\"006040\" SignalName=\"整流模块故障告警\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-10-006040\" AlarmLevel=\"3\" Threshold=\"1\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"0\" ID=\"006041\" SignalName=\"整流模块00风扇告警\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-10-006041\" AlarmLevel=\"3\" Threshold=\"1\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"0\" ID=\"006042\" SignalName=\"整流模块过压关机告警\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-10-006042\" AlarmLevel=\"3\" Threshold=\"1\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"0\" ID=\"006043\" SignalName=\"整流模块过温告警\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-10-006043\" AlarmLevel=\"3\" Threshold=\"1\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"0\" ID=\"006044\" SignalName=\"整流模块通讯告警\" SignalNumber=\"000\" NMAlarmID=\"0500-002-006-10-006044\" AlarmLevel=\"3\" Threshold=\"1\"/>\n" +
                    "\t\t\t\t\t\t\t\t</Signals>\n" +
                    "\t\t\t\t\t\t\t</Device>\n" +
                    "\t\t\t\t\t\t\t<Device DeviceID=\"760300001000001\" DeviceName=\"vertiv_动环监控_001\" SiteID=\"4301053000001\" RoomID=\"020600120\" RoomName=\"测试电力机房\" SiteName=\"长沙市一中\" DeviceType=\"76\" DeviceSubType=\"3\" Model=\"动环监控\" Brand=\"vertiv\" RatedCapacity=\"1\" Version=\"1.0\" BeginRunTime=\"\" DevDescribe=\"\" ConfRemark=\"\">\n" +
                    "\t\t\t\t\t\t\t\t<Signals Count=\"6\">\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"3\" ID=\"076301\" SignalName=\"FSU硬盘占用率\" SignalNumber=\"000\" NMAlarmID=\"0500-002-076-00-076301\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"3\" ID=\"076302\" SignalName=\"内存占用率\" SignalNumber=\"000\" NMAlarmID=\"0500-002-076-00-076302\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"3\" ID=\"076303\" SignalName=\"CPU使用率\" SignalNumber=\"000\" NMAlarmID=\"0500-002-076-00-076303\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"1\" ID=\"076101\" SignalName=\"FSU复位\" SignalNumber=\"000\" NMAlarmID=\"0500-002-076-00-076101\" AlarmLevel=\"0\" Threshold=\"\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"0\" ID=\"076010\" SignalName=\"FSU通信中断\" SignalNumber=\"000\" NMAlarmID=\"0500-002-076-10-076010\" AlarmLevel=\"2\" Threshold=\"0\"/>\n" +
                    "\t\t\t\t\t\t\t\t\t<Signal Type=\"0\" ID=\"076501\" SignalName=\"艾默生组合开关电源001/001_开关电源通信状态告警\" SignalNumber=\"000\" NMAlarmID=\"0500-002-076-10-076501\" AlarmLevel=\"3\" Threshold=\"0\"/>\n" +
                    "\t\t\t\t\t\t\t\t</Signals>\n" +
                    "\t\t\t\t\t\t\t</Device>\n" +
                    "\t\t\t\t\t\t</Values>\n" +
                    "\t\t\t\t\t</Info>\n" +
                    "\t\t\t\t</Request>\n" +
                    "\t\t\t</xmlData>\n" +
                    "\t\t</ns:invoke>\n" +
                    "\t</soap:Body>\n" +
                    "</soap:Envelope>";
            PK_TypeName pkTypeName = MobileXmlUtil.parseXmlPkType(config);
            String fsuId = MobileXmlUtil.parseXmlObjectId(config);
            MobileBRawMessage message = new MobileBRawMessage(fsuId,pkTypeName, config);
            Future<Object> ask = Patterns.ask(cmccFsuShading, message, 5000);
            Object o = ask.value().get().get();
            log.info("SouthCmccPlugin started successfully");
        } catch (Exception e) {
            log.error("Error starting SouthCmccPlugin", e);
        }
    }

    @Override
    public void onStop() {
        log.info("Stopping SouthCmccPlugin");

        // 移除资源引用，避免影响其他插件
        try {
            serviceRegistry.cleanupPluginReferences("south-cmcc-plugin");
        } catch (Exception e) {
            log.warn("Failed to remove resource reference for {}: {}", dbResourceId, e.getMessage());
        }

        // Actor系统会处理停止Actor
    }
} 