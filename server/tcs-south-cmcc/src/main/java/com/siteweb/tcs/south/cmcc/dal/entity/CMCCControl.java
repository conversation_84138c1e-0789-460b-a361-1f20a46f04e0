package com.siteweb.tcs.south.cmcc.dal.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.tcs.hub.domain.letter.ForeignControlConfigChange;
import com.siteweb.tcs.hub.domain.letter.enums.ControlTypeEnum;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventType;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * CMCC控制点信息实体
 * <AUTHOR> (2025-05-16)
 **/
@Data
@NoArgsConstructor
@TableName("cmcc_controls")
public class CMCCControl implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * FSU ID
     */
    @TableField("fsu_id")
    private String fsuId;

    /**
     * 设备ID
     */
    @TableField("device_id")
    private String deviceId;

    /**
     * 控制点ID
     */
    @TableField("sp_id")
    private String spId;

    /**
     * 控制点名称
     */
    @TableField("control_name")
    private String controlName;

    /**
     * 控制含义
     */
    @TableField("control_meanings")
    private String controlMeanings;

    /**
     * 告警含义: DO表示动作/AO表示直接赋值
     */
    @TableField("alarm_meanings")
    private Integer alarmMeanings;

    /**
     * 控制点类型
     */
    @TableField("sp_type")
    private String spType;

    /**
     * 最大值
     */
    @TableField("max_value")
    private Double maxValue;

    /**
     * 最小值
     */
    @TableField("min_value")
    private Double minValue;

    /**
     * 操作类型
     */
    @TableField("operate_type")
    private Integer operateType;

    public ForeignControlConfigChange toForeignControlConfigChange(){
        ForeignControlConfigChange foreignControlConfigChange = new ForeignControlConfigChange();
        foreignControlConfigChange.setForeignControlId(spId);
        foreignControlConfigChange.setForeignControlName(controlName);
        foreignControlConfigChange.setForeignSignalId(spId);
        foreignControlConfigChange.setForeignControlMeaning(controlMeanings);
        foreignControlConfigChange.setForeignControlValue(alarmMeanings);
        //dic_item字典表4遥控,5遥调
        foreignControlConfigChange.setControlCategoryEnum(spType.equals("2") ? ControlTypeEnum.REMOTE_REGULATING.getValue() : ControlTypeEnum.REMOTE_CONTROL.getValue());
        foreignControlConfigChange.setMaxValue(99d);
        foreignControlConfigChange.setMinValue(0d);
        //todo xsx 2025-06-22 后续补生命周期和基类
//        foreignControlConfigChange.setBaseTypeId(StdSignalProvider.getStdSignalByDeviceIdAndSpId(deviceId, spId).getControlBaseTypeId());
//        foreignControlConfigChange.setEventType(ConfigToCTCCProvider.convertToLifeCycleEventType(this.operateType));
        foreignControlConfigChange.setEventType(LifeCycleEventType.CREATE);
        foreignControlConfigChange.setDescription(spId);
        return foreignControlConfigChange;

    }
}
