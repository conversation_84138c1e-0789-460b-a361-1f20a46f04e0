package com.siteweb.tcs.south.cmcc.web.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 告警状态信息VO
 * 
 * <AUTHOR> from CMCC B Interface Specification 5.6.2
 */
@Data
public class AlarmStateVO {

    /**
     * 告警唯一标识
     */
    private String alarmKey;

    /**
     * FSU ID
     */
    private String fsuId;

    /**
     * 告警状态
     */
    private String state;

    /**
     * 告警序号
     */
    private String serialNo;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 告警级别
     */
    private String alarmLevel;

    /**
     * 告警标志
     */
    private String alarmFlag;

    /**
     * 告警描述
     */
    private String alarmDesc;

    /**
     * 告警时间
     */
    private String alarmTime;

    /**
     * 告警开始时间
     */
    private LocalDateTime startTime;

    /**
     * 告警结束时间
     */
    private LocalDateTime endTime;

    /**
     * 告警持续时间（毫秒）
     */
    private Long durationMillis;
} 