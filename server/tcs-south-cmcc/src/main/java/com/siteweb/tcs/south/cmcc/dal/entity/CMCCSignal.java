package com.siteweb.tcs.south.cmcc.dal.entity;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.tcs.hub.domain.letter.ForeignSignalConfigChange;
import com.siteweb.tcs.hub.domain.letter.enums.DataTypeEnum;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventType;
import com.siteweb.tcs.hub.domain.letter.enums.SignalCategoryEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Map;

/**
 * CMCC信号点信息实体
 * <AUTHOR> (2025-05-16)
 **/
@Data
@NoArgsConstructor
@TableName("cmcc_signals")
public class CMCCSignal implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * FSU ID
     */
    @TableField("fsu_id")
    private String fsuId;

    /**
     * 设备ID
     */
    @TableField("device_id")
    private String deviceId;

    /**
     * 信号点ID
     */
    @TableField("sp_id")
    private String spId;

    /**
     * 信号点名称
     */
    @TableField("sp_name")
    private String spName;

    /**
     * 信号点类型
     */
    @TableField("sp_type")
    private String spType;

    /**
     * 告警含义
     */
    @TableField("alarm_meanings")
    private String alarmMeanings;

    /**
     * 正常含义
     */
    @TableField("normal_meanings")
    private String normalMeanings;

    /**
     * 单位
     */
    private String unit;

    /**
     * NM告警ID
     */
    @TableField("nm_alarm_id")
    private String nmAlarmId;

    /**
     * 告警级别
     */
    @TableField("alarm_level")
    private Integer alarmLevel;

    /**
     * 设备HL类型
     */
    @TableField("device_hl_type")
    private String deviceHlType;

    /**
     * 含义映射（非数据库字段）
     */
    @TableField(exist = false)
    private Map<Integer, String> meanings;

    /**
     * 操作类型（非数据库字段）
     */
    @TableField(exist = false)
    private Integer operateType;

    /**
     * 是否可见（非数据库字段）
     */
    @TableField(exist = false)
    private Boolean visible;

    public ForeignSignalConfigChange toForeignSignalConfigChange(){
        ForeignSignalConfigChange foreignSignalConfigChange = new ForeignSignalConfigChange();
        foreignSignalConfigChange.setForeignSignalId(spId);
        foreignSignalConfigChange.setSignalName(spName);
        foreignSignalConfigChange.setDataTypeEnum(DataTypeEnum.FLOAT);
        foreignSignalConfigChange.setUnit(unit);
        //todo 生命周期类型
//        foreignSignalConfigChange.setEventType(ConfigToCTCCProvider.convertToLifeCycleEventType(this.operateType));
        //todo 基类
//        foreignSignalConfigChange.setBaseTypeId(StdSignalProvider.getStdSignalByDeviceIdAndSpId(deviceId, spId).getSignalBaseTypeId());
        foreignSignalConfigChange.setEventType(LifeCycleEventType.CREATE);
        foreignSignalConfigChange.setDescription(spId);
        if (spType.equals("1")) {
            ForeignSignalConfigChange tempConfigChange = new ForeignSignalConfigChange();
            tempConfigChange.setForeignSignalMeaningList(new ArrayList<>());


            // 值为0：正常
            ForeignSignalConfigChange.ForeignSignalMeaning meaning = tempConfigChange.new ForeignSignalMeaning();
            meaning.setStateValue(0);
            meaning.setMeanings(normalMeanings);
            tempConfigChange.getForeignSignalMeaningList().add(meaning);

            // 枚举报警值
            if (ObjectUtil.isNotEmpty(alarmMeanings) && alarmMeanings.contains(":")) {
                String[] meanings = alarmMeanings.split(",");
                for (String mean : meanings) {
                    String[] meanArr = mean.split(":");
                    if (meanArr.length == 2) {
                        meaning = tempConfigChange.new ForeignSignalMeaning();
                        int value = Integer.parseInt(meanArr[0]);
                        String label = meanArr[1];
                        meaning.setStateValue(value);
                        meaning.setMeanings(label);
                        tempConfigChange.getForeignSignalMeaningList().add(meaning);
                    }
                }
            } else if (ObjectUtil.isNotEmpty(alarmMeanings)) {
                // 非枚举的报警值为1
                meaning = tempConfigChange.new ForeignSignalMeaning();
                meaning.setStateValue(1);
                meaning.setMeanings(alarmMeanings);
                tempConfigChange.getForeignSignalMeaningList().add(meaning);
            }


            // 设置到结果对象（你可以设置到某个字段）
            foreignSignalConfigChange.setForeignSignalMeaningList(tempConfigChange.getForeignSignalMeaningList());
        }


        return foreignSignalConfigChange;
    }
}
