package com.siteweb.tcs.south.cmcc.web.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.XmlUtil;
import com.siteweb.tcs.south.cmcc.dal.entity.CmccAlarmDic;
import com.siteweb.tcs.south.cmcc.dal.entity.CmccSignalDic;
import com.siteweb.tcs.south.cmcc.web.service.ICmccAlarmDicService;
import com.siteweb.tcs.south.cmcc.web.service.ICmccSignalDicService;
import com.siteweb.tcs.south.cmcc.web.service.ICmccStandardDicExportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CmccStandardDicExportServiceImpl implements ICmccStandardDicExportService {

    @Autowired
    private ICmccAlarmDicService cmccAlarmDicService;

    @Autowired
    private ICmccSignalDicService cmccSignalDicService;

    @Override
    public String exportXmlStr(Integer standardVersion) {
        Document document = XmlUtil.createXml();
        document.setXmlStandalone(false);
        Element standardDicElement = document.createElement("StandardDic");
        standardDicElement.setAttribute("StandardName", "CMCC标准化字典表");

        // 获取当前时间并格式化
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss");
        String currentTime = dateFormat.format(new Date());
        standardDicElement.setAttribute("CreateTime", currentTime);

        // 获取信号节点
        Element signalsElement = getSignalsElement(document, standardVersion);
        standardDicElement.appendChild(signalsElement);

        // 获取告警节点
        Element eventsElement = getEventsElement(document, standardVersion);
        standardDicElement.appendChild(eventsElement);

        // 获取控制节点
        Element controlsElement = getControlsElement(document, standardVersion);
        standardDicElement.appendChild(controlsElement);

        return XmlUtil.toStr(standardDicElement, true);
    }

    @Override
    public String generateXmlFile(Integer standardVersion) {
        try {
            String xmlContent = exportXmlStr(standardVersion);
            String filePath = getXmlFilePath(standardVersion);
            
            // 确保目录存在
            File file = new File(filePath);
            File parentDir = file.getParentFile();
            if (!parentDir.exists()) {
                parentDir.mkdirs();
            }
            
            // 写入文件
            try (FileWriter writer = new FileWriter(file, false)) {
                writer.write(xmlContent);
            }
            
            log.info("CMCC标准字典XML文件生成成功: {}", filePath);
            return filePath;
        } catch (IOException e) {
            log.error("生成CMCC标准字典XML文件失败", e);
            throw new RuntimeException("生成XML文件失败: " + e.getMessage());
        }
    }

    @Override
    public String getXmlFilePath(Integer standardVersion) {
        return Paths.get("plugins", "south-cmcc-plugin", "workspace", "standard-dic", "cmb_dictionary.xml")
                .toString();
    }

    private Element getSignalsElement(Document document, Integer standardVersion) {
        Element signals = document.createElement("Signals");
        signals.setAttribute("Name", "标准化信号");
        
        List<CmccSignalDic> signalDicList = cmccSignalDicService.listByStandardVersion(standardVersion)
                .stream()
                .filter(signal -> signal.getSemaphoreType() != null &&
                        (signal.getSemaphoreType() == 3 || signal.getSemaphoreType() == 4))
                .toList();;
        for (CmccSignalDic signalDic : signalDicList) {
            Element signalElement = document.createElement("Signal");
            signalElement.setAttribute("Name", signalDic.getStandardSignalName());
            signalElement.setAttribute("Unit", Convert.convert(String.class, signalDic.getUnit(), ""));
            signalElement.setAttribute("Code", signalDic.getSignalStandardId());
            signalElement.setAttribute("StoreInterval", ""); // CMCC字典中没有存储周期字段
            signalElement.setAttribute("Relativeval", ""); // CMCC字典中没有百分比阈值字段
            signalElement.setAttribute("Absoluteval", ""); // CMCC字典中没有绝对值阈值字段
            signals.appendChild(signalElement);
        }
        return signals;
    }

    private Element getEventsElement(Document document, Integer standardVersion) {
        Element events = document.createElement("Events");
        events.setAttribute("Name", "标准化事件");
        
        List<CmccAlarmDic> alarmDicList = cmccAlarmDicService.listByStandardVersion(standardVersion);
        for (CmccAlarmDic alarmDic : alarmDicList) {
            Element eventElement = document.createElement("Event");
            eventElement.setAttribute("Name", alarmDic.getAlarmStandardName());
            eventElement.setAttribute("Meaning", Convert.convert(String.class, alarmDic.getMeaning(), ""));
            eventElement.setAttribute("Code", alarmDic.getAlarmStandardId());
            
            // 设置各种告警级别 - 使用CMCC特有的告警级别字段
            eventElement.setAttribute("AlarmLevelOfTXJL", getUserGrade(alarmDic.getCommunicationBuildingAlarmLevel()));
            eventElement.setAttribute("AlarmLevelOfCSJD", getUserGrade(alarmDic.getTransmissionNodeAlarmLevel()));
            eventElement.setAttribute("AlarmLevelOfTXJZ", getUserGrade(alarmDic.getCommunicationBaseStationAlarmLevel()));
            eventElement.setAttribute("AlarmLevelOfIDC", getUserGrade(alarmDic.getIdcAlarmLevel()));

            eventElement.setAttribute("Threshold", "");
            eventElement.setAttribute("StartOper", "");
            eventElement.setAttribute("StartDelay", "");
            eventElement.setAttribute("EndDelay", "");
            eventElement.setAttribute("Hyteresis", "");
            events.appendChild(eventElement);
        }
        return events;
    }

    private Element getControlsElement(Document document, Integer standardVersion) {
        Element controls = document.createElement("Controls");
        controls.setAttribute("Name", "标准化控制");

        // 获取控制类型的信号字典数据 (semaphoreType为1遥控或2遥调)
        List<CmccSignalDic> controlSignalList = cmccSignalDicService.listByStandardVersion(standardVersion)
                .stream()
                .filter(signal -> signal.getSemaphoreType() != null &&
                        (signal.getSemaphoreType() == 1 || signal.getSemaphoreType() == 2))
                .toList();

        for (CmccSignalDic signalDic : controlSignalList) {
            Element controlElement = document.createElement("Control");
            controlElement.setAttribute("Name", signalDic.getStandardSignalName());
            controlElement.setAttribute("Unit", Convert.convert(String.class, signalDic.getUnit(), ""));
            controlElement.setAttribute("Code", signalDic.getSignalStandardId());
            controls.appendChild(controlElement);
        }
        return controls;
    }

    /**
     * 获取告警等级
     * 这个告警等级是与4互补的
     * @param alarmLevel 告警等级
     * @return 转换后的告警等级字符串
     */
    private String getUserGrade(Object alarmLevel) {
        if (Objects.isNull(alarmLevel) || CharSequenceUtil.isBlank(alarmLevel.toString())) {
            return "";
        }
        Integer level = Convert.convert(Integer.class, alarmLevel);
        return String.valueOf((4 - level));
    }
}
