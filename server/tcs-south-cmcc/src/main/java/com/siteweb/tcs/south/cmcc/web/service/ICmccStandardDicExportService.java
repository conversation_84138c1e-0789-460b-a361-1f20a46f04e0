package com.siteweb.tcs.south.cmcc.web.service;

/**
 * CMCC标准字典导出Service接口
 */
public interface ICmccStandardDicExportService {

    /**
     * 导出XML字符串
     * @param standardVersion 标准版本
     * @return XML字符串
     */
    String exportXmlStr(Integer standardVersion);

    /**
     * 生成XML文件并保存到指定位置
     * @param standardVersion 标准版本
     * @return 生成的文件路径
     */
    String generateXmlFile(Integer standardVersion);

    /**
     * 获取XML文件的完整路径
     * @param standardVersion 标准版本
     * @return 文件完整路径
     */
    String getXmlFilePath(Integer standardVersion);
}
