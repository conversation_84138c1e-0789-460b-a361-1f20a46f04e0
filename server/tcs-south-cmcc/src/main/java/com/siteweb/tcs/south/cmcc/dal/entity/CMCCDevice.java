package com.siteweb.tcs.south.cmcc.dal.entity;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.tcs.hub.domain.letter.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * CMCC设备信息实体
 * <AUTHOR> (2025-05-12)
 **/
@Data
@NoArgsConstructor
@Slf4j
@TableName("cmcc_devices")
public class CMCCDevice implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * FSU ID
     */
    @TableField("fsu_id")
    private String fsuId;

    /**
     * 设备ID
     */
    @TableField("device_id")
    private String deviceId;

    /**
     * 设备名称
     */
    @TableField("device_name")
    private String deviceName;

    /**
     * 站点名称
     */
    @TableField("site_name")
    private String siteName;

    /**
     * 机房名称
     */
    @TableField("room_name")
    private String roomName;

    /**
     * 设备类型
     */
    @TableField("device_type")
    private String deviceType;

    /**
     * 设备子类型
     */
    @TableField("device_sub_type")
    private String deviceSubType;

    /**
     * 型号
     */
    private String model;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 额定容量
     */
    @TableField("rated_capacity")
    private Float ratedCapacity;

    /**
     * 版本
     */
    private String version;

    /**
     * 开始运行时间
     */
    @TableField("begin_run_time")
    private String beginRunTime;

    /**
     * 设备安装位置描述
     */
    @TableField("dev_describe")
    private String devDescribe;

    /**
     * 设备描述
     */
    private String description;

    /**
     * 信号点列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<CMCCSignal> signalList;

    /**
     * 告警列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<CMCCAlarm> alarmList;


    /**
     * 控制点列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<CMCCControl> controlList;

    public ForeignDeviceConfigChange toForeignDeviceConfigChange(){
        ForeignDeviceConfigChange foreignDeviceConfigChange = new ForeignDeviceConfigChange();
        foreignDeviceConfigChange.setForeignDeviceId(deviceId);
        foreignDeviceConfigChange.setDeviceName(deviceName);
        foreignDeviceConfigChange.setForeignGatewayId(fsuId);
        foreignDeviceConfigChange.setEquipmentCategory(12);
        if(CollectionUtil.isNotEmpty(signalList)){
            List<ForeignSignalConfigChange> foreignSignalChangeList = new ArrayList<>();
            signalList.forEach(e->foreignSignalChangeList.add(e.toForeignSignalConfigChange()));
            foreignDeviceConfigChange.setForeignSignalConfigChangeList(foreignSignalChangeList);
        }
        if(CollectionUtil.isNotEmpty(alarmList)){
            List<ForeignAlarmConfigChange> foreignAlarmConfigChangeList = new ArrayList<>();
            alarmList.forEach(e->foreignAlarmConfigChangeList.add(e.toForeignAlarmConfigChange()));
            foreignDeviceConfigChange.setForeignAlarmConfigChangeList(foreignAlarmConfigChangeList);
        }
        if(CollectionUtil.isNotEmpty(controlList)){
            List<ForeignControlConfigChange> foreignControlConfigChangeList = new ArrayList<>();
            controlList.forEach(e->foreignControlConfigChangeList.add(e.toForeignControlConfigChange()));
            foreignDeviceConfigChange.setForeignControlConfigChangeList(foreignControlConfigChangeList);
        }
        return foreignDeviceConfigChange;
    }
}
