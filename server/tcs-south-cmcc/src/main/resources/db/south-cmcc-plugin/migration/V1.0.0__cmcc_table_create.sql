-- =====================================================
-- CMCC South Plugin Database Schema
-- Database Type: H2
-- Generated from Entity Classes
-- Version: 1.0
-- Author: TCS Team
-- =====================================================

-- Drop tables if they exist (for overwrite support)
DROP TABLE IF EXISTS cmcc_events;
DROP TABLE IF EXISTS cmcc_alarms;
DROP TABLE IF EXISTS cmcc_signals;
DROP TABLE IF EXISTS cmcc_controls;
DROP TABLE IF EXISTS cmcc_devices;
DROP TABLE IF EXISTS cmcc_pending_fsus;
DROP TABLE IF EXISTS cmcc_fsus;

-- =====================================================
-- Table: cmcc_fsus
-- Entity: CMCCFsu
-- Description: FSU信息主表
-- =====================================================
CREATE TABLE cmcc_fsus (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    fsu_id VARCHAR(100) NOT NULL COMMENT 'FSU ID号',
    fsu_name VARCHAR(100) COMMENT 'FSU 名称',
    fsu_port VARCHAR(16) COMMENT 'FSU 端口',
    graph_id BIGINT COMMENT '图形ID',
    ip_address VARCHAR(50) COMMENT 'FSU的内网IP',
    mac VARCHAR(50) COMMENT 'FSU的MAC地址',
    version VARCHAR(50) COMMENT 'FSU版本号',
    username VARCHAR(100) COMMENT '用户名',
    password VARCHAR(255) COMMENT '密码',
    vendor VARCHAR(100) COMMENT '厂商',
    model VARCHAR(100) COMMENT '型号',
    region_id INTEGER COMMENT '区域ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    firmware_version VARCHAR(50) COMMENT '固件版本',
    software_version VARCHAR(50) COMMENT '软件版本',
    first_login_date TIMESTAMP COMMENT '首次登录时间',
    last_login_date TIMESTAMP COMMENT '最后登录时间',
    last_ota_date TIMESTAMP COMMENT '最后OTA时间',
    last_sync_factory_date TIMESTAMP COMMENT '最后同步工厂设置时间',
    last_sync_scheme_date TIMESTAMP COMMENT '最后同步方案时间',
    last_su_ready_date TIMESTAMP COMMENT '最后SU就绪时间',
    ftp_type INTEGER DEFAULT 0 COMMENT 'FTP类型: 0=普通FTP, 1=SFTP',
    ftp_port VARCHAR(10) COMMENT 'FTP端口',
    ftp_user_name VARCHAR(100) COMMENT 'FTP用户名',
    ftp_password VARCHAR(255) COMMENT 'FTP密码',
    
    UNIQUE KEY uk_cmcc_fsus_fsu_id (fsu_id),
    INDEX idx_cmcc_fsus_ip_address (ip_address),
    INDEX idx_cmcc_fsus_region_id (region_id),
    INDEX idx_cmcc_fsus_create_time (create_time)
);

-- =====================================================
-- Table: cmcc_pending_fsus
-- Entity: CMCCPendingFsu
-- Description: 待处理FSU信息表
-- =====================================================
CREATE TABLE cmcc_pending_fsus (
    id VARCHAR(100) PRIMARY KEY COMMENT 'FSU ID号',
    ip_address VARCHAR(50) COMMENT 'FSU的内网IP',
    fsu_port VARCHAR(16) COMMENT 'FSU 端口',
    mac VARCHAR(50) COMMENT 'FSU的MAC地址',
    version VARCHAR(50) COMMENT 'FSU版本号',
    username VARCHAR(100) COMMENT '用户名',
    password VARCHAR(255) COMMENT '密码',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_cmcc_pending_fsus_ip_address (ip_address),
    INDEX idx_cmcc_pending_fsus_create_time (create_time)
);

-- =====================================================
-- Table: cmcc_devices
-- Entity: CMCCDevice  
-- Description: CMCC设备信息表
-- =====================================================
CREATE TABLE cmcc_devices (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    fsu_id VARCHAR(100) NOT NULL COMMENT 'FSU ID',
    device_id VARCHAR(100) NOT NULL COMMENT '设备ID',
    device_name VARCHAR(200) COMMENT '设备名称',
    site_name VARCHAR(200) COMMENT '站点名称',
    room_name VARCHAR(200) COMMENT '机房名称',
    device_type VARCHAR(100) COMMENT '设备类型',
    device_sub_type VARCHAR(100) COMMENT '设备子类型',
    model VARCHAR(100) COMMENT '型号',
    brand VARCHAR(100) COMMENT '品牌',
    rated_capacity FLOAT COMMENT '额定容量',
    version VARCHAR(50) COMMENT '版本',
    begin_run_time VARCHAR(50) COMMENT '开始运行时间',
    dev_describe VARCHAR(500) COMMENT '设备安装位置描述',
    description VARCHAR(1000) COMMENT '设备描述',
    
    UNIQUE KEY uk_cmcc_devices_fsu_device (fsu_id, device_id),
    INDEX idx_cmcc_devices_fsu_id (fsu_id),
    INDEX idx_cmcc_devices_device_type (device_type),
    INDEX idx_cmcc_devices_site_name (site_name)
);

-- =====================================================
-- Table: cmcc_signals
-- Entity: CMCCSignal
-- Description: CMCC信号点信息表
-- =====================================================
CREATE TABLE cmcc_signals (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    fsu_id VARCHAR(100) NOT NULL COMMENT 'FSU ID',
    device_id VARCHAR(100) NOT NULL COMMENT '设备ID',
    sp_id VARCHAR(100) NOT NULL COMMENT '信号点ID',
    sp_name VARCHAR(200) COMMENT '信号点名称',
    sp_type VARCHAR(50) COMMENT '信号点类型',
    alarm_meanings VARCHAR(500) COMMENT '告警含义',
    normal_meanings VARCHAR(500) COMMENT '正常含义',
    unit VARCHAR(50) COMMENT '单位',
    nm_alarm_id VARCHAR(100) COMMENT 'NM告警ID',
    alarm_level INTEGER COMMENT '告警级别',
    device_hl_type VARCHAR(100) COMMENT '设备HL类型',
    
    UNIQUE KEY uk_cmcc_signals_fsu_device_sp (fsu_id, device_id, sp_id),
    INDEX idx_cmcc_signals_fsu_id (fsu_id),
    INDEX idx_cmcc_signals_device_id (device_id),
    INDEX idx_cmcc_signals_sp_type (sp_type),
    INDEX idx_cmcc_signals_alarm_level (alarm_level)
);

-- =====================================================
-- Table: cmcc_controls
-- Entity: CMCCControl
-- Description: CMCC控制点信息表
-- =====================================================
CREATE TABLE cmcc_controls (
    fsu_id VARCHAR(100) NOT NULL COMMENT 'FSU ID',
    device_id VARCHAR(100) NOT NULL COMMENT '设备ID',
    sp_id VARCHAR(100) NOT NULL COMMENT '控制点ID',
    control_name VARCHAR(200) COMMENT '控制点名称',
    control_meanings VARCHAR(500) COMMENT '控制含义',
    alarm_meanings INTEGER COMMENT '告警含义: DO表示动作/AO表示直接赋值',
    sp_type VARCHAR(50) COMMENT '控制点类型',
    max_value DOUBLE COMMENT '最大值',
    min_value DOUBLE COMMENT '最小值',
    operate_type INTEGER NOT NULL COMMENT '操作类型',
    
    PRIMARY KEY (fsu_id, device_id, sp_id),
    INDEX idx_cmcc_controls_fsu_id (fsu_id),
    INDEX idx_cmcc_controls_device_id (device_id),
    INDEX idx_cmcc_controls_sp_type (sp_type)
);

-- =====================================================
-- Table: cmcc_alarms
-- Entity: CMCCAlarm
-- Description: CMCC告警信息表
-- =====================================================
CREATE TABLE cmcc_alarms (
    fsu_id VARCHAR(100) NOT NULL COMMENT 'FSU ID',
    device_id VARCHAR(100) NOT NULL COMMENT '设备ID',
    sp_id VARCHAR(100) NOT NULL COMMENT '告警点ID',
    alarm_level INTEGER COMMENT '告警级别: 0=正常数据, 1=一级告警, 2=二级告警, 3=三级告警, 4=四级告警, 5=操作事件, 6=无效数据',
    alarm_name VARCHAR(200) COMMENT '告警名称',
    unit VARCHAR(50) COMMENT '标识告警类别',
    operate_type INTEGER NOT NULL COMMENT '操作类型',
    
    PRIMARY KEY (fsu_id, device_id, sp_id),
    INDEX idx_cmcc_alarms_fsu_id (fsu_id),
    INDEX idx_cmcc_alarms_device_id (device_id),
    INDEX idx_cmcc_alarms_alarm_level (alarm_level)
);

-- =====================================================
-- Table: cmcc_events
-- Entity: CMCCEvent
-- Description: CMCC事件信息表
-- =====================================================
CREATE TABLE cmcc_events (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    fsu_id VARCHAR(100) NOT NULL COMMENT 'FSU ID',
    device_id VARCHAR(100) COMMENT '设备ID',
    event_type VARCHAR(100) NOT NULL COMMENT '事件类型',
    event_content TEXT COMMENT '事件内容',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_cmcc_events_fsu_id (fsu_id),
    INDEX idx_cmcc_events_device_id (device_id),
    INDEX idx_cmcc_events_event_type (event_type),
    INDEX idx_cmcc_events_create_time (create_time)
);

-- =====================================================
-- Add Foreign Key Constraints (Optional in H2)
-- 注意：根据项目需求决定是否启用外键约束
-- =====================================================

-- 如果需要启用外键约束，取消下面的注释
/*
-- cmcc_devices 引用 cmcc_fsus
ALTER TABLE cmcc_devices 
ADD CONSTRAINT fk_cmcc_devices_fsu 
FOREIGN KEY (fsu_id) REFERENCES cmcc_fsus(fsu_id) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- cmcc_signals 引用 cmcc_devices  
ALTER TABLE cmcc_signals 
ADD CONSTRAINT fk_cmcc_signals_device 
FOREIGN KEY (fsu_id, device_id) REFERENCES cmcc_devices(fsu_id, device_id) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- cmcc_controls 引用 cmcc_devices
ALTER TABLE cmcc_controls 
ADD CONSTRAINT fk_cmcc_controls_device 
FOREIGN KEY (fsu_id, device_id) REFERENCES cmcc_devices(fsu_id, device_id) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- cmcc_alarms 引用 cmcc_devices
ALTER TABLE cmcc_alarms 
ADD CONSTRAINT fk_cmcc_alarms_device 
FOREIGN KEY (fsu_id, device_id) REFERENCES cmcc_devices(fsu_id, device_id) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- cmcc_events 引用 cmcc_devices (可选)
ALTER TABLE cmcc_events 
ADD CONSTRAINT fk_cmcc_events_device 
FOREIGN KEY (fsu_id, device_id) REFERENCES cmcc_devices(fsu_id, device_id) 
ON DELETE CASCADE ON UPDATE CASCADE;
