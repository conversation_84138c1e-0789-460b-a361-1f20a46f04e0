<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.south.cmcc.dal.mapper.CmccAlarmDicMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.south.cmcc.dal.entity.CmccAlarmDic">
        <id column="id" property="id" />
        <result column="StandardVersion" property="standardVersion" />
        <result column="AlarmStandardId" property="alarmStandardId" />
        <result column="DeviceType" property="deviceType" />
        <result column="AlarmLogicClass" property="alarmLogicClass" />
        <result column="AlarmLogicSubclass" property="alarmLogicSubclass" />
        <result column="AlarmStandardName" property="alarmStandardName" />
        <result column="Meaning" property="meaning" />
        <result column="CommunicationBuildingAlarmLevel" property="communicationBuildingAlarmLevel" />
        <result column="TransmissionNodeAlarmLevel" property="transmissionNodeAlarmLevel" />
        <result column="CommunicationBaseStationAlarmLevel" property="communicationBaseStationAlarmLevel" />
        <result column="IDCAlarmLevel" property="idcAlarmLevel" />
        <result column="Description" property="description" />
        <result column="ExtendFiled" property="extendFiled" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, StandardVersion, AlarmStandardId, DeviceType, AlarmLogicClass, AlarmLogicSubclass, 
        AlarmStandardName, Meaning, CommunicationBuildingAlarmLevel, TransmissionNodeAlarmLevel, 
        CommunicationBaseStationAlarmLevel, IDCAlarmLevel, Description, ExtendFiled
    </sql>

    <!-- 根据告警标准ID查询告警字典 -->
    <select id="selectByAlarmStandardId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM cmcc_alarm_dic
        WHERE AlarmStandardId = #{alarmStandardId}
    </select>

    <!-- 根据设备类型查询告警字典列表 -->
    <select id="selectByDeviceType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM cmcc_alarm_dic
        WHERE DeviceType = #{deviceType}
        ORDER BY id
    </select>

    <!-- 根据标准版本查询告警字典列表 -->
    <select id="selectByStandardVersion" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM cmcc_alarm_dic
        <if test="standardVersion != null">
            WHERE StandardVersion = #{standardVersion}
        </if>
        ORDER BY id
    </select>

    <!-- 根据告警逻辑类别查询告警字典列表 -->
    <select id="selectByAlarmLogicClass" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM cmcc_alarm_dic
        WHERE AlarmLogicClass = #{alarmLogicClass}
        ORDER BY id
    </select>

</mapper> 