# CMCC 告警处理配置
# 根据中国移动B接口技术规范5.6.2章节

cmcc:
  alarm:
    # 告警缓存配置
    cache:
      # 告警状态缓存过期时间（小时）
      expireHours: 24
      # 最大缓存条目数
      maxEntries: 10000
      # 是否启用缓存清理任务
      enableCleanupTask: true
      # 缓存清理任务执行间隔（分钟）
      cleanupIntervalMinutes: 60

    # 告警验证配置
    validation:
      # 是否启用严格验证
      strict: true
      # 最大告警描述长度
      maxDescLength: 120
      # 最大设备ID长度
      maxDeviceIdLength: 26
      # 最大序列号长度
      maxSerialNoLength: 10

    # 告警持久化配置
    persistence:
      # 是否启用告警持久化
      enabled: true
      # 批量保存大小
      batchSize: 100
      # 保存超时时间（秒）
      timeoutSeconds: 30

    # 告警通知配置
    notification:
      # 是否启用告警通知
      enabled: true
      # 通知方式列表
      methods:
        - queue
        - webhook
      # 通知超时时间（秒）
      timeoutSeconds: 10

# Spring调度配置
spring:
  task:
    scheduling:
      # 调度线程池大小
      pool:
        size: 2
      # 线程名前缀
      thread-name-prefix: "cmcc-scheduler-"

# 日志配置
logging:
  level:
    com.siteweb.tcs.south.cmcc.connector.process: INFO
    com.siteweb.tcs.south.cmcc.scheduler: INFO
    com.siteweb.tcs.south.cmcc.web.controller: INFO 