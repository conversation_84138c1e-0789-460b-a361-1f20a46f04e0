<template>
  <div class="signal-standard flex flex-col h-full">
    <!-- 工具栏 -->
    <div class="toolbar-container bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-4 flex-shrink-0">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <el-button type="primary" size="default" @click="openModal()">
            <el-icon size="16" class="mr-2"><Plus /></el-icon>
            添加信号标准化
          </el-button>
          <el-button 
            type="default" 
            size="default" 
            @click="refreshData"
            :loading="loading"
          >
            <el-icon size="16" class="mr-2"><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
        <div class="flex items-center space-x-4">
          <!-- 搜索框 -->
          <el-input
            v-model="searchKeyword"
            placeholder="搜索信号标准ID、设备类型、信号名称..."
            style="width: 300px"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <span class="text-sm text-gray-600 dark:text-gray-400">
            共 {{ filteredData.length }} 条记录
          </span>
        </div>
      </div>
    </div>
    
    <!-- 表格容器 -->
    <div class="table-container bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 flex-1 min-h-0 flex flex-col">
      <!-- 数据表格 -->
      <div class="flex-1 min-h-0">
        <el-auto-resizer>
          <template #default="{ height, width }">
            <el-table-v2
              :data="filteredData"
              :columns="tableColumns"
              :width="width"
              :height="height"
              :row-height="36"
              :header-height="40"
              v-loading="loading"
              fixed
              :sort-by="sortBy"
              @column-sort="onSort"
              @resize="() => updateTableColumns(width)"
            />
          </template>
        </el-auto-resizer>
      </div>
    </div>
    
    <!-- 添加/编辑模态框 -->
    <SignalStandardModal
      v-model:visible="modalVisible"
      :data="modalData"
      @confirm="handleModalConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, nextTick, h } from 'vue';
import { ElMessage, ElMessageBox, ElButton, ElIcon } from 'element-plus';
import { Plus, Edit, Delete, Refresh, Search } from '@element-plus/icons-vue';
import type { SignalStandardData } from '@/api/standard';
import { getSignalStandardList, deleteSignalStandard } from '@/api/standard';
import SignalStandardModal from './SignalStandardModal.vue';
import { debounce, PerformanceTimer, optimizeTableRendering } from '@/utils/performance';

// 表格数据
const tableData = ref<SignalStandardData[]>([]);
const loading = ref(false);

// 搜索相关
const searchKeyword = ref('');

// 分页相关
const pagination = ref({
  current: 1,
  size: 20,
  total: 0
});

// 模态框相关
const modalVisible = ref(false);
const modalData = ref<SignalStandardData | null>(null);

// 过滤后的数据
const filteredData = computed(() => {
  if (!searchKeyword.value) {
    return tableData.value;
  }
  
  const keyword = searchKeyword.value.toLowerCase();
  return tableData.value.filter(item => 
    item.signalStandardId?.toLowerCase().includes(keyword) ||
    item.deviceTypeName?.toLowerCase().includes(keyword) ||
    item.standardSignalName?.toLowerCase().includes(keyword) ||
    item.semaphoreTypeName?.toLowerCase().includes(keyword) ||
    item.meaning?.toLowerCase().includes(keyword)
  );
});

// 排序相关
const sortBy = ref({ key: 'signalStandardId', order: 'asc' });

// 排序处理
const onSort = (sortData: any) => {
  sortBy.value = {
    key: sortData.key,
    order: sortData.order
  };
};

// 创建操作按钮渲染器
const createActionButtons = (row: SignalStandardData) => {
  return h('div', { class: 'flex items-center justify-center space-x-2' }, [
    h(
      ElButton,
      {
        type: 'primary',
        size: 'default',
        text: true,
        onClick: () => openModal(row),
        title: '修改',
        style: { padding: '6px 8px' }
      },
      {
        default: () => h(ElIcon, { size: 16 }, { default: () => h(Edit) })
      }
    ),
    h(
      ElButton,
      {
        type: 'danger',
        size: 'default', 
        text: true,
        onClick: () => deleteItem(row),
        title: '删除',
        style: { padding: '6px 8px' }
      },
      {
        default: () => h(ElIcon, { size: 16 }, { default: () => h(Delete) })
      }
    )
  ]);
};

// 创建文本单元格渲染器
const createTextCell = (text: string) => {
  return h(
    'div',
    {
      class: 'text-cell',
      style: {
        padding: '8px 12px',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap'
      },
      title: text
    },
    text || '-'
  );
};

// 基础列配置
const baseColumns = [
  {
    key: 'signalStandardId',
    title: '信号标准ID',
    dataKey: 'signalStandardId',
    width: 150,
    minWidth: 120,
    sortable: true,
    cellRenderer: ({ rowData }: any) => createTextCell(rowData.signalStandardId)
  },
  {
    key: 'deviceTypeName',
    title: '设备类型',
    dataKey: 'deviceTypeName',
    width: 120,
    minWidth: 100,
    sortable: true,
    cellRenderer: ({ rowData }: any) => createTextCell(rowData.deviceTypeName)
  },
  {
    key: 'deviceSubType',
    title: '设备子类型',
    dataKey: 'deviceSubType',
    width: 120,
    minWidth: 100,
    sortable: true,
    cellRenderer: ({ rowData }: any) => createTextCell(rowData.deviceSubType)
  },
  {
    key: 'standardSignalName',
    title: '标准信号名称',
    dataKey: 'standardSignalName',
    width: 180,
    minWidth: 150,
    sortable: true,
    cellRenderer: ({ rowData }: any) => createTextCell(rowData.standardSignalName)
  },
  {
    key: 'semaphoreTypeName',
    title: '信号量类型',
    dataKey: 'semaphoreTypeName',
    width: 120,
    minWidth: 100,
    sortable: true,
    cellRenderer: ({ rowData }: any) => createTextCell(rowData.semaphoreTypeName)
  },
  {
    key: 'meaning',
    title: '含义',
    dataKey: 'meaning',
    width: 150,
    minWidth: 120,
    sortable: true,
    cellRenderer: ({ rowData }: any) => createTextCell(rowData.meaning)
  },
  {
    key: 'description',
    title: '描述',
    dataKey: 'description',
    width: 200,
    minWidth: 150,
    sortable: true,
    cellRenderer: ({ rowData }: any) => createTextCell(rowData.description)
  },
  {
    key: 'actions',
    title: '操作',
    dataKey: 'actions',
    width: 120,
    minWidth: 120,
    fixed: 'right',
    align: 'center',
    cellRenderer: ({ rowData }: any) => createActionButtons(rowData)
  }
];

// 动态计算列宽
const calculateDynamicColumns = (containerWidth: number) => {
  const totalMinWidth = baseColumns.reduce((sum, col) => sum + (col.minWidth || 100), 0);
  
  // 如果容器宽度足够，使用原始宽度
  if (containerWidth >= totalMinWidth * 1.2) {
    return baseColumns;
  }
  
  // 计算缩放比例
  const scale = Math.max(0.8, (containerWidth - 50) / totalMinWidth);
  
  return baseColumns.map(col => ({
    ...col,
    width: Math.max(col.minWidth || 100, Math.floor((col.width || 100) * scale))
  }));
};

// 虚拟表格列配置 - 支持响应式
const tableColumns = ref(baseColumns);

// 监听容器尺寸变化，动态调整列宽
const updateTableColumns = (width: number) => {
  tableColumns.value = calculateDynamicColumns(width);
};


// 性能计时器
const timer = new PerformanceTimer();

// 获取表格数据
const fetchTableData = async () => {
  loading.value = true;
  timer.start('信号标准化数据加载');
  
  try {
    const response = await getSignalStandardList();
    if (response.state === true && response.data) {
      tableData.value = response.data;
      timer.end('信号标准化数据加载');
    } else {
      ElMessage.error('获取数据失败');
    }
  } catch (error) {
    console.error('获取数据失败:', error);
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

// 刷新数据
const refreshData = async () => {
  await fetchTableData();
  ElMessage.success('数据已刷新');
};

// 防抖搜索
const handleSearch = debounce(() => {
  // 搜索时无需处理分页
}, 300);

// 打开模态框
const openModal = (item?: SignalStandardData) => {
  modalData.value = item ? JSON.parse(JSON.stringify(item)) : null;
  modalVisible.value = true;
};

// 删除项目
const deleteItem = async (item: SignalStandardData) => {
  try {
    await ElMessageBox.confirm(
      `请确认是否删除信号标准化：${item.standardSignalName}?`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      }
    );
    
    if (item.id) {
      const response = await deleteSignalStandard(item.id);
      if (response.state === true) {
        ElMessage.success('删除成功');
        fetchTableData();
      } else {
        ElMessage.error('删除失败');
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error);
      ElMessage.error('删除失败');
    }
  }
};

// 模态框确认处理
const handleModalConfirm = async () => {
  const text = modalData.value?.id ? '修改' : '新增';
  ElMessage.success(`${text}成功`);
  await fetchTableData();
};

// 组件挂载后获取数据
onMounted(() => {
  // 启用表格渲染优化
  optimizeTableRendering();
  fetchTableData();
});
</script>

<style scoped>
.signal-standard {
  height: 100%;
  width: 100%;
}

.table-container {
  border-radius: 8px;
}

.toolbar-container {
  border-radius: 8px;
}

/* 虚拟表格样式优化 */
:deep(.el-table-v2) {
  border-radius: 8px;
  overflow: hidden;
  will-change: scroll-position;
  contain: layout style paint;
}

:deep(.el-table-v2__header) {
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
}

:deep(.el-table-v2__header-cell) {
  background: #f8fafc !important;
  color: #374151;
  font-weight: 600;
  border-right: 1px solid #e5e7eb;
}

:deep(.el-table-v2__row) {
  border-bottom: 1px solid #f3f4f6;
  will-change: transform;
  contain: layout;
}

:deep(.el-table-v2__row:hover) {
  background-color: #f8fafc !important;
}

:deep(.el-table-v2__row-cell) {
  border-right: 1px solid #f3f4f6;
}

/* 暗色主题适配 */
:deep(.dark .el-table-v2__header) {
  background: #374151;
  border-bottom-color: #4b5563;
}

:deep(.dark .el-table-v2__header-cell) {
  background: #374151 !important;
  color: #f3f4f6;
  border-right-color: #4b5563;
}

:deep(.dark .el-table-v2__row) {
  border-bottom-color: #374151;
}

:deep(.dark .el-table-v2__row:hover) {
  background-color: #374151 !important;
}

:deep(.dark .el-table-v2__row-cell) {
  border-right-color: #374151;
}

/* 文本单元格样式 */
.text-cell {
  font-size: 14px;
  line-height: 1.5;
  color: var(--el-text-color-primary);
}

/* 表格加载状态优化 */
:deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(1px);
}


/* 搜索框样式优化 */
:deep(.el-input__wrapper) {
  transition: all 0.2s ease;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #409eff inset;
}
</style> 