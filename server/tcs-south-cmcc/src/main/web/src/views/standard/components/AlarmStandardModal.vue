<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑告警标准化' : '添加告警标准化'"
    width="800px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="160px"
      label-position="right"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="告警编码ID" prop="alarmStandardId">
            <el-input
              v-model="formData.alarmStandardId"
              placeholder="请输入6位告警编码ID"
              maxlength="6"
              clearable
              :disabled="isEdit"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备类型名称" prop="deviceType">
            <el-select
              v-model="formData.deviceType"
              placeholder="请选择设备类型"
              clearable
              style="width: 100%"
              @change="handleDeviceTypeChange"
            >
              <el-option
                v-for="item in deviceTypeOptions"
                :key="item.itemId"
                :label="item.itemValue"
                :value="item.itemId"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="告警逻辑类别" prop="alarmLogicClass">
            <el-select
              v-model="formData.alarmLogicClass"
              placeholder="请先选择设备类型"
              clearable
              style="width: 100%"
              @change="handleAlarmLogicClassChange"
              :disabled="!formData.deviceType"
            >
              <el-option
                v-for="item in alarmLogicClassOptions"
                :key="item.itemId"
                :label="item.itemValue"
                :value="item.itemId"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <!-- 告警逻辑类别名称字段已移除 -->
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="告警逻辑子类别" prop="alarmLogicSubclass">
            <el-input
              v-model="formData.alarmLogicSubclass"
              placeholder="请输入告警逻辑子类别"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="告警标准名称" prop="alarmStandardName">
            <el-input
              v-model="formData.alarmStandardName"
              placeholder="请输入告警标准名称"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="含义" prop="meaning">
        <el-input
          v-model="formData.meaning"
          placeholder="请输入含义"
          clearable
        />
      </el-form-item>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="通信楼宇告警级别" prop="communicationBuildingAlarmLevel">
            <el-select
              v-model="formData.communicationBuildingAlarmLevel"
              placeholder="请选择通信楼宇告警级别"
              clearable
              style="width: 100%"
              @change="handleCommunicationBuildingAlarmLevelChange"
            >
              <el-option
                v-for="item in eventSeverityOptions"
                :key="item.itemId"
                :label="item.itemValue"
                :value="item.itemId"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="传输节点告警级别" prop="transmissionNodeAlarmLevel">
            <el-select
              v-model="formData.transmissionNodeAlarmLevel"
              placeholder="请选择传输节点告警级别"
              clearable
              style="width: 100%"
              @change="handleTransmissionNodeAlarmLevelChange"
            >
              <el-option
                v-for="item in eventSeverityOptions"
                :key="item.itemId"
                :label="item.itemValue"
                :value="item.itemId"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="通信基站告警级别" prop="communicationBaseStationAlarmLevel">
            <el-select
              v-model="formData.communicationBaseStationAlarmLevel"
              placeholder="请选择通信基站告警级别"
              clearable
              style="width: 100%"
              @change="handleCommunicationBaseStationAlarmLevelChange"
            >
              <el-option
                v-for="item in eventSeverityOptions"
                :key="item.itemId"
                :label="item.itemValue"
                :value="item.itemId"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="IDC告警级别" prop="idcAlarmLevel">
            <el-select
              v-model="formData.idcAlarmLevel"
              placeholder="请选择IDC告警级别"
              clearable
              style="width: 100%"
              @change="handleIdcAlarmLevelChange"
            >
              <el-option
                v-for="item in eventSeverityOptions"
                :key="item.itemId"
                :label="item.itemValue"
                :value="item.itemId"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入描述"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import type { AlarmStandardData, DictionaryItem } from '@/api/standard';
import { addAlarmStandard, updateAlarmStandard, dictionaryApi } from '@/api/standard';

// Props
interface Props {
  visible: boolean;
  data: AlarmStandardData | null;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
  'confirm': [];
}>();

// 表单引用
const formRef = ref<FormInstance>();
const loading = ref(false);

// 下拉选项数据
const deviceTypeOptions = ref<DictionaryItem[]>([]);
const alarmLogicClassOptions = ref<DictionaryItem[]>([]);
const eventSeverityOptions = ref<DictionaryItem[]>([]);

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const isEdit = computed(() => !!props.data?.id);

// 表单数据
const formData = reactive<AlarmStandardData>({
  alarmStandardId: '',
  deviceType: undefined,
  deviceTypeName: '',
  alarmLogicClass: undefined, // 改为undefined，默认为空
  alarmLogicClassName: '',
  alarmLogicSubclass: '',
  alarmStandardName: '',
  meaning: '',
  communicationBuildingAlarmLevel: undefined,
  communicationBuildingAlarmLevelName: '',
  transmissionNodeAlarmLevel: undefined,
  transmissionNodeAlarmLevelName: '',
  communicationBaseStationAlarmLevel: undefined,
  communicationBaseStationAlarmLevelName: '',
  idcAlarmLevel: undefined,
  idcAlarmLevelName: '',
  description: ''
});

// 自定义校验函数
const validateAlarmId = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback(new Error('请输入告警编码ID'));
  } else if (!/^\d{6}$/.test(value)) {
    callback(new Error('告警编码ID必须为6位数字'));
  } else {
    callback();
  }
};

// 表单验证规则
const rules: FormRules = {
  alarmStandardId: [
    { required: true, message: '请输入告警编码ID', trigger: 'blur' },
    { validator: validateAlarmId, trigger: 'blur' }
  ],
  deviceType: [
    { required: true, message: '请选择设备类型', trigger: 'change' }
  ],
  alarmLogicClass: [
    { required: true, message: '请选择告警逻辑类别', trigger: 'change' }
  ],
  alarmStandardName: [
    { required: true, message: '请输入告警标准名称', trigger: 'blur' }
  ],
  meaning: [
    { required: true, message: '请输入含义', trigger: 'blur' }
  ]
};

// 处理设备类型选择变化
const handleDeviceTypeChange = async (value: number) => {
  const selectedItem = deviceTypeOptions.value.find(item => item.itemId === value);
  if (selectedItem) {
    formData.deviceTypeName = selectedItem.itemValue;
  }
  
  // 清空告警逻辑类别相关字段
  formData.alarmLogicClass = undefined;
  alarmLogicClassOptions.value = [];
  
  // 根据设备类型加载告警逻辑类别
  if (value) {
    await loadAlarmLogicClassByDeviceType(value);
  }
};

// 处理告警逻辑类别选择变化
const handleAlarmLogicClassChange = (value: number) => {
  // 告警逻辑类别选择变化处理（如需要可在此添加逻辑）
};

// 处理通信楼宇告警级别选择变化
const handleCommunicationBuildingAlarmLevelChange = (value: number) => {
  const selectedItem = eventSeverityOptions.value.find(item => item.itemId === value);
  if (selectedItem) {
    formData.communicationBuildingAlarmLevelName = selectedItem.itemValue;
  }
};

// 处理传输节点告警级别选择变化
const handleTransmissionNodeAlarmLevelChange = (value: number) => {
  const selectedItem = eventSeverityOptions.value.find(item => item.itemId === value);
  if (selectedItem) {
    formData.transmissionNodeAlarmLevelName = selectedItem.itemValue;
  }
};

// 处理通信基站告警级别选择变化
const handleCommunicationBaseStationAlarmLevelChange = (value: number) => {
  const selectedItem = eventSeverityOptions.value.find(item => item.itemId === value);
  if (selectedItem) {
    formData.communicationBaseStationAlarmLevelName = selectedItem.itemValue;
  }
};

// 处理IDC告警级别选择变化
const handleIdcAlarmLevelChange = (value: number) => {
  const selectedItem = eventSeverityOptions.value.find(item => item.itemId === value);
  if (selectedItem) {
    formData.idcAlarmLevelName = selectedItem.itemValue;
  }
};

// 加载设备类型选项
const loadDeviceTypes = async () => {
  try {
    const response = await dictionaryApi.getDeviceType();
    if (response.state) {
      deviceTypeOptions.value = response.data || [];
    }
  } catch (error) {
    console.error('加载设备类型失败:', error);
  }
};

// 根据设备类型加载告警逻辑类别
const loadAlarmLogicClassByDeviceType = async (deviceType: number) => {
  try {
    const response = await dictionaryApi.getAlarmTypesByDeviceType(deviceType);
    if (response.state) {
      alarmLogicClassOptions.value = response.data || [];
    }
  } catch (error) {
    console.error('加载告警逻辑类别失败:', error);
  }
};

// 加载告警级别选项
const loadEventSeverity = async () => {
  try {
    const response = await dictionaryApi.getEventSeverity();
    if (response.state) {
      eventSeverityOptions.value = response.data || [];
    }
  } catch (error) {
    console.error('加载告警级别失败:', error);
  }
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    alarmStandardId: '',
    deviceType: undefined,
    deviceTypeName: '',
    alarmLogicClass: undefined,
    alarmLogicClassName: '',
    alarmLogicSubclass: '',
    alarmStandardName: '',
    meaning: '',
    communicationBuildingAlarmLevel: undefined,
    communicationBuildingAlarmLevelName: '',
    transmissionNodeAlarmLevel: undefined,
    transmissionNodeAlarmLevelName: '',
    communicationBaseStationAlarmLevel: undefined,
    communicationBaseStationAlarmLevelName: '',
    idcAlarmLevel: undefined,
    idcAlarmLevelName: '',
    description: ''
  });
  formRef.value?.clearValidate();
};

// 初始化表单数据
const initFormData = async () => {
  if (props.data) {
    Object.assign(formData, props.data);
    // 如果是编辑模式且有设备类型ID，需要加载对应的告警逻辑类别
    if (props.data.deviceType) {
      await loadAlarmLogicClassByDeviceType(props.data.deviceType);
    }
  } else {
    resetForm();
  }
};

// 监听数据变化
watch(() => props.data, initFormData, { immediate: true });

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  resetForm();
};

// 确认操作
const handleConfirm = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    loading.value = true;
    
    let response;
    if (isEdit.value) {
      response = await updateAlarmStandard(formData);
    } else {
      response = await addAlarmStandard(formData);
    }
    
    if (response.state === true) {
      ElMessage.success(isEdit.value ? '修改成功' : '添加成功');
      emit('confirm');
      handleClose();
    } else {
      ElMessage.error(response.message || '操作失败');
    }
  } catch (error) {
    console.error('操作失败:', error);
    ElMessage.error('操作失败');
  } finally {
    loading.value = false;
  }
};

// 组件挂载时加载选项数据
onMounted(() => {
  loadDeviceTypes();
  loadEventSeverity();
});
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px 20px;
  border-top: 1px solid #ebeef5;
}
</style> 