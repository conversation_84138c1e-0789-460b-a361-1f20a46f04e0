<template>
  <div class="alarm-standard">
    <!-- 工具栏 -->
    <div class="toolbar-container bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <el-button type="primary" size="default" @click="openModal()">
            <el-icon size="16" class="mr-2"><Plus /></el-icon>
            添加告警标准化
          </el-button>
          <el-button 
            type="default" 
            size="default" 
            @click="refreshData"
            :loading="loading"
          >
            <el-icon size="16" class="mr-2"><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
        <div class="flex items-center space-x-4">
          <!-- 搜索框 -->
          <el-input
            v-model="searchKeyword"
            placeholder="搜索告警标准ID、设备类型、告警名称..."
            style="width: 300px"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <span class="text-sm text-gray-600 dark:text-gray-400">
            共 {{ pagination.total }} 条记录
          </span>
        </div>
      </div>
    </div>
    
    <!-- 表格容器 -->
    <div class="table-container bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
      <div style="height: calc(100vh - 500px); min-height: 400px;">
        <el-table
          :data="displayData"
          style="width: 100%; height: 100%;"
          stripe
          border
          v-loading="loading"
          :default-sort="{ prop: 'alarmStandardId', order: 'ascending' }"
          table-layout="fixed"
          :scroll-y="{ gt: 20 }"
        >
          <el-table-column
            prop="alarmStandardId"
            label="告警标准ID"
            width="150"
            sortable
            show-overflow-tooltip
          />
          
          <el-table-column
            prop="deviceTypeName"
            label="设备类型"
            width="120"
            sortable
            show-overflow-tooltip
          />
          
          <el-table-column
            prop="alarmLogicClass"
            label="告警逻辑类别"
            width="120"
            sortable
            show-overflow-tooltip
          />
          
          <el-table-column
            prop="alarmLogicClassName"
            label="告警逻辑类别名称"
            width="150"
            sortable
            show-overflow-tooltip
          />
          
          <el-table-column
            prop="alarmLogicSubclass"
            label="告警逻辑子类别"
            width="150"
            sortable
            show-overflow-tooltip
          />
          
          <el-table-column
            prop="alarmStandardName"
            label="告警标准名称"
            min-width="150"
            sortable
            show-overflow-tooltip
          />
          
          <el-table-column
            prop="meaning"
            label="含义"
            min-width="150"
            sortable
            show-overflow-tooltip
          />
          
          <el-table-column
            prop="communicationBuildingAlarmLevelName"
            label="通信楼宇告警级别"
            width="150"
            sortable
            show-overflow-tooltip
          />
          
          <el-table-column
            prop="transmissionNodeAlarmLevelName"
            label="传输节点告警级别"
            width="150"
            sortable
            show-overflow-tooltip
          />
          
          <el-table-column
            prop="communicationBaseStationAlarmLevelName"
            label="通信基站告警级别"
            width="150"
            sortable
            show-overflow-tooltip
          />
          
          <el-table-column
            prop="idcAlarmLevelName"
            label="IDC告警级别"
            width="120"
            sortable
            show-overflow-tooltip
          />
          
          <el-table-column
            prop="description"
            label="描述"
            min-width="200"
            sortable
            show-overflow-tooltip
          />
          
          <el-table-column
            label="操作"
            width="120"
            fixed="right"
            align="center"
          >
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                text
                @click="openModal(row)"
                title="修改"
              >
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button
                type="danger"
                size="small"
                text
                @click="deleteItem(row)"
                title="删除"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </template>
          </el-table-column>
        
          <template #empty>
            <el-empty description="暂无数据" />
          </template>
        </el-table>
      </div>
      
      <!-- 分页组件 -->
      <div class="pagination-container mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    
    <!-- 添加/编辑模态框 -->
    <AlarmStandardModal
      v-model:visible="modalVisible"
      :data="modalData"
      @confirm="handleModalConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Edit, Delete, Refresh, Search } from '@element-plus/icons-vue';
import type { AlarmStandardData } from '@/api/standard';
import { getAlarmStandardList, deleteAlarmStandard } from '@/api/standard';
import AlarmStandardModal from './AlarmStandardModal.vue';
import { debounce, PerformanceTimer, optimizeTableRendering } from '@/utils/performance';

// 表格数据
const tableData = ref<AlarmStandardData[]>([]);
const loading = ref(false);

// 搜索相关
const searchKeyword = ref('');

// 分页相关
const pagination = ref({
  current: 1,
  size: 20,
  total: 0
});

// 模态框相关
const modalVisible = ref(false);
const modalData = ref<AlarmStandardData | null>(null);

// 过滤后的数据
const filteredData = computed(() => {
  if (!searchKeyword.value) {
    return tableData.value;
  }
  
  const keyword = searchKeyword.value.toLowerCase();
  return tableData.value.filter(item => 
    item.alarmStandardId?.toLowerCase().includes(keyword) ||
    item.deviceTypeName?.toLowerCase().includes(keyword) ||
    item.alarmStandardName?.toLowerCase().includes(keyword) ||
    item.alarmLogicClassName?.toLowerCase().includes(keyword) ||
    item.meaning?.toLowerCase().includes(keyword)
  );
});

// 当前页显示的数据
const displayData = computed(() => {
  const start = (pagination.value.current - 1) * pagination.value.size;
  const end = start + pagination.value.size;
  return filteredData.value.slice(start, end);
});

// 更新分页总数
const updatePagination = () => {
  pagination.value.total = filteredData.value.length;
  // 如果当前页超出范围，重置到第一页
  if (pagination.value.current > Math.ceil(pagination.value.total / pagination.value.size)) {
    pagination.value.current = 1;
  }
};

// 性能计时器
const timer = new PerformanceTimer();

// 获取表格数据
const fetchTableData = async () => {
  loading.value = true;
  timer.start('告警标准化数据加载');
  
  try {
    const response = await getAlarmStandardList();
    if (response.state === true && response.data) {
      tableData.value = response.data;
      updatePagination();
      timer.end('告警标准化数据加载');
    } else {
      ElMessage.error('获取数据失败');
    }
  } catch (error) {
    console.error('获取数据失败:', error);
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

// 刷新数据
const refreshData = async () => {
  await fetchTableData();
  ElMessage.success('数据已刷新');
};

// 防抖搜索
const handleSearch = debounce(() => {
  pagination.value.current = 1; // 搜索时重置到第一页
  updatePagination();
}, 300);

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.value.size = size;
  pagination.value.current = 1;
  updatePagination();
};

// 当前页改变
const handleCurrentChange = (current: number) => {
  pagination.value.current = current;
};

// 打开模态框
const openModal = (item?: AlarmStandardData) => {
  modalData.value = item ? JSON.parse(JSON.stringify(item)) : null;
  modalVisible.value = true;
};

// 删除项目
const deleteItem = async (item: AlarmStandardData) => {
  try {
    await ElMessageBox.confirm(
      `请确认是否删除告警标准化：${item.alarmStandardName}?`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      }
    );
    
    if (item.id) {
      const response = await deleteAlarmStandard(item.id);
      if (response.state === true) {
        ElMessage.success('删除成功');
        fetchTableData();
      } else {
        ElMessage.error('删除失败');
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error);
      ElMessage.error('删除失败');
    }
  }
};

// 模态框确认处理
const handleModalConfirm = async () => {
  const text = modalData.value?.id ? '修改' : '新增';
  ElMessage.success(`${text}成功`);
  await fetchTableData();
};

// 组件挂载后获取数据
onMounted(() => {
  // 启用表格渲染优化
  optimizeTableRendering();
  fetchTableData();
});
</script>

<style scoped>
.alarm-standard {
  height: 100%;
  width: 100%;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header-wrapper) {
  background: #f8fafc;
}

:deep(.el-table th) {
  background: #f8fafc !important;
  color: #374151;
  font-weight: 600;
  border-bottom: 1px solid #e5e7eb;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f3f4f6;
}

:deep(.el-table__row:hover > td) {
  background-color: #f8fafc !important;
}

/* 工具栏样式 */
.toolbar-container {
  border-radius: 8px;
}

.table-container {
  border-radius: 8px;
}

/* 暗色主题适配 */
:deep(.dark .el-table th) {
  background: #374151 !important;
  color: #f3f4f6;
  border-bottom-color: #4b5563;
}

:deep(.dark .el-table td) {
  border-bottom-color: #374151;
}

:deep(.dark .el-table__row:hover > td) {
  background-color: #374151 !important;
}

/* 性能优化样式 */
:deep(.el-table__body-wrapper) {
  /* 启用硬件加速 */
  transform: translateZ(0);
  will-change: transform;
}

:deep(.el-table__row) {
  /* 优化行渲染 */
  contain: layout style paint;
}

/* 分页样式 */
.pagination-container {
  padding: 16px 0;
  border-top: 1px solid #ebeef5;
}

:deep(.dark .pagination-container) {
  border-top-color: #4b5563;
}

/* 搜索框样式优化 */
:deep(.el-input__wrapper) {
  transition: all 0.2s ease;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #409eff inset;
}
</style> 