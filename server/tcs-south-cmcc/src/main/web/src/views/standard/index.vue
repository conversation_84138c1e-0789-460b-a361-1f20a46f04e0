<template>
  <div class="standard-management-container min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex items-center mb-4">
        <div class="w-1 h-8 bg-primary rounded-full mr-4" />
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            移动标准化管理
          </h1>
          <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
            管理信号标准化和告警标准化数据
          </p>
        </div>
      </div>
    </div>

    <!-- Tab页面容器 -->
    <div class="tab-container bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <el-tabs
        v-model="activeTab"
        class="standard-tabs"
        type="border-card"
      >
        <el-tab-pane label="信号标准化" name="signal-standard">
          <div class="tab-content">
            <SignalStandard />
          </div>
        </el-tab-pane>
        <el-tab-pane label="告警标准化" name="alarm-standard">
          <div class="tab-content">
            <AlarmStandard />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import SignalStandard from './components/SignalStandard.vue';
import AlarmStandard from './components/AlarmStandard.vue';

defineOptions({
  name: "StandardManagement"
});

// 当前活跃的标签页
const activeTab = ref<string>('signal-standard');
</script>

<style scoped>
.standard-management-container {
  min-height: 100vh;
}

.tab-container {
  overflow: hidden;
}

.tab-content {
  padding: 16px;
  background: #fafafa;
  min-height: calc(100vh - 220px);
}

/* Tab头部样式优化 */
:deep(.standard-tabs) {
  border: none;
  background: transparent;
}

:deep(.standard-tabs .el-tabs__header) {
  margin: 0;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
}

:deep(.standard-tabs .el-tabs__nav-wrap) {
  padding: 0 24px;
}

:deep(.standard-tabs .el-tabs__nav-scroll) {
  background: transparent;
}

:deep(.standard-tabs .el-tabs__nav) {
  border: none;
  background: transparent;
}

:deep(.standard-tabs .el-tabs__item) {
  border: none;
  background: transparent;
  color: #6b7280;
  font-weight: 500;
  font-size: 14px;
  padding: 0 20px;
  height: 48px;
  line-height: 48px;
  margin-right: 8px;
  border-radius: 6px 6px 0 0;
  transition: all 0.2s ease;
}

:deep(.standard-tabs .el-tabs__item:hover) {
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
}

:deep(.standard-tabs .el-tabs__item.is-active) {
  background: #3b82f6;
  color: white;
  border: none;
}

:deep(.standard-tabs .el-tabs__active-bar) {
  display: none;
}

:deep(.standard-tabs .el-tabs__content) {
  background: #fafafa;
  border: none;
  padding: 0;
}

:deep(.standard-tabs .el-tab-pane) {
  background: #fafafa;
}

/* 暗色主题适配 */
:deep(.dark .standard-tabs .el-tabs__header) {
  background: #374151;
  border-bottom-color: #4b5563;
}

:deep(.dark .standard-tabs .el-tabs__item) {
  color: #d1d5db;
}

:deep(.dark .standard-tabs .el-tabs__item:hover) {
  color: #60a5fa;
  background: rgba(96, 165, 250, 0.1);
}

:deep(.dark .standard-tabs .el-tabs__item.is-active) {
  background: #3b82f6;
  color: white;
}

:deep(.dark .tab-content) {
  background: #1f2937;
}

:deep(.dark .standard-tabs .el-tabs__content) {
  background: #1f2937;
}

:deep(.dark .standard-tabs .el-tab-pane) {
  background: #1f2937;
}
</style> 