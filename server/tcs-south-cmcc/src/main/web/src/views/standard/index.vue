<template>
  <div
    class="standard-management-container bg-gray-50 dark:bg-gray-900 p-4 flex flex-col"
    style="height: calc(100vh - 48px)"
  >
    <!-- 页面头部 -->
    <div class="mb-4 flex-shrink-0">
      <div class="flex items-center justify-between mb-3">
        <div class="flex items-center">
          <div class="w-1 h-6 bg-primary rounded-full mr-3" />
          <h1 class="text-xl font-bold text-gray-900 dark:text-white">
            移动标准化管理
          </h1>
        </div>
        <span class="text-sm text-gray-600 dark:text-gray-400">
          管理信号标准化和告警标准化数据
        </span>
      </div>

      <!-- 工具栏区域 -->
      <div
        class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-3"
      >
        <el-tabs
          v-model="activeTab"
          class="standard-tabs"
          @tab-change="handleTabChange"
        >
          <el-tab-pane label="信号标准化" name="signal-standard">
          </el-tab-pane>
          <el-tab-pane label="告警标准化" name="alarm-standard">
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="flex-1 min-h-0">
      <div v-if="activeTab === 'signal-standard'" class="h-full">
        <SignalStandard />
      </div>
      <div v-else-if="activeTab === 'alarm-standard'" class="h-full">
        <AlarmStandard />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import SignalStandard from './components/SignalStandard.vue';
import AlarmStandard from './components/AlarmStandard.vue';

defineOptions({
  name: "StandardManagement"
});

// 当前活跃的标签页
const activeTab = ref<string>('signal-standard');

// Tab切换处理
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName;
};
</script>

<style scoped>
.standard-management-container {
  height: calc(100vh - 48px);
}

.standard-tabs {
  :deep(.el-tabs__header) {
    margin: 0 !important;
    padding: 0;
    background: transparent;
  }

  :deep(.el-tabs__nav-wrap::after) {
    display: none;
  }

  :deep(.el-tabs__item) {
    height: 40px;
    line-height: 40px;
    font-weight: 500;
    font-size: 14px;
    padding: 0 16px;
  }

  :deep(.el-tabs__content) {
    padding: 0;
    display: none;
  }

  :deep(.el-tabs__active-bar) {
    height: 2px;
  }
}
</style> 