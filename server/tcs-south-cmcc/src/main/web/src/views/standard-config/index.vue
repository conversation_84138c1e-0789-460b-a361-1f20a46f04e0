<template>
  <div class="device-template-management dark:bg-gray-900 p-4" style="height: calc(100vh - 48px)">
    <!-- 主内容区域 -->
    <div class="flex gap-4 h-full">
      <!-- 左侧面板 -->
      <div :class="[
        'flex-shrink-0 transition-all duration-500 ease-in-out',
        currentMode === 'template' ? 'w-80' : 'w-1/2'
      ]">
        <div
          class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 h-full flex flex-col"
        >
          <!-- 调整宽度的分割线 -->
          <div class="resize-handle" @mousedown="startResize">
            <div class="resize-line" />
          </div>

          <!-- 标题栏和模式切换 -->
          <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between mb-3">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                {{ currentMode === 'template' ? '设备模板' : getStandardTitle() }}
              </h3>
            </div>
            <!-- 模式切换按钮 -->
            <div class="flex space-x-2">
              <el-button
                :type="currentMode === 'template' ? 'primary' : 'default'"
                size="small"
                @click="switchMode('template')"
              >
                模板管理
              </el-button>
              <el-button
                :type="currentMode === 'standard' ? 'primary' : 'default'"
                size="small"
                :disabled="!selectedNode"
                @click="switchMode('standard')"
              >
                标准化配置
              </el-button>
            </div>
          </div>

          <!-- 模板管理模式内容 -->
          <div v-if="currentMode === 'template'" class="flex-1 flex flex-col">
            <!-- 搜索框 -->
            <div class="px-4 pt-3 pb-4">
              <el-input
                v-model="searchText"
                placeholder="搜索模板..."
                clearable
                :prefix-icon="Search"
                size="default"
                class="mb-3"
                @input="onSearchChange"
                @keyup.enter="selectSearchNode"
              />

              <div class="text-xs text-gray-500 dark:text-gray-400">
                共 {{ countTotalTemplates(treeData) }} 个模板
              </div>
            </div>

            <!-- 树形结构 -->
            <div class="flex-1 overflow-auto px-2">
              <div v-if="loading" class="flex justify-center items-center h-32">
                <el-icon size="20" class="text-primary animate-spin">
                  <Loading />
                </el-icon>
              </div>

              <div v-else-if="treeData.length === 0" class="text-center py-12">
                <div
                  class="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4"
                >
                  <el-icon size="20" class="text-gray-400"><Files /></el-icon>
                </div>
                <h3
                  class="text-lg font-medium text-gray-900 dark:text-white mb-2"
                >
                  {{ searchText ? "未找到相关模板" : "暂无模板" }}
                </h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{ searchText ? "请尝试其他搜索条件" : "请联系管理员添加模板" }}
                </p>
              </div>

              <div v-else>
                <el-tree
                  ref="treeRef"
                  :data="treeData"
                  :props="treeProps"
                  :filter-node-method="filterNode"
                  :highlight-current="true"
                  :expand-on-click-node="false"
                  :check-on-click-node="false"
                  node-key="id"
                  class="template-tree"
                  :lazy="false"
                  :load="undefined"
                  @node-click="handleNodeClick"
                  @ready="handleTreeReady"
                >
                  <template #default="{ node, data }">
                    <div class="flex items-center space-x-2 flex-1 py-1">
                      <el-icon
                        v-if="!data.template"
                        size="18"
                        class="text-orange-500"
                      >
                        <Folder />
                      </el-icon>
                      <el-icon v-else size="18" class="text-blue-500">
                        <Document />
                      </el-icon>
                      <span
                        class="flex-1 overflow-hidden text-ellipsis whitespace-nowrap"
                        :title="data.name"
                        v-html="highlightSearchText(data.name)"
                      />
                    </div>
                  </template>
                </el-tree>
              </div>
            </div>
          </div>

          <!-- 标准化配置模式内容 -->
          <div v-else-if="currentMode === 'standard'" class="flex-1 overflow-hidden">
            <SignalStandardTable
              v-if="selectedNode"
              ref="standardTableRef"
              :template-id="selectedNode.id"
              :active-tab="activeTabName"
              @signal-select="handleSignalSelect"
              @alarm-select="handleAlarmSelect"
              @drag-start="handleDragStart"
              @drag-end="handleDragEnd"
            />
            <div v-else class="flex-1 flex items-center justify-center">
              <div class="text-center">
                <div
                  class="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4"
                >
                  <el-icon size="20" class="text-gray-400"><Files /></el-icon>
                </div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  请选择模板
                </h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  请先在模板管理模式下选择一个模板
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧内容区 -->
      <div :class="[
        'flex-1 min-w-0 transition-all duration-500 ease-in-out',
        currentMode === 'template' ? '' : 'w-1/2'
      ]">
        <div
          v-if="selectedNode"
          class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 h-full flex flex-col"
          v-loading="rightPanelLoading"
          element-loading-text="正在加载数据..."
          element-loading-background="rgba(255, 255, 255, 0.8)"
        >
          <!-- 使用 el-tabs 但自定义样式 -->
          <div class="flex-1 flex flex-col relative">
            <el-tabs v-model="activeTabName" class="custom-tabs flex-1">
              <el-tab-pane label="设备模板" name="template">
                <DeviceTemplateInfo
                  :template-data="selectedNode"
                  @update-template-name="handleUpdateTemplateName"
                />
              </el-tab-pane>

              <el-tab-pane label="信号" name="signal">
                <DeviceTemplateSignal
                  v-if="activeTabName === 'signal'"
                  ref="signalRef"
                  :template-data="selectedNode"
                  :tab-index="1"
                  :mu-category="0"
                  :table-search-text="currentMode === 'standard' ? '' : searchText"
                  :equipment-id="''"
                  :button-flag="false"
                  :is-root-template="true"
                  :drag-data="currentMode === 'standard' ? dragData : null"
                  :is-dragging="currentMode === 'standard' ? isDragging : false"
                  @select-tab="handleTabSelect"
                  @drop-standard="handleDropStandard"
                />
              </el-tab-pane>

              <el-tab-pane label="事件" name="event">
                <DeviceTemplateEvent
                  v-if="activeTabName === 'event'"
                  ref="eventRef"
                  :template-data="selectedNode"
                  :tab-index="2"
                  :mu-category="0"
                  :table-search-text="currentMode === 'standard' ? '' : searchText"
                  :equipment-id="''"
                  :button-flag="false"
                  :is-root-template="true"
                  :drag-data="currentMode === 'standard' ? dragData : null"
                  :is-dragging="currentMode === 'standard' ? isDragging : false"
                  @select-tab="handleTabSelect"
                  @drop-standard="handleDropStandard"
                />
              </el-tab-pane>

              <el-tab-pane label="控制" name="control">
                <DeviceTemplateControl
                  v-if="activeTabName === 'control'"
                  ref="controlRef"
                  :template-data="selectedNode"
                  :tab-index="3"
                  :mu-category="0"
                  :table-search-text="currentMode === 'standard' ? '' : searchText"
                  :equipment-id="''"
                  :button-flag="false"
                  :is-root-template="true"
                  :drag-data="currentMode === 'standard' ? dragData : null"
                  :is-dragging="currentMode === 'standard' ? isDragging : false"
                  @select-tab="handleTabSelect"
                  @drop-standard="handleDropStandard"
                />
              </el-tab-pane>

              <el-tab-pane label="变更记录" name="changelog">
                <DeviceTemplateLog
                  v-if="activeTabName === 'changelog'"
                  :template-data="selectedNode"
                  :tab-index="activeTabName === 'changelog' ? 4 : 0"
                  :mu-category="0"
                  :table-search-text="searchText"
                  :equipment-id="''"
                  :button-flag="false"
                  :is-root-template="true"
                  @select-tab="handleTabSelect"
                />
              </el-tab-pane>
            </el-tabs>

            <!-- 模板信息和操作按钮显示在右上角 -->
            <div
              class="absolute top-3 right-6 flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400 z-10"
            >
              <div class="flex items-center space-x-2">
              <span>当前模板：</span>
              <span class="font-medium text-gray-900 dark:text-white">
                {{ selectedNode.name }}#{{ selectedNode.id }}
              </span>
              </div>

              <!-- 操作按钮 - 仅在模板管理模式下显示 -->
              <div v-if="currentMode === 'template'" class="flex items-center space-x-2">
                <el-button
                  type="success"
                  size="small"
                  :icon="Share"
                  @click="handleApplyToSubTemplates"
                >
                  应用标准化到子模版
                </el-button>
              </div>

            </div>
          </div>
        </div>

        <!-- 未选择状态 -->
        <div
          v-else
          class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 h-full flex items-center justify-center"
        >
          <div class="text-center">
            <div
              class="w-20 h-20 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-6"
            >
              <el-icon size="24" class="text-gray-400"><Files /></el-icon>
            </div>
            <h3 class="text-xl font-medium text-gray-900 dark:text-white mb-3">
              请选择一个模板
            </h3>
            <p class="text-gray-500 dark:text-gray-400 max-w-md">
              从左侧列表中选择要查看的设备模板，查看其详细信息和配置
            </p>
          </div>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick, watch, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import type { ElTree } from "element-plus";
import {
  Search,
  Document,
  Files,
  Loading,
  Folder,
  Share
} from "@element-plus/icons-vue";
import DeviceTemplateInfo from "./components/DeviceTemplateInfo.vue";
import DeviceTemplateLog from "./components/DeviceTemplateLog.vue";
import SignalStandardTable from "./components/SignalStandardTable.vue";
// 使用支持拖拽的标准化配置组件
import DeviceTemplateSignal from "./components/DeviceTemplateSignal.vue";
import DeviceTemplateEvent from "./components/DeviceTemplateEvent.vue";
import DeviceTemplateControl from "./components/DeviceTemplateControl.vue";
import {
  getTemplateTree,
  updateSignal,
  applyStandardizationToChildren,
  type TemplateTreeNode,
  type SignalInfo
} from "@/api/device-template";

// 页面状态
const loading = ref(false);
const siderWidth = ref(300);
const searchText = ref("");
const treeData = ref<TemplateTreeNode[]>([]);
const selectedNode = ref<TemplateTreeNode | null>(null);
const activeTabName = ref("template");
const currentMode = ref<'template' | 'standard'>('template');

// 标准化配置相关状态
const isDragging = ref(false);
const dragData = ref<any>(null);

// 搜索相关
const searchChanged = ref(false);
const matchNodes = ref<TemplateTreeNode[]>([]);
const matchIndex = ref(0);

// DOM引用 - 添加类型安全
const treeRef = ref<InstanceType<typeof ElTree>>();
const signalRef = ref();
const eventRef = ref();
const controlRef = ref();
const standardTableRef = ref();

// 添加树组件状态管理
const treeReady = ref(false);

// 添加右侧组件加载状态管理
const rightPanelLoading = ref(false);
const componentReady = ref({
  signal: false,
  event: false,
  control: false,
  changelog: false
});

// 定时器管理 - 防止内存泄漏
const timers = new Set<number>();

// 清理定时器的辅助函数
const clearTimer = (timerId: number) => {
  clearTimeout(timerId);
  timers.delete(timerId);
};

const addTimer = (callback: () => void, delay: number) => {
  const timerId = window.setTimeout(() => {
    callback();
    timers.delete(timerId);
  }, delay);
  timers.add(timerId);
  return timerId;
};

// 拖拽调整宽度相关
const isResizing = ref(false);

// 路由相关
const route = useRoute();
const router = useRouter();



// 树配置
const treeProps = {
  children: "children",
  label: "name",
  isLeaf: "isLeaf"
};

// 生命周期
onMounted(() => {
  initTree();

  // 检查路由，如果是从 /standard-config 进入，自动切换到标准化模式
  if (route.path === '/standard-config' || route.query.mode === 'standard') {
    // 延迟切换，等待模板数据加载完成
    nextTick(() => {
      if (route.query.templateId) {
        // 如果有模板ID，等待模板选中后再切换模式
        const unwatch = watch(selectedNode, (newNode) => {
          if (newNode) {
            currentMode.value = 'standard';
            activeTabName.value = 'signal';
            unwatch(); // 取消监听
          }
        });
      }
    });
  }
});

// 组件卸载时清理资源
onUnmounted(() => {
  // 清理所有定时器
  timers.forEach(timerId => {
    clearTimeout(timerId);
  });
  timers.clear();

  // 重置状态
  treeReady.value = false;
  rightPanelLoading.value = false;
  selectedNode.value = null;
  treeData.value = [];

  // 重置组件就绪状态
  Object.keys(componentReady.value).forEach(key => {
    componentReady.value[key as keyof typeof componentReady.value] = false;
  });
});

// 监听搜索文本变化 - 添加防抖优化
watch(searchText, (val) => {
  // 使用 nextTick 确保 DOM 更新完成后再执行过滤
  nextTick(() => {
    if (treeRef.value && treeRef.value.filter) {
      try {
        treeRef.value.filter(val);
      } catch (error) {
        console.warn('Tree filter error:', error);
      }
    }
  });
}, {
  // 添加防抖，避免频繁触发
  flush: 'post'
});

// 监听路由查询参数变化
watch(
  () => route.query.templateId,
  (templateId) => {
    if (templateId && treeData.value.length > 0) {
      // 使用管理的定时器避免内存泄漏
      addTimer(() => {
        selectNodeByTemplateId(templateId as string);
      }, 100);
    }
  },
  { immediate: false } // 避免初始化时立即执行
);

// 安全的组件方法调用
const safeCallComponentMethod = (componentRef: any, methodName: string, tabName: string) => {
  if (componentRef?.value && typeof componentRef.value[methodName] === 'function') {
    try {
      console.log(`🔄 调用 ${tabName} 组件的 ${methodName} 方法`);
      componentRef.value[methodName]();
      return true;
    } catch (error) {
      console.warn(`调用 ${tabName} 组件的 ${methodName} 方法失败:`, error);
      return false;
    }
  } else {
    console.warn(`${tabName} 组件或 ${methodName} 方法不可用`);
    return false;
  }
};

// 监听标签页切换，在标准化模式下同步更新左侧表格 - 优化性能和错误处理
watch(activeTabName, (newTabName, oldTabName) => {
  console.log(`🔄 标签页切换: ${oldTabName} -> ${newTabName}`);

  // 重置组件就绪状态
  if (newTabName !== oldTabName) {
    componentReady.value[newTabName as keyof typeof componentReady.value] = false;
  }

  if (currentMode.value === 'standard' && selectedNode.value) {
    // 只有在支持拖拽的标签页时才需要更新数据和左侧标准化表格
    const supportDragTabs = ['signal', 'event', 'control'];

    if (supportDragTabs.includes(newTabName)) {
      console.log("🔄 切换到支持拖拽的标签页:", newTabName, "开始更新数据");

      // 设置加载状态
      rightPanelLoading.value = true;

      // 使用 nextTick 和延迟确保组件完全渲染后再加载数据
      nextTick(() => {
        addTimer(() => {
          // 根据当前标签页加载对应数据
          let success = false;
          switch (newTabName) {
            case 'signal':
              success = safeCallComponentMethod(signalRef, 'loadSignalList', 'Signal');
              break;
            case 'event':
              success = safeCallComponentMethod(eventRef, 'loadEventList', 'Event');
              break;
            case 'control':
              success = safeCallComponentMethod(controlRef, 'loadControlList', 'Control');
              break;
          }

          if (success) {
            // 标记组件已就绪
            componentReady.value[newTabName as keyof typeof componentReady.value] = true;

            // 延迟更新左侧标准化表格，避免同时加载
            addTimer(() => {
              safeCallComponentMethod(standardTableRef, 'loadStandardData', 'StandardTable');
              rightPanelLoading.value = false;
            }, 300);
          } else {
            rightPanelLoading.value = false;
          }
        }, 150);
      });
    } else {
      console.log("⚠️ 当前标签页不支持拖拽:", newTabName, "跳过数据更新");
      rightPanelLoading.value = false;
    }
  } else {
    rightPanelLoading.value = false;
  }
});

// 处理树组件就绪事件
const handleTreeReady = () => {
  treeReady.value = true;
  console.log('Tree component is ready');
};

// 初始化树数据 - 优化加载流程
const initTree = async () => {
  loading.value = true;
  treeReady.value = false;

  try {
    const res = await getTemplateTree();

    if (res.code === 0) {
      treeData.value = buildTreeData(res.data);

      // 等待树组件完全渲染后再进行节点选择
      await nextTick();

      // 使用管理的定时器确保树组件完全初始化
      addTimer(() => {
        const templateId = route.query.templateId as string;
        if (templateId) {
          // 如果有指定的模板ID，则选中该模板
          selectNodeByTemplateId(templateId);
        } else if (!selectedNode.value) {
          // 如果没有选中的节点，则选中第一个叶子节点
          selectFirstLeafNode();
        }
      }, 300);
    } else {
      ElMessage.error(res.msg || "获取模板树失败");
    }
  } catch (error) {
    ElMessage.error("获取模板树异常");
    console.error(error);
  } finally {
    loading.value = false;
  }
};

// 构建树数据
const buildTreeData = (nodes: TemplateTreeNode[]): TemplateTreeNode[] => {
  return nodes.map((node, index) => {
    const newNode = {
      ...node,
      title: node.name,
      key: node.id
    };

    if (node.children && node.children.length > 0) {
      newNode.children = buildTreeData(node.children);
    } else {
      newNode.isLeaf = true;
    }

    return newNode;
  });
};

// 计算总模板数
const countTotalTemplates = (nodes: TemplateTreeNode[]): number => {
  let count = 0;
  nodes.forEach(node => {
    if (node.template) count++;
    if (node.children && node.children.length > 0) {
      count += countTotalTemplates(node.children);
    }
  });
  return count;
};

// 选中第一个叶子节点 - 优化性能
const selectFirstLeafNode = () => {
  const findFirstLeaf = (
    nodes: TemplateTreeNode[]
  ): TemplateTreeNode | null => {
    for (const node of nodes) {
      if (node.isLeaf) {
        return node;
      }
      if (node.children && node.children.length > 0) {
        const leaf = findFirstLeaf(node.children);
        if (leaf) return leaf;
      }
    }
    return null;
  };

  const firstLeaf = findFirstLeaf(treeData.value);
  if (firstLeaf) {
    // 使用 nextTick 确保 DOM 更新完成
    nextTick(() => {
      if (treeRef.value && treeRef.value.setCurrentKey) {
        try {
          treeRef.value.setCurrentKey(firstLeaf.id);
          selectedNode.value = firstLeaf;
        } catch (error) {
          console.warn('Set current key error:', error);
          // 如果设置失败，直接设置选中节点
          selectedNode.value = firstLeaf;
        }
      } else {
        selectedNode.value = firstLeaf;
      }
    });
  }
};

// 根据模板ID查找并选中节点 - 优化性能和错误处理
const selectNodeByTemplateId = (templateId: string) => {
  const findNodeById = (
    nodes: TemplateTreeNode[],
    id: string
  ): TemplateTreeNode | null => {
    for (const node of nodes) {
      if (node.id == id) {
        return node;
      }
      if (node.children && node.children.length > 0) {
        const found = findNodeById(node.children, id);
        if (found) return found;
      }
    }
    return null;
  };

  const targetNode = findNodeById(treeData.value, templateId);
  if (targetNode) {
    // 使用 nextTick 确保 DOM 更新完成
    nextTick(() => {
      if (treeRef.value && treeRef.value.setCurrentKey) {
        try {
          // 展开父节点
          treeRef.value.setCurrentKey(targetNode.id);
          selectedNode.value = targetNode;

          // 使用管理的定时器确保节点可见
          addTimer(() => {
            const nodeElement = document.querySelector(
              `[data-key="${targetNode.id}"]`
            );
            if (nodeElement) {
              nodeElement.scrollIntoView({ behavior: "smooth", block: "center" });
            }
          }, 200);

          ElMessage.success(`已自动选择模板: ${targetNode.name}`);
        } catch (error) {
          console.warn('Set current key error:', error);
          // 如果设置失败，直接设置选中节点
          selectedNode.value = targetNode;
          ElMessage.success(`已选择模板: ${targetNode.name}`);
        }
      } else {
        selectedNode.value = targetNode;
        ElMessage.success(`已选择模板: ${targetNode.name}`);
      }
    });
  } else {
    ElMessage.warning(`未找到指定的模板 ID: ${templateId}`);
    // 如果找不到指定模板，则选择第一个叶子节点
    selectFirstLeafNode();
  }
};

// 过滤节点
const filterNode = (value: string, data: TemplateTreeNode) => {
  if (!value) return true;
  return data.name.toLowerCase().includes(value.toLowerCase());
};

// 高亮搜索文本
const highlightSearchText = (text: string) => {
  if (!searchText.value.trim()) {
    return text;
  }
  const regex = new RegExp(`(${searchText.value.trim()})`, "gi");
  return text.replace(regex, '<span class="highlight-text">$1</span>');
};

// 搜索变化处理
const onSearchChange = () => {
  searchChanged.value = true;
};

// 选择搜索节点
const selectSearchNode = () => {
  if (searchChanged.value) {
    searchChanged.value = false;
    matchNodes.value = [];
    matchIndex.value = 0;
    findMatchingNodes(searchText.value.trim(), treeData.value);

    if (matchNodes.value.length > 0) {
      selectMatchNode(0);
    }
  } else {
    matchIndex.value++;
    if (matchIndex.value >= matchNodes.value.length) {
      matchIndex.value = 0;
    }
    if (matchIndex.value < matchNodes.value.length) {
      selectMatchNode(matchIndex.value);
    }
  }
};

// 查找匹配节点
const findMatchingNodes = (searchValue: string, nodes: TemplateTreeNode[]) => {
  nodes.forEach(node => {
    if (node.name.toLowerCase().includes(searchValue.toLowerCase())) {
      matchNodes.value.push(node);
    }
    if (node.children && node.children.length > 0) {
      findMatchingNodes(searchValue, node.children);
    }
  });
};

// 选择匹配节点 - 优化错误处理
const selectMatchNode = (index: number) => {
  const node = matchNodes.value[index];
  if (node) {
    nextTick(() => {
      if (treeRef.value && treeRef.value.setCurrentKey) {
        try {
          treeRef.value.setCurrentKey(node.id);
          selectedNode.value = node;
        } catch (error) {
          console.warn('Select match node error:', error);
          selectedNode.value = node;
        }
      } else {
        selectedNode.value = node;
      }
    });
  }
};

// 处理节点点击 - 优化加载性能和错误处理
const handleNodeClick = (data: TemplateTreeNode) => {
  const previousNode = selectedNode.value;
  selectedNode.value = data;

  console.log(`🔄 节点点击: ${data.name} (ID: ${data.id})`);

  // 如果在标准化模式下且当前是支持拖拽的标签页，需要重新加载数据
  if (currentMode.value === 'standard' && data.id !== previousNode?.id) {
    const supportDragTabs = ['signal', 'event', 'control'];
    if (supportDragTabs.includes(activeTabName.value)) {
      console.log("🔄 节点变更，重新加载标准化数据:", data.name);

      // 设置加载状态
      rightPanelLoading.value = true;
      componentReady.value[activeTabName.value as keyof typeof componentReady.value] = false;

      // 使用延迟确保组件完全接收到新的模板数据后再加载
      addTimer(() => {
        // 重新加载当前标签页的数据
        let success = false;
        switch (activeTabName.value) {
          case 'signal':
            success = safeCallComponentMethod(signalRef, 'loadSignalList', 'Signal');
            break;
          case 'event':
            success = safeCallComponentMethod(eventRef, 'loadEventList', 'Event');
            break;
          case 'control':
            success = safeCallComponentMethod(controlRef, 'loadControlList', 'Control');
            break;
        }

        if (success) {
          componentReady.value[activeTabName.value as keyof typeof componentReady.value] = true;

          // 延迟加载左侧标准化表格，避免同时请求
          addTimer(() => {
            safeCallComponentMethod(standardTableRef, 'loadStandardData', 'StandardTable');
            rightPanelLoading.value = false;
          }, 400);
        } else {
          rightPanelLoading.value = false;
        }
      }, 250);
    }
  }
};



// 处理标签页选择
const handleTabSelect = (data: { index: number }) => {
  const tabNames = ["template", "signal", "event", "control", "changelog"];
  if (data.index >= 0 && data.index < tabNames.length) {
    activeTabName.value = tabNames[data.index];
  }
};

// 处理应用标准化到子模版
const handleApplyToSubTemplates = async () => {
  if (!selectedNode.value) {
    ElMessage.warning("请先选择一个模板");
    return;
  }

  try {
    // 显示确认对话框
    await ElMessageBox.confirm(
      `确定要将模板 "${selectedNode.value.name}" 的标准化配置应用到所有子模版吗？`,
      "确认应用标准化",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    // 显示加载提示
    const loading = ElMessage({
      message: "正在应用标准化配置到子模版...",
      type: "info",
      duration: 0
    });

    try {
      // 调用API应用标准化到子模版
      const response = await applyStandardizationToChildren(selectedNode.value.id);

      // 关闭加载提示
      loading.close();

      if (response.code === 0) {
        ElMessage.success("标准化配置已成功应用到所有子模版");
      } else {
        ElMessage.error(response.msg || "应用标准化配置失败");
      }
    } catch (error) {
      // 关闭加载提示
      loading.close();
      console.error("应用标准化配置失败:", error);
      ElMessage.error("应用标准化配置失败，请检查网络连接");
    }
  } catch (error) {
    // 用户取消操作
    console.log("用户取消了应用标准化操作");
  }
};


// 处理模板名称更新
const handleUpdateTemplateName = (newName: string) => {
  if (selectedNode.value) {
    // 更新当前选中节点的名称
    selectedNode.value.name = newName;
    selectedNode.value.title = newName;

    // 更新树中对应节点的名称
    updateNodeNameInTree(treeData.value, selectedNode.value.id, newName);
  }
};

// 在树数据中更新节点名称
const updateNodeNameInTree = (
  nodes: TemplateTreeNode[],
  nodeId: string,
  newName: string
): boolean => {
  for (const node of nodes) {
    if (node.id === nodeId) {
      node.name = newName;
      node.title = newName;
      return true;
    }
    if (node.children && node.children.length > 0) {
      if (updateNodeNameInTree(node.children, nodeId, newName)) {
        return true;
      }
    }
  }
  return false;
};



// 开始调整宽度
const startResize = (e: MouseEvent) => {
  isResizing.value = true;
  const startX = e.clientX;
  const startWidth = siderWidth.value;

  const handleMouseMove = (e: MouseEvent) => {
    if (!isResizing.value) return;

    const deltaX = e.clientX - startX;
    const newWidth = startWidth + deltaX;

    // 限制最小和最大宽度
    if (newWidth >= 200 && newWidth <= 600) {
      siderWidth.value = newWidth;
    }
  };

  const handleMouseUp = () => {
    isResizing.value = false;
    document.removeEventListener("mousemove", handleMouseMove);
    document.removeEventListener("mouseup", handleMouseUp);
  };

  document.addEventListener("mousemove", handleMouseMove);
  document.addEventListener("mouseup", handleMouseUp);
};

// ==================== 标准化配置相关方法 ====================

// 模式切换
const switchMode = (mode: 'template' | 'standard') => {
  if (mode === 'standard' && !selectedNode.value) {
    ElMessage.warning("请先选择一个模板");
    return;
  }
  
  const previousMode = currentMode.value;
  currentMode.value = mode;

  // 切换到标准化模式时，确保右侧显示信号标签页，并加载相应数据 - 优化加载
  if (mode === 'standard') {
    activeTabName.value = 'signal';

    // 延迟加载数据，确保组件完全切换后再执行
    if (selectedNode.value) {
      rightPanelLoading.value = true;
      componentReady.value.signal = false;

      addTimer(() => {
        console.log("🔄 切换到标准化模式，加载信号数据");
        const success = safeCallComponentMethod(signalRef, 'loadSignalList', 'Signal');

        if (success) {
          componentReady.value.signal = true;

          // 延迟加载标准化表格数据，避免同时请求
          addTimer(() => {
            safeCallComponentMethod(standardTableRef, 'loadStandardData', 'StandardTable');
            rightPanelLoading.value = false;
          }, 400);
        } else {
          rightPanelLoading.value = false;
        }
      }, 300);
    }
  }
  
  // 从标准化配置切换到模板管理时，确保在树中重新选中当前模板 - 优化性能
  if (previousMode === 'standard' && mode === 'template' && selectedNode.value) {
    // 使用管理的定时器避免频繁的 DOM 操作
    addTimer(() => {
      if (treeRef.value && selectedNode.value && treeRef.value.setCurrentKey) {
        try {
          console.log("🔄 重新选中模板节点:", selectedNode.value.id);
          treeRef.value.setCurrentKey(selectedNode.value.id);

          // 使用管理的定时器确保节点可见
          addTimer(() => {
            const nodeElement = document.querySelector(
              `[data-key="${selectedNode.value?.id}"]`
            );
            if (nodeElement) {
              nodeElement.scrollIntoView({ behavior: "smooth", block: "center" });
            }
          }, 200);
        } catch (error) {
          console.warn('Re-select node error:', error);
        }
      }
    }, 100);
  }
};

// 获取标准化标题
const getStandardTitle = () => {
  switch (activeTabName.value) {
    case 'signal':
      return '信号标准化（仅显示遥信/遥测）';
    case 'event':
      return '告警标准化';
    case 'control':
      return '信号标准化（仅显示遥控/遥调）';
    default:
      return '标准化配置';
  }
};

// 处理信号选择
const handleSignalSelect = (signalData: any) => {
  console.log('选中信号标准化数据:', signalData);
};

// 处理告警选择
const handleAlarmSelect = (alarmData: any) => {
  console.log('选中告警标准化数据:', alarmData);
};

// 处理拖拽开始
const handleDragStart = (data: { data: any; type: string }) => {
  isDragging.value = true;
  dragData.value = data;
  console.log("Drag start:", data);
};

// 处理拖拽结束
const handleDragEnd = () => {
  console.log("Drag end");
  // 重置拖拽状态
  isDragging.value = false;
  dragData.value = null;
  
  // 可以在这里添加一些清理工作，比如重置表格状态等
  nextTick(() => {
    console.log("拖拽状态已重置");
  });
};

// 处理标准化数据拖放
const handleDropStandard = async (data: { targetRow: any; standardData: any; type: string }) => {
  try {
    console.log("🚀 handleDropStandard called! Received data:", data);
    console.log("🚀 Current mode:", currentMode.value);
    console.log("🚀 isDragging:", isDragging.value);

    // 确保在标准化模式下才处理拖拽
    if (currentMode.value !== 'standard') {
      console.log("⚠️ 当前不是标准化模式，忽略拖拽事件");
      return;
    }

    // 验证必要的数据
    if (!data.targetRow || !data.standardData || !data.type) {
      console.log("❌ 拖拽数据不完整:", { targetRow: !!data.targetRow, standardData: !!data.standardData, type: data.type });
      ElMessage.error("拖拽数据不完整，请重试");
      return;
    }

    let updateData: any;
    let standardId: string;
    let apiResponse: any;

    // 添加加载状态提示
    const loadingMessage = ElMessage({
      message: '正在保存标准化配置...',
      type: 'info',
      duration: 0
    });

    try {
      // 根据类型处理不同的拖拽更新
      if (data.type === 'signal') {
        // 处理信号类型的拖拽更新
        updateData = { ...data.targetRow };
        standardId = data.standardData.signalStandardId;
        
        if (!standardId) {
          ElMessage.error("信号标准化ID不能为空");
          return;
        }
        
        updateData.description = standardId;

        console.log("更新信号标准化数据:", updateData);

        // 调用信号更新API
        apiResponse = await updateSignal(updateData as SignalInfo);

        // 检查API调用结果
        if (apiResponse && apiResponse.code === 0) {
          // 更新成功，刷新本地表格数据
          if (signalRef.value?.refreshData) {
            signalRef.value.refreshData(updateData);
          }
          
          ElMessage.success(`信号标准化ID "${standardId}" 设置成功！`);
        } else {
          // 更新失败
          const errorMsg = apiResponse?.msg || "更新信号标准化数据失败";
          ElMessage.error(errorMsg);
          console.error("更新信号标准化数据失败:", apiResponse);
        }

      } else if (data.type === 'event') {
        // 处理事件类型的拖拽更新
        updateData = { ...data.targetRow };
        standardId = data.standardData.alarmStandardId;
        
        if (!standardId) {
          ElMessage.error("告警标准化ID不能为空");
          return;
        }
        
        updateData.description = standardId;

        console.log("更新事件标准化数据:", updateData);

        // 动态导入事件更新API
        const { updateEvent } = await import("@/api/device-template");
        apiResponse = await updateEvent(updateData);

        // 检查API调用结果
        if (apiResponse && apiResponse.code === 0) {
          // 更新成功，刷新本地表格数据
          if (eventRef.value?.refreshData) {
            eventRef.value.refreshData(updateData);
          }
          
          ElMessage.success(`事件标准化ID "${standardId}" 设置成功！`);
        } else {
          // 更新失败
          const errorMsg = apiResponse?.msg || "更新事件标准化数据失败";
          ElMessage.error(errorMsg);
          console.error("更新事件标准化数据失败:", apiResponse);
        }

      } else if (data.type === 'control') {
        // 处理控制类型的拖拽更新
        updateData = { ...data.targetRow };
        standardId = data.standardData.signalStandardId; // 控制使用信号标准化数据
        
        if (!standardId) {
          ElMessage.error("控制标准化ID不能为空");
          return;
        }
        
        updateData.description = standardId;

        console.log("更新控制标准化数据:", updateData);

        // 动态导入控制更新API
        const { updateControl } = await import("@/api/device-template");
        apiResponse = await updateControl(updateData);

        // 检查API调用结果
        if (apiResponse && apiResponse.code === 0) {
          // 更新成功，刷新本地表格数据
          if (controlRef.value?.refreshData) {
            controlRef.value.refreshData(updateData);
          }
          
          ElMessage.success(`控制标准化ID "${standardId}" 设置成功！`);
        } else {
          // 更新失败
          const errorMsg = apiResponse?.msg || "更新控制标准化数据失败";
          ElMessage.error(errorMsg);
          console.error("更新控制标准化数据失败:", apiResponse);
        }

      } else {
        ElMessage.warning(`暂不支持 ${data.type} 类型的拖拽更新`);
        console.log(`暂不支持 ${data.type} 类型的拖拽更新`);
      }
    } finally {
      // 关闭加载提示
      loadingMessage.close();
    }

  } catch (error) {
    console.error("更新标准化数据失败:", error);
    ElMessage.error("更新标准化数据失败，请检查网络连接");
  }
};
</script>

<style scoped>
.device-template-management {
}

.resize-handle {
  position: absolute;
  top: 0;
  right: -2px;
  width: 4px;
  height: 100%;
  cursor: ew-resize;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
}

.resize-handle:hover {
  background-color: rgba(175, 184, 193, 0.2);
}

.resize-line {
  width: 1px;
  height: 100%;
  background-color: var(--el-border-color);
}

/* 布局动画优化 */
.device-template-management {
  /* 确保整体布局平滑 */
  contain: layout;
}

/* 左右面板宽度动画 */
.flex.gap-4 > div {
  /* 防止动画过程中内容跳跃 */
  will-change: width;
  backface-visibility: hidden;
  transform: translateZ(0);
}

/* 面板内容动画优化 */
.bg-white.dark\\:bg-gray-800 {
  /* 确保面板内容在宽度变化时平滑调整 */
  transition: all 0.5s ease-in-out;
}

/* 表格宽度自适应 */
.el-table,
.el-table__body-wrapper,
.el-table__header-wrapper {
  transition: width 0.3s ease-in-out;
}

/* 优化标准化模式下的表格显示 */
.flex-1.overflow-hidden > * {
  transition: all 0.3s ease-in-out;
}

.template-tree {
  padding: 0;
}

.template-tree :deep(.el-tree-node__content) {
  height: 36px;
  padding: 0 16px;
  border-radius: 6px;
  margin-bottom: 4px;
}

.template-tree :deep(.el-tree-node__content:hover) {
  background-color: var(--el-fill-color-light);
}

.template-tree :deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

:deep(.highlight-text) {
  background-color: var(--el-color-warning-light-8);
  color: var(--el-color-warning-dark-2);
  font-weight: bold;
}

/* 自定义标签页样式 */
.custom-tabs {
  height: 100%;
}

.custom-tabs :deep(.el-tabs__header) {
  margin: 0;
  padding-left: 24px; /* 添加左边距 */
  position: relative;
}

.custom-tabs :deep(.el-tabs__nav-wrap) {
  border-bottom: 1px solid var(--el-border-color);
}

.custom-tabs :deep(.el-tabs__content) {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.custom-tabs :deep(.el-tab-pane) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 全局禁止选中样式 */
.device-template-management {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.device-template-management * {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 允许输入框和可编辑内容选中 */
.device-template-management input,
.device-template-management textarea,
.device-template-management [contenteditable="true"],
.device-template-management .el-input__inner,
.device-template-management .el-textarea__inner {
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
}

/* 拖拽时的额外限制 */
:global(.dragging-active *) {
  pointer-events: none;
}

/* 拖拽时保持表格和按钮的交互性 */
:global(.dragging-active .el-table),
:global(.dragging-active .el-table *),
:global(.dragging-active .el-button),
:global(.dragging-active .drop-zone),
:global(.dragging-active .drop-zone *) {
  pointer-events: auto;
}

/* 拖拽时鼠标样式 */
:global(.dragging-active) {
  cursor: grabbing !important;
}

:global(.dragging-active *) {
  cursor: grabbing !important;
}

/* 拖拽时表格行的视觉反馈 */
.device-template-management :deep(.el-table__row) {
  transition: background-color 0.2s ease;
}

.device-template-management :deep(.el-table__row.drag-over) {
  background-color: var(--el-color-primary-light-9) !important;
}

.device-template-management :deep(.el-table__row.drag-source) {
  opacity: 0.6;
  transform: scale(0.98);
  transition: all 0.2s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .device-template-management {
    padding: 16px;
  }

  .device-template-management .flex {
    flex-direction: column;
    height: auto;
  }

  .device-template-management .w-80 {
    width: 100%;
    height: 400px;
    margin-bottom: 24px;
  }
}
</style>
