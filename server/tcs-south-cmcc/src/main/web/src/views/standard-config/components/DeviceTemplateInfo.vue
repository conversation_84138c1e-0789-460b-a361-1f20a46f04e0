<template>
  <div class="device-template-info">
    <el-scrollbar class="h-full">
      <div class="form-container">
        <!-- 第一行：设备模板ID 和 名称 -->
        <div class="form-row">
          <div class="form-item">
            <label class="form-label">设备模板ID</label>
            <el-input
              v-model="formData.equipmentTemplateId"
              class="form-input"
              readonly
              disabled
              type="number"
            />
          </div>
          <div class="form-item">
            <label class="form-label">名称</label>
            <el-input
              v-model="formData.equipmentTemplateName"
              class="form-input"
              readonly
              disabled
            />
          </div>
        </div>

        <!-- 第二行：类型 和 分类 -->
        <div class="form-row">
          <div class="form-item">
            <label class="form-label">类型</label>
            <el-input
              v-model="deviceCategoryDisplay"
              class="form-input"
              readonly
              disabled
            />
          </div>
          <div class="form-item">
            <label class="form-label">分类</label>
            <el-input
              v-model="deviceTypeDisplay"
              class="form-input"
              readonly
              disabled
            />
          </div>
        </div>

        <!-- 第三行：厂商 和 单位 -->
        <div class="form-row">
          <div class="form-item">
            <label class="form-label">厂商</label>
            <el-input
              v-model="formData.vendor"
              class="form-input"
              readonly
              disabled
            />
          </div>
          <div class="form-item">
            <label class="form-label">单位</label>
            <el-input
              v-model="formData.unit"
              class="form-input"
              readonly
              disabled
            />
          </div>
        </div>

        <!-- 第四行：协议编码 和 设备型号 -->
        <div class="form-row">
          <div class="form-item">
            <label class="form-label">协议编码</label>
            <el-input
              v-model="formData.protocolCode"
              class="form-input"
              readonly
              disabled
              :title="formData.protocolCode"
            />
          </div>
          <div class="form-item">
            <label class="form-label">设备型号</label>
            <el-input
              v-model="formData.equipmentStyle"
              class="form-input"
              readonly
              disabled
            />
          </div>
        </div>

        <!-- 第五行：采集器 和 属性 -->
        <div class="form-row">
          <div class="form-item">
            <label class="form-label">采集器</label>
            <el-input
              v-model="formData.samplerName"
              class="form-input"
              readonly
              disabled
            />
          </div>
          <div class="form-item">
            <label class="form-label">属性</label>
            <el-input
              v-model="propertyDisplay"
              class="form-input"
              readonly
              disabled
            />
          </div>
        </div>

        <!-- 第六行：设备基类 和 局站类型 -->

        <!-- 第七行：说明 -->
        <div class="form-row">
          <div class="form-item full-width">
            <label class="form-label">说明</label>
            <el-input
              v-model="formData.description"
              class="form-textarea"
              type="textarea"
              :rows="4"
              readonly
              disabled
            />
          </div>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, computed } from "vue";
import {
  getTemplateInfoById,
  getDeviceTypeList,
  getDeviceCategoryList,
  getPropertyList,
  getVendorList,
  type DeviceTemplateInfo,
  type DataDictionaryItem,
  type VendorItem
} from "@/api/device-template";

interface Props {
  templateData?: any;
}

const props = withDefaults(defineProps<Props>(), {
  templateData: null
});

// 表单数据
const formData = reactive<DeviceTemplateInfo>({
  equipmentTemplateId: null,
  equipmentTemplateName: "",
  equipmentType: null,
  equipmentCategory: null,
  vendor: null,
  unit: null,
  protocolCode: null,
  equipmentStyle: null,
  memo: null,
  propertyList: [],
  equipmentBaseType: null,
  stationCategory: null,
  samplerName: "",
  description: ""
});

// 下拉选项数据
const deviceTypeList = ref<DataDictionaryItem[]>([]);
const deviceCategoryList = ref<DataDictionaryItem[]>([]);
const propertyList = ref<DataDictionaryItem[]>([]);
const vendorList = ref<VendorItem[]>([]);

// 显示文本的计算属性
const deviceCategoryDisplay = computed(() => {
  const category = deviceCategoryList.value.find(item => item.itemId === formData.equipmentCategory);
  return category ? category.itemValue : '';
});

const deviceTypeDisplay = computed(() => {
  const type = deviceTypeList.value.find(item => item.itemId === formData.equipmentType);
  return type ? type.itemValue : '';
});

const propertyDisplay = computed(() => {
  if (!formData.propertyList || formData.propertyList.length === 0) return '';
  const selectedProperties = propertyList.value
    .filter(item => formData.propertyList.includes(item.itemId))
    .map(item => item.itemValue);
  return selectedProperties.join(', ');
});

// 组件初始化
onMounted(() => {
  initializeDictionaryData();
});

// 初始化字典数据
const initializeDictionaryData = async () => {
  try {
    await Promise.all([
      loadDeviceTypeList(),
      loadDeviceCategoryList(),
      loadPropertyList(),
      loadVendorList()
    ]);
  } catch (error) {
    console.error("初始化字典数据失败:", error);
  }
};

// 加载设备类型列表
const loadDeviceTypeList = async () => {
  try {
    const res = await getDeviceTypeList();
    if (res.code === 0) {
      deviceTypeList.value = res.data;
    }
  } catch (error) {
    console.error("获取设备类型列表失败:", error);
  }
};

// 加载设备分类列表
const loadDeviceCategoryList = async () => {
  try {
    const res = await getDeviceCategoryList();
    if (res.code === 0) {
      deviceCategoryList.value = res.data;
    }
  } catch (error) {
    console.error("获取设备分类列表失败:", error);
  }
};

// 加载属性列表
const loadPropertyList = async () => {
  try {
    const res = await getPropertyList();
    if (res.code === 0) {
      propertyList.value = res.data;
    }
  } catch (error) {
    console.error("获取属性列表失败:", error);
  }
};

// 加载厂商列表
const loadVendorList = async () => {
  try {
    const res = await getVendorList();
    if (res.code === 0 && Array.isArray(res.data)) {
      vendorList.value = res.data.map(item => ({
        label: item.itemValue,
        value: item.itemValue
      }));
    }
  } catch (error) {
    console.error("获取厂商列表失败:", error);
  }
};

// 加载模板信息
const loadTemplateInfo = async (templateData: any) => {
  if (!templateData || !templateData.id) return;

  try {
    const res = await getTemplateInfoById(templateData.id);
    if (res.code === 0 && res.data) {
      Object.assign(formData, res.data);
    }
  } catch (error) {
    console.error("获取模板信息失败:", error);
  }
};

// 监听模板数据变化
watch(
  () => props.templateData,
  newData => {
    if (newData && newData.template) {
      loadTemplateInfo(newData);
    }
  },
  { immediate: true }
);
</script>

<style scoped>
.device-template-info {
  width: 100%;
  height: 100%;
  padding: 24px;
}

.form-container {
  max-width: 800px;
}

.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 60px;
}

.form-item {
  display: flex;
  align-items: center;
  min-width: 0;
}

.form-item.full-width {
  width: 100%;
}

.form-label {
  width: 120px;
  text-align: right;
  margin-right: 12px;
  color: var(--el-text-color-regular);
  font-size: 14px;
  flex-shrink: 0;
}

.form-input,
.form-select {
  width: 250px;
}

.form-textarea {
  width: 635px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .form-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .form-item {
    width: 100%;
  }

  .form-input,
  .form-select,
  .form-textarea {
    width: 100%;
    max-width: 400px;
  }
}

@media (max-width: 768px) {
  .device-template-info {
    padding: 16px;
  }

  .form-label {
    width: 100px;
    margin-right: 8px;
  }

  .form-input,
  .form-select,
  .form-textarea {
    max-width: 300px;
  }
}
</style>
