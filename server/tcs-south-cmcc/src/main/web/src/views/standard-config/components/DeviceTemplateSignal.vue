<template>
  <div class="device-template-signal">
    <!-- 表格区域 -->
    <div class="signal-table-container" :class="{ dragging: props.isDragging }">
      <el-table-v2
        ref="tableRef"
        v-loading="loading"
        :columns="tableColumns"
        :data="filteredData"
        :width="tableWidth"
        :height="tableHeight"
        :row-height="40"
        :header-height="40"
        :row-class="getRowClass"
        :row-event-handlers="rowEventHandlers"
        fixed
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, h } from "vue";
import {
  ElIcon,
  ElPopover,
  ElSelect,
  ElOption,
  ElButton
} from "element-plus";
import { Filter } from "@element-plus/icons-vue";
import {
  getSignalList,
  getSignalListByTempIdEqId,
  getSignalCategoryList,
  getSignalTypeList,
  getChannelTypeList,
  getDataTypeList,
  getSignalPropertyList,
  getBatteryDeviceCategory,
  type SignalInfo as BaseSignalInfo,
  type DataDictionaryItem
} from "@/api/device-template";

// 扩展信号信息类型
interface SignalInfo extends BaseSignalInfo {
  [key: string]: any; // 允许动态属性
}

interface Props {
  templateData?: any;
  tabIndex?: number;
  muCategory?: number;
  tableSearchText?: string;
  equipmentId?: string | number;
  buttonFlag?: boolean;
  isRootTemplate?: boolean;
  dragData?: any;
  isDragging?: boolean;
}

interface Emits {
  (e: "refresh"): void;
  (e: "selectTab", data: { index: number }): void;
  (e: "drop-standard", data: { targetRow: any; standardData: any; type: string }): void;
}

const props = withDefaults(defineProps<Props>(), {
  templateData: null,
  tabIndex: 0,
  muCategory: 0,
  tableSearchText: "",
  equipmentId: "",
  buttonFlag: false,
  isRootTemplate: false,
  dragData: null,
  isDragging: false
});

const emit = defineEmits<Emits>();

// 过滤器状态接口
interface FilterState {
  [key: string]: {
    value: any[];
    options: Array<{ label: string; value: any }>;
  };
}

// 状态变量
const loading = ref(false);
const tableData = ref<SignalInfo[]>([]);
const batteryData = ref<any>(null);

// 表格选中相关
const selectedRowIndexes = ref<number[]>([]);
const selectedRows = ref<SignalInfo[]>([]);

// 拖拽目标相关
const dropTargetRowIndex = ref<number>(-1);
const isDropTarget = ref(false);

// 下拉选项数据
const signalCategoryList = ref<DataDictionaryItem[]>([]);
const signalTypeList = ref<DataDictionaryItem[]>([]);
const channelTypeList = ref<DataDictionaryItem[]>([]);
const dataTypeList = ref<DataDictionaryItem[]>([]);
const signalPropertyList = ref<DataDictionaryItem[]>([]);

// 过滤器状态
const filterState = ref<FilterState>({});

// 存储周期选项
const storeIntervalOptions = [
  "86400",
  "28800",
  "14400",
  "3600",
  "1800",
  "600",
  "300",
  "43200",
  "21600",
  "17280",
  "10800",
  "9600",
  "864",
  "7200",
  "5760",
  "5400",
  "4800",
  "4320",
  "3456",
  "3200",
  "2880",
  "2700",
  "2400",
  "2160",
  "1920",
  "1728",
  "1600",
  "1440",
  "1350",
  "1200",
  "1152",
  "1080",
  "960",
  "900",
  "864",
  "800",
  "720",
  "675",
  "640",
  "576",
  "540",
  "480",
  "450",
  "432",
  "400",
  "384",
  "360",
  "320",
  "288",
  "270",
  "240",
  "225",
  "216",
  "200",
  "192",
  "180",
  "160",
  "150",
  "144",
  "135",
  "128",
  "120",
  "108",
  "100",
  "96",
  "90",
  "80",
  "75",
  "72",
  "64",
  "60",
  "54",
  "50",
  "48",
  "45",
  "40",
  "36",
  "32",
  "30",
  "27",
  "25",
  "24",
  "20",
  "18",
  "16",
  "15",
  "12",
  "10",
  "9",
  "8",
  "6",
  "5",
  "4",
  "3",
  "2",
  "1",
  "0"
];

// 创建只读单元格渲染器
const createReadOnlyCell = (column: any) => {
  return ({ rowData, rowIndex }: any) => {
    const fieldKey = column.dataKey;
    let value = rowData[fieldKey];

    // 获取显示值（处理字典值的显示）
    const getDisplayValue = () => {
      switch (fieldKey) {
        case "signalCategory":
          const categoryOption = signalCategoryList.value.find(
            item => item.itemId === value
          );
          return categoryOption ? categoryOption.itemValue : value || "";
        case "signalType":
          const typeOption = signalTypeList.value.find(
            item => item.itemId === value
          );
          return typeOption ? typeOption.itemValue : value || "";
        case "channelType":
          const channelOption = channelTypeList.value.find(
            item => item.itemId === value
          );
          return channelOption ? channelOption.itemValue : value || "";
        case "dataType":
          const dataOption = dataTypeList.value.find(
            item => item.itemId === value
          );
          return dataOption ? dataOption.itemValue : value || "";
        case "signalProperty":
          if (!value) return "";
          const ids = value.toString().split(";").map(Number);
          return ids
            .map((id: number) => {
              const option = signalPropertyList.value.find(
                opt => opt.itemId === id
              );
              return option ? option.itemValue : id;
            })
            .join(", ");
        case "enable":
        case "visible":
          return value ? "是" : "否";
        default:
          return value || "";
      }
    };

    const displayValue = getDisplayValue();

    // 特殊处理标准化ID列，添加删除按钮
    if (fieldKey === 'description') {
      return h(
        "div",
        {
          class: "standard-id-cell",
          style: {
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            padding: "4px 8px",
            minHeight: "24px",
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap"
          }
        },
        [
          h(
            "span",
            {
              style: {
                flex: 1,
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap"
              },
              title: displayValue
            },
            displayValue
          ),
          // 删除按钮（只有当有标准化ID时才显示）
          displayValue ? h(
            ElButton,
            {
              size: "small",
              type: "danger",
              text: true,
              icon: "Delete",
              style: {
                marginLeft: "8px",
                padding: "2px 4px",
                minWidth: "auto"
              },
              onClick: (e: Event) => {
                e.stopPropagation();
                handleRemoveStandardId(rowData);
              }
            },
            () => "删除"
          ) : null
        ]
      );
    }

    return h(
      "div",
      {
        class: "read-only-cell",
        style: {
          padding: "4px 8px",
          minHeight: "24px",
          lineHeight: "24px",
          overflow: "hidden",
          textOverflow: "ellipsis",
          whiteSpace: "nowrap"
        },
        title: displayValue
      },
      displayValue
    );
  };
};

// 初始化过滤器状态
const initFilterState = () => {
  filterState.value = {
    signalName: {
      value: [],
      options: []
    },
    signalId: {
      value: [],
      options: []
    },
    displayIndex: {
      value: [],
      options: []
    },
    signalCategory: {
      value: [],
      options: [
        { label: "模拟量", value: 1 },
        { label: "开关量", value: 2 },
        { label: "脉冲量", value: 3 }
      ]
    },
    signalType: {
      value: [],
      options: [
        { label: "输入信号", value: 1 },
        { label: "输出信号", value: 2 },
        { label: "计算信号", value: 3 }
      ]
    },
    channelNo: {
      value: [],
      options: []
    },
    channelType: {
      value: [],
      options: [
        { label: "AI", value: 1 },
        { label: "DI", value: 2 },
        { label: "AO", value: 3 },
        { label: "DO", value: 4 }
      ]
    },
    expression: {
      value: [],
      options: []
    },
    dataType: {
      value: [],
      options: []
    },
    showPrecision: {
      value: [],
      options: []
    },
    unit: {
      value: [],
      options: []
    },
    storeInterval: {
      value: [],
      options: storeIntervalOptions.map(item => ({
        label: item,
        value: Number(item)
      }))
    },
    enable: {
      value: [],
      options: [
        { label: "是", value: true },
        { label: "否", value: false }
      ]
    },
    visible: {
      value: [],
      options: [
        { label: "是", value: true },
        { label: "否", value: false }
      ]
    },
    description: {
      value: [],
      options: []
    },
    stateValue: {
      value: [],
      options: []
    },
    baseTypeName: {
      value: [],
      options: []
    }
  };
};

// 更新动态选项
const updateDynamicOptions = () => {
  if (tableData.value.length === 0) return;

  // 更新信号名称选项
  const signalNames = [
    ...new Set(tableData.value.map(item => item.signalName).filter(Boolean))
  ];
  filterState.value.signalName.options = signalNames.map(name => ({
    label: name,
    value: name
  }));

  // 更新信号ID选项
  const signalIds = [
    ...new Set(
      tableData.value
        .map(item => item.signalId)
        .filter(id => id !== null && id !== undefined)
    )
  ];
  filterState.value.signalId.options = signalIds.map(id => ({
    label: id.toString(),
    value: id
  }));

  // 更新显示顺序选项
  const displayIndexes = [
    ...new Set(
      tableData.value
        .map(item => item.displayIndex)
        .filter(index => index !== null && index !== undefined)
    )
  ];
  filterState.value.displayIndex.options = displayIndexes.map(index => ({
    label: index.toString(),
    value: index
  }));

  // 更新通道号选项
  const channelNos = [
    ...new Set(
      tableData.value
        .map(item => item.channelNo)
        .filter(no => no !== null && no !== undefined)
    )
  ];
  filterState.value.channelNo.options = channelNos.map(no => ({
    label: no.toString(),
    value: no
  }));

  // 更新表达式选项
  const expressions = [
    ...new Set(tableData.value.map(item => item.expression).filter(Boolean))
  ];
  filterState.value.expression.options = expressions.map(expr => ({
    label: expr,
    value: expr
  }));

  // 更新精度选项
  const precisions = [
    ...new Set(tableData.value.map(item => item.showPrecision).filter(Boolean))
  ];
  filterState.value.showPrecision.options = precisions.map(precision => ({
    label: precision,
    value: precision
  }));

  // 更新单位选项
  const units = [
    ...new Set(tableData.value.map(item => item.unit).filter(Boolean))
  ];
  filterState.value.unit.options = units.map(unit => ({
    label: unit,
    value: unit
  }));

  // 更新说明选项
  const descriptions = [
    ...new Set(tableData.value.map(item => item.description).filter(Boolean))
  ];
  filterState.value.description.options = descriptions.map(desc => ({
    label: desc,
    value: desc
  }));

  // 更新状态值选项
  const stateValues = [
    ...new Set(tableData.value.map(item => item.stateValue).filter(Boolean))
  ];
  filterState.value.stateValue.options = stateValues.map(state => ({
    label: state,
    value: state
  }));

  // 更新基类信号选项
  const baseTypeNames = [
    ...new Set(tableData.value.map(item => item.baseTypeName).filter(Boolean))
  ];
  filterState.value.baseTypeName.options = baseTypeNames.map(name => ({
    label: name,
    value: name
  }));

  // 从字典更新选项
  if (signalCategoryList.value.length > 0) {
    filterState.value.signalCategory.options = signalCategoryList.value.map(
      item => ({
        label: item.itemValue,
        value: item.itemId
      })
    );
  }

  if (signalTypeList.value.length > 0) {
    filterState.value.signalType.options = signalTypeList.value.map(item => ({
      label: item.itemValue,
      value: item.itemId
    }));
  }

  if (channelTypeList.value.length > 0) {
    filterState.value.channelType.options = channelTypeList.value.map(item => ({
      label: item.itemValue,
      value: item.itemId
    }));
  }

  if (dataTypeList.value.length > 0) {
    filterState.value.dataType.options = dataTypeList.value.map(item => ({
      label: item.itemValue,
      value: item.itemId
    }));
  }
};

// 创建过滤器头部组件
const createFilterHeader = (column: any) => {
  const filterKey = column.dataKey;

  return (props: any) => {
    const popoverRef = ref();

    const onFilter = () => {
      popoverRef.value?.hide();
    };

    const onReset = () => {
      if (filterState.value[filterKey]) {
        filterState.value[filterKey].value = [];
      }
    };

    return h("div", { class: "flex items-center justify-center" }, [
      h("span", { class: "mr-2 text-xs" }, props.column.title),
      h(
        ElPopover,
        {
          ref: popoverRef,
          trigger: "click",
          width: 250
        },
        {
          default: () =>
            h("div", { class: "filter-wrapper" }, [
              h("div", { class: "filter-group" }, [
                h(
                  ElSelect,
                  {
                    modelValue: filterState.value[filterKey]?.value || [],
                    "onUpdate:modelValue": (value: any[]) => {
                      if (filterState.value[filterKey]) {
                        filterState.value[filterKey].value = value;
                      }
                    },
                    placeholder: "选择过滤条件",
                    size: "small",
                    multiple: true,
                    collapseTags: true,
                    filterable: true,
                    clearable: true,
                    style: { width: "100%" }
                  },
                  {
                    default: () =>
                      (filterState.value[filterKey]?.options || []).map(
                        (option: any) =>
                          h(ElOption, {
                            key: option.value,
                            label: option.label,
                            value: option.value
                          })
                      )
                  }
                )
              ]),
              h("div", { class: "el-table-v2__demo-filter" }, [
                h(ElButton, { text: true, onClick: onFilter }, () => "确认"),
                h(ElButton, { text: true, onClick: onReset }, () => "重置")
              ])
            ]),
          reference: () =>
            h(ElIcon, { class: "cursor-pointer" }, () => [h(Filter)])
        }
      )
    ]);
  };
};

// 过滤后的数据
const filteredData = computed(() => {
  let data = tableData.value;

  // 首先应用搜索文本过滤
  if (props.tableSearchText && props.tableSearchText.trim() !== "") {
    const searchText = props.tableSearchText.toLowerCase();
    data = data.filter(
      item =>
        item.signalName?.toLowerCase().includes(searchText) ||
        item.signalId?.toString().includes(searchText) ||
        item.description?.toLowerCase().includes(searchText)
    );
  }

  // 应用列过滤器
  Object.entries(filterState.value).forEach(([key, filter]) => {
    if (filter.value && filter.value.length > 0) {
      data = data.filter(item => {
        const itemValue = item[key as keyof SignalInfo];
        return filter.value.includes(itemValue);
      });
    }
  });

  return data;
});

// 虚拟表格配置
const tableWidth = ref(1200);
const tableHeight = ref(500);

// 获取容器尺寸并更新表格尺寸
const updateTableSize = () => {
  // 查找 el-tabs__content 容器
  const tabsContent = document.querySelector(
    ".el-tabs__content"
  ) as HTMLElement;
  if (tabsContent) {
    const rect = tabsContent.getBoundingClientRect();
    tableWidth.value = rect.width - 32; // 减去内边距
    tableHeight.value = rect.height - 32; // 减去内边距
  }
};

// 监听窗口尺寸变化
let resizeObserver: ResizeObserver | null = null;

// 表格列配置
const tableColumns: any[] = [
  {
    key: "signalName",
    title: "名称",
    dataKey: "signalName",
    width: 150,
    cellRenderer: createReadOnlyCell({ dataKey: "signalName" }),
    headerCellRenderer: createFilterHeader({ dataKey: "signalName" })
  },
  {
    key: "signalId",
    title: "信号ID",
    dataKey: "signalId",
    width: 120,
    cellRenderer: createReadOnlyCell({ dataKey: "signalId" }),
    headerCellRenderer: createFilterHeader({ dataKey: "signalId" })
  },
  {
    key: "description",
    title: "标准化ID",
    dataKey: "description",
    width: 250,
    cellRenderer: createReadOnlyCell({ dataKey: "description" }),
    headerCellRenderer: createFilterHeader({ dataKey: "description" })
  },
  {
    key: "displayIndex",
    title: "显示顺序",
    dataKey: "displayIndex",
    width: 100,
    cellRenderer: createReadOnlyCell({ dataKey: "displayIndex" }),
    headerCellRenderer: createFilterHeader({ dataKey: "displayIndex" })
  },
  {
    key: "signalCategory",
    title: "种类",
    dataKey: "signalCategory",
    width: 120,
    cellRenderer: createReadOnlyCell({ dataKey: "signalCategory" }),
    headerCellRenderer: createFilterHeader({ dataKey: "signalCategory" })
  },
  {
    key: "signalType",
    title: "分类",
    dataKey: "signalType",
    width: 120,
    cellRenderer: createReadOnlyCell({ dataKey: "signalType" }),
    headerCellRenderer: createFilterHeader({ dataKey: "signalType" })
  },
  {
    key: "channelNo",
    title: "通道号",
    dataKey: "channelNo",
    width: 90,
    cellRenderer: createReadOnlyCell({ dataKey: "channelNo" }),
    headerCellRenderer: createFilterHeader({ dataKey: "channelNo" })
  },
  {
    key: "channelType",
    title: "通道类型",
    dataKey: "channelType",
    width: 120,
    cellRenderer: createReadOnlyCell({ dataKey: "channelType" }),
    headerCellRenderer: createFilterHeader({ dataKey: "channelType" })
  },
  {
    key: "expression",
    title: "表达式",
    dataKey: "expression",
    width: 170,
    cellRenderer: createReadOnlyCell({ dataKey: "expression" }),
    headerCellRenderer: createFilterHeader({ dataKey: "expression" })
  },
  {
    key: "dataType",
    title: "数据类型",
    dataKey: "dataType",
    width: 180,
    cellRenderer: createReadOnlyCell({ dataKey: "dataType" }),
    headerCellRenderer: createFilterHeader({ dataKey: "dataType" })
  },
  {
    key: "showPrecision",
    title: "精度",
    dataKey: "showPrecision",
    width: 100,
    cellRenderer: createReadOnlyCell({ dataKey: "showPrecision" }),
    headerCellRenderer: createFilterHeader({ dataKey: "showPrecision" })
  },
  {
    key: "unit",
    title: "单位",
    dataKey: "unit",
    width: 100,
    cellRenderer: createReadOnlyCell({ dataKey: "unit" }),
    headerCellRenderer: createFilterHeader({ dataKey: "unit" })
  },
  {
    key: "storeInterval",
    title: "存储周期(秒)",
    dataKey: "storeInterval",
    width: 120,
    cellRenderer: createReadOnlyCell({ dataKey: "storeInterval" }),
    headerCellRenderer: createFilterHeader({ dataKey: "storeInterval" })
  },
  {
    key: "enable",
    title: "有效",
    dataKey: "enable",
    width: 80,
    align: "center",
    cellRenderer: createReadOnlyCell({ dataKey: "enable" }),
    headerCellRenderer: createFilterHeader({ dataKey: "enable" })
  },
  {
    key: "visible",
    title: "可见",
    dataKey: "visible",
    width: 80,
    align: "center",
    cellRenderer: createReadOnlyCell({ dataKey: "visible" }),
    headerCellRenderer: createFilterHeader({ dataKey: "visible" })
  },

  {
    key: "signalProperty",
    title: "信号属性",
    dataKey: "signalProperty",
    width: 150,
    cellRenderer: createReadOnlyCell({ dataKey: "signalProperty" }),
    headerCellRenderer: createFilterHeader({ dataKey: "signalProperty" })
  },
  {
    key: "stateValue",
    title: "状态信号",
    dataKey: "stateValue",
    width: 200,
    cellRenderer: createReadOnlyCell({ dataKey: "stateValue" }),
    headerCellRenderer: createFilterHeader({ dataKey: "stateValue" })
  },
  {
    key: "moduleNo",
    title: "所属模块",
    dataKey: "moduleNo",
    width: 100,
    cellRenderer: createReadOnlyCell({ dataKey: "moduleNo" }),
    headerCellRenderer: createFilterHeader({ dataKey: "moduleNo" })
  },
  {
    key: "baseTypeName",
    title: "基类信号",
    dataKey: "baseTypeName",
    width: 180,
    cellRenderer: createReadOnlyCell({ dataKey: "baseTypeName" }),
    headerCellRenderer: createFilterHeader({ dataKey: "baseTypeName" })
  }
];

// 组件初始化
onMounted(() => {
  // 初始化过滤器状态
  initFilterState();

  initializeDictionaryData();

  // 初始化表格尺寸
  updateTableSize();

  // 设置 ResizeObserver 监听容器尺寸变化
  const tabsContent = document.querySelector(
    ".el-tabs__content"
  ) as HTMLElement;
  if (tabsContent && window.ResizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      updateTableSize();
    });
    resizeObserver.observe(tabsContent);
  }

  // 监听窗口尺寸变化作为备选方案
  window.addEventListener("resize", updateTableSize);
});

// 初始化字典数据
const initializeDictionaryData = async () => {
  try {
    await Promise.all([
      loadSignalCategoryList(),
      loadSignalTypeList(),
      loadChannelTypeList(),
      loadDataTypeList(),
      loadSignalPropertyList(),
      loadBatteryDeviceCategory()
    ]);
  } catch (error) {
    console.error("初始化字典数据失败:", error);
  }
};

// 加载信号种类列表
const loadSignalCategoryList = async () => {
  try {
    const res = await getSignalCategoryList();
    if (res.code === 0) {
      signalCategoryList.value = res.data;
    }
  } catch (error) {
    console.error("获取信号种类列表失败:", error);
  }
};

// 加载信号分类列表
const loadSignalTypeList = async () => {
  try {
    const res = await getSignalTypeList();
    if (res.code === 0) {
      signalTypeList.value = res.data;
    }
  } catch (error) {
    console.error("获取信号分类列表失败:", error);
  }
};

// 加载通道类型列表
const loadChannelTypeList = async () => {
  try {
    const res = await getChannelTypeList();
    if (res.code === 0) {
      channelTypeList.value = res.data;
    }
  } catch (error) {
    console.error("获取通道类型列表失败:", error);
  }
};

// 加载数据类型列表
const loadDataTypeList = async () => {
  try {
    const res = await getDataTypeList();
    if (res.code === 0) {
      dataTypeList.value = res.data;
    }
  } catch (error) {
    console.error("获取数据类型列表失败:", error);
  }
};

// 加载信号属性列表
const loadSignalPropertyList = async () => {
  try {
    const res = await getSignalPropertyList();
    if (res.code === 0) {
      signalPropertyList.value = res.data;
    }
  } catch (error) {
    console.error("获取信号属性列表失败:", error);
  }
};

// 加载电池设备分类
const loadBatteryDeviceCategory = async () => {
  try {
    const res = await getBatteryDeviceCategory();
    if (res.code === 0) {
      batteryData.value = res.data;
    }
  } catch (error) {
    console.error("获取电池设备分类失败:", error);
  }
};

// 加载信号列表
const loadSignalList = async () => {
  if (!props.templateData?.id) return;

  loading.value = true;
  try {
    let res;
    if (props.muCategory === 24 && props.equipmentId) {
      res = await getSignalListByTempIdEqId(
        props.templateData.id,
        props.equipmentId
      );
    } else {
      res = await getSignalList(props.templateData.id);
    }

    if (res.code === 0 && res.data) {
      processSignalData(res.data);
    } else {
      tableData.value = [];
    }
  } catch (error) {
    console.error("获取信号列表失败:", error);
  } finally {
    loading.value = false;
  }
};

// 监听模板数据变化
watch(
  () => props.templateData,
  newData => {
    if (newData && newData.template && props.tabIndex === 1) {
      loadSignalList();
    }
  },
  { immediate: true }
);

// 处理信号数据
const processSignalData = (data: SignalInfo[]) => {
  data.forEach(item => {
    // 处理状态信号显示
    if (item.signalMeaningsList && item.signalMeaningsList.length > 0) {
      item.stateValue = item.signalMeaningsList
        .map((meaning: any) => meaning.stateValue)
        .join("/");
    }

    // 处理信号属性显示
    if (item.signalPropertyList && item.signalPropertyList.length > 0) {
      item.signalProperty = item.signalPropertyList
        .map((prop: any) => prop.signalPropertyId)
        .join(";");
    }
  });

  tableData.value = data;

  // 数据加载后更新过滤器选项
  updateDynamicOptions();
};

// 行事件处理器
const rowEventHandlers = {
  onClick: ({ rowData, rowIndex, event }: any) => {
    console.log("Row clicked:", rowIndex, "isDragging:", props.isDragging, "dragData:", props.dragData);

    // 如果正在拖拽，不处理选中逻辑
    if (props.isDragging && props.dragData) {
      console.log("Dragging in progress, ignoring click");
      return;
    }

    // 单击时选中行
    const clickedRowIndex = filteredData.value.findIndex(
      item => item.signalId === rowData.signalId
    );
    if (clickedRowIndex !== -1) {
      // 如果按住Ctrl键，支持多选
      if (event.ctrlKey || event.metaKey) {
        const isSelected = selectedRowIndexes.value.includes(clickedRowIndex);
        if (isSelected) {
          // 取消选中
          selectedRowIndexes.value = selectedRowIndexes.value.filter(
            index => index !== clickedRowIndex
          );
        } else {
          // 添加选中
          selectedRowIndexes.value.push(clickedRowIndex);
        }
      } else {
        // 单选
        selectedRowIndexes.value = [clickedRowIndex];
      }

      // 更新选中的行数据
      selectedRows.value = selectedRowIndexes.value.map(
        index => filteredData.value[index]
      );
    }
  },
  onMouseup: ({ rowIndex }: any) => {
    console.log("Mouse up on row:", rowIndex, "isDragging:", props.isDragging, "dragData:", props.dragData);

    // 鼠标松开时处理拖放
    if (props.isDragging && props.dragData) {
      console.log("Triggering drop for row:", rowIndex);
      handleDrop(rowIndex);
    }
  },
  onMouseenter: ({ rowIndex }: any) => {
    handleRowMouseEnter(rowIndex);
  },
  onMouseleave: () => {
    handleRowMouseLeave();
  }
};

// 获取行样式类
const getRowClass = ({ rowIndex }: { rowIndex: number }) => {
  const isSelected = selectedRowIndexes.value.includes(rowIndex);
  const isDropTargetRow = dropTargetRowIndex.value === rowIndex && isDropTarget.value;

  let classes = "";
  if (isSelected) classes += "selected-row ";
  if (isDropTargetRow) classes += "drop-target-row ";

  return classes.trim();
};

// 拖拽目标相关方法
const handleRowMouseEnter = (rowIndex: number) => {
  console.log("Mouse enter row:", rowIndex, "isDragging:", props.isDragging, "dragData:", props.dragData);
  if (props.isDragging && props.dragData) {
    dropTargetRowIndex.value = rowIndex;
    isDropTarget.value = true;
    console.log("Set drop target row:", rowIndex);
  }
};

const handleRowMouseLeave = () => {
  console.log("Mouse leave, isDragging:", props.isDragging);
  if (props.isDragging) {
    dropTargetRowIndex.value = -1;
    isDropTarget.value = false;
    console.log("Clear drop target");
  }
};

// 处理拖放事件
const handleDrop = (rowIndex: number) => {
  console.log("handleDrop called:", { 
    rowIndex, 
    isDragging: props.isDragging, 
    dragData: props.dragData,
    dropTargetRowIndex: dropTargetRowIndex.value
  });

  if (props.isDragging && props.dragData && dropTargetRowIndex.value === rowIndex) {
    const targetRow = filteredData.value[rowIndex];
    if (targetRow) {
      console.log("Emitting drop-standard event:", {
        targetRow,
        standardData: props.dragData.data,
        type: "signal"
      });
      
      emit("drop-standard", {
        targetRow,
        standardData: props.dragData.data,
        type: "signal"
      });
    }
  }

  // 重置拖拽状态
  dropTargetRowIndex.value = -1;
  isDropTarget.value = false;
};

// 删除标准化ID
const handleRemoveStandardId = async (rowData: any) => {
  try {
    const updateData = { ...rowData };
    updateData.description = ""; // 清空标准化ID

    console.log("删除标准化ID:", updateData);

    // 调用更新API，将标准化ID设置为空
    const { updateSignal } = await import("@/api/device-template");
    const apiResponse = await updateSignal(updateData);

    // 检查API调用结果
    if (apiResponse && apiResponse.code === 0) {
      // 更新成功，更新本地数据
      const index = tableData.value.findIndex(item => item.signalId === rowData.signalId);
      if (index !== -1) {
        tableData.value[index].description = "";
      }
      
      // 显示成功消息
      const { ElMessage } = await import("element-plus");
      ElMessage.success("标准化ID删除成功！");
    } else {
      // 更新失败
      const errorMsg = apiResponse?.msg || "删除标准化ID失败";
      const { ElMessage } = await import("element-plus");
      ElMessage.error(errorMsg);
      console.error("删除标准化ID失败:", apiResponse);
    }

  } catch (error) {
    console.error("删除标准化ID失败:", error);
    const { ElMessage } = await import("element-plus");
    ElMessage.error("删除标准化ID失败");
  }
};

// 刷新数据方法
const refreshData = (updatedRow: any) => {
  const index = tableData.value.findIndex(item => item.signalId === updatedRow.signalId);
  if (index !== -1) {
    tableData.value[index] = { ...tableData.value[index], ...updatedRow };
  }
};

// 组件卸载时清理监听器
onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
  // 清理选中状态
  selectedRowIndexes.value = [];
  selectedRows.value = [];
  window.removeEventListener("resize", updateTableSize);
});

// 暴露方法给父组件
defineExpose({
  loadSignalList,
  refreshData
});
</script>

<style scoped>
.device-template-signal {
  width: 100%;
  height: 100%;
}

.signal-table-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* 只读单元格样式 */
.read-only-cell {
  padding: 4px 8px;
  min-height: 24px;
  line-height: 24px;
  color: var(--el-text-color-regular);
}

/* 过滤器样式 */
.el-table-v2__demo-filter {
  border-top: var(--el-border);
  margin: 12px -12px -12px;
  padding: 0 12px;
  display: flex;
  justify-content: space-between;
}

.filter-wrapper {
  padding: 8px 0;
}

.filter-group {
  margin-bottom: 8px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.text-xs {
  font-size: 12px;
}

.mr-2 {
  margin-right: 8px;
}

.cursor-pointer {
  cursor: pointer;
}

/* 全局表格样式 */
:deep(.el-table) {
  font-size: 12px;
}

:deep(.el-table .cell) {
  padding: 4px 8px;
}

/* 选中行样式 */
:deep(.selected-row) {
  background-color: var(--el-color-primary-light-9) !important;
}

:deep(.selected-row:hover) {
  background-color: var(--el-color-primary-light-8) !important;
}



/* 表格行样式优化 - 确保整行都能响应拖拽 */
:deep(.el-table-v2__row) {
  position: relative;
  cursor: pointer;
}

:deep(.el-table-v2__row-cell) {
  position: relative;
  z-index: 1;
  pointer-events: auto;
}

/* 确保行间空白区域也能响应鼠标事件 */
:deep(.el-table-v2__row) {
  background-clip: padding-box;
}

/* 当正在拖拽时，为目标行添加视觉反馈 */
:deep(.drop-target-row) {
  background-color: var(--el-color-success-light-9) !important;
  border: 2px dashed var(--el-color-success) !important;
  box-sizing: border-box;
}

/* 拖拽时的鼠标样式 */
:deep(.el-table-v2__row):hover {
  background-color: var(--el-fill-color-light);
}

/* 当有拖拽数据时，整个表格区域显示可放置状态 */
.signal-table-container.dragging {
  position: relative;
}

.signal-table-container.dragging :deep(.el-table-v2__row):hover {
  background-color: var(--el-color-success-light-9);
  cursor: copy;
}

/* 确保拖拽目标行的边框不被其他元素遮挡 */
:deep(.drop-target-row) {
  position: relative;
  z-index: 10;
}

/* 标准化ID单元格样式 */
.standard-id-cell {
  position: relative;
}

.standard-id-cell:hover .delete-btn {
  opacity: 1;
}
</style>