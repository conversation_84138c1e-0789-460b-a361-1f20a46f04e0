<template>
  <div class="device-template-event">
    <!-- 表格区域 -->
    <div class="event-table-container" :class="{ dragging: props.isDragging }">
      <el-table-v2
        ref="tableRef"
        v-loading="loading"
        :columns="tableColumns"
        :data="filteredData"
        :width="tableWidth"
        :height="tableHeight"
        :row-height="40"
        :header-height="40"
        :row-class="getRowClass"
        :row-event-handlers="rowEventHandlers"
        fixed
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, h } from "vue";
import {
  ElIcon,
  ElPopover,
  ElSelect,
  ElOption,
  ElButton
} from "element-plus";
import { Filter } from "@element-plus/icons-vue";
import {
  getTemplateEventById,
  getSignalList,
  getEventCategoryList,
  getStartTypeList,
  getEndTypeList,
  type DataDictionaryItem,
  type EventInfo as BaseEventInfo
} from "@/api/device-template";

// 扩展事件信息接口
export interface EventInfo extends BaseEventInfo {
  [key: string]: any; // 允许动态属性
}

interface Props {
  templateData?: any;
  tabIndex?: number;
  muCategory?: number;
  tableSearchText?: string;
  equipmentId?: string | number;
  buttonFlag?: boolean;
  isRootTemplate?: boolean;
  dragData?: any;
  isDragging?: boolean;
}

interface Emits {
  (e: "refresh"): void;
  (e: "selectTab", data: { index: number }): void;
  (e: "drop-standard", data: { targetRow: any; standardData: any; type: string }): void;
}

const props = withDefaults(defineProps<Props>(), {
  templateData: null,
  tabIndex: 0,
  muCategory: 0,
  tableSearchText: "",
  equipmentId: "",
  buttonFlag: false,
  isRootTemplate: false,
  dragData: null,
  isDragging: false
});

const emit = defineEmits<Emits>();

// 过滤器状态接口
interface FilterState {
  [key: string]: {
    value: any[];
    options: Array<{ label: string; value: any }>;
  };
}

// 状态变量
const loading = ref(false);
const tableData = ref<EventInfo[]>([]);

// 表格选中相关
const selectedRowIndexes = ref<number[]>([]);
const selectedRows = ref<EventInfo[]>([]);

// 拖拽目标相关
const dropTargetRowIndex = ref<number>(-1);
const isDropTarget = ref(false);

// 下拉选项数据
const eventCategoryList = ref<DataDictionaryItem[]>([]);
const startTypeList = ref<DataDictionaryItem[]>([]);
const endTypeList = ref<DataDictionaryItem[]>([]);
const signalList = ref<any[]>([]);

// 过滤器状态
const filterState = ref<FilterState>({});

/**
 * 更新表格行数据
 * @param apiType API类型 (signal | event | control)
 * @param rowData 行数据
 */
const updateTableRow = async (apiType: string, rowData: any) => {
  const { http } = await import("@/utils/http");
  return http.request<any>(
    "put",
    `/api/thing/south-omc-siteweb/${apiType}/update`,
    { data: rowData }
  );
};

// 创建只读单元格渲染器
const createReadOnlyCell = (column: any) => {
  return ({ rowData, rowIndex }: any) => {
    const fieldKey = column.dataKey;
    let value = rowData[fieldKey];

    // 获取显示值（处理字典值的显示）
    const getDisplayValue = () => {
      switch (fieldKey) {
        case "eventCategory":
          const categoryOption = eventCategoryList.value.find(
            item => item.itemId === value
          );
          return categoryOption ? categoryOption.itemValue : value || "";
        case "startType":
          const startTypeOption = startTypeList.value.find(
            item => item.itemId === value
          );
          return startTypeOption ? startTypeOption.itemValue : value || "";
        case "endType":
          const endTypeOption = endTypeList.value.find(
            item => item.itemId === value
          );
          return endTypeOption ? endTypeOption.itemValue : value || "";
        case "signalId":
          const signalOption = signalList.value.find(
            item => item.id === value
          );
          return signalOption ? signalOption.name : value || "";
        case "enable":
        case "visible":
          return value ? "是" : "否";
        default:
          return value || "";
      }
    };

    const displayValue = getDisplayValue();

    // 特殊处理标准化ID列，添加删除按钮
    if (fieldKey === 'description') {
      return h(
        "div",
        {
          class: "standard-id-cell",
          style: {
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            padding: "4px 8px",
            minHeight: "24px",
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap"
          }
        },
        [
          h(
            "span",
            {
              style: {
                flex: 1,
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap"
              },
              title: displayValue
            },
            displayValue
          ),
          // 删除按钮（只有当有标准化ID时才显示）
          displayValue ? h(
            ElButton,
            {
              size: "small",
              type: "danger",
              text: true,
              icon: "Delete",
              style: {
                marginLeft: "8px",
                padding: "2px 4px",
                minWidth: "auto"
              },
              onClick: (e: Event) => {
                e.stopPropagation();
                handleRemoveStandardId(rowData);
              }
            },
            () => "删除"
          ) : null
        ]
      );
    }

    return h(
      "div",
      {
        class: "read-only-cell"
      },
      displayValue
    );
  };
};

// 初始化过滤器状态
const initFilterState = () => {
  filterState.value = {
    eventName: {
      value: [],
      options: []
    },
    eventId: {
      value: [],
      options: []
    },
    displayIndex: {
      value: [],
      options: []
    },
    eventCategory: {
      value: [],
      options: []
    },
    startType: {
      value: [],
      options: []
    },
    endType: {
      value: [],
      options: []
    },
    signalId: {
      value: [],
      options: []
    },
    startExpression: {
      value: [],
      options: []
    },
    suppressExpression: {
      value: [],
      options: []
    },
    eventConditionListLabel: {
      value: [],
      options: []
    },
    enable: {
      value: [],
      options: []
    },
    visible: {
      value: [],
      options: []
    },
    description: {
      value: [],
      options: []
    },
    moduleNo: {
      value: [],
      options: []
    },
    turnover: {
      value: [],
      options: []
    }
  };
};

// 更新动态选项
const updateDynamicOptions = () => {
  if (tableData.value.length === 0) return;

  // 更新事件名称选项
  const eventNames = [
    ...new Set(tableData.value.map(item => item.eventName).filter(Boolean))
  ];
  filterState.value.eventName.options = eventNames.map(name => ({
    label: name,
    value: name
  }));

  // 更新事件ID选项
  const eventIds = [
    ...new Set(
      tableData.value
        .map(item => item.eventId)
        .filter(id => id !== null && id !== undefined)
    )
  ];
  filterState.value.eventId.options = eventIds.map(id => ({
    label: id.toString(),
    value: id
  }));

  // 更新显示顺序选项
  const displayIndexes = [
    ...new Set(
      tableData.value
        .map(item => item.displayIndex)
        .filter(index => index !== null && index !== undefined)
    )
  ];
  filterState.value.displayIndex.options = displayIndexes.map(index => ({
    label: index.toString(),
    value: index
  }));

  // 更新信号ID选项
  const signalIds = [
    ...new Set(
      tableData.value
        .map(item => item.signalId)
        .filter(id => id !== null && id !== undefined)
    )
  ];
  filterState.value.signalId.options = signalIds.map(id => ({
    label: id.toString(),
    value: id
  }));

  // 更新开始表达式选项
  const startExpressions = [
    ...new Set(
      tableData.value.map(item => item.startExpression).filter(Boolean)
    )
  ];
  filterState.value.startExpression.options = startExpressions.map(expr => ({
    label: expr,
    value: expr
  }));

  // 更新抑制表达式选项
  const suppressExpressions = [
    ...new Set(
      tableData.value.map(item => item.suppressExpression).filter(Boolean)
    )
  ];
  filterState.value.suppressExpression.options = suppressExpressions.map(
    expr => ({ label: expr, value: expr })
  );

  // 更新事件条件列表标签选项
  const eventConditionListLabels = [
    ...new Set(
      tableData.value.map(item => item.eventConditionListLabel).filter(Boolean)
    )
  ];
  filterState.value.eventConditionListLabel.options =
    eventConditionListLabels.map(label => ({ label: label, value: label }));

  // 更新说明选项
  const descriptions = [
    ...new Set(tableData.value.map(item => item.description).filter(Boolean))
  ];
  filterState.value.description.options = descriptions.map(desc => ({
    label: desc,
    value: desc
  }));

  // 从字典更新选项
  if (eventCategoryList.value.length > 0) {
    filterState.value.eventCategory.options = eventCategoryList.value.map(
      item => ({
        label: item.itemValue,
        value: item.itemId
      })
    );
  }

  if (startTypeList.value.length > 0) {
    filterState.value.startType.options = startTypeList.value.map(item => ({
      label: item.itemValue,
      value: item.itemId
    }));
  }

  if (endTypeList.value.length > 0) {
    filterState.value.endType.options = endTypeList.value.map(item => ({
      label: item.itemValue,
      value: item.itemId
    }));
  }

  if (signalList.value.length > 0) {
    filterState.value.signalId.options = signalList.value.map(item => ({
      label: item.name,
      value: item.id
    }));
  }

  // 设置布尔值字段的过滤选项
  filterState.value.enable.options = [
    { label: "是", value: true },
    { label: "否", value: false }
  ];

  filterState.value.visible.options = [
    { label: "是", value: true },
    { label: "否", value: false }
  ];
};

// 创建过滤器头部组件
const createFilterHeader = (column: any) => {
  const filterKey = column.dataKey;

  return (props: any) => {
    const popoverRef = ref();

    const onFilter = () => {
      popoverRef.value?.hide();
    };

    const onReset = () => {
      if (filterState.value[filterKey]) {
        filterState.value[filterKey].value = [];
      }
    };

    return h("div", { class: "flex items-center justify-center" }, [
      h("span", { class: "mr-2 text-xs" }, props.column.title),
      h(
        ElPopover,
        {
          ref: popoverRef,
          trigger: "click",
          width: 250
        },
        {
          default: () =>
            h("div", { class: "filter-wrapper" }, [
              h("div", { class: "filter-group" }, [
                h(
                  ElSelect,
                  {
                    modelValue: filterState.value[filterKey]?.value || [],
                    "onUpdate:modelValue": (value: any[]) => {
                      if (filterState.value[filterKey]) {
                        filterState.value[filterKey].value = value;
                      }
                    },
                    placeholder: "选择过滤条件",
                    size: "small",
                    multiple: true,
                    collapseTags: true,
                    filterable: true,
                    clearable: true,
                    style: { width: "100%" }
                  },
                  {
                    default: () =>
                      (filterState.value[filterKey]?.options || []).map(
                        (option: any) =>
                          h(ElOption, {
                            key: option.value,
                            label: option.label,
                            value: option.value
                          })
                      )
                  }
                )
              ]),
              h("div", { class: "el-table-v2__demo-filter" }, [
                h(ElButton, { text: true, onClick: onFilter }, () => "确认"),
                h(ElButton, { text: true, onClick: onReset }, () => "重置")
              ])
            ]),
          reference: () =>
            h(ElIcon, { class: "cursor-pointer" }, () => [h(Filter)])
        }
      )
    ]);
  };
};

// 过滤后的数据
const filteredData = computed(() => {
  let data = tableData.value;

  // 首先应用搜索文本过滤
  if (props.tableSearchText && props.tableSearchText.trim() !== "") {
    const searchText = props.tableSearchText.toLowerCase();
    data = data.filter(
      item =>
        item.eventName?.toLowerCase().includes(searchText) ||
        item.eventId?.toString().includes(searchText) ||
        item.description?.toLowerCase().includes(searchText)
    );
  }

  // 应用列过滤器
  Object.entries(filterState.value).forEach(([key, filter]) => {
    if (filter.value && filter.value.length > 0) {
      data = data.filter(item => {
        const itemValue = item[key as keyof EventInfo];
        return filter.value.includes(itemValue);
      });
    }
  });

  return data;
});

// 虚拟表格配置
const tableWidth = ref(1200);
const tableHeight = ref(500);

// 获取容器尺寸并更新表格尺寸
const updateTableSize = () => {
  // 查找 el-tabs__content 容器
  const tabsContent = document.querySelector(
    ".el-tabs__content"
  ) as HTMLElement;
  if (tabsContent) {
    const rect = tabsContent.getBoundingClientRect();
    tableWidth.value = rect.width - 32; // 减去内边距
    tableHeight.value = rect.height - 32; // 减去内边距
  }
};

// 监听窗口尺寸变化
let resizeObserver: ResizeObserver | null = null;

// 表格列配置
const tableColumns: any[] = [
  {
    key: "eventName",
    title: "名称",
    dataKey: "eventName",
    width: 150,
    cellRenderer: createReadOnlyCell({ dataKey: "eventName" }),
    headerCellRenderer: createFilterHeader({ dataKey: "eventName" })
  },
  {
    key: "eventId",
    title: "事件ID",
    dataKey: "eventId",
    width: 100,
    cellRenderer: createReadOnlyCell({ dataKey: "eventId" }),
    headerCellRenderer: createFilterHeader({ dataKey: "eventId" })
  },
  {
    key: "description",
    title: "标准化ID",
    dataKey: "description",
    width: 250,
    cellRenderer: createReadOnlyCell({ dataKey: "description" }),
    headerCellRenderer: createFilterHeader({ dataKey: "description" })
  },
  {
    key: "displayIndex",
    title: "显示顺序",
    dataKey: "displayIndex",
    width: 100,
    cellRenderer: createReadOnlyCell({ dataKey: "displayIndex" }),
    headerCellRenderer: createFilterHeader({ dataKey: "displayIndex" })
  },
  {
    key: "eventCategory",
    title: "种类",
    dataKey: "eventCategory",
    width: 120,
    cellRenderer: createReadOnlyCell({ dataKey: "eventCategory" }),
    headerCellRenderer: createFilterHeader({ dataKey: "eventCategory" })
  },
  {
    key: "startType",
    title: "开始类型",
    dataKey: "startType",
    width: 120,
    cellRenderer: createReadOnlyCell({ dataKey: "startType" }),
    headerCellRenderer: createFilterHeader({ dataKey: "startType" })
  },
  {
    key: "endType",
    title: "结束类型",
    dataKey: "endType",
    width: 120,
    cellRenderer: createReadOnlyCell({ dataKey: "endType" }),
    headerCellRenderer: createFilterHeader({ dataKey: "endType" })
  },
  {
    key: "eventConditionListLabel",
    title: "条件",
    dataKey: "eventConditionListLabel",
    width: 120,
    cellRenderer: createReadOnlyCell({ dataKey: "eventConditionListLabel" }),
    headerCellRenderer: createFilterHeader({
      dataKey: "eventConditionListLabel"
    })
  },
  {
    key: "startExpression",
    title: "开始表达式",
    dataKey: "startExpression",
    width: 150,
    cellRenderer: createReadOnlyCell({ dataKey: "startExpression" }),
    headerCellRenderer: createFilterHeader({ dataKey: "startExpression" })
  },
  {
    key: "suppressExpression",
    title: "抑制表达式",
    dataKey: "suppressExpression",
    width: 150,
    cellRenderer: createReadOnlyCell({ dataKey: "suppressExpression" }),
    headerCellRenderer: createFilterHeader({ dataKey: "suppressExpression" })
  },
  {
    key: "signalId",
    title: "关联信号",
    dataKey: "signalId",
    width: 150,
    cellRenderer: createReadOnlyCell({ dataKey: "signalId" }),
    headerCellRenderer: createFilterHeader({ dataKey: "signalId" })
  },
  {
    key: "enable",
    title: "有效",
    dataKey: "enable",
    width: 80,
    align: "center",
    cellRenderer: createReadOnlyCell({ dataKey: "enable" }),
    headerCellRenderer: createFilterHeader({ dataKey: "enable" })
  },
  {
    key: "visible",
    title: "可见",
    dataKey: "visible",
    width: 80,
    align: "center",
    cellRenderer: createReadOnlyCell({ dataKey: "visible" }),
    headerCellRenderer: createFilterHeader({ dataKey: "visible" })
  },
  {
    key: "moduleNo",
    title: "所属模块",
    dataKey: "moduleNo",
    width: 100,
    cellRenderer: createReadOnlyCell({ dataKey: "moduleNo" }),
    headerCellRenderer: createFilterHeader({ dataKey: "moduleNo" })
  },
  {
    key: "turnover",
    title: "翻转时间",
    dataKey: "turnover",
    width: 100,
    cellRenderer: createReadOnlyCell({ dataKey: "turnover" }),
    headerCellRenderer: createFilterHeader({ dataKey: "turnover" })
  }
];

// 组件初始化
onMounted(() => {
  // 初始化过滤器状态
  initFilterState();

  initializeDictionaryData();

  // 初始化表格尺寸
  updateTableSize();

  // 设置 ResizeObserver 监听容器尺寸变化
  const tabsContent = document.querySelector(
    ".el-tabs__content"
  ) as HTMLElement;
  if (tabsContent && window.ResizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      updateTableSize();
    });
    resizeObserver.observe(tabsContent);
  }

  // 监听窗口尺寸变化作为备选方案
  window.addEventListener("resize", updateTableSize);
});

// 加载事件列表
const loadEventList = async () => {
  if (!props.templateData?.id) return;

  try {
    loading.value = true;
    const res = await getTemplateEventById(props.templateData.id);
    if (res.code === 0 && res.data) {
      processEventData(res.data);
    } else {
      tableData.value = [];
    }
  } catch (error) {
    console.error("获取事件列表失败:", error);
    tableData.value = [];
  } finally {
    loading.value = false;
  }
};

// 监听模板数据变化
watch(
  () => props.templateData,
  newData => {
    if (newData && newData.template && props.tabIndex === 2) {
      loadEventList();
    }
  },
  { immediate: true }
);

// 初始化字典数据
const initializeDictionaryData = async () => {
  try {
    await Promise.all([
      loadEventCategoryList(),
      loadStartTypeList(),
      loadEndTypeList(),
      loadSignalList()
    ]);
  } catch (error) {
    console.error("初始化字典数据失败:", error);
  }
};

// 加载事件种类列表
const loadEventCategoryList = async () => {
  try {
    const res = await getEventCategoryList();
    if (res.code === 0) {
      eventCategoryList.value = res.data;
    }
  } catch (error) {
    console.error("获取事件种类列表失败:", error);
  }
};

// 加载开始类型列表
const loadStartTypeList = async () => {
  try {
    const res = await getStartTypeList();
    if (res.code === 0) {
      startTypeList.value = res.data;
    }
  } catch (error) {
    console.error("获取开始类型列表失败:", error);
  }
};

// 加载结束类型列表
const loadEndTypeList = async () => {
  try {
    const res = await getEndTypeList();
    if (res.code === 0) {
      endTypeList.value = res.data;
    }
  } catch (error) {
    console.error("获取结束类型列表失败:", error);
  }
};

// 加载信号列表
const loadSignalList = async () => {
  if (!props.templateData?.id) return;

  try {
    const res = await getSignalList(props.templateData.id);
    if (res.code === 0 && res.data) {
      signalList.value = res.data.map((signal: any) => ({
        id: signal.signalId,
        name: signal.signalName
      }));
    }
  } catch (error) {
    console.error("获取信号列表失败:", error);
  }
};

// 处理事件数据
const processEventData = (data: EventInfo[]) => {
  data.forEach(item => {
    // 处理事件条件列表标签 - 与Angular版本保持一致
    if (item.eventConditionList && item.eventConditionList.length > 0) {
      let text = "";
      item.eventConditionList.forEach((obj: any) => {
        if (text.length === 0) {
          text = obj["eventConditionId"].toString();
        } else {
          text = text + "/" + obj["eventConditionId"].toString();
        }
      });
      item.eventConditionListLabel = text;
    } else {
      item.eventConditionListLabel = "";
    }
  });

  tableData.value = data;

  // 数据加载后更新过滤器选项
  updateDynamicOptions();
};

// 行事件处理器
const rowEventHandlers = {
  onClick: ({ rowData, rowIndex, event }: any) => {
    console.log("Row clicked:", rowIndex, "isDragging:", props.isDragging, "dragData:", props.dragData);

    // 如果正在拖拽，不处理选中逻辑
    if (props.isDragging && props.dragData) {
      console.log("Dragging in progress, ignoring click");
      return;
    }

    // 单击时选中行
    const clickedRowIndex = filteredData.value.findIndex(
      item => item.eventId === rowData.eventId
    );
    if (clickedRowIndex !== -1) {
      // 如果按住Ctrl键，支持多选
      if (event.ctrlKey || event.metaKey) {
        const isSelected = selectedRowIndexes.value.includes(clickedRowIndex);
        if (isSelected) {
          // 取消选中
          selectedRowIndexes.value = selectedRowIndexes.value.filter(
            index => index !== clickedRowIndex
          );
        } else {
          // 添加选中
          selectedRowIndexes.value.push(clickedRowIndex);
        }
      } else {
        // 单选
        selectedRowIndexes.value = [clickedRowIndex];
      }

      // 更新选中的行数据
      selectedRows.value = selectedRowIndexes.value.map(
        index => filteredData.value[index]
      );
    }
  },
  onMouseup: ({ rowIndex }: any) => {
    console.log("Mouse up on row:", rowIndex, "isDragging:", props.isDragging, "dragData:", props.dragData);

    // 鼠标松开时处理拖放
    if (props.isDragging && props.dragData) {
      console.log("Triggering drop for row:", rowIndex);
      handleDrop(rowIndex);
    }
  },
  onMouseenter: ({ rowIndex }: any) => {
    handleRowMouseEnter(rowIndex);
  },
  onMouseleave: () => {
    handleRowMouseLeave();
  }
};

// 获取行样式类
const getRowClass = ({ rowIndex }: { rowIndex: number }) => {
  const isSelected = selectedRowIndexes.value.includes(rowIndex);
  const isDropTargetRow = dropTargetRowIndex.value === rowIndex && isDropTarget.value;

  let classes = "";
  if (isSelected) classes += "selected-row ";
  if (isDropTargetRow) classes += "drop-target-row ";

  return classes.trim();
};

// 拖拽目标相关方法
const handleRowMouseEnter = (rowIndex: number) => {
  console.log("Mouse enter row:", rowIndex, "isDragging:", props.isDragging, "dragData:", props.dragData);
  if (props.isDragging && props.dragData) {
    dropTargetRowIndex.value = rowIndex;
    isDropTarget.value = true;
    console.log("Set drop target row:", rowIndex);
  }
};

const handleRowMouseLeave = () => {
  console.log("Mouse leave, isDragging:", props.isDragging);
  if (props.isDragging) {
    dropTargetRowIndex.value = -1;
    isDropTarget.value = false;
    console.log("Clear drop target");
  }
};

// 处理拖放事件
const handleDrop = (rowIndex: number) => {
  console.log("handleDrop called:", { 
    rowIndex, 
    isDragging: props.isDragging, 
    dragData: props.dragData,
    dropTargetRowIndex: dropTargetRowIndex.value
  });

  if (props.isDragging && props.dragData && dropTargetRowIndex.value === rowIndex) {
    const targetRow = filteredData.value[rowIndex];
    if (targetRow) {
      console.log("Emitting drop-standard event:", {
        targetRow,
        standardData: props.dragData.data,
        type: "event"
      });
      
      emit("drop-standard", {
        targetRow,
        standardData: props.dragData.data,
        type: "event"
      });
    }
  }

  // 重置拖拽状态
  dropTargetRowIndex.value = -1;
  isDropTarget.value = false;
};

// 删除标准化ID
const handleRemoveStandardId = async (rowData: any) => {
  try {
    const updateData = { ...rowData };
    updateData.description = ""; // 清空标准化ID

    console.log("删除事件标准化ID:", updateData);

    // 调用更新API，将标准化ID设置为空
    const apiResponse = await updateTableRow("event", updateData);

    // 检查API调用结果
    if (apiResponse && apiResponse.code === 0) {
      // 更新成功，更新本地数据
      const index = tableData.value.findIndex(item => item.eventId === rowData.eventId);
      if (index !== -1) {
        tableData.value[index].description = "";
      }
      
      // 显示成功消息
      const { ElMessage } = await import("element-plus");
      ElMessage.success("标准化ID删除成功！");
    } else {
      // 更新失败
      const errorMsg = apiResponse?.msg || "删除标准化ID失败";
      const { ElMessage } = await import("element-plus");
      ElMessage.error(errorMsg);
      console.error("删除事件标准化ID失败:", apiResponse);
    }

  } catch (error) {
    console.error("删除事件标准化ID失败:", error);
    const { ElMessage } = await import("element-plus");
    ElMessage.error("删除标准化ID失败");
  }
};

// 刷新数据方法
const refreshData = (updatedRow: any) => {
  const index = tableData.value.findIndex(item => item.eventId === updatedRow.eventId);
  if (index !== -1) {
    tableData.value[index] = { ...tableData.value[index], ...updatedRow };
  }
};

// 组件卸载时清理监听器
onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
  // 清理选中状态
  selectedRowIndexes.value = [];
  selectedRows.value = [];
  window.removeEventListener("resize", updateTableSize);
});

// 暴露方法给父组件
defineExpose({
  loadEventList,
  refreshData
});
</script>

<style scoped>
.device-template-event {
  width: 100%;
  height: 100%;
}

.event-table-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* 只读单元格样式 */
.read-only-cell {
  padding: 4px 8px;
  min-height: 24px;
  line-height: 24px;
  color: var(--el-text-color-regular);
}

/* 过滤器样式 */
.el-table-v2__demo-filter {
  border-top: var(--el-border);
  margin: 12px -12px -12px;
  padding: 0 12px;
  display: flex;
  justify-content: space-between;
}

.filter-wrapper {
  padding: 8px 0;
}

.filter-group {
  margin-bottom: 8px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.text-xs {
  font-size: 12px;
}

.mr-2 {
  margin-right: 8px;
}

.cursor-pointer {
  cursor: pointer;
}

/* 全局表格样式 */
:deep(.el-table) {
  font-size: 12px;
}

:deep(.el-table .cell) {
  padding: 4px 8px;
}

/* 选中行样式 */
:deep(.selected-row) {
  background-color: var(--el-color-primary-light-9) !important;
}

:deep(.selected-row:hover) {
  background-color: var(--el-color-primary-light-8) !important;
}

/* 表格行样式优化 - 确保整行都能响应拖拽 */
:deep(.el-table-v2__row) {
  position: relative;
  cursor: pointer;
}

:deep(.el-table-v2__row-cell) {
  position: relative;
  z-index: 1;
  pointer-events: auto;
}

/* 确保行间空白区域也能响应鼠标事件 */
:deep(.el-table-v2__row) {
  background-clip: padding-box;
}

/* 当正在拖拽时，为目标行添加视觉反馈 */
:deep(.drop-target-row) {
  background-color: var(--el-color-success-light-9) !important;
  border: 2px dashed var(--el-color-success) !important;
  box-sizing: border-box;
}

/* 拖拽时的鼠标样式 */
:deep(.el-table-v2__row):hover {
  background-color: var(--el-fill-color-light);
}

/* 当有拖拽数据时，整个表格区域显示可放置状态 */
.event-table-container.dragging {
  position: relative;
}

.event-table-container.dragging :deep(.el-table-v2__row):hover {
  background-color: var(--el-color-success-light-9);
  cursor: copy;
}

/* 确保拖拽目标行的边框不被其他元素遮挡 */
:deep(.drop-target-row) {
  position: relative;
  z-index: 10;
}

/* 标准化ID单元格样式 */
.standard-id-cell {
  position: relative;
}

.standard-id-cell:hover .delete-btn {
  opacity: 1;
}
</style>