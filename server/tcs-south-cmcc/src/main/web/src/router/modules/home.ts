const Layout = () => import("@/layout/index.vue");

export default {
  path: "/",
  name: "Plugin",
  component: Layout,
  redirect: "/standard",
  meta: {
    icon: "ep/home-filled",
    title: "移动B接口管理",
    rank: 0
  },
  children: [
    {
      path: "/plugin",
      name: "Plugin-Home",
      component: () => import("@/views/welcome/index.vue"),
      meta: {
        title: "插件模板"
      }
    },
    {
      path: "/standard",
      name: "Standard-Home",
      component: () => import("@/views/standard/index.vue"),
      meta: {
        title: "标准化管理"
      }
    }
  ]
} satisfies RouteConfigsTable;
