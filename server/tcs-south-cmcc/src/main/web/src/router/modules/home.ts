const Layout = () => import("@/layout/index.vue");

export default {
  path: "/",
  name: "CMCCPlugin",
  component: Layout,
  redirect: "/standard",
  meta: {
    icon: "ep/home-filled",
    title: "移动B接口管理",
    rank: 0
  },
  children: [
    {
      path: "/standard",
      name: "Standard-Home",
      component: () => import("@/views/standard/index.vue"),
      meta: {
        title: "标准化管理"
      }
    },
    {
      path: "/standard-setting",
      name: "StandardSetting",
      component: () => import("@/views/standard-config/index.vue"),
      meta: {
        title: "标准化设置"
      }
    }
  ]
} satisfies RouteConfigsTable;
