import { http } from "@/utils/http";

// 信号标准化数据类型
export interface SignalStandardData {
  id?: number;
  signalStandardId: string;
  deviceType?: number; // 设备类型ID
  deviceTypeName: string;
  deviceSubType: string;
  standardSignalName: string;
  semaphoreType?: number; // 信号类型ID
  semaphoreTypeName: string;
  unit?: string; // 单位字段
  meaning: string;
  description: string;
}

// 告警标准化数据类型
export interface AlarmStandardData {
  id?: number;
  alarmStandardId: string;
  deviceType?: number; // 设备类型ID
  deviceTypeName: string;
  alarmLogicClass?: number; // 告警逻辑类别ID，可选
  alarmLogicClassName: string;
  alarmLogicSubclass: string;
  alarmStandardName: string;
  meaning: string;
  communicationBuildingAlarmLevel?: number; // 通信楼宇告警级别ID
  communicationBuildingAlarmLevelName: string;
  transmissionNodeAlarmLevel?: number; // 传输节点告警级别ID
  transmissionNodeAlarmLevelName: string;
  communicationBaseStationAlarmLevel?: number; // 通信基站告警级别ID
  communicationBaseStationAlarmLevelName: string;
  idcAlarmLevel?: number; // IDC告警级别ID
  idcAlarmLevelName: string;
  description: string;
}

// 字典项数据类型
export interface DictionaryItem {
  dictionaryItemId: number;
  itemId: number;
  itemValue: string;
  categoryId: number;
  parentCategoryId: number;
  parentItemId?: number;
  translateKey?: string;
  enable: number;
  isSystem?: number;
  isDefault?: number;
  description?: string;
  isExpired?: number;
  extendField?: string;
}

// 分页参数类型
export interface PageParams {
  current?: number;
  size?: number;
  keyword?: string;
}

// 分页响应类型
export interface PageResponse<T> {
  records: T[];
  total: number;
  current: number;
  size: number;
}

// API响应类型
export interface ApiResponse<T = any> {
  state: boolean;
  message: string;
  data: T;
}

/** 获取所有信号标准化数据（包含字典名称） */
export const getSignalStandardList = (params?: PageParams) => {
  return http.request<ApiResponse<SignalStandardData[]>>("get", "/api/thing/south-cmcc-plugin/signal-dic/all-with-names", { params });
};

/** 获取所有告警标准化数据（包含字典名称） */
export const getAlarmStandardList = (params?: PageParams) => {
  return http.request<ApiResponse<AlarmStandardData[]>>("get", "/api/thing/south-cmcc-plugin/alarm-dic/all-with-names", { params });
};

/** 新增信号标准化 */
export const addSignalStandard = (data: SignalStandardData) => {
  return http.request<ApiResponse<boolean>>("post", "/api/thing/south-cmcc-plugin/signal-dic", { data });
};

/** 更新信号标准化 */
export const updateSignalStandard = (data: SignalStandardData) => {
  return http.request<ApiResponse<boolean>>("put", "/api/thing/south-cmcc-plugin/signal-dic", { data });
};

/** 删除信号标准化 */
export const deleteSignalStandard = (id: number) => {
  return http.request<ApiResponse<boolean>>("delete", `/api/thing/south-cmcc-plugin/signal-dic/${id}`);
};

/** 新增告警标准化 */
export const addAlarmStandard = (data: AlarmStandardData) => {
  return http.request<ApiResponse<boolean>>("post", "/api/thing/south-cmcc-plugin/alarm-dic", { data });
};

/** 更新告警标准化 */
export const updateAlarmStandard = (data: AlarmStandardData) => {
  return http.request<ApiResponse<boolean>>("put", "/api/thing/south-cmcc-plugin/alarm-dic", { data });
};

/** 删除告警标准化 */
export const deleteAlarmStandard = (id: number) => {
  return http.request<ApiResponse<boolean>>("delete", `/api/thing/south-cmcc-plugin/alarm-dic/${id}`);
};

// 字典相关API
export const dictionaryApi = {
  // 获取设备类型列表
  getDeviceType: () => {
    return http.request<ApiResponse<DictionaryItem[]>>("get", "/api/thing/south-cmcc-plugin/dictionary/device-type");
  },

  // 获取信号类型列表
  getSemaphoreType: () => {
    return http.request<ApiResponse<DictionaryItem[]>>("get", "/api/thing/south-cmcc-plugin/dictionary/semaphore-type");
  },

  // 根据设备类型ID获取告警类型列表
  getAlarmTypesByDeviceType: (deviceTypeId: number) => {
    return http.request<ApiResponse<DictionaryItem[]>>("get", `/api/thing/south-cmcc-plugin/dictionary/alarm-type-by-device-type?deviceTypeId=${deviceTypeId}`);
  },

  // 获取告警级别列表
  getEventSeverity: () => {
    return http.request<ApiResponse<DictionaryItem[]>>("get", "/api/thing/south-cmcc-plugin/dictionary/event-severity");
  }
}; 