/**
 * 性能监控工具
 */

// 性能计时器
export class PerformanceTimer {
  private startTime: number = 0;
  private endTime: number = 0;

  start(label?: string) {
    this.startTime = performance.now();
    if (label) {
      console.time(label);
    }
  }

  end(label?: string): number {
    this.endTime = performance.now();
    const duration = this.endTime - this.startTime;
    
    if (label) {
      console.timeEnd(label);
      console.log(`${label} 耗时: ${duration.toFixed(2)}ms`);
    }
    
    return duration;
  }
}

// 防抖函数
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate?: boolean
): ((...args: Parameters<T>) => void) => {
  let timeout: ReturnType<typeof setTimeout> | null = null;
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    
    const callNow = immediate && !timeout;
    
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func(...args);
  };
};

// 节流函数
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

// 内存使用监控
export const getMemoryUsage = () => {
  if ('memory' in performance) {
    const memory = (performance as any).memory;
    return {
      used: Math.round(memory.usedJSHeapSize / 1048576 * 100) / 100,
      total: Math.round(memory.totalJSHeapSize / 1048576 * 100) / 100,
      limit: Math.round(memory.jsHeapSizeLimit / 1048576 * 100) / 100
    };
  }
  return null;
};

// 表格渲染性能优化
export const optimizeTableRendering = () => {
  // 启用 CSS containment
  const style = document.createElement('style');
  style.textContent = `
    .el-table__row {
      contain: layout style paint;
    }
    .el-table__body-wrapper {
      transform: translateZ(0);
      will-change: transform;
    }
  `;
  document.head.appendChild(style);
};

// 虚拟滚动配置
export const getVirtualScrollConfig = (itemHeight: number = 50) => {
  return {
    height: 400,
    itemHeight,
    buffer: 5,
    threshold: 20
  };
};

// 分页配置优化
export const getOptimizedPaginationConfig = () => {
  return {
    pageSizes: [10, 20, 50, 100],
    defaultPageSize: 20,
    layout: "total, sizes, prev, pager, next, jumper",
    background: true,
    small: false
  };
}; 