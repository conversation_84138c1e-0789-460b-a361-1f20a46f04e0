package com.siteweb.stream.common.runtime.events;

import com.siteweb.tcs.common.expression.enums.OperationType;
import com.siteweb.stream.common.messages.StreamMessage;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @ClassName: StreamResourceLifeCycleResponseEvent
 * @descriptions:
 * @author: xsx
 * @date: 2/20/2025 10:19 AM
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class StreamResourceLifeCycleResponseEvent <T> extends StreamMessage {
    private OperationType operationType;
    private Boolean isSuccess;
    private Long streamResourceId;
    private T t;
}
