package com.siteweb.stream.common.stream;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;


/**
 * Shape 前端描述文件
 */
@Data
public class StreamShapeInfo {

    /**
     * Shape 所属PluginId
     */
    private long pluginId;

    /**
     * Shape的标识符
     */
    private String shapeType;

    /**
     * Shape全名，前端显示
     */
    private String shapeName;

    /**
     * Shape别名，短名 显示在Node中
     */
    private String shapeAlias;

    /**
     * 前端默认图标
     */
    private String defaultIcon;
    /**
     * 前端默认图标颜色
     */
    private String defaultIconColor;
    /**
     * 默认背景颜色
     */
    private  String defaultBkColor;

    /**
     * 当前组件版本
     */
    private StreamShapeVersion version ;
    /**
     * 作者
     */
    private String author;

    /**
     * Shape前端工具箱分组
     */
    private String[] groups;

    /**
     * Shape标签，搜索标签
     */
    private String[] tags;

    /**
     * Shape描述,简述，一句话描述干什么用的 工具栏tooltip
     */
    private String tooltip;


    /**
     * 默认配置
     */
    private StreamShapeOption defaultOptions;

    private JsonNode optionSchema;
    /**
     * 该形状组件是否被标记为弃用
     * 弃用组件后面不再维护，并在新的数据流中不再可用
     */
    private boolean deprecated;

    /**
     * 帮助/说明文档
     */
    private String document;

    /**
     * 入口元配置
     */
    private List<StreamShapeLetInfo> inlets = new ArrayList<>();

    /**
     * 出口元配置
     */
    private List<StreamShapeLetInfo> outlets = new ArrayList<>();




}
