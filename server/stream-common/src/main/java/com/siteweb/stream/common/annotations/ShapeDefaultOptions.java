package com.siteweb.stream.common.annotations;

import com.siteweb.stream.common.stream.AbstractShapeDefaultOption;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Shape的默认配置实现定义
 *
 * <AUTHOR> (2025-02-15)
 **/
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface ShapeDefaultOptions {

    Class<? extends AbstractShapeDefaultOption> value();

}
