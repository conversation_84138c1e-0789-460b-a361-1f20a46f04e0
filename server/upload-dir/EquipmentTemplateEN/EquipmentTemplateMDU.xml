<?xml version="1.0" encoding="utf-8"?>
<EquipmentTemplates Name="Equipment Template List">
  <EquipmentTemplate EquipmentTemplateId="876003590" ParentTemplateId="0" EquipmentTemplateName="MDU" ProtocolCode="MDUIO 6-00" EquipmentCategory="53" EquipmentType="1" Memo="2018/8/23 13:41:40" Property="1/3" Decription="" EquipmentStyle="" Unit="" Vendor="" EquipmentBaseType="" StationCategory="">
    <Signals Name="Template Signal">
      <Signal SignalId="-3" SignalName="Communication Status" SignalCategory="2" SignalType="2" ChannelNo="-3" ChannelType="1" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="1" SignalProperty="27" SignalMeanings="0:communication abnormal;1:communication normal" />
      <Signal SignalId="530000010" SignalName="Power status" SignalCategory="2" SignalType="1" ChannelNo="0" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="1" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="2" SignalProperty="27" SignalMeanings="0:Power On;1:Power Loss" />
      <Signal SignalId="530000020" SignalName="Water" SignalCategory="2" SignalType="1" ChannelNo="1" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="1" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="3" SignalProperty="27" SignalMeanings="0:Normal;1:Alarm" />
      <Signal SignalId="530000030" SignalName="General standard DI1" SignalCategory="2" SignalType="1" ChannelNo="2" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="1" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="4" SignalProperty="27" SignalMeanings="0:Disconnect;1:Closure" />
      <Signal SignalId="530000040" SignalName="General standard DI2" SignalCategory="2" SignalType="1" ChannelNo="3" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="1" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="5" SignalProperty="27" SignalMeanings="0:Disconnect;1:Closure" />
      <Signal SignalId="530000050" SignalName="Smoke" SignalCategory="2" SignalType="1" ChannelNo="4" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="1" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="6" SignalProperty="27" SignalMeanings="0:Normal;1:Alarm" />
      <Signal SignalId="530000060" SignalName="Door magnet" SignalCategory="2" SignalType="1" ChannelNo="5" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="1" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="7" SignalProperty="27" SignalMeanings="0:Door Open;1:Door Close" />
      <Signal SignalId="530000171" SignalName="Templerature" SignalCategory="1" SignalType="1" ChannelNo="16" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.00" Unit="℃" StoreInterval="10800" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="8" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="530000181" SignalName="Signal Intensity" SignalCategory="1" SignalType="1" ChannelNo="17" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.00" Unit="dBm" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="9" SignalProperty="27" SignalMeanings="" />
    </Signals>
    <Events Name="Template Event">
      <Event EventId="-3" EventName="Communication Status" EventCategory="63" StartType="1" EndType="3" StartExpression="[-1,-3]" SuppressExpression="" SignalId="-3" Enable="True" Visible="True" Description="" DisplayIndex="1">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="0" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="communication abnormal" EquipmentState="" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="530000010" EventName="Power status" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,530000010]" SuppressExpression="" SignalId="530000010" Enable="True" Visible="True" Description="" DisplayIndex="2">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="2" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Power Off" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="530000020" EventName="Water" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,530000020]" SuppressExpression="" SignalId="530000020" Enable="True" Visible="True" Description="" DisplayIndex="3">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="530000050" EventName="Smoke" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,530000050]" SuppressExpression="" SignalId="530000050" Enable="True" Visible="True" Description="" DisplayIndex="6">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="530000060" EventName="Door magnet" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,530000060]" SuppressExpression="" SignalId="530000060" Enable="True" Visible="True" Description="" DisplayIndex="7">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="2" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Door Open" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="530000171" EventName="Templerature" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,530000171]" SuppressExpression="" SignalId="530000171" Enable="True" Visible="True" Description="" DisplayIndex="8">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="2" StartOperation="&gt;" StartCompareValue="50" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="height Templerature" EquipmentState="2" BaseTypeId="" StandardName="" />
          <EventCondition EventConditionId="1" EventSeverity="2" StartOperation="&lt;" StartCompareValue="10" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="low Templerature" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
    </Events>
    <Controls Name="Template Control" />
  </EquipmentTemplate>
  <Samplers>
    <Sampler SamplerId="876000119" SamplerName="MDU" SamplerType="18" ProtocolCode="MDUIO 6-00" DllCode="" DLLVersion="" ProtocolFilePath="" DLLFilePath="" DllPath="MDUAGENT.dll" Setting="9600,N,8,1" Description="" />
  </Samplers>
</EquipmentTemplates>